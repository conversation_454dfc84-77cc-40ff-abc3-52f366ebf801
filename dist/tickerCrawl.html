<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <script type="module" crossorigin>
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();var V=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function W8(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Y8={exports:{}},Hs={},X8={exports:{}},U={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zi=Symbol.for("react.element"),bp=Symbol.for("react.portal"),Cp=Symbol.for("react.fragment"),Ep=Symbol.for("react.strict_mode"),kp=Symbol.for("react.profiler"),Pp=Symbol.for("react.provider"),Dp=Symbol.for("react.context"),Ap=Symbol.for("react.forward_ref"),Lp=Symbol.for("react.suspense"),Op=Symbol.for("react.memo"),Mp=Symbol.for("react.lazy"),K0=Symbol.iterator;function Np(e){return e===null||typeof e!="object"?null:(e=K0&&e[K0]||e["@@iterator"],typeof e=="function"?e:null)}var Q8={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},K8=Object.assign,Z8={};function M1(e,t,n){this.props=e,this.context=t,this.refs=Z8,this.updater=n||Q8}M1.prototype.isReactComponent={};M1.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};M1.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function J8(){}J8.prototype=M1.prototype;function Zc(e,t,n){this.props=e,this.context=t,this.refs=Z8,this.updater=n||Q8}var Jc=Zc.prototype=new J8;Jc.constructor=Zc;K8(Jc,M1.prototype);Jc.isPureReactComponent=!0;var Z0=Array.isArray,t9=Object.prototype.hasOwnProperty,tu={current:null},e9={key:!0,ref:!0,__self:!0,__source:!0};function n9(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)t9.call(t,r)&&!e9.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),c=0;c<a;c++)l[c]=arguments[c+2];i.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:Zi,type:e,key:o,ref:s,props:i,_owner:tu.current}}function Rp(e,t){return{$$typeof:Zi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function eu(e){return typeof e=="object"&&e!==null&&e.$$typeof===Zi}function Ip(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var J0=/\/+/g;function Ea(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Ip(""+e.key):t.toString(36)}function Io(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Zi:case bp:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+Ea(s,0):r,Z0(i)?(n="",e!=null&&(n=e.replace(J0,"$&/")+"/"),Io(i,t,n,"",function(c){return c})):i!=null&&(eu(i)&&(i=Rp(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(J0,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",Z0(e))for(var a=0;a<e.length;a++){o=e[a];var l=r+Ea(o,a);s+=Io(o,t,n,l,i)}else if(l=Np(e),typeof l=="function")for(e=l.call(e),a=0;!(o=e.next()).done;)o=o.value,l=r+Ea(o,a++),s+=Io(o,t,n,l,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function fo(e,t,n){if(e==null)return e;var r=[],i=0;return Io(e,r,"","",function(o){return t.call(n,o,i++)}),r}function Vp(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ut={current:null},Vo={transition:null},qp={ReactCurrentDispatcher:Ut,ReactCurrentBatchConfig:Vo,ReactCurrentOwner:tu};function r9(){throw Error("act(...) is not supported in production builds of React.")}U.Children={map:fo,forEach:function(e,t,n){fo(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return fo(e,function(){t++}),t},toArray:function(e){return fo(e,function(t){return t})||[]},only:function(e){if(!eu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};U.Component=M1;U.Fragment=Cp;U.Profiler=kp;U.PureComponent=Zc;U.StrictMode=Ep;U.Suspense=Lp;U.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=qp;U.act=r9;U.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=K8({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=tu.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)t9.call(t,l)&&!e9.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var c=0;c<l;c++)a[c]=arguments[c+2];r.children=a}return{$$typeof:Zi,type:e.type,key:i,ref:o,props:r,_owner:s}};U.createContext=function(e){return e={$$typeof:Dp,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Pp,_context:e},e.Consumer=e};U.createElement=n9;U.createFactory=function(e){var t=n9.bind(null,e);return t.type=e,t};U.createRef=function(){return{current:null}};U.forwardRef=function(e){return{$$typeof:Ap,render:e}};U.isValidElement=eu;U.lazy=function(e){return{$$typeof:Mp,_payload:{_status:-1,_result:e},_init:Vp}};U.memo=function(e,t){return{$$typeof:Op,type:e,compare:t===void 0?null:t}};U.startTransition=function(e){var t=Vo.transition;Vo.transition={};try{e()}finally{Vo.transition=t}};U.unstable_act=r9;U.useCallback=function(e,t){return Ut.current.useCallback(e,t)};U.useContext=function(e){return Ut.current.useContext(e)};U.useDebugValue=function(){};U.useDeferredValue=function(e){return Ut.current.useDeferredValue(e)};U.useEffect=function(e,t){return Ut.current.useEffect(e,t)};U.useId=function(){return Ut.current.useId()};U.useImperativeHandle=function(e,t,n){return Ut.current.useImperativeHandle(e,t,n)};U.useInsertionEffect=function(e,t){return Ut.current.useInsertionEffect(e,t)};U.useLayoutEffect=function(e,t){return Ut.current.useLayoutEffect(e,t)};U.useMemo=function(e,t){return Ut.current.useMemo(e,t)};U.useReducer=function(e,t,n){return Ut.current.useReducer(e,t,n)};U.useRef=function(e){return Ut.current.useRef(e)};U.useState=function(e){return Ut.current.useState(e)};U.useSyncExternalStore=function(e,t,n){return Ut.current.useSyncExternalStore(e,t,n)};U.useTransition=function(){return Ut.current.useTransition()};U.version="18.3.1";X8.exports=U;var D=X8.exports;const K=W8(D);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fp=D,Bp=Symbol.for("react.element"),zp=Symbol.for("react.fragment"),jp=Object.prototype.hasOwnProperty,Up=Fp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,$p={key:!0,ref:!0,__self:!0,__source:!0};function i9(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)jp.call(t,r)&&!$p.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Bp,type:e,key:o,ref:s,props:i,_owner:Up.current}}Hs.Fragment=zp;Hs.jsx=i9;Hs.jsxs=i9;Y8.exports=Hs;var tn=Y8.exports,Gt={loading:0,loaded:1,playing:2,paused:3,stopped:4,removed:5},o9={exports:{}},_e={},s9={exports:{}},a9={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,M){var N=P.length;P.push(M);t:for(;0<N;){var j=N-1>>>1,q=P[j];if(0<i(q,M))P[j]=M,P[N]=q,N=j;else break t}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var M=P[0],N=P.pop();if(N!==M){P[0]=N;t:for(var j=0,q=P.length,nr=q>>>1;j<nr;){var Et=2*(j+1)-1,Le=P[Et],rr=Et+1,uo=P[rr];if(0>i(Le,N))rr<q&&0>i(uo,Le)?(P[j]=uo,P[rr]=N,j=rr):(P[j]=Le,P[Et]=N,j=Et);else if(rr<q&&0>i(uo,N))P[j]=uo,P[rr]=N,j=rr;else break t}}return M}function i(P,M){var N=P.sortIndex-M.sortIndex;return N!==0?N:P.id-M.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],c=[],u=1,f=null,d=3,p=!1,v=!1,h=!1,_=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,g=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(P){for(var M=n(c);M!==null;){if(M.callback===null)r(c);else if(M.startTime<=P)r(c),M.sortIndex=M.expirationTime,t(l,M);else break;M=n(c)}}function w(P){if(h=!1,y(P),!v)if(n(l)!==null)v=!0,F(x);else{var M=n(c);M!==null&&z(w,M.startTime-P)}}function x(P,M){v=!1,h&&(h=!1,m(b),b=-1),p=!0;var N=d;try{for(y(M),f=n(l);f!==null&&(!(f.expirationTime>M)||P&&!L());){var j=f.callback;if(typeof j=="function"){f.callback=null,d=f.priorityLevel;var q=j(f.expirationTime<=M);M=e.unstable_now(),typeof q=="function"?f.callback=q:f===n(l)&&r(l),y(M)}else r(l);f=n(l)}if(f!==null)var nr=!0;else{var Et=n(c);Et!==null&&z(w,Et.startTime-M),nr=!1}return nr}finally{f=null,d=N,p=!1}}var S=!1,T=null,b=-1,C=5,E=-1;function L(){return!(e.unstable_now()-E<C)}function O(){if(T!==null){var P=e.unstable_now();E=P;var M=!0;try{M=T(!0,P)}finally{M?B():(S=!1,T=null)}}else S=!1}var B;if(typeof g=="function")B=function(){g(O)};else if(typeof MessageChannel<"u"){var G=new MessageChannel,I=G.port2;G.port1.onmessage=O,B=function(){I.postMessage(null)}}else B=function(){_(O,0)};function F(P){T=P,S||(S=!0,B())}function z(P,M){b=_(function(){P(e.unstable_now())},M)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){v||p||(v=!0,F(x))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(P){switch(d){case 1:case 2:case 3:var M=3;break;default:M=d}var N=d;d=M;try{return P()}finally{d=N}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,M){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var N=d;d=P;try{return M()}finally{d=N}},e.unstable_scheduleCallback=function(P,M,N){var j=e.unstable_now();switch(typeof N=="object"&&N!==null?(N=N.delay,N=typeof N=="number"&&0<N?j+N:j):N=j,P){case 1:var q=-1;break;case 2:q=250;break;case 5:q=**********;break;case 4:q=1e4;break;default:q=5e3}return q=N+q,P={id:u++,callback:M,priorityLevel:P,startTime:N,expirationTime:q,sortIndex:-1},N>j?(P.sortIndex=N,t(c,P),n(l)===null&&P===n(c)&&(h?(m(b),b=-1):h=!0,z(w,N-j))):(P.sortIndex=q,t(l,P),v||p||(v=!0,F(x))),P},e.unstable_shouldYield=L,e.unstable_wrapCallback=function(P){var M=d;return function(){var N=d;d=M;try{return P.apply(this,arguments)}finally{d=N}}}})(a9);s9.exports=a9;var Hp=s9.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gp=D,ge=Hp;function k(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l9=new Set,Si={};function Mr(e,t){y1(e,t),y1(e+"Capture",t)}function y1(e,t){for(Si[e]=t,e=0;e<t.length;e++)l9.add(t[e])}var pn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Sl=Object.prototype.hasOwnProperty,Wp=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,tf={},ef={};function Yp(e){return Sl.call(ef,e)?!0:Sl.call(tf,e)?!1:Wp.test(e)?ef[e]=!0:(tf[e]=!0,!1)}function Xp(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Qp(e,t,n,r){if(t===null||typeof t>"u"||Xp(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function $t(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var At={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){At[e]=new $t(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];At[t]=new $t(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){At[e]=new $t(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){At[e]=new $t(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){At[e]=new $t(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){At[e]=new $t(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){At[e]=new $t(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){At[e]=new $t(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){At[e]=new $t(e,5,!1,e.toLowerCase(),null,!1,!1)});var nu=/[\-:]([a-z])/g;function ru(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(nu,ru);At[t]=new $t(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(nu,ru);At[t]=new $t(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(nu,ru);At[t]=new $t(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){At[e]=new $t(e,1,!1,e.toLowerCase(),null,!1,!1)});At.xlinkHref=new $t("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){At[e]=new $t(e,1,!1,e.toLowerCase(),null,!0,!0)});function iu(e,t,n,r){var i=At.hasOwnProperty(t)?At[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Qp(t,n,i,r)&&(n=null),r||i===null?Yp(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var wn=Gp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ho=Symbol.for("react.element"),Ur=Symbol.for("react.portal"),$r=Symbol.for("react.fragment"),ou=Symbol.for("react.strict_mode"),Tl=Symbol.for("react.profiler"),c9=Symbol.for("react.provider"),u9=Symbol.for("react.context"),su=Symbol.for("react.forward_ref"),bl=Symbol.for("react.suspense"),Cl=Symbol.for("react.suspense_list"),au=Symbol.for("react.memo"),bn=Symbol.for("react.lazy"),f9=Symbol.for("react.offscreen"),nf=Symbol.iterator;function V1(e){return e===null||typeof e!="object"?null:(e=nf&&e[nf]||e["@@iterator"],typeof e=="function"?e:null)}var ct=Object.assign,ka;function X1(e){if(ka===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);ka=t&&t[1]||""}return`
`+ka+e}var Pa=!1;function Da(e,t){if(!e||Pa)return"";Pa=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var i=c.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,a=o.length-1;1<=s&&0<=a&&i[s]!==o[a];)a--;for(;1<=s&&0<=a;s--,a--)if(i[s]!==o[a]){if(s!==1||a!==1)do if(s--,a--,0>a||i[s]!==o[a]){var l=`
`+i[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{Pa=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?X1(e):""}function Kp(e){switch(e.tag){case 5:return X1(e.type);case 16:return X1("Lazy");case 13:return X1("Suspense");case 19:return X1("SuspenseList");case 0:case 2:case 15:return e=Da(e.type,!1),e;case 11:return e=Da(e.type.render,!1),e;case 1:return e=Da(e.type,!0),e;default:return""}}function El(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case $r:return"Fragment";case Ur:return"Portal";case Tl:return"Profiler";case ou:return"StrictMode";case bl:return"Suspense";case Cl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case u9:return(e.displayName||"Context")+".Consumer";case c9:return(e._context.displayName||"Context")+".Provider";case su:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case au:return t=e.displayName||null,t!==null?t:El(e.type)||"Memo";case bn:t=e._payload,e=e._init;try{return El(e(t))}catch{}}return null}function Zp(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return El(t);case 8:return t===ou?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Gn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function d9(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Jp(e){var t=d9(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function po(e){e._valueTracker||(e._valueTracker=Jp(e))}function h9(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=d9(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function es(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function kl(e,t){var n=t.checked;return ct({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n!=null?n:e._wrapperState.initialChecked})}function rf(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Gn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function p9(e,t){t=t.checked,t!=null&&iu(e,"checked",t,!1)}function Pl(e,t){p9(e,t);var n=Gn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Dl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Dl(e,t.type,Gn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function of(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Dl(e,t,n){(t!=="number"||es(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Q1=Array.isArray;function a1(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Gn(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Al(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(k(91));return ct({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function sf(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(k(92));if(Q1(n)){if(1<n.length)throw Error(k(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Gn(n)}}function m9(e,t){var n=Gn(t.value),r=Gn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function af(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function g9(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ll(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?g9(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var mo,v9=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(mo=mo||document.createElement("div"),mo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=mo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Ti(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var ri={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},t7=["Webkit","ms","Moz","O"];Object.keys(ri).forEach(function(e){t7.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),ri[t]=ri[e]})});function y9(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||ri.hasOwnProperty(e)&&ri[e]?(""+t).trim():t+"px"}function _9(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=y9(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var e7=ct({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ol(e,t){if(t){if(e7[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(k(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(k(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(k(61))}if(t.style!=null&&typeof t.style!="object")throw Error(k(62))}}function Ml(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Nl=null;function lu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Rl=null,l1=null,c1=null;function lf(e){if(e=eo(e)){if(typeof Rl!="function")throw Error(k(280));var t=e.stateNode;t&&(t=Qs(t),Rl(e.stateNode,e.type,t))}}function w9(e){l1?c1?c1.push(e):c1=[e]:l1=e}function x9(){if(l1){var e=l1,t=c1;if(c1=l1=null,lf(e),t)for(e=0;e<t.length;e++)lf(t[e])}}function S9(e,t){return e(t)}function T9(){}var Aa=!1;function b9(e,t,n){if(Aa)return e(t,n);Aa=!0;try{return S9(e,t,n)}finally{Aa=!1,(l1!==null||c1!==null)&&(T9(),x9())}}function bi(e,t){var n=e.stateNode;if(n===null)return null;var r=Qs(n);if(r===null)return null;n=r[t];t:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break t;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(k(231,t,typeof n));return n}var Il=!1;if(pn)try{var q1={};Object.defineProperty(q1,"passive",{get:function(){Il=!0}}),window.addEventListener("test",q1,q1),window.removeEventListener("test",q1,q1)}catch{Il=!1}function n7(e,t,n,r,i,o,s,a,l){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var ii=!1,ns=null,rs=!1,Vl=null,r7={onError:function(e){ii=!0,ns=e}};function i7(e,t,n,r,i,o,s,a,l){ii=!1,ns=null,n7.apply(r7,arguments)}function o7(e,t,n,r,i,o,s,a,l){if(i7.apply(this,arguments),ii){if(ii){var c=ns;ii=!1,ns=null}else throw Error(k(198));rs||(rs=!0,Vl=c)}}function Nr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function C9(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function cf(e){if(Nr(e)!==e)throw Error(k(188))}function s7(e){var t=e.alternate;if(!t){if(t=Nr(e),t===null)throw Error(k(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return cf(i),e;if(o===r)return cf(i),t;o=o.sibling}throw Error(k(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s){for(a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s)throw Error(k(189))}}if(n.alternate!==r)throw Error(k(190))}if(n.tag!==3)throw Error(k(188));return n.stateNode.current===n?e:t}function E9(e){return e=s7(e),e!==null?k9(e):null}function k9(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=k9(e);if(t!==null)return t;e=e.sibling}return null}var P9=ge.unstable_scheduleCallback,uf=ge.unstable_cancelCallback,a7=ge.unstable_shouldYield,l7=ge.unstable_requestPaint,mt=ge.unstable_now,c7=ge.unstable_getCurrentPriorityLevel,cu=ge.unstable_ImmediatePriority,D9=ge.unstable_UserBlockingPriority,is=ge.unstable_NormalPriority,u7=ge.unstable_LowPriority,A9=ge.unstable_IdlePriority,Gs=null,We=null;function f7(e){if(We&&typeof We.onCommitFiberRoot=="function")try{We.onCommitFiberRoot(Gs,e,void 0,(e.current.flags&128)===128)}catch{}}var Ve=Math.clz32?Math.clz32:p7,d7=Math.log,h7=Math.LN2;function p7(e){return e>>>=0,e===0?32:31-(d7(e)/h7|0)|0}var go=64,vo=4194304;function K1(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function os(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~i;a!==0?r=K1(a):(o&=s,o!==0&&(r=K1(o)))}else s=n&~i,s!==0?r=K1(s):o!==0&&(r=K1(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ve(t),i=1<<n,r|=e[n],t&=~i;return r}function m7(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function g7(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Ve(o),a=1<<s,l=i[s];l===-1?(!(a&n)||a&r)&&(i[s]=m7(a,t)):l<=t&&(e.expiredLanes|=a),o&=~a}}function ql(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function L9(){var e=go;return go<<=1,!(go&4194240)&&(go=64),e}function La(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ji(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ve(t),e[t]=n}function v7(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Ve(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function uu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ve(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var Y=0;function O9(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var M9,fu,N9,R9,I9,Fl=!1,yo=[],Rn=null,In=null,Vn=null,Ci=new Map,Ei=new Map,kn=[],y7="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ff(e,t){switch(e){case"focusin":case"focusout":Rn=null;break;case"dragenter":case"dragleave":In=null;break;case"mouseover":case"mouseout":Vn=null;break;case"pointerover":case"pointerout":Ci.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ei.delete(t.pointerId)}}function F1(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=eo(t),t!==null&&fu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function _7(e,t,n,r,i){switch(t){case"focusin":return Rn=F1(Rn,e,t,n,r,i),!0;case"dragenter":return In=F1(In,e,t,n,r,i),!0;case"mouseover":return Vn=F1(Vn,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Ci.set(o,F1(Ci.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Ei.set(o,F1(Ei.get(o)||null,e,t,n,r,i)),!0}return!1}function V9(e){var t=pr(e.target);if(t!==null){var n=Nr(t);if(n!==null){if(t=n.tag,t===13){if(t=C9(n),t!==null){e.blockedOn=t,I9(e.priority,function(){N9(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function qo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Bl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Nl=r,n.target.dispatchEvent(r),Nl=null}else return t=eo(n),t!==null&&fu(t),e.blockedOn=n,!1;t.shift()}return!0}function df(e,t,n){qo(e)&&n.delete(t)}function w7(){Fl=!1,Rn!==null&&qo(Rn)&&(Rn=null),In!==null&&qo(In)&&(In=null),Vn!==null&&qo(Vn)&&(Vn=null),Ci.forEach(df),Ei.forEach(df)}function B1(e,t){e.blockedOn===t&&(e.blockedOn=null,Fl||(Fl=!0,ge.unstable_scheduleCallback(ge.unstable_NormalPriority,w7)))}function ki(e){function t(i){return B1(i,e)}if(0<yo.length){B1(yo[0],e);for(var n=1;n<yo.length;n++){var r=yo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Rn!==null&&B1(Rn,e),In!==null&&B1(In,e),Vn!==null&&B1(Vn,e),Ci.forEach(t),Ei.forEach(t),n=0;n<kn.length;n++)r=kn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<kn.length&&(n=kn[0],n.blockedOn===null);)V9(n),n.blockedOn===null&&kn.shift()}var u1=wn.ReactCurrentBatchConfig,ss=!0;function x7(e,t,n,r){var i=Y,o=u1.transition;u1.transition=null;try{Y=1,du(e,t,n,r)}finally{Y=i,u1.transition=o}}function S7(e,t,n,r){var i=Y,o=u1.transition;u1.transition=null;try{Y=4,du(e,t,n,r)}finally{Y=i,u1.transition=o}}function du(e,t,n,r){if(ss){var i=Bl(e,t,n,r);if(i===null)za(e,t,r,as,n),ff(e,r);else if(_7(i,e,t,n,r))r.stopPropagation();else if(ff(e,r),t&4&&-1<y7.indexOf(e)){for(;i!==null;){var o=eo(i);if(o!==null&&M9(o),o=Bl(e,t,n,r),o===null&&za(e,t,r,as,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else za(e,t,r,null,n)}}var as=null;function Bl(e,t,n,r){if(as=null,e=lu(r),e=pr(e),e!==null)if(t=Nr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=C9(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return as=e,null}function q9(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(c7()){case cu:return 1;case D9:return 4;case is:case u7:return 16;case A9:return 536870912;default:return 16}default:return 16}}var Dn=null,hu=null,Fo=null;function F9(){if(Fo)return Fo;var e,t=hu,n=t.length,r,i="value"in Dn?Dn.value:Dn.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return Fo=i.slice(e,1<r?1-r:void 0)}function Bo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function _o(){return!0}function hf(){return!1}function we(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?_o:hf,this.isPropagationStopped=hf,this}return ct(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=_o)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=_o)},persist:function(){},isPersistent:_o}),t}var N1={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},pu=we(N1),to=ct({},N1,{view:0,detail:0}),T7=we(to),Oa,Ma,z1,Ws=ct({},to,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:mu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==z1&&(z1&&e.type==="mousemove"?(Oa=e.screenX-z1.screenX,Ma=e.screenY-z1.screenY):Ma=Oa=0,z1=e),Oa)},movementY:function(e){return"movementY"in e?e.movementY:Ma}}),pf=we(Ws),b7=ct({},Ws,{dataTransfer:0}),C7=we(b7),E7=ct({},to,{relatedTarget:0}),Na=we(E7),k7=ct({},N1,{animationName:0,elapsedTime:0,pseudoElement:0}),P7=we(k7),D7=ct({},N1,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),A7=we(D7),L7=ct({},N1,{data:0}),mf=we(L7),O7={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},M7={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},N7={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function R7(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=N7[e])?!!t[e]:!1}function mu(){return R7}var I7=ct({},to,{key:function(e){if(e.key){var t=O7[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Bo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?M7[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:mu,charCode:function(e){return e.type==="keypress"?Bo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Bo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),V7=we(I7),q7=ct({},Ws,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),gf=we(q7),F7=ct({},to,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:mu}),B7=we(F7),z7=ct({},N1,{propertyName:0,elapsedTime:0,pseudoElement:0}),j7=we(z7),U7=ct({},Ws,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),$7=we(U7),H7=[9,13,27,32],gu=pn&&"CompositionEvent"in window,oi=null;pn&&"documentMode"in document&&(oi=document.documentMode);var G7=pn&&"TextEvent"in window&&!oi,B9=pn&&(!gu||oi&&8<oi&&11>=oi),vf=" ",yf=!1;function z9(e,t){switch(e){case"keyup":return H7.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function j9(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Hr=!1;function W7(e,t){switch(e){case"compositionend":return j9(t);case"keypress":return t.which!==32?null:(yf=!0,vf);case"textInput":return e=t.data,e===vf&&yf?null:e;default:return null}}function Y7(e,t){if(Hr)return e==="compositionend"||!gu&&z9(e,t)?(e=F9(),Fo=hu=Dn=null,Hr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return B9&&t.locale!=="ko"?null:t.data;default:return null}}var X7={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function _f(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!X7[e.type]:t==="textarea"}function U9(e,t,n,r){w9(r),t=ls(t,"onChange"),0<t.length&&(n=new pu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var si=null,Pi=null;function Q7(e){t2(e,0)}function Ys(e){var t=Yr(e);if(h9(t))return e}function K7(e,t){if(e==="change")return t}var $9=!1;if(pn){var Ra;if(pn){var Ia="oninput"in document;if(!Ia){var wf=document.createElement("div");wf.setAttribute("oninput","return;"),Ia=typeof wf.oninput=="function"}Ra=Ia}else Ra=!1;$9=Ra&&(!document.documentMode||9<document.documentMode)}function xf(){si&&(si.detachEvent("onpropertychange",H9),Pi=si=null)}function H9(e){if(e.propertyName==="value"&&Ys(Pi)){var t=[];U9(t,Pi,e,lu(e)),b9(Q7,t)}}function Z7(e,t,n){e==="focusin"?(xf(),si=t,Pi=n,si.attachEvent("onpropertychange",H9)):e==="focusout"&&xf()}function J7(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ys(Pi)}function t3(e,t){if(e==="click")return Ys(t)}function e3(e,t){if(e==="input"||e==="change")return Ys(t)}function n3(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Fe=typeof Object.is=="function"?Object.is:n3;function Di(e,t){if(Fe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Sl.call(t,i)||!Fe(e[i],t[i]))return!1}return!0}function Sf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Tf(e,t){var n=Sf(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=Sf(n)}}function G9(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?G9(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function W9(){for(var e=window,t=es();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=es(e.document)}return t}function vu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function r3(e){var t=W9(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&G9(n.ownerDocument.documentElement,n)){if(r!==null&&vu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=Tf(n,o);var s=Tf(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var i3=pn&&"documentMode"in document&&11>=document.documentMode,Gr=null,zl=null,ai=null,jl=!1;function bf(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;jl||Gr==null||Gr!==es(r)||(r=Gr,"selectionStart"in r&&vu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),ai&&Di(ai,r)||(ai=r,r=ls(zl,"onSelect"),0<r.length&&(t=new pu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Gr)))}function wo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Wr={animationend:wo("Animation","AnimationEnd"),animationiteration:wo("Animation","AnimationIteration"),animationstart:wo("Animation","AnimationStart"),transitionend:wo("Transition","TransitionEnd")},Va={},Y9={};pn&&(Y9=document.createElement("div").style,"AnimationEvent"in window||(delete Wr.animationend.animation,delete Wr.animationiteration.animation,delete Wr.animationstart.animation),"TransitionEvent"in window||delete Wr.transitionend.transition);function Xs(e){if(Va[e])return Va[e];if(!Wr[e])return e;var t=Wr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Y9)return Va[e]=t[n];return e}var X9=Xs("animationend"),Q9=Xs("animationiteration"),K9=Xs("animationstart"),Z9=Xs("transitionend"),J9=new Map,Cf="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Zn(e,t){J9.set(e,t),Mr(t,[e])}for(var qa=0;qa<Cf.length;qa++){var Fa=Cf[qa],o3=Fa.toLowerCase(),s3=Fa[0].toUpperCase()+Fa.slice(1);Zn(o3,"on"+s3)}Zn(X9,"onAnimationEnd");Zn(Q9,"onAnimationIteration");Zn(K9,"onAnimationStart");Zn("dblclick","onDoubleClick");Zn("focusin","onFocus");Zn("focusout","onBlur");Zn(Z9,"onTransitionEnd");y1("onMouseEnter",["mouseout","mouseover"]);y1("onMouseLeave",["mouseout","mouseover"]);y1("onPointerEnter",["pointerout","pointerover"]);y1("onPointerLeave",["pointerout","pointerover"]);Mr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Mr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Mr("onBeforeInput",["compositionend","keypress","textInput","paste"]);Mr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Mr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Mr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Z1="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),a3=new Set("cancel close invalid load scroll toggle".split(" ").concat(Z1));function Ef(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,o7(r,t,void 0,e),e.currentTarget=null}function t2(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;t:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,c=a.currentTarget;if(a=a.listener,l!==o&&i.isPropagationStopped())break t;Ef(i,a,c),o=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,c=a.currentTarget,a=a.listener,l!==o&&i.isPropagationStopped())break t;Ef(i,a,c),o=l}}}if(rs)throw e=Vl,rs=!1,Vl=null,e}function et(e,t){var n=t[Wl];n===void 0&&(n=t[Wl]=new Set);var r=e+"__bubble";n.has(r)||(e2(t,e,2,!1),n.add(r))}function Ba(e,t,n){var r=0;t&&(r|=4),e2(n,e,r,t)}var xo="_reactListening"+Math.random().toString(36).slice(2);function Ai(e){if(!e[xo]){e[xo]=!0,l9.forEach(function(n){n!=="selectionchange"&&(a3.has(n)||Ba(n,!1,e),Ba(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[xo]||(t[xo]=!0,Ba("selectionchange",!1,t))}}function e2(e,t,n,r){switch(q9(t)){case 1:var i=x7;break;case 4:i=S7;break;default:i=du}n=i.bind(null,t,n,e),i=void 0,!Il||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function za(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)t:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;s=s.return}for(;a!==null;){if(s=pr(a),s===null)return;if(l=s.tag,l===5||l===6){r=o=s;continue t}a=a.parentNode}}r=r.return}b9(function(){var c=o,u=lu(n),f=[];t:{var d=J9.get(e);if(d!==void 0){var p=pu,v=e;switch(e){case"keypress":if(Bo(n)===0)break t;case"keydown":case"keyup":p=V7;break;case"focusin":v="focus",p=Na;break;case"focusout":v="blur",p=Na;break;case"beforeblur":case"afterblur":p=Na;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":p=pf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":p=C7;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":p=B7;break;case X9:case Q9:case K9:p=P7;break;case Z9:p=j7;break;case"scroll":p=T7;break;case"wheel":p=$7;break;case"copy":case"cut":case"paste":p=A7;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":p=gf}var h=(t&4)!==0,_=!h&&e==="scroll",m=h?d!==null?d+"Capture":null:d;h=[];for(var g=c,y;g!==null;){y=g;var w=y.stateNode;if(y.tag===5&&w!==null&&(y=w,m!==null&&(w=bi(g,m),w!=null&&h.push(Li(g,w,y)))),_)break;g=g.return}0<h.length&&(d=new p(d,v,null,n,u),f.push({event:d,listeners:h}))}}if(!(t&7)){t:{if(d=e==="mouseover"||e==="pointerover",p=e==="mouseout"||e==="pointerout",d&&n!==Nl&&(v=n.relatedTarget||n.fromElement)&&(pr(v)||v[mn]))break t;if((p||d)&&(d=u.window===u?u:(d=u.ownerDocument)?d.defaultView||d.parentWindow:window,p?(v=n.relatedTarget||n.toElement,p=c,v=v?pr(v):null,v!==null&&(_=Nr(v),v!==_||v.tag!==5&&v.tag!==6)&&(v=null)):(p=null,v=c),p!==v)){if(h=pf,w="onMouseLeave",m="onMouseEnter",g="mouse",(e==="pointerout"||e==="pointerover")&&(h=gf,w="onPointerLeave",m="onPointerEnter",g="pointer"),_=p==null?d:Yr(p),y=v==null?d:Yr(v),d=new h(w,g+"leave",p,n,u),d.target=_,d.relatedTarget=y,w=null,pr(u)===c&&(h=new h(m,g+"enter",v,n,u),h.target=y,h.relatedTarget=_,w=h),_=w,p&&v)e:{for(h=p,m=v,g=0,y=h;y;y=Br(y))g++;for(y=0,w=m;w;w=Br(w))y++;for(;0<g-y;)h=Br(h),g--;for(;0<y-g;)m=Br(m),y--;for(;g--;){if(h===m||m!==null&&h===m.alternate)break e;h=Br(h),m=Br(m)}h=null}else h=null;p!==null&&kf(f,d,p,h,!1),v!==null&&_!==null&&kf(f,_,v,h,!0)}}t:{if(d=c?Yr(c):window,p=d.nodeName&&d.nodeName.toLowerCase(),p==="select"||p==="input"&&d.type==="file")var x=K7;else if(_f(d))if($9)x=e3;else{x=J7;var S=Z7}else(p=d.nodeName)&&p.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(x=t3);if(x&&(x=x(e,c))){U9(f,x,n,u);break t}S&&S(e,d,c),e==="focusout"&&(S=d._wrapperState)&&S.controlled&&d.type==="number"&&Dl(d,"number",d.value)}switch(S=c?Yr(c):window,e){case"focusin":(_f(S)||S.contentEditable==="true")&&(Gr=S,zl=c,ai=null);break;case"focusout":ai=zl=Gr=null;break;case"mousedown":jl=!0;break;case"contextmenu":case"mouseup":case"dragend":jl=!1,bf(f,n,u);break;case"selectionchange":if(i3)break;case"keydown":case"keyup":bf(f,n,u)}var T;if(gu)t:{switch(e){case"compositionstart":var b="onCompositionStart";break t;case"compositionend":b="onCompositionEnd";break t;case"compositionupdate":b="onCompositionUpdate";break t}b=void 0}else Hr?z9(e,n)&&(b="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(b="onCompositionStart");b&&(B9&&n.locale!=="ko"&&(Hr||b!=="onCompositionStart"?b==="onCompositionEnd"&&Hr&&(T=F9()):(Dn=u,hu="value"in Dn?Dn.value:Dn.textContent,Hr=!0)),S=ls(c,b),0<S.length&&(b=new mf(b,e,null,n,u),f.push({event:b,listeners:S}),T?b.data=T:(T=j9(n),T!==null&&(b.data=T)))),(T=G7?W7(e,n):Y7(e,n))&&(c=ls(c,"onBeforeInput"),0<c.length&&(u=new mf("onBeforeInput","beforeinput",null,n,u),f.push({event:u,listeners:c}),u.data=T))}t2(f,t)})}function Li(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ls(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=bi(e,n),o!=null&&r.unshift(Li(e,o,i)),o=bi(e,t),o!=null&&r.push(Li(e,o,i))),e=e.return}return r}function Br(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function kf(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,c=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&c!==null&&(a=c,i?(l=bi(n,o),l!=null&&s.unshift(Li(n,l,a))):i||(l=bi(n,o),l!=null&&s.push(Li(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var l3=/\r\n?/g,c3=/\u0000|\uFFFD/g;function Pf(e){return(typeof e=="string"?e:""+e).replace(l3,`
`).replace(c3,"")}function So(e,t,n){if(t=Pf(t),Pf(e)!==t&&n)throw Error(k(425))}function cs(){}var Ul=null,$l=null;function Hl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Gl=typeof setTimeout=="function"?setTimeout:void 0,u3=typeof clearTimeout=="function"?clearTimeout:void 0,Df=typeof Promise=="function"?Promise:void 0,f3=typeof queueMicrotask=="function"?queueMicrotask:typeof Df<"u"?function(e){return Df.resolve(null).then(e).catch(d3)}:Gl;function d3(e){setTimeout(function(){throw e})}function ja(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),ki(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);ki(t)}function qn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Af(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var R1=Math.random().toString(36).slice(2),$e="__reactFiber$"+R1,Oi="__reactProps$"+R1,mn="__reactContainer$"+R1,Wl="__reactEvents$"+R1,h3="__reactListeners$"+R1,p3="__reactHandles$"+R1;function pr(e){var t=e[$e];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mn]||n[$e]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Af(e);e!==null;){if(n=e[$e])return n;e=Af(e)}return t}e=n,n=e.parentNode}return null}function eo(e){return e=e[$e]||e[mn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Yr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(k(33))}function Qs(e){return e[Oi]||null}var Yl=[],Xr=-1;function Jn(e){return{current:e}}function nt(e){0>Xr||(e.current=Yl[Xr],Yl[Xr]=null,Xr--)}function tt(e,t){Xr++,Yl[Xr]=e.current,e.current=t}var Wn={},Vt=Jn(Wn),Kt=Jn(!1),Er=Wn;function _1(e,t){var n=e.type.contextTypes;if(!n)return Wn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Zt(e){return e=e.childContextTypes,e!=null}function us(){nt(Kt),nt(Vt)}function Lf(e,t,n){if(Vt.current!==Wn)throw Error(k(168));tt(Vt,t),tt(Kt,n)}function n2(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(k(108,Zp(e)||"Unknown",i));return ct({},n,r)}function fs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Wn,Er=Vt.current,tt(Vt,e),tt(Kt,Kt.current),!0}function Of(e,t,n){var r=e.stateNode;if(!r)throw Error(k(169));n?(e=n2(e,t,Er),r.__reactInternalMemoizedMergedChildContext=e,nt(Kt),nt(Vt),tt(Vt,e)):nt(Kt),tt(Kt,n)}var an=null,Ks=!1,Ua=!1;function r2(e){an===null?an=[e]:an.push(e)}function m3(e){Ks=!0,r2(e)}function tr(){if(!Ua&&an!==null){Ua=!0;var e=0,t=Y;try{var n=an;for(Y=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}an=null,Ks=!1}catch(i){throw an!==null&&(an=an.slice(e+1)),P9(cu,tr),i}finally{Y=t,Ua=!1}}return null}var Qr=[],Kr=0,ds=null,hs=0,Se=[],Te=0,kr=null,cn=1,un="";function lr(e,t){Qr[Kr++]=hs,Qr[Kr++]=ds,ds=e,hs=t}function i2(e,t,n){Se[Te++]=cn,Se[Te++]=un,Se[Te++]=kr,kr=e;var r=cn;e=un;var i=32-Ve(r)-1;r&=~(1<<i),n+=1;var o=32-Ve(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,cn=1<<32-Ve(t)+i|n<<i|r,un=o+e}else cn=1<<o|n<<i|r,un=e}function yu(e){e.return!==null&&(lr(e,1),i2(e,1,0))}function _u(e){for(;e===ds;)ds=Qr[--Kr],Qr[Kr]=null,hs=Qr[--Kr],Qr[Kr]=null;for(;e===kr;)kr=Se[--Te],Se[Te]=null,un=Se[--Te],Se[Te]=null,cn=Se[--Te],Se[Te]=null}var he=null,fe=null,rt=!1,Ie=null;function o2(e,t){var n=be(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Mf(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,he=e,fe=qn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,he=e,fe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=kr!==null?{id:cn,overflow:un}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=be(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,he=e,fe=null,!0):!1;default:return!1}}function Xl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ql(e){if(rt){var t=fe;if(t){var n=t;if(!Mf(e,t)){if(Xl(e))throw Error(k(418));t=qn(n.nextSibling);var r=he;t&&Mf(e,t)?o2(r,n):(e.flags=e.flags&-4097|2,rt=!1,he=e)}}else{if(Xl(e))throw Error(k(418));e.flags=e.flags&-4097|2,rt=!1,he=e}}}function Nf(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;he=e}function To(e){if(e!==he)return!1;if(!rt)return Nf(e),rt=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Hl(e.type,e.memoizedProps)),t&&(t=fe)){if(Xl(e))throw s2(),Error(k(418));for(;t;)o2(e,t),t=qn(t.nextSibling)}if(Nf(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(k(317));t:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){fe=qn(e.nextSibling);break t}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}fe=null}}else fe=he?qn(e.stateNode.nextSibling):null;return!0}function s2(){for(var e=fe;e;)e=qn(e.nextSibling)}function w1(){fe=he=null,rt=!1}function wu(e){Ie===null?Ie=[e]:Ie.push(e)}var g3=wn.ReactCurrentBatchConfig;function j1(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(k(309));var r=n.stateNode}if(!r)throw Error(k(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var a=i.refs;s===null?delete a[o]:a[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(k(284));if(!n._owner)throw Error(k(290,e))}return e}function bo(e,t){throw e=Object.prototype.toString.call(t),Error(k(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Rf(e){var t=e._init;return t(e._payload)}function a2(e){function t(m,g){if(e){var y=m.deletions;y===null?(m.deletions=[g],m.flags|=16):y.push(g)}}function n(m,g){if(!e)return null;for(;g!==null;)t(m,g),g=g.sibling;return null}function r(m,g){for(m=new Map;g!==null;)g.key!==null?m.set(g.key,g):m.set(g.index,g),g=g.sibling;return m}function i(m,g){return m=jn(m,g),m.index=0,m.sibling=null,m}function o(m,g,y){return m.index=y,e?(y=m.alternate,y!==null?(y=y.index,y<g?(m.flags|=2,g):y):(m.flags|=2,g)):(m.flags|=1048576,g)}function s(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,g,y,w){return g===null||g.tag!==6?(g=Qa(y,m.mode,w),g.return=m,g):(g=i(g,y),g.return=m,g)}function l(m,g,y,w){var x=y.type;return x===$r?u(m,g,y.props.children,w,y.key):g!==null&&(g.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===bn&&Rf(x)===g.type)?(w=i(g,y.props),w.ref=j1(m,g,y),w.return=m,w):(w=Wo(y.type,y.key,y.props,null,m.mode,w),w.ref=j1(m,g,y),w.return=m,w)}function c(m,g,y,w){return g===null||g.tag!==4||g.stateNode.containerInfo!==y.containerInfo||g.stateNode.implementation!==y.implementation?(g=Ka(y,m.mode,w),g.return=m,g):(g=i(g,y.children||[]),g.return=m,g)}function u(m,g,y,w,x){return g===null||g.tag!==7?(g=xr(y,m.mode,w,x),g.return=m,g):(g=i(g,y),g.return=m,g)}function f(m,g,y){if(typeof g=="string"&&g!==""||typeof g=="number")return g=Qa(""+g,m.mode,y),g.return=m,g;if(typeof g=="object"&&g!==null){switch(g.$$typeof){case ho:return y=Wo(g.type,g.key,g.props,null,m.mode,y),y.ref=j1(m,null,g),y.return=m,y;case Ur:return g=Ka(g,m.mode,y),g.return=m,g;case bn:var w=g._init;return f(m,w(g._payload),y)}if(Q1(g)||V1(g))return g=xr(g,m.mode,y,null),g.return=m,g;bo(m,g)}return null}function d(m,g,y,w){var x=g!==null?g.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return x!==null?null:a(m,g,""+y,w);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case ho:return y.key===x?l(m,g,y,w):null;case Ur:return y.key===x?c(m,g,y,w):null;case bn:return x=y._init,d(m,g,x(y._payload),w)}if(Q1(y)||V1(y))return x!==null?null:u(m,g,y,w,null);bo(m,y)}return null}function p(m,g,y,w,x){if(typeof w=="string"&&w!==""||typeof w=="number")return m=m.get(y)||null,a(g,m,""+w,x);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case ho:return m=m.get(w.key===null?y:w.key)||null,l(g,m,w,x);case Ur:return m=m.get(w.key===null?y:w.key)||null,c(g,m,w,x);case bn:var S=w._init;return p(m,g,y,S(w._payload),x)}if(Q1(w)||V1(w))return m=m.get(y)||null,u(g,m,w,x,null);bo(g,w)}return null}function v(m,g,y,w){for(var x=null,S=null,T=g,b=g=0,C=null;T!==null&&b<y.length;b++){T.index>b?(C=T,T=null):C=T.sibling;var E=d(m,T,y[b],w);if(E===null){T===null&&(T=C);break}e&&T&&E.alternate===null&&t(m,T),g=o(E,g,b),S===null?x=E:S.sibling=E,S=E,T=C}if(b===y.length)return n(m,T),rt&&lr(m,b),x;if(T===null){for(;b<y.length;b++)T=f(m,y[b],w),T!==null&&(g=o(T,g,b),S===null?x=T:S.sibling=T,S=T);return rt&&lr(m,b),x}for(T=r(m,T);b<y.length;b++)C=p(T,m,b,y[b],w),C!==null&&(e&&C.alternate!==null&&T.delete(C.key===null?b:C.key),g=o(C,g,b),S===null?x=C:S.sibling=C,S=C);return e&&T.forEach(function(L){return t(m,L)}),rt&&lr(m,b),x}function h(m,g,y,w){var x=V1(y);if(typeof x!="function")throw Error(k(150));if(y=x.call(y),y==null)throw Error(k(151));for(var S=x=null,T=g,b=g=0,C=null,E=y.next();T!==null&&!E.done;b++,E=y.next()){T.index>b?(C=T,T=null):C=T.sibling;var L=d(m,T,E.value,w);if(L===null){T===null&&(T=C);break}e&&T&&L.alternate===null&&t(m,T),g=o(L,g,b),S===null?x=L:S.sibling=L,S=L,T=C}if(E.done)return n(m,T),rt&&lr(m,b),x;if(T===null){for(;!E.done;b++,E=y.next())E=f(m,E.value,w),E!==null&&(g=o(E,g,b),S===null?x=E:S.sibling=E,S=E);return rt&&lr(m,b),x}for(T=r(m,T);!E.done;b++,E=y.next())E=p(T,m,b,E.value,w),E!==null&&(e&&E.alternate!==null&&T.delete(E.key===null?b:E.key),g=o(E,g,b),S===null?x=E:S.sibling=E,S=E);return e&&T.forEach(function(O){return t(m,O)}),rt&&lr(m,b),x}function _(m,g,y,w){if(typeof y=="object"&&y!==null&&y.type===$r&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case ho:t:{for(var x=y.key,S=g;S!==null;){if(S.key===x){if(x=y.type,x===$r){if(S.tag===7){n(m,S.sibling),g=i(S,y.props.children),g.return=m,m=g;break t}}else if(S.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===bn&&Rf(x)===S.type){n(m,S.sibling),g=i(S,y.props),g.ref=j1(m,S,y),g.return=m,m=g;break t}n(m,S);break}else t(m,S);S=S.sibling}y.type===$r?(g=xr(y.props.children,m.mode,w,y.key),g.return=m,m=g):(w=Wo(y.type,y.key,y.props,null,m.mode,w),w.ref=j1(m,g,y),w.return=m,m=w)}return s(m);case Ur:t:{for(S=y.key;g!==null;){if(g.key===S)if(g.tag===4&&g.stateNode.containerInfo===y.containerInfo&&g.stateNode.implementation===y.implementation){n(m,g.sibling),g=i(g,y.children||[]),g.return=m,m=g;break t}else{n(m,g);break}else t(m,g);g=g.sibling}g=Ka(y,m.mode,w),g.return=m,m=g}return s(m);case bn:return S=y._init,_(m,g,S(y._payload),w)}if(Q1(y))return v(m,g,y,w);if(V1(y))return h(m,g,y,w);bo(m,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,g!==null&&g.tag===6?(n(m,g.sibling),g=i(g,y),g.return=m,m=g):(n(m,g),g=Qa(y,m.mode,w),g.return=m,m=g),s(m)):n(m,g)}return _}var x1=a2(!0),l2=a2(!1),ps=Jn(null),ms=null,Zr=null,xu=null;function Su(){xu=Zr=ms=null}function Tu(e){var t=ps.current;nt(ps),e._currentValue=t}function Kl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function f1(e,t){ms=e,xu=Zr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Qt=!0),e.firstContext=null)}function Pe(e){var t=e._currentValue;if(xu!==e)if(e={context:e,memoizedValue:t,next:null},Zr===null){if(ms===null)throw Error(k(308));Zr=e,ms.dependencies={lanes:0,firstContext:e}}else Zr=Zr.next=e;return t}var mr=null;function bu(e){mr===null?mr=[e]:mr.push(e)}function c2(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,bu(t)):(n.next=i.next,i.next=n),t.interleaved=n,gn(e,r)}function gn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Cn=!1;function Cu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function u2(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function fn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Fn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,W&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,gn(e,n)}return i=r.interleaved,i===null?(t.next=t,bu(r)):(t.next=i.next,i.next=t),r.interleaved=t,gn(e,n)}function zo(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,uu(e,n)}}function If(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function gs(e,t,n,r){var i=e.updateQueue;Cn=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,c=l.next;l.next=null,s===null?o=c:s.next=c,s=l;var u=e.alternate;u!==null&&(u=u.updateQueue,a=u.lastBaseUpdate,a!==s&&(a===null?u.firstBaseUpdate=c:a.next=c,u.lastBaseUpdate=l))}if(o!==null){var f=i.baseState;s=0,u=c=l=null,a=o;do{var d=a.lane,p=a.eventTime;if((r&d)===d){u!==null&&(u=u.next={eventTime:p,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});t:{var v=e,h=a;switch(d=t,p=n,h.tag){case 1:if(v=h.payload,typeof v=="function"){f=v.call(p,f,d);break t}f=v;break t;case 3:v.flags=v.flags&-65537|128;case 0:if(v=h.payload,d=typeof v=="function"?v.call(p,f,d):v,d==null)break t;f=ct({},f,d);break t;case 2:Cn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,d=i.effects,d===null?i.effects=[a]:d.push(a))}else p={eventTime:p,lane:d,tag:a.tag,payload:a.payload,callback:a.callback,next:null},u===null?(c=u=p,l=f):u=u.next=p,s|=d;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;d=a,a=d.next,d.next=null,i.lastBaseUpdate=d,i.shared.pending=null}}while(!0);if(u===null&&(l=f),i.baseState=l,i.firstBaseUpdate=c,i.lastBaseUpdate=u,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);Dr|=s,e.lanes=s,e.memoizedState=f}}function Vf(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(k(191,i));i.call(r)}}}var no={},Ye=Jn(no),Mi=Jn(no),Ni=Jn(no);function gr(e){if(e===no)throw Error(k(174));return e}function Eu(e,t){switch(tt(Ni,t),tt(Mi,e),tt(Ye,no),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ll(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ll(t,e)}nt(Ye),tt(Ye,t)}function S1(){nt(Ye),nt(Mi),nt(Ni)}function f2(e){gr(Ni.current);var t=gr(Ye.current),n=Ll(t,e.type);t!==n&&(tt(Mi,e),tt(Ye,n))}function ku(e){Mi.current===e&&(nt(Ye),nt(Mi))}var ot=Jn(0);function vs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var $a=[];function Pu(){for(var e=0;e<$a.length;e++)$a[e]._workInProgressVersionPrimary=null;$a.length=0}var jo=wn.ReactCurrentDispatcher,Ha=wn.ReactCurrentBatchConfig,Pr=0,lt=null,_t=null,xt=null,ys=!1,li=!1,Ri=0,v3=0;function Lt(){throw Error(k(321))}function Du(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Fe(e[n],t[n]))return!1;return!0}function Au(e,t,n,r,i,o){if(Pr=o,lt=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,jo.current=e===null||e.memoizedState===null?x3:S3,e=n(r,i),li){o=0;do{if(li=!1,Ri=0,25<=o)throw Error(k(301));o+=1,xt=_t=null,t.updateQueue=null,jo.current=T3,e=n(r,i)}while(li)}if(jo.current=_s,t=_t!==null&&_t.next!==null,Pr=0,xt=_t=lt=null,ys=!1,t)throw Error(k(300));return e}function Lu(){var e=Ri!==0;return Ri=0,e}function ze(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return xt===null?lt.memoizedState=xt=e:xt=xt.next=e,xt}function De(){if(_t===null){var e=lt.alternate;e=e!==null?e.memoizedState:null}else e=_t.next;var t=xt===null?lt.memoizedState:xt.next;if(t!==null)xt=t,_t=e;else{if(e===null)throw Error(k(310));_t=e,e={memoizedState:_t.memoizedState,baseState:_t.baseState,baseQueue:_t.baseQueue,queue:_t.queue,next:null},xt===null?lt.memoizedState=xt=e:xt=xt.next=e}return xt}function Ii(e,t){return typeof t=="function"?t(e):t}function Ga(e){var t=De(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=_t,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var a=s=null,l=null,c=o;do{var u=c.lane;if((Pr&u)===u)l!==null&&(l=l.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:u,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};l===null?(a=l=f,s=r):l=l.next=f,lt.lanes|=u,Dr|=u}c=c.next}while(c!==null&&c!==o);l===null?s=r:l.next=a,Fe(r,t.memoizedState)||(Qt=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,lt.lanes|=o,Dr|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Wa(e){var t=De(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);Fe(o,t.memoizedState)||(Qt=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function d2(){}function h2(e,t){var n=lt,r=De(),i=t(),o=!Fe(r.memoizedState,i);if(o&&(r.memoizedState=i,Qt=!0),r=r.queue,Ou(g2.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||xt!==null&&xt.memoizedState.tag&1){if(n.flags|=2048,Vi(9,m2.bind(null,n,r,i,t),void 0,null),bt===null)throw Error(k(349));Pr&30||p2(n,t,i)}return i}function p2(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=lt.updateQueue,t===null?(t={lastEffect:null,stores:null},lt.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function m2(e,t,n,r){t.value=n,t.getSnapshot=r,v2(t)&&y2(e)}function g2(e,t,n){return n(function(){v2(t)&&y2(e)})}function v2(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Fe(e,n)}catch{return!0}}function y2(e){var t=gn(e,1);t!==null&&qe(t,e,1,-1)}function qf(e){var t=ze();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ii,lastRenderedState:e},t.queue=e,e=e.dispatch=w3.bind(null,lt,e),[t.memoizedState,e]}function Vi(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=lt.updateQueue,t===null?(t={lastEffect:null,stores:null},lt.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function _2(){return De().memoizedState}function Uo(e,t,n,r){var i=ze();lt.flags|=e,i.memoizedState=Vi(1|t,n,void 0,r===void 0?null:r)}function Zs(e,t,n,r){var i=De();r=r===void 0?null:r;var o=void 0;if(_t!==null){var s=_t.memoizedState;if(o=s.destroy,r!==null&&Du(r,s.deps)){i.memoizedState=Vi(t,n,o,r);return}}lt.flags|=e,i.memoizedState=Vi(1|t,n,o,r)}function Ff(e,t){return Uo(8390656,8,e,t)}function Ou(e,t){return Zs(2048,8,e,t)}function w2(e,t){return Zs(4,2,e,t)}function x2(e,t){return Zs(4,4,e,t)}function S2(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function T2(e,t,n){return n=n!=null?n.concat([e]):null,Zs(4,4,S2.bind(null,t,e),n)}function Mu(){}function b2(e,t){var n=De();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Du(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function C2(e,t){var n=De();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Du(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function E2(e,t,n){return Pr&21?(Fe(n,t)||(n=L9(),lt.lanes|=n,Dr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Qt=!0),e.memoizedState=n)}function y3(e,t){var n=Y;Y=n!==0&&4>n?n:4,e(!0);var r=Ha.transition;Ha.transition={};try{e(!1),t()}finally{Y=n,Ha.transition=r}}function k2(){return De().memoizedState}function _3(e,t,n){var r=zn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},P2(e))D2(t,n);else if(n=c2(e,t,n,r),n!==null){var i=jt();qe(n,e,r,i),A2(n,t,r)}}function w3(e,t,n){var r=zn(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(P2(e))D2(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,a=o(s,n);if(i.hasEagerState=!0,i.eagerState=a,Fe(a,s)){var l=t.interleaved;l===null?(i.next=i,bu(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=c2(e,t,i,r),n!==null&&(i=jt(),qe(n,e,r,i),A2(n,t,r))}}function P2(e){var t=e.alternate;return e===lt||t!==null&&t===lt}function D2(e,t){li=ys=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function A2(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,uu(e,n)}}var _s={readContext:Pe,useCallback:Lt,useContext:Lt,useEffect:Lt,useImperativeHandle:Lt,useInsertionEffect:Lt,useLayoutEffect:Lt,useMemo:Lt,useReducer:Lt,useRef:Lt,useState:Lt,useDebugValue:Lt,useDeferredValue:Lt,useTransition:Lt,useMutableSource:Lt,useSyncExternalStore:Lt,useId:Lt,unstable_isNewReconciler:!1},x3={readContext:Pe,useCallback:function(e,t){return ze().memoizedState=[e,t===void 0?null:t],e},useContext:Pe,useEffect:Ff,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Uo(4194308,4,S2.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Uo(4194308,4,e,t)},useInsertionEffect:function(e,t){return Uo(4,2,e,t)},useMemo:function(e,t){var n=ze();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ze();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=_3.bind(null,lt,e),[r.memoizedState,e]},useRef:function(e){var t=ze();return e={current:e},t.memoizedState=e},useState:qf,useDebugValue:Mu,useDeferredValue:function(e){return ze().memoizedState=e},useTransition:function(){var e=qf(!1),t=e[0];return e=y3.bind(null,e[1]),ze().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=lt,i=ze();if(rt){if(n===void 0)throw Error(k(407));n=n()}else{if(n=t(),bt===null)throw Error(k(349));Pr&30||p2(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,Ff(g2.bind(null,r,o,e),[e]),r.flags|=2048,Vi(9,m2.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=ze(),t=bt.identifierPrefix;if(rt){var n=un,r=cn;n=(r&~(1<<32-Ve(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Ri++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=v3++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},S3={readContext:Pe,useCallback:b2,useContext:Pe,useEffect:Ou,useImperativeHandle:T2,useInsertionEffect:w2,useLayoutEffect:x2,useMemo:C2,useReducer:Ga,useRef:_2,useState:function(){return Ga(Ii)},useDebugValue:Mu,useDeferredValue:function(e){var t=De();return E2(t,_t.memoizedState,e)},useTransition:function(){var e=Ga(Ii)[0],t=De().memoizedState;return[e,t]},useMutableSource:d2,useSyncExternalStore:h2,useId:k2,unstable_isNewReconciler:!1},T3={readContext:Pe,useCallback:b2,useContext:Pe,useEffect:Ou,useImperativeHandle:T2,useInsertionEffect:w2,useLayoutEffect:x2,useMemo:C2,useReducer:Wa,useRef:_2,useState:function(){return Wa(Ii)},useDebugValue:Mu,useDeferredValue:function(e){var t=De();return _t===null?t.memoizedState=e:E2(t,_t.memoizedState,e)},useTransition:function(){var e=Wa(Ii)[0],t=De().memoizedState;return[e,t]},useMutableSource:d2,useSyncExternalStore:h2,useId:k2,unstable_isNewReconciler:!1};function Ne(e,t){if(e&&e.defaultProps){t=ct({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Zl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ct({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Js={isMounted:function(e){return(e=e._reactInternals)?Nr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=jt(),i=zn(e),o=fn(r,i);o.payload=t,n!=null&&(o.callback=n),t=Fn(e,o,i),t!==null&&(qe(t,e,i,r),zo(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=jt(),i=zn(e),o=fn(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Fn(e,o,i),t!==null&&(qe(t,e,i,r),zo(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=jt(),r=zn(e),i=fn(n,r);i.tag=2,t!=null&&(i.callback=t),t=Fn(e,i,r),t!==null&&(qe(t,e,r,n),zo(t,e,r))}};function Bf(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!Di(n,r)||!Di(i,o):!0}function L2(e,t,n){var r=!1,i=Wn,o=t.contextType;return typeof o=="object"&&o!==null?o=Pe(o):(i=Zt(t)?Er:Vt.current,r=t.contextTypes,o=(r=r!=null)?_1(e,i):Wn),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Js,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function zf(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Js.enqueueReplaceState(t,t.state,null)}function Jl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Cu(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=Pe(o):(o=Zt(t)?Er:Vt.current,i.context=_1(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Zl(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Js.enqueueReplaceState(i,i.state,null),gs(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function T1(e,t){try{var n="",r=t;do n+=Kp(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function Ya(e,t,n){return{value:e,source:null,stack:n!=null?n:null,digest:t!=null?t:null}}function tc(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var b3=typeof WeakMap=="function"?WeakMap:Map;function O2(e,t,n){n=fn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){xs||(xs=!0,uc=r),tc(e,t)},n}function M2(e,t,n){n=fn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){tc(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){tc(e,t),typeof r!="function"&&(Bn===null?Bn=new Set([this]):Bn.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function jf(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new b3;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=q3.bind(null,e,t,n),t.then(e,e))}function Uf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function $f(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=fn(-1,1),t.tag=2,Fn(n,t,1))),n.lanes|=1),e)}var C3=wn.ReactCurrentOwner,Qt=!1;function Ft(e,t,n,r){t.child=e===null?l2(t,null,n,r):x1(t,e.child,n,r)}function Hf(e,t,n,r,i){n=n.render;var o=t.ref;return f1(t,i),r=Au(e,t,n,r,o,i),n=Lu(),e!==null&&!Qt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,vn(e,t,i)):(rt&&n&&yu(t),t.flags|=1,Ft(e,t,r,i),t.child)}function Gf(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!zu(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,N2(e,t,o,r,i)):(e=Wo(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:Di,n(s,r)&&e.ref===t.ref)return vn(e,t,i)}return t.flags|=1,e=jn(o,r),e.ref=t.ref,e.return=t,t.child=e}function N2(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(Di(o,r)&&e.ref===t.ref)if(Qt=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Qt=!0);else return t.lanes=e.lanes,vn(e,t,i)}return ec(e,t,n,r,i)}function R2(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},tt(t1,oe),oe|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,tt(t1,oe),oe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,tt(t1,oe),oe|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,tt(t1,oe),oe|=r;return Ft(e,t,i,n),t.child}function I2(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ec(e,t,n,r,i){var o=Zt(n)?Er:Vt.current;return o=_1(t,o),f1(t,i),n=Au(e,t,n,r,o,i),r=Lu(),e!==null&&!Qt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,vn(e,t,i)):(rt&&r&&yu(t),t.flags|=1,Ft(e,t,n,i),t.child)}function Wf(e,t,n,r,i){if(Zt(n)){var o=!0;fs(t)}else o=!1;if(f1(t,i),t.stateNode===null)$o(e,t),L2(t,n,r),Jl(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,c=n.contextType;typeof c=="object"&&c!==null?c=Pe(c):(c=Zt(n)?Er:Vt.current,c=_1(t,c));var u=n.getDerivedStateFromProps,f=typeof u=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==c)&&zf(t,s,r,c),Cn=!1;var d=t.memoizedState;s.state=d,gs(t,r,s,i),l=t.memoizedState,a!==r||d!==l||Kt.current||Cn?(typeof u=="function"&&(Zl(t,n,u,r),l=t.memoizedState),(a=Cn||Bf(t,n,a,r,d,l,c))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=c,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,u2(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:Ne(t.type,a),s.props=c,f=t.pendingProps,d=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=Pe(l):(l=Zt(n)?Er:Vt.current,l=_1(t,l));var p=n.getDerivedStateFromProps;(u=typeof p=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==f||d!==l)&&zf(t,s,r,l),Cn=!1,d=t.memoizedState,s.state=d,gs(t,r,s,i);var v=t.memoizedState;a!==f||d!==v||Kt.current||Cn?(typeof p=="function"&&(Zl(t,n,p,r),v=t.memoizedState),(c=Cn||Bf(t,n,c,r,d,v,l)||!1)?(u||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,v,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,v,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),s.props=r,s.state=v,s.context=l,r=c):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return nc(e,t,n,r,o,i)}function nc(e,t,n,r,i,o){I2(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&Of(t,n,!1),vn(e,t,o);r=t.stateNode,C3.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=x1(t,e.child,null,o),t.child=x1(t,null,a,o)):Ft(e,t,a,o),t.memoizedState=r.state,i&&Of(t,n,!0),t.child}function V2(e){var t=e.stateNode;t.pendingContext?Lf(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Lf(e,t.context,!1),Eu(e,t.containerInfo)}function Yf(e,t,n,r,i){return w1(),wu(i),t.flags|=256,Ft(e,t,n,r),t.child}var rc={dehydrated:null,treeContext:null,retryLane:0};function ic(e){return{baseLanes:e,cachePool:null,transitions:null}}function q2(e,t,n){var r=t.pendingProps,i=ot.current,o=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),tt(ot,i&1),e===null)return Ql(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=na(s,r,0,null),e=xr(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=ic(n),t.memoizedState=rc,e):Nu(t,s));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return E3(e,t,s,r,a,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=jn(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?o=jn(a,o):(o=xr(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?ic(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=rc,r}return o=e.child,e=o.sibling,r=jn(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Nu(e,t){return t=na({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Co(e,t,n,r){return r!==null&&wu(r),x1(t,e.child,null,n),e=Nu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function E3(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=Ya(Error(k(422))),Co(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=na({mode:"visible",children:r.children},i,0,null),o=xr(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&x1(t,e.child,null,s),t.child.memoizedState=ic(s),t.memoizedState=rc,o);if(!(t.mode&1))return Co(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,o=Error(k(419)),r=Ya(o,r,void 0),Co(e,t,s,r)}if(a=(s&e.childLanes)!==0,Qt||a){if(r=bt,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,gn(e,i),qe(r,e,i,-1))}return Bu(),r=Ya(Error(k(421))),Co(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=F3.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,fe=qn(i.nextSibling),he=t,rt=!0,Ie=null,e!==null&&(Se[Te++]=cn,Se[Te++]=un,Se[Te++]=kr,cn=e.id,un=e.overflow,kr=t),t=Nu(t,r.children),t.flags|=4096,t)}function Xf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Kl(e.return,t,n)}function Xa(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function F2(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(Ft(e,t,r.children,n),r=ot.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)t:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Xf(e,n,t);else if(e.tag===19)Xf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(tt(ot,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&vs(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Xa(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&vs(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Xa(t,!0,n,null,o);break;case"together":Xa(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function $o(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function vn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Dr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(k(153));if(t.child!==null){for(e=t.child,n=jn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=jn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function k3(e,t,n){switch(t.tag){case 3:V2(t),w1();break;case 5:f2(t);break;case 1:Zt(t.type)&&fs(t);break;case 4:Eu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;tt(ps,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(tt(ot,ot.current&1),t.flags|=128,null):n&t.child.childLanes?q2(e,t,n):(tt(ot,ot.current&1),e=vn(e,t,n),e!==null?e.sibling:null);tt(ot,ot.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return F2(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),tt(ot,ot.current),r)break;return null;case 22:case 23:return t.lanes=0,R2(e,t,n)}return vn(e,t,n)}var B2,oc,z2,j2;B2=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};oc=function(){};z2=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,gr(Ye.current);var o=null;switch(n){case"input":i=kl(e,i),r=kl(e,r),o=[];break;case"select":i=ct({},i,{value:void 0}),r=ct({},r,{value:void 0}),o=[];break;case"textarea":i=Al(e,i),r=Al(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=cs)}Ol(n,r);var s;n=null;for(c in i)if(!r.hasOwnProperty(c)&&i.hasOwnProperty(c)&&i[c]!=null)if(c==="style"){var a=i[c];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Si.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var l=r[c];if(a=i!=null?i[c]:void 0,r.hasOwnProperty(c)&&l!==a&&(l!=null||a!=null))if(c==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(o||(o=[]),o.push(c,n)),n=l;else c==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(o=o||[]).push(c,l)):c==="children"?typeof l!="string"&&typeof l!="number"||(o=o||[]).push(c,""+l):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Si.hasOwnProperty(c)?(l!=null&&c==="onScroll"&&et("scroll",e),o||a===l||(o=[])):(o=o||[]).push(c,l))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}};j2=function(e,t,n,r){n!==r&&(t.flags|=4)};function U1(e,t){if(!rt)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ot(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function P3(e,t,n){var r=t.pendingProps;switch(_u(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ot(t),null;case 1:return Zt(t.type)&&us(),Ot(t),null;case 3:return r=t.stateNode,S1(),nt(Kt),nt(Vt),Pu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(To(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ie!==null&&(hc(Ie),Ie=null))),oc(e,t),Ot(t),null;case 5:ku(t);var i=gr(Ni.current);if(n=t.type,e!==null&&t.stateNode!=null)z2(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(k(166));return Ot(t),null}if(e=gr(Ye.current),To(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[$e]=t,r[Oi]=o,e=(t.mode&1)!==0,n){case"dialog":et("cancel",r),et("close",r);break;case"iframe":case"object":case"embed":et("load",r);break;case"video":case"audio":for(i=0;i<Z1.length;i++)et(Z1[i],r);break;case"source":et("error",r);break;case"img":case"image":case"link":et("error",r),et("load",r);break;case"details":et("toggle",r);break;case"input":rf(r,o),et("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},et("invalid",r);break;case"textarea":sf(r,o),et("invalid",r)}Ol(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var a=o[s];s==="children"?typeof a=="string"?r.textContent!==a&&(o.suppressHydrationWarning!==!0&&So(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&So(r.textContent,a,e),i=["children",""+a]):Si.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&et("scroll",r)}switch(n){case"input":po(r),of(r,o,!0);break;case"textarea":po(r),af(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=cs)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=g9(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[$e]=t,e[Oi]=r,B2(e,t,!1,!1),t.stateNode=e;t:{switch(s=Ml(n,r),n){case"dialog":et("cancel",e),et("close",e),i=r;break;case"iframe":case"object":case"embed":et("load",e),i=r;break;case"video":case"audio":for(i=0;i<Z1.length;i++)et(Z1[i],e);i=r;break;case"source":et("error",e),i=r;break;case"img":case"image":case"link":et("error",e),et("load",e),i=r;break;case"details":et("toggle",e),i=r;break;case"input":rf(e,r),i=kl(e,r),et("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=ct({},r,{value:void 0}),et("invalid",e);break;case"textarea":sf(e,r),i=Al(e,r),et("invalid",e);break;default:i=r}Ol(n,i),a=i;for(o in a)if(a.hasOwnProperty(o)){var l=a[o];o==="style"?_9(e,l):o==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&v9(e,l)):o==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Ti(e,l):typeof l=="number"&&Ti(e,""+l):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Si.hasOwnProperty(o)?l!=null&&o==="onScroll"&&et("scroll",e):l!=null&&iu(e,o,l,s))}switch(n){case"input":po(e),of(e,r,!1);break;case"textarea":po(e),af(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Gn(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?a1(e,!!r.multiple,o,!1):r.defaultValue!=null&&a1(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=cs)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break t;case"img":r=!0;break t;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ot(t),null;case 6:if(e&&t.stateNode!=null)j2(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(k(166));if(n=gr(Ni.current),gr(Ye.current),To(t)){if(r=t.stateNode,n=t.memoizedProps,r[$e]=t,(o=r.nodeValue!==n)&&(e=he,e!==null))switch(e.tag){case 3:So(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&So(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[$e]=t,t.stateNode=r}return Ot(t),null;case 13:if(nt(ot),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(rt&&fe!==null&&t.mode&1&&!(t.flags&128))s2(),w1(),t.flags|=98560,o=!1;else if(o=To(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(k(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(k(317));o[$e]=t}else w1(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ot(t),o=!1}else Ie!==null&&(hc(Ie),Ie=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ot.current&1?wt===0&&(wt=3):Bu())),t.updateQueue!==null&&(t.flags|=4),Ot(t),null);case 4:return S1(),oc(e,t),e===null&&Ai(t.stateNode.containerInfo),Ot(t),null;case 10:return Tu(t.type._context),Ot(t),null;case 17:return Zt(t.type)&&us(),Ot(t),null;case 19:if(nt(ot),o=t.memoizedState,o===null)return Ot(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)U1(o,!1);else{if(wt!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=vs(e),s!==null){for(t.flags|=128,U1(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return tt(ot,ot.current&1|2),t.child}e=e.sibling}o.tail!==null&&mt()>b1&&(t.flags|=128,r=!0,U1(o,!1),t.lanes=4194304)}else{if(!r)if(e=vs(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),U1(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!rt)return Ot(t),null}else 2*mt()-o.renderingStartTime>b1&&n!==1073741824&&(t.flags|=128,r=!0,U1(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=mt(),t.sibling=null,n=ot.current,tt(ot,r?n&1|2:n&1),t):(Ot(t),null);case 22:case 23:return Fu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?oe&1073741824&&(Ot(t),t.subtreeFlags&6&&(t.flags|=8192)):Ot(t),null;case 24:return null;case 25:return null}throw Error(k(156,t.tag))}function D3(e,t){switch(_u(t),t.tag){case 1:return Zt(t.type)&&us(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return S1(),nt(Kt),nt(Vt),Pu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ku(t),null;case 13:if(nt(ot),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(k(340));w1()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return nt(ot),null;case 4:return S1(),null;case 10:return Tu(t.type._context),null;case 22:case 23:return Fu(),null;case 24:return null;default:return null}}var Eo=!1,Mt=!1,A3=typeof WeakSet=="function"?WeakSet:Set,A=null;function Jr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ut(e,t,r)}else n.current=null}function sc(e,t,n){try{n()}catch(r){ut(e,t,r)}}var Qf=!1;function L3(e,t){if(Ul=ss,e=W9(),vu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else t:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break t}var s=0,a=-1,l=-1,c=0,u=0,f=e,d=null;e:for(;;){for(var p;f!==n||i!==0&&f.nodeType!==3||(a=s+i),f!==o||r!==0&&f.nodeType!==3||(l=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(p=f.firstChild)!==null;)d=f,f=p;for(;;){if(f===e)break e;if(d===n&&++c===i&&(a=s),d===o&&++u===r&&(l=s),(p=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=p}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for($l={focusedElem:e,selectionRange:n},ss=!1,A=t;A!==null;)if(t=A,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,A=e;else for(;A!==null;){t=A;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var h=v.memoizedProps,_=v.memoizedState,m=t.stateNode,g=m.getSnapshotBeforeUpdate(t.elementType===t.type?h:Ne(t.type,h),_);m.__reactInternalSnapshotBeforeUpdate=g}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(k(163))}}catch(w){ut(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,A=e;break}A=t.return}return v=Qf,Qf=!1,v}function ci(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&sc(t,n,o)}i=i.next}while(i!==r)}}function ta(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ac(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function U2(e){var t=e.alternate;t!==null&&(e.alternate=null,U2(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[$e],delete t[Oi],delete t[Wl],delete t[h3],delete t[p3])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function $2(e){return e.tag===5||e.tag===3||e.tag===4}function Kf(e){t:for(;;){for(;e.sibling===null;){if(e.return===null||$2(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue t;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function lc(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=cs));else if(r!==4&&(e=e.child,e!==null))for(lc(e,t,n),e=e.sibling;e!==null;)lc(e,t,n),e=e.sibling}function cc(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(cc(e,t,n),e=e.sibling;e!==null;)cc(e,t,n),e=e.sibling}var kt=null,Re=!1;function Sn(e,t,n){for(n=n.child;n!==null;)H2(e,t,n),n=n.sibling}function H2(e,t,n){if(We&&typeof We.onCommitFiberUnmount=="function")try{We.onCommitFiberUnmount(Gs,n)}catch{}switch(n.tag){case 5:Mt||Jr(n,t);case 6:var r=kt,i=Re;kt=null,Sn(e,t,n),kt=r,Re=i,kt!==null&&(Re?(e=kt,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):kt.removeChild(n.stateNode));break;case 18:kt!==null&&(Re?(e=kt,n=n.stateNode,e.nodeType===8?ja(e.parentNode,n):e.nodeType===1&&ja(e,n),ki(e)):ja(kt,n.stateNode));break;case 4:r=kt,i=Re,kt=n.stateNode.containerInfo,Re=!0,Sn(e,t,n),kt=r,Re=i;break;case 0:case 11:case 14:case 15:if(!Mt&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&sc(n,t,s),i=i.next}while(i!==r)}Sn(e,t,n);break;case 1:if(!Mt&&(Jr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ut(n,t,a)}Sn(e,t,n);break;case 21:Sn(e,t,n);break;case 22:n.mode&1?(Mt=(r=Mt)||n.memoizedState!==null,Sn(e,t,n),Mt=r):Sn(e,t,n);break;default:Sn(e,t,n)}}function Zf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new A3),t.forEach(function(r){var i=B3.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Oe(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,a=s;t:for(;a!==null;){switch(a.tag){case 5:kt=a.stateNode,Re=!1;break t;case 3:kt=a.stateNode.containerInfo,Re=!0;break t;case 4:kt=a.stateNode.containerInfo,Re=!0;break t}a=a.return}if(kt===null)throw Error(k(160));H2(o,s,i),kt=null,Re=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(c){ut(i,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)G2(t,e),t=t.sibling}function G2(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Oe(t,e),Be(e),r&4){try{ci(3,e,e.return),ta(3,e)}catch(h){ut(e,e.return,h)}try{ci(5,e,e.return)}catch(h){ut(e,e.return,h)}}break;case 1:Oe(t,e),Be(e),r&512&&n!==null&&Jr(n,n.return);break;case 5:if(Oe(t,e),Be(e),r&512&&n!==null&&Jr(n,n.return),e.flags&32){var i=e.stateNode;try{Ti(i,"")}catch(h){ut(e,e.return,h)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&p9(i,o),Ml(a,s);var c=Ml(a,o);for(s=0;s<l.length;s+=2){var u=l[s],f=l[s+1];u==="style"?_9(i,f):u==="dangerouslySetInnerHTML"?v9(i,f):u==="children"?Ti(i,f):iu(i,u,f,c)}switch(a){case"input":Pl(i,o);break;case"textarea":m9(i,o);break;case"select":var d=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var p=o.value;p!=null?a1(i,!!o.multiple,p,!1):d!==!!o.multiple&&(o.defaultValue!=null?a1(i,!!o.multiple,o.defaultValue,!0):a1(i,!!o.multiple,o.multiple?[]:"",!1))}i[Oi]=o}catch(h){ut(e,e.return,h)}}break;case 6:if(Oe(t,e),Be(e),r&4){if(e.stateNode===null)throw Error(k(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(h){ut(e,e.return,h)}}break;case 3:if(Oe(t,e),Be(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ki(t.containerInfo)}catch(h){ut(e,e.return,h)}break;case 4:Oe(t,e),Be(e);break;case 13:Oe(t,e),Be(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(Vu=mt())),r&4&&Zf(e);break;case 22:if(u=n!==null&&n.memoizedState!==null,e.mode&1?(Mt=(c=Mt)||u,Oe(t,e),Mt=c):Oe(t,e),Be(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!u&&e.mode&1)for(A=e,u=e.child;u!==null;){for(f=A=u;A!==null;){switch(d=A,p=d.child,d.tag){case 0:case 11:case 14:case 15:ci(4,d,d.return);break;case 1:Jr(d,d.return);var v=d.stateNode;if(typeof v.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(h){ut(r,n,h)}}break;case 5:Jr(d,d.return);break;case 22:if(d.memoizedState!==null){td(f);continue}}p!==null?(p.return=d,A=p):td(f)}u=u.sibling}t:for(u=null,f=e;;){if(f.tag===5){if(u===null){u=f;try{i=f.stateNode,c?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=f.stateNode,l=f.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=y9("display",s))}catch(h){ut(e,e.return,h)}}}else if(f.tag===6){if(u===null)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(h){ut(e,e.return,h)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break t;for(;f.sibling===null;){if(f.return===null||f.return===e)break t;u===f&&(u=null),f=f.return}u===f&&(u=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Oe(t,e),Be(e),r&4&&Zf(e);break;case 21:break;default:Oe(t,e),Be(e)}}function Be(e){var t=e.flags;if(t&2){try{t:{for(var n=e.return;n!==null;){if($2(n)){var r=n;break t}n=n.return}throw Error(k(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Ti(i,""),r.flags&=-33);var o=Kf(e);cc(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,a=Kf(e);lc(e,a,s);break;default:throw Error(k(161))}}catch(l){ut(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function O3(e,t,n){A=e,W2(e)}function W2(e,t,n){for(var r=(e.mode&1)!==0;A!==null;){var i=A,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||Eo;if(!s){var a=i.alternate,l=a!==null&&a.memoizedState!==null||Mt;a=Eo;var c=Mt;if(Eo=s,(Mt=l)&&!c)for(A=i;A!==null;)s=A,l=s.child,s.tag===22&&s.memoizedState!==null?ed(i):l!==null?(l.return=s,A=l):ed(i);for(;o!==null;)A=o,W2(o),o=o.sibling;A=i,Eo=a,Mt=c}Jf(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,A=o):Jf(e)}}function Jf(e){for(;A!==null;){var t=A;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Mt||ta(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Mt)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ne(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Vf(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Vf(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var u=c.memoizedState;if(u!==null){var f=u.dehydrated;f!==null&&ki(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(k(163))}Mt||t.flags&512&&ac(t)}catch(d){ut(t,t.return,d)}}if(t===e){A=null;break}if(n=t.sibling,n!==null){n.return=t.return,A=n;break}A=t.return}}function td(e){for(;A!==null;){var t=A;if(t===e){A=null;break}var n=t.sibling;if(n!==null){n.return=t.return,A=n;break}A=t.return}}function ed(e){for(;A!==null;){var t=A;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ta(4,t)}catch(l){ut(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){ut(t,i,l)}}var o=t.return;try{ac(t)}catch(l){ut(t,o,l)}break;case 5:var s=t.return;try{ac(t)}catch(l){ut(t,s,l)}}}catch(l){ut(t,t.return,l)}if(t===e){A=null;break}var a=t.sibling;if(a!==null){a.return=t.return,A=a;break}A=t.return}}var M3=Math.ceil,ws=wn.ReactCurrentDispatcher,Ru=wn.ReactCurrentOwner,ke=wn.ReactCurrentBatchConfig,W=0,bt=null,vt=null,Dt=0,oe=0,t1=Jn(0),wt=0,qi=null,Dr=0,ea=0,Iu=0,ui=null,Yt=null,Vu=0,b1=1/0,on=null,xs=!1,uc=null,Bn=null,ko=!1,An=null,Ss=0,fi=0,fc=null,Ho=-1,Go=0;function jt(){return W&6?mt():Ho!==-1?Ho:Ho=mt()}function zn(e){return e.mode&1?W&2&&Dt!==0?Dt&-Dt:g3.transition!==null?(Go===0&&(Go=L9()),Go):(e=Y,e!==0||(e=window.event,e=e===void 0?16:q9(e.type)),e):1}function qe(e,t,n,r){if(50<fi)throw fi=0,fc=null,Error(k(185));Ji(e,n,r),(!(W&2)||e!==bt)&&(e===bt&&(!(W&2)&&(ea|=n),wt===4&&Pn(e,Dt)),Jt(e,r),n===1&&W===0&&!(t.mode&1)&&(b1=mt()+500,Ks&&tr()))}function Jt(e,t){var n=e.callbackNode;g7(e,t);var r=os(e,e===bt?Dt:0);if(r===0)n!==null&&uf(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&uf(n),t===1)e.tag===0?m3(nd.bind(null,e)):r2(nd.bind(null,e)),f3(function(){!(W&6)&&tr()}),n=null;else{switch(O9(r)){case 1:n=cu;break;case 4:n=D9;break;case 16:n=is;break;case 536870912:n=A9;break;default:n=is}n=e5(n,Y2.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Y2(e,t){if(Ho=-1,Go=0,W&6)throw Error(k(327));var n=e.callbackNode;if(d1()&&e.callbackNode!==n)return null;var r=os(e,e===bt?Dt:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ts(e,r);else{t=r;var i=W;W|=2;var o=Q2();(bt!==e||Dt!==t)&&(on=null,b1=mt()+500,wr(e,t));do try{I3();break}catch(a){X2(e,a)}while(!0);Su(),ws.current=o,W=i,vt!==null?t=0:(bt=null,Dt=0,t=wt)}if(t!==0){if(t===2&&(i=ql(e),i!==0&&(r=i,t=dc(e,i))),t===1)throw n=qi,wr(e,0),Pn(e,r),Jt(e,mt()),n;if(t===6)Pn(e,r);else{if(i=e.current.alternate,!(r&30)&&!N3(i)&&(t=Ts(e,r),t===2&&(o=ql(e),o!==0&&(r=o,t=dc(e,o))),t===1))throw n=qi,wr(e,0),Pn(e,r),Jt(e,mt()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(k(345));case 2:cr(e,Yt,on);break;case 3:if(Pn(e,r),(r&130023424)===r&&(t=Vu+500-mt(),10<t)){if(os(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){jt(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Gl(cr.bind(null,e,Yt,on),t);break}cr(e,Yt,on);break;case 4:if(Pn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Ve(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=mt()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*M3(r/1960))-r,10<r){e.timeoutHandle=Gl(cr.bind(null,e,Yt,on),r);break}cr(e,Yt,on);break;case 5:cr(e,Yt,on);break;default:throw Error(k(329))}}}return Jt(e,mt()),e.callbackNode===n?Y2.bind(null,e):null}function dc(e,t){var n=ui;return e.current.memoizedState.isDehydrated&&(wr(e,t).flags|=256),e=Ts(e,t),e!==2&&(t=Yt,Yt=n,t!==null&&hc(t)),e}function hc(e){Yt===null?Yt=e:Yt.push.apply(Yt,e)}function N3(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!Fe(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Pn(e,t){for(t&=~Iu,t&=~ea,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ve(t),r=1<<n;e[n]=-1,t&=~r}}function nd(e){if(W&6)throw Error(k(327));d1();var t=os(e,0);if(!(t&1))return Jt(e,mt()),null;var n=Ts(e,t);if(e.tag!==0&&n===2){var r=ql(e);r!==0&&(t=r,n=dc(e,r))}if(n===1)throw n=qi,wr(e,0),Pn(e,t),Jt(e,mt()),n;if(n===6)throw Error(k(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,cr(e,Yt,on),Jt(e,mt()),null}function qu(e,t){var n=W;W|=1;try{return e(t)}finally{W=n,W===0&&(b1=mt()+500,Ks&&tr())}}function Ar(e){An!==null&&An.tag===0&&!(W&6)&&d1();var t=W;W|=1;var n=ke.transition,r=Y;try{if(ke.transition=null,Y=1,e)return e()}finally{Y=r,ke.transition=n,W=t,!(W&6)&&tr()}}function Fu(){oe=t1.current,nt(t1)}function wr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,u3(n)),vt!==null)for(n=vt.return;n!==null;){var r=n;switch(_u(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&us();break;case 3:S1(),nt(Kt),nt(Vt),Pu();break;case 5:ku(r);break;case 4:S1();break;case 13:nt(ot);break;case 19:nt(ot);break;case 10:Tu(r.type._context);break;case 22:case 23:Fu()}n=n.return}if(bt=e,vt=e=jn(e.current,null),Dt=oe=t,wt=0,qi=null,Iu=ea=Dr=0,Yt=ui=null,mr!==null){for(t=0;t<mr.length;t++)if(n=mr[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}mr=null}return e}function X2(e,t){do{var n=vt;try{if(Su(),jo.current=_s,ys){for(var r=lt.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}ys=!1}if(Pr=0,xt=_t=lt=null,li=!1,Ri=0,Ru.current=null,n===null||n.return===null){wt=1,qi=t,vt=null;break}t:{var o=e,s=n.return,a=n,l=t;if(t=Dt,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var c=l,u=a,f=u.tag;if(!(u.mode&1)&&(f===0||f===11||f===15)){var d=u.alternate;d?(u.updateQueue=d.updateQueue,u.memoizedState=d.memoizedState,u.lanes=d.lanes):(u.updateQueue=null,u.memoizedState=null)}var p=Uf(s);if(p!==null){p.flags&=-257,$f(p,s,a,o,t),p.mode&1&&jf(o,c,t),t=p,l=c;var v=t.updateQueue;if(v===null){var h=new Set;h.add(l),t.updateQueue=h}else v.add(l);break t}else{if(!(t&1)){jf(o,c,t),Bu();break t}l=Error(k(426))}}else if(rt&&a.mode&1){var _=Uf(s);if(_!==null){!(_.flags&65536)&&(_.flags|=256),$f(_,s,a,o,t),wu(T1(l,a));break t}}o=l=T1(l,a),wt!==4&&(wt=2),ui===null?ui=[o]:ui.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=O2(o,l,t);If(o,m);break t;case 1:a=l;var g=o.type,y=o.stateNode;if(!(o.flags&128)&&(typeof g.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(Bn===null||!Bn.has(y)))){o.flags|=65536,t&=-t,o.lanes|=t;var w=M2(o,a,t);If(o,w);break t}}o=o.return}while(o!==null)}Z2(n)}catch(x){t=x,vt===n&&n!==null&&(vt=n=n.return);continue}break}while(!0)}function Q2(){var e=ws.current;return ws.current=_s,e===null?_s:e}function Bu(){(wt===0||wt===3||wt===2)&&(wt=4),bt===null||!(Dr&268435455)&&!(ea&268435455)||Pn(bt,Dt)}function Ts(e,t){var n=W;W|=2;var r=Q2();(bt!==e||Dt!==t)&&(on=null,wr(e,t));do try{R3();break}catch(i){X2(e,i)}while(!0);if(Su(),W=n,ws.current=r,vt!==null)throw Error(k(261));return bt=null,Dt=0,wt}function R3(){for(;vt!==null;)K2(vt)}function I3(){for(;vt!==null&&!a7();)K2(vt)}function K2(e){var t=t5(e.alternate,e,oe);e.memoizedProps=e.pendingProps,t===null?Z2(e):vt=t,Ru.current=null}function Z2(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=D3(n,t),n!==null){n.flags&=32767,vt=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{wt=6,vt=null;return}}else if(n=P3(n,t,oe),n!==null){vt=n;return}if(t=t.sibling,t!==null){vt=t;return}vt=t=e}while(t!==null);wt===0&&(wt=5)}function cr(e,t,n){var r=Y,i=ke.transition;try{ke.transition=null,Y=1,V3(e,t,n,r)}finally{ke.transition=i,Y=r}return null}function V3(e,t,n,r){do d1();while(An!==null);if(W&6)throw Error(k(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(k(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(v7(e,o),e===bt&&(vt=bt=null,Dt=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ko||(ko=!0,e5(is,function(){return d1(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=ke.transition,ke.transition=null;var s=Y;Y=1;var a=W;W|=4,Ru.current=null,L3(e,n),G2(n,e),r3($l),ss=!!Ul,$l=Ul=null,e.current=n,O3(n),l7(),W=a,Y=s,ke.transition=o}else e.current=n;if(ko&&(ko=!1,An=e,Ss=i),o=e.pendingLanes,o===0&&(Bn=null),f7(n.stateNode),Jt(e,mt()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(xs)throw xs=!1,e=uc,uc=null,e;return Ss&1&&e.tag!==0&&d1(),o=e.pendingLanes,o&1?e===fc?fi++:(fi=0,fc=e):fi=0,tr(),null}function d1(){if(An!==null){var e=O9(Ss),t=ke.transition,n=Y;try{if(ke.transition=null,Y=16>e?16:e,An===null)var r=!1;else{if(e=An,An=null,Ss=0,W&6)throw Error(k(331));var i=W;for(W|=4,A=e.current;A!==null;){var o=A,s=o.child;if(A.flags&16){var a=o.deletions;if(a!==null){for(var l=0;l<a.length;l++){var c=a[l];for(A=c;A!==null;){var u=A;switch(u.tag){case 0:case 11:case 15:ci(8,u,o)}var f=u.child;if(f!==null)f.return=u,A=f;else for(;A!==null;){u=A;var d=u.sibling,p=u.return;if(U2(u),u===c){A=null;break}if(d!==null){d.return=p,A=d;break}A=p}}}var v=o.alternate;if(v!==null){var h=v.child;if(h!==null){v.child=null;do{var _=h.sibling;h.sibling=null,h=_}while(h!==null)}}A=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,A=s;else t:for(;A!==null;){if(o=A,o.flags&2048)switch(o.tag){case 0:case 11:case 15:ci(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,A=m;break t}A=o.return}}var g=e.current;for(A=g;A!==null;){s=A;var y=s.child;if(s.subtreeFlags&2064&&y!==null)y.return=s,A=y;else t:for(s=g;A!==null;){if(a=A,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:ta(9,a)}}catch(x){ut(a,a.return,x)}if(a===s){A=null;break t}var w=a.sibling;if(w!==null){w.return=a.return,A=w;break t}A=a.return}}if(W=i,tr(),We&&typeof We.onPostCommitFiberRoot=="function")try{We.onPostCommitFiberRoot(Gs,e)}catch{}r=!0}return r}finally{Y=n,ke.transition=t}}return!1}function rd(e,t,n){t=T1(n,t),t=O2(e,t,1),e=Fn(e,t,1),t=jt(),e!==null&&(Ji(e,1,t),Jt(e,t))}function ut(e,t,n){if(e.tag===3)rd(e,e,n);else for(;t!==null;){if(t.tag===3){rd(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Bn===null||!Bn.has(r))){e=T1(n,e),e=M2(t,e,1),t=Fn(t,e,1),e=jt(),t!==null&&(Ji(t,1,e),Jt(t,e));break}}t=t.return}}function q3(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=jt(),e.pingedLanes|=e.suspendedLanes&n,bt===e&&(Dt&n)===n&&(wt===4||wt===3&&(Dt&130023424)===Dt&&500>mt()-Vu?wr(e,0):Iu|=n),Jt(e,t)}function J2(e,t){t===0&&(e.mode&1?(t=vo,vo<<=1,!(vo&130023424)&&(vo=4194304)):t=1);var n=jt();e=gn(e,t),e!==null&&(Ji(e,t,n),Jt(e,n))}function F3(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),J2(e,n)}function B3(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(k(314))}r!==null&&r.delete(t),J2(e,n)}var t5;t5=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Kt.current)Qt=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Qt=!1,k3(e,t,n);Qt=!!(e.flags&131072)}else Qt=!1,rt&&t.flags&1048576&&i2(t,hs,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;$o(e,t),e=t.pendingProps;var i=_1(t,Vt.current);f1(t,n),i=Au(null,t,r,e,i,n);var o=Lu();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Zt(r)?(o=!0,fs(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Cu(t),i.updater=Js,t.stateNode=i,i._reactInternals=t,Jl(t,r,e,n),t=nc(null,t,r,!0,o,n)):(t.tag=0,rt&&o&&yu(t),Ft(null,t,i,n),t=t.child),t;case 16:r=t.elementType;t:{switch($o(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=j3(r),e=Ne(r,e),i){case 0:t=ec(null,t,r,e,n);break t;case 1:t=Wf(null,t,r,e,n);break t;case 11:t=Hf(null,t,r,e,n);break t;case 14:t=Gf(null,t,r,Ne(r.type,e),n);break t}throw Error(k(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ne(r,i),ec(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ne(r,i),Wf(e,t,r,i,n);case 3:t:{if(V2(t),e===null)throw Error(k(387));r=t.pendingProps,o=t.memoizedState,i=o.element,u2(e,t),gs(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=T1(Error(k(423)),t),t=Yf(e,t,r,n,i);break t}else if(r!==i){i=T1(Error(k(424)),t),t=Yf(e,t,r,n,i);break t}else for(fe=qn(t.stateNode.containerInfo.firstChild),he=t,rt=!0,Ie=null,n=l2(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(w1(),r===i){t=vn(e,t,n);break t}Ft(e,t,r,n)}t=t.child}return t;case 5:return f2(t),e===null&&Ql(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,Hl(r,i)?s=null:o!==null&&Hl(r,o)&&(t.flags|=32),I2(e,t),Ft(e,t,s,n),t.child;case 6:return e===null&&Ql(t),null;case 13:return q2(e,t,n);case 4:return Eu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=x1(t,null,r,n):Ft(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ne(r,i),Hf(e,t,r,i,n);case 7:return Ft(e,t,t.pendingProps,n),t.child;case 8:return Ft(e,t,t.pendingProps.children,n),t.child;case 12:return Ft(e,t,t.pendingProps.children,n),t.child;case 10:t:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,tt(ps,r._currentValue),r._currentValue=s,o!==null)if(Fe(o.value,s)){if(o.children===i.children&&!Kt.current){t=vn(e,t,n);break t}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){s=o.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(o.tag===1){l=fn(-1,n&-n),l.tag=2;var c=o.updateQueue;if(c!==null){c=c.shared;var u=c.pending;u===null?l.next=l:(l.next=u.next,u.next=l),c.pending=l}}o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),Kl(o.return,n,t),a.lanes|=n;break}l=l.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(k(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),Kl(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}Ft(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,f1(t,n),i=Pe(i),r=r(i),t.flags|=1,Ft(e,t,r,n),t.child;case 14:return r=t.type,i=Ne(r,t.pendingProps),i=Ne(r.type,i),Gf(e,t,r,i,n);case 15:return N2(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ne(r,i),$o(e,t),t.tag=1,Zt(r)?(e=!0,fs(t)):e=!1,f1(t,n),L2(t,r,i),Jl(t,r,i,n),nc(null,t,r,!0,e,n);case 19:return F2(e,t,n);case 22:return R2(e,t,n)}throw Error(k(156,t.tag))};function e5(e,t){return P9(e,t)}function z3(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function be(e,t,n,r){return new z3(e,t,n,r)}function zu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function j3(e){if(typeof e=="function")return zu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===su)return 11;if(e===au)return 14}return 2}function jn(e,t){var n=e.alternate;return n===null?(n=be(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Wo(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")zu(e)&&(s=1);else if(typeof e=="string")s=5;else t:switch(e){case $r:return xr(n.children,i,o,t);case ou:s=8,i|=8;break;case Tl:return e=be(12,n,t,i|2),e.elementType=Tl,e.lanes=o,e;case bl:return e=be(13,n,t,i),e.elementType=bl,e.lanes=o,e;case Cl:return e=be(19,n,t,i),e.elementType=Cl,e.lanes=o,e;case f9:return na(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case c9:s=10;break t;case u9:s=9;break t;case su:s=11;break t;case au:s=14;break t;case bn:s=16,r=null;break t}throw Error(k(130,e==null?e:typeof e,""))}return t=be(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function xr(e,t,n,r){return e=be(7,e,r,t),e.lanes=n,e}function na(e,t,n,r){return e=be(22,e,r,t),e.elementType=f9,e.lanes=n,e.stateNode={isHidden:!1},e}function Qa(e,t,n){return e=be(6,e,null,t),e.lanes=n,e}function Ka(e,t,n){return t=be(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function U3(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=La(0),this.expirationTimes=La(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=La(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function ju(e,t,n,r,i,o,s,a,l){return e=new U3(e,t,n,a,l),t===1?(t=1,o===!0&&(t|=8)):t=0,o=be(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Cu(o),e}function $3(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Ur,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function n5(e){if(!e)return Wn;e=e._reactInternals;t:{if(Nr(e)!==e||e.tag!==1)throw Error(k(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break t;case 1:if(Zt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break t}}t=t.return}while(t!==null);throw Error(k(171))}if(e.tag===1){var n=e.type;if(Zt(n))return n2(e,n,t)}return t}function r5(e,t,n,r,i,o,s,a,l){return e=ju(n,r,!0,e,i,o,s,a,l),e.context=n5(null),n=e.current,r=jt(),i=zn(n),o=fn(r,i),o.callback=t!=null?t:null,Fn(n,o,i),e.current.lanes=i,Ji(e,i,r),Jt(e,r),e}function ra(e,t,n,r){var i=t.current,o=jt(),s=zn(i);return n=n5(n),t.context===null?t.context=n:t.pendingContext=n,t=fn(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Fn(i,t,s),e!==null&&(qe(e,i,s,o),zo(e,i,s)),s}function bs(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function id(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Uu(e,t){id(e,t),(e=e.alternate)&&id(e,t)}function H3(){return null}var i5=typeof reportError=="function"?reportError:function(e){console.error(e)};function $u(e){this._internalRoot=e}ia.prototype.render=$u.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(k(409));ra(e,t,null,null)};ia.prototype.unmount=$u.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Ar(function(){ra(null,e,null,null)}),t[mn]=null}};function ia(e){this._internalRoot=e}ia.prototype.unstable_scheduleHydration=function(e){if(e){var t=R9();e={blockedOn:null,target:e,priority:t};for(var n=0;n<kn.length&&t!==0&&t<kn[n].priority;n++);kn.splice(n,0,e),n===0&&V9(e)}};function Hu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function oa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function od(){}function G3(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var c=bs(s);o.call(c)}}var s=r5(t,r,e,0,null,!1,!1,"",od);return e._reactRootContainer=s,e[mn]=s.current,Ai(e.nodeType===8?e.parentNode:e),Ar(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var c=bs(l);a.call(c)}}var l=ju(e,0,!1,null,null,!1,!1,"",od);return e._reactRootContainer=l,e[mn]=l.current,Ai(e.nodeType===8?e.parentNode:e),Ar(function(){ra(t,l,n,r)}),l}function sa(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var a=i;i=function(){var l=bs(s);a.call(l)}}ra(t,s,e,i)}else s=G3(n,t,e,i,r);return bs(s)}M9=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=K1(t.pendingLanes);n!==0&&(uu(t,n|1),Jt(t,mt()),!(W&6)&&(b1=mt()+500,tr()))}break;case 13:Ar(function(){var r=gn(e,1);if(r!==null){var i=jt();qe(r,e,1,i)}}),Uu(e,1)}};fu=function(e){if(e.tag===13){var t=gn(e,134217728);if(t!==null){var n=jt();qe(t,e,134217728,n)}Uu(e,134217728)}};N9=function(e){if(e.tag===13){var t=zn(e),n=gn(e,t);if(n!==null){var r=jt();qe(n,e,t,r)}Uu(e,t)}};R9=function(){return Y};I9=function(e,t){var n=Y;try{return Y=e,t()}finally{Y=n}};Rl=function(e,t,n){switch(t){case"input":if(Pl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Qs(r);if(!i)throw Error(k(90));h9(r),Pl(r,i)}}}break;case"textarea":m9(e,n);break;case"select":t=n.value,t!=null&&a1(e,!!n.multiple,t,!1)}};S9=qu;T9=Ar;var W3={usingClientEntryPoint:!1,Events:[eo,Yr,Qs,w9,x9,qu]},$1={findFiberByHostInstance:pr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Y3={bundleType:$1.bundleType,version:$1.version,rendererPackageName:$1.rendererPackageName,rendererConfig:$1.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:wn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=E9(e),e===null?null:e.stateNode},findFiberByHostInstance:$1.findFiberByHostInstance||H3,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Po=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Po.isDisabled&&Po.supportsFiber)try{Gs=Po.inject(Y3),We=Po}catch{}}_e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=W3;_e.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Hu(t))throw Error(k(200));return $3(e,t,null,n)};_e.createRoot=function(e,t){if(!Hu(e))throw Error(k(299));var n=!1,r="",i=i5;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=ju(e,1,!1,null,null,n,!1,r,i),e[mn]=t.current,Ai(e.nodeType===8?e.parentNode:e),new $u(t)};_e.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(k(188)):(e=Object.keys(e).join(","),Error(k(268,e)));return e=E9(t),e=e===null?null:e.stateNode,e};_e.flushSync=function(e){return Ar(e)};_e.hydrate=function(e,t,n){if(!oa(t))throw Error(k(200));return sa(null,e,t,!0,n)};_e.hydrateRoot=function(e,t,n){if(!Hu(e))throw Error(k(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=i5;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=r5(t,null,e,1,n!=null?n:null,i,!1,o,s),e[mn]=t.current,Ai(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new ia(t)};_e.render=function(e,t,n){if(!oa(t))throw Error(k(200));return sa(null,e,t,!1,n)};_e.unmountComponentAtNode=function(e){if(!oa(e))throw Error(k(40));return e._reactRootContainer?(Ar(function(){sa(null,null,e,!1,function(){e._reactRootContainer=null,e[mn]=null})}),!0):!1};_e.unstable_batchedUpdates=qu;_e.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!oa(n))throw Error(k(200));if(e==null||e._reactInternals===void 0)throw Error(k(38));return sa(e,t,n,!1,r)};_e.version="18.3.1-next-f1338f8080-20240426";function o5(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(o5)}catch(e){console.error(e)}}o5(),o9.exports=_e;var s5=o9.exports;const X3=W8(s5);var a5,sd=X3;a5=sd.createRoot,sd.hydrateRoot;const l5="3.7.7",Q3=l5,I1=typeof Buffer=="function",ad=typeof TextDecoder=="function"?new TextDecoder:void 0,ld=typeof TextEncoder=="function"?new TextEncoder:void 0,K3="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",J1=Array.prototype.slice.call(K3),Do=(e=>{let t={};return e.forEach((n,r)=>t[n]=r),t})(J1),Z3=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,Pt=String.fromCharCode.bind(String),cd=typeof Uint8Array.from=="function"?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),c5=e=>e.replace(/=/g,"").replace(/[+\/]/g,t=>t=="+"?"-":"_"),u5=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),f5=e=>{let t,n,r,i,o="";const s=e.length%3;for(let a=0;a<e.length;){if((n=e.charCodeAt(a++))>255||(r=e.charCodeAt(a++))>255||(i=e.charCodeAt(a++))>255)throw new TypeError("invalid character found");t=n<<16|r<<8|i,o+=J1[t>>18&63]+J1[t>>12&63]+J1[t>>6&63]+J1[t&63]}return s?o.slice(0,s-3)+"===".substring(s):o},Gu=typeof btoa=="function"?e=>btoa(e):I1?e=>Buffer.from(e,"binary").toString("base64"):f5,pc=I1?e=>Buffer.from(e).toString("base64"):e=>{let n=[];for(let r=0,i=e.length;r<i;r+=4096)n.push(Pt.apply(null,e.subarray(r,r+4096)));return Gu(n.join(""))},Yo=(e,t=!1)=>t?c5(pc(e)):pc(e),J3=e=>{if(e.length<2){var t=e.charCodeAt(0);return t<128?e:t<2048?Pt(192|t>>>6)+Pt(128|t&63):Pt(224|t>>>12&15)+Pt(128|t>>>6&63)+Pt(128|t&63)}else{var t=65536+(e.charCodeAt(0)-55296)*1024+(e.charCodeAt(1)-56320);return Pt(240|t>>>18&7)+Pt(128|t>>>12&63)+Pt(128|t>>>6&63)+Pt(128|t&63)}},tm=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,d5=e=>e.replace(tm,J3),ud=I1?e=>Buffer.from(e,"utf8").toString("base64"):ld?e=>pc(ld.encode(e)):e=>Gu(d5(e)),h1=(e,t=!1)=>t?c5(ud(e)):ud(e),fd=e=>h1(e,!0),em=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,nm=e=>{switch(e.length){case 4:var t=(7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3),n=t-65536;return Pt((n>>>10)+55296)+Pt((n&1023)+56320);case 3:return Pt((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return Pt((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},h5=e=>e.replace(em,nm),p5=e=>{if(e=e.replace(/\s+/g,""),!Z3.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(e.length&3));let t,n="",r,i;for(let o=0;o<e.length;)t=Do[e.charAt(o++)]<<18|Do[e.charAt(o++)]<<12|(r=Do[e.charAt(o++)])<<6|(i=Do[e.charAt(o++)]),n+=r===64?Pt(t>>16&255):i===64?Pt(t>>16&255,t>>8&255):Pt(t>>16&255,t>>8&255,t&255);return n},Wu=typeof atob=="function"?e=>atob(u5(e)):I1?e=>Buffer.from(e,"base64").toString("binary"):p5,m5=I1?e=>cd(Buffer.from(e,"base64")):e=>cd(Wu(e).split("").map(t=>t.charCodeAt(0))),g5=e=>m5(v5(e)),rm=I1?e=>Buffer.from(e,"base64").toString("utf8"):ad?e=>ad.decode(m5(e)):e=>h5(Wu(e)),v5=e=>u5(e.replace(/[-_]/g,t=>t=="-"?"+":"/")),mc=e=>rm(v5(e)),im=e=>{if(typeof e!="string")return!1;const t=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},y5=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),_5=function(){const e=(t,n)=>Object.defineProperty(String.prototype,t,y5(n));e("fromBase64",function(){return mc(this)}),e("toBase64",function(t){return h1(this,t)}),e("toBase64URI",function(){return h1(this,!0)}),e("toBase64URL",function(){return h1(this,!0)}),e("toUint8Array",function(){return g5(this)})},w5=function(){const e=(t,n)=>Object.defineProperty(Uint8Array.prototype,t,y5(n));e("toBase64",function(t){return Yo(this,t)}),e("toBase64URI",function(){return Yo(this,!0)}),e("toBase64URL",function(){return Yo(this,!0)})},om=()=>{_5(),w5()},sm={version:l5,VERSION:Q3,atob:Wu,atobPolyfill:p5,btoa:Gu,btoaPolyfill:f5,fromBase64:mc,toBase64:h1,encode:h1,encodeURI:fd,encodeURL:fd,utob:d5,btou:h5,decode:mc,isValid:im,fromUint8Array:Yo,toUint8Array:g5,extendString:_5,extendUint8Array:w5,extendBuiltins:om};function am(e){if(typeof e=="object")return e;if(typeof e!="string")return null;try{return e.startsWith("{")?JSON.parse(e):lm(e)}catch(t){return console.log("parse failed:"+t.message),null}}function lm(e){var t=new window.DOMParser().parseFromString(e,"text/xml"),n=t.getElementsByTagName("componentData");if(!n.length){var r,i=(r=e.match(/<templateData>(.*)<\/templateData>/))===null||r===void 0?void 0:r[1];return JSON.parse(sm.decode(i))}var o={};for(var s of n)for(var a of s.getElementsByTagName("data")||[])if(a.getAttribute("value")!=null){o[s.getAttribute("id")]=a.getAttribute("value");break}return o}var Yu=K.createContext(),cm=e=>{var{children:t,name:n}=e,[r,i]=D.useState(Gt.loading),[o,s]=D.useState({}),[a,l]=D.useState([]),[c,u]=D.useState(),[f,d]=D.useState(!1),p=_=>{console.log("".concat(n||"").concat(_))},v=D.useCallback(_=>(l(m=>[...m,_]),()=>{l(m=>m.filter(g=>g!==_))}),[]);D.useLayoutEffect(()=>{var _=!1;return window.load=()=>{i(Gt.loaded),p(".load()")},window.play=()=>{_=!0,d(!0),p(".play()")},window.pause=()=>{i(Gt.paused),p(".pause()")},window.stop=()=>{_?(i(Gt.stopped),p(".stop()")):(i(Gt.removed),p(".stop() without play"))},window.update=m=>{var g=am(m);if(g&&(p(".update(".concat(g?JSON.stringify(g||{},null,2):"null",")")),s(g),!_)){var y=v("__initialData");u(()=>y)}},()=>{delete window.load,delete window.play,delete window.pause,delete window.stop,delete window.update}},[]),D.useEffect(()=>{c==null||c()},[c]),D.useEffect(()=>{r<Gt.playing&&f&&!a.length&&i(Gt.playing)},[r,o,f,a]),D.useEffect(()=>{if(r===Gt.removed){var _,m;p(".remove()"),(_=(m=window).remove)===null||_===void 0||_.call(m)}},[r]);var h=D.useCallback(()=>{i(Gt.removed)},[]);return K.createElement(Yu.Provider,{value:{data:o,state:r,name:n,safeToRemove:h,delayPlay:v}},r!==Gt.removed?K.createElement(um,null,t):null)},um=D.memo(e=>{var{children:t}=e;return t}),Za=null,fm=(e,t)=>{var{container:n=document.getElementById("root"),cssReset:r=!0,name:i=e.name}={};if(n||(n=document.createElement("div"),n.id="root",document.body.appendChild(n)),r){var o=` 
      width: 100vw;
      height: 100vh;
      overflow: hidden;
      margin: 0;
    `;document.body.style.cssText=o,n.style.cssText=o}Za||(Za=a5(n)),s5.flushSync(()=>{Za.render(D.createElement(cm,{name:i},D.createElement(e)))})};function sn(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function x5(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}/*!
 * GSAP 3.12.5
 * https://gsap.com
 *
 * @license Copyright 2008-2024, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license or for
 * Club GSAP members, the agreement issued with that membership.
 * @author: Jack Doyle, <EMAIL>
*/var pe={autoSleep:120,force3D:"auto",nullTargetWarn:1,units:{lineHeight:""}},C1={duration:.5,overwrite:!1,delay:0},Xu,Rt,it,Ce=1e8,Z=1/Ce,gc=Math.PI*2,dm=gc/4,hm=0,S5=Math.sqrt,pm=Math.cos,mm=Math.sin,Ct=function(t){return typeof t=="string"},ht=function(t){return typeof t=="function"},yn=function(t){return typeof t=="number"},Qu=function(t){return typeof t>"u"},Ze=function(t){return typeof t=="object"},te=function(t){return t!==!1},Ku=function(){return typeof window<"u"},Ao=function(t){return ht(t)||Ct(t)},T5=typeof ArrayBuffer=="function"&&ArrayBuffer.isView||function(){},It=Array.isArray,vc=/(?:-?\.?\d|\.)+/gi,b5=/[-+=.]*\d+[.e\-+]*\d*[e\-+]*\d*/g,e1=/[-+=.]*\d+[.e-]*\d*[a-z%]*/g,Ja=/[-+=.]*\d+\.?\d*(?:e-|e\+)?\d*/gi,C5=/[+-]=-?[.\d]+/,E5=/[^,'"\[\]\s]+/gi,gm=/^[+\-=e\s\d]*\d+[.\d]*([a-z]*|%)\s*$/i,st,Ue,yc,Zu,ve={},Cs={},k5,P5=function(t){return(Cs=Lr(t,ve))&&ie},Ju=function(t,n){return console.warn("Invalid property",t,"set to",n,"Missing plugin? gsap.registerPlugin()")},Fi=function(t,n){return!n&&console.warn(t)},D5=function(t,n){return t&&(ve[t]=n)&&Cs&&(Cs[t]=n)||ve},Bi=function(){return 0},vm={suppressEvents:!0,isStart:!0,kill:!1},Xo={suppressEvents:!0,kill:!1},ym={suppressEvents:!0},t0={},Un=[],_c={},A5,se={},tl={},dd=30,Qo=[],e0="",n0=function(t){var n=t[0],r,i;if(Ze(n)||ht(n)||(t=[t]),!(r=(n._gsap||{}).harness)){for(i=Qo.length;i--&&!Qo[i].targetTest(n););r=Qo[i]}for(i=t.length;i--;)t[i]&&(t[i]._gsap||(t[i]._gsap=new t6(t[i],r)))||t.splice(i,1);return t},Sr=function(t){return t._gsap||n0(Ee(t))[0]._gsap},L5=function(t,n,r){return(r=t[n])&&ht(r)?t[n]():Qu(r)&&t.getAttribute&&t.getAttribute(n)||r},ee=function(t,n){return(t=t.split(",")).forEach(n)||t},pt=function(t){return Math.round(t*1e5)/1e5||0},Tt=function(t){return Math.round(t*1e7)/1e7||0},p1=function(t,n){var r=n.charAt(0),i=parseFloat(n.substr(2));return t=parseFloat(t),r==="+"?t+i:r==="-"?t-i:r==="*"?t*i:t/i},_m=function(t,n){for(var r=n.length,i=0;t.indexOf(n[i])<0&&++i<r;);return i<r},Es=function(){var t=Un.length,n=Un.slice(0),r,i;for(_c={},Un.length=0,r=0;r<t;r++)i=n[r],i&&i._lazy&&(i.render(i._lazy[0],i._lazy[1],!0)._lazy=0)},O5=function(t,n,r,i){Un.length&&!Rt&&Es(),t.render(n,r,Rt&&n<0&&(t._initted||t._startAt)),Un.length&&!Rt&&Es()},M5=function(t){var n=parseFloat(t);return(n||n===0)&&(t+"").match(E5).length<2?n:Ct(t)?t.trim():t},N5=function(t){return t},Ae=function(t,n){for(var r in n)r in t||(t[r]=n[r]);return t},wm=function(t){return function(n,r){for(var i in r)i in n||i==="duration"&&t||i==="ease"||(n[i]=r[i])}},Lr=function(t,n){for(var r in n)t[r]=n[r];return t},hd=function e(t,n){for(var r in n)r!=="__proto__"&&r!=="constructor"&&r!=="prototype"&&(t[r]=Ze(n[r])?e(t[r]||(t[r]={}),n[r]):n[r]);return t},ks=function(t,n){var r={},i;for(i in t)i in n||(r[i]=t[i]);return r},di=function(t){var n=t.parent||st,r=t.keyframes?wm(It(t.keyframes)):Ae;if(te(t.inherit))for(;n;)r(t,n.vars.defaults),n=n.parent||n._dp;return t},xm=function(t,n){for(var r=t.length,i=r===n.length;i&&r--&&t[r]===n[r];);return r<0},R5=function(t,n,r,i,o){var s=t[i],a;if(o)for(a=n[o];s&&s[o]>a;)s=s._prev;return s?(n._next=s._next,s._next=n):(n._next=t[r],t[r]=n),n._next?n._next._prev=n:t[i]=n,n._prev=s,n.parent=n._dp=t,n},aa=function(t,n,r,i){r===void 0&&(r="_first"),i===void 0&&(i="_last");var o=n._prev,s=n._next;o?o._next=s:t[r]===n&&(t[r]=s),s?s._prev=o:t[i]===n&&(t[i]=o),n._next=n._prev=n.parent=null},Yn=function(t,n){t.parent&&(!n||t.parent.autoRemoveChildren)&&t.parent.remove&&t.parent.remove(t),t._act=0},Tr=function(t,n){if(t&&(!n||n._end>t._dur||n._start<0))for(var r=t;r;)r._dirty=1,r=r.parent;return t},Sm=function(t){for(var n=t.parent;n&&n.parent;)n._dirty=1,n.totalDuration(),n=n.parent;return t},wc=function(t,n,r,i){return t._startAt&&(Rt?t._startAt.revert(Xo):t.vars.immediateRender&&!t.vars.autoRevert||t._startAt.render(n,!0,i))},Tm=function e(t){return!t||t._ts&&e(t.parent)},pd=function(t){return t._repeat?E1(t._tTime,t=t.duration()+t._rDelay)*t:0},E1=function(t,n){var r=Math.floor(t/=n);return t&&r===t?r-1:r},Ps=function(t,n){return(t-n._start)*n._ts+(n._ts>=0?0:n._dirty?n.totalDuration():n._tDur)},la=function(t){return t._end=Tt(t._start+(t._tDur/Math.abs(t._ts||t._rts||Z)||0))},ca=function(t,n){var r=t._dp;return r&&r.smoothChildTiming&&t._ts&&(t._start=Tt(r._time-(t._ts>0?n/t._ts:((t._dirty?t.totalDuration():t._tDur)-n)/-t._ts)),la(t),r._dirty||Tr(r,t)),t},I5=function(t,n){var r;if((n._time||!n._dur&&n._initted||n._start<t._time&&(n._dur||!n.add))&&(r=Ps(t.rawTime(),n),(!n._dur||ro(0,n.totalDuration(),r)-n._tTime>Z)&&n.render(r,!0)),Tr(t,n)._dp&&t._initted&&t._time>=t._dur&&t._ts){if(t._dur<t.duration())for(r=t;r._dp;)r.rawTime()>=0&&r.totalTime(r._tTime),r=r._dp;t._zTime=-Z}},He=function(t,n,r,i){return n.parent&&Yn(n),n._start=Tt((yn(r)?r:r||t!==st?xe(t,r,n):t._time)+n._delay),n._end=Tt(n._start+(n.totalDuration()/Math.abs(n.timeScale())||0)),R5(t,n,"_first","_last",t._sort?"_start":0),xc(n)||(t._recent=n),i||I5(t,n),t._ts<0&&ca(t,t._tTime),t},V5=function(t,n){return(ve.ScrollTrigger||Ju("scrollTrigger",n))&&ve.ScrollTrigger.create(n,t)},q5=function(t,n,r,i,o){if(i0(t,n,o),!t._initted)return 1;if(!r&&t._pt&&!Rt&&(t._dur&&t.vars.lazy!==!1||!t._dur&&t.vars.lazy)&&A5!==ae.frame)return Un.push(t),t._lazy=[o,i],1},bm=function e(t){var n=t.parent;return n&&n._ts&&n._initted&&!n._lock&&(n.rawTime()<0||e(n))},xc=function(t){var n=t.data;return n==="isFromStart"||n==="isStart"},Cm=function(t,n,r,i){var o=t.ratio,s=n<0||!n&&(!t._start&&bm(t)&&!(!t._initted&&xc(t))||(t._ts<0||t._dp._ts<0)&&!xc(t))?0:1,a=t._rDelay,l=0,c,u,f;if(a&&t._repeat&&(l=ro(0,t._tDur,n),u=E1(l,a),t._yoyo&&u&1&&(s=1-s),u!==E1(t._tTime,a)&&(o=1-s,t.vars.repeatRefresh&&t._initted&&t.invalidate())),s!==o||Rt||i||t._zTime===Z||!n&&t._zTime){if(!t._initted&&q5(t,n,i,r,l))return;for(f=t._zTime,t._zTime=n||(r?Z:0),r||(r=n&&!f),t.ratio=s,t._from&&(s=1-s),t._time=0,t._tTime=l,c=t._pt;c;)c.r(s,c.d),c=c._next;n<0&&wc(t,n,r,!0),t._onUpdate&&!r&&de(t,"onUpdate"),l&&t._repeat&&!r&&t.parent&&de(t,"onRepeat"),(n>=t._tDur||n<0)&&t.ratio===s&&(s&&Yn(t,1),!r&&!Rt&&(de(t,s?"onComplete":"onReverseComplete",!0),t._prom&&t._prom()))}else t._zTime||(t._zTime=n)},Em=function(t,n,r){var i;if(r>n)for(i=t._first;i&&i._start<=r;){if(i.data==="isPause"&&i._start>n)return i;i=i._next}else for(i=t._last;i&&i._start>=r;){if(i.data==="isPause"&&i._start<n)return i;i=i._prev}},k1=function(t,n,r,i){var o=t._repeat,s=Tt(n)||0,a=t._tTime/t._tDur;return a&&!i&&(t._time*=s/t._dur),t._dur=s,t._tDur=o?o<0?1e10:Tt(s*(o+1)+t._rDelay*o):s,a>0&&!i&&ca(t,t._tTime=t._tDur*a),t.parent&&la(t),r||Tr(t.parent,t),t},md=function(t){return t instanceof zt?Tr(t):k1(t,t._dur)},km={_start:0,endTime:Bi,totalDuration:Bi},xe=function e(t,n,r){var i=t.labels,o=t._recent||km,s=t.duration()>=Ce?o.endTime(!1):t._dur,a,l,c;return Ct(n)&&(isNaN(n)||n in i)?(l=n.charAt(0),c=n.substr(-1)==="%",a=n.indexOf("="),l==="<"||l===">"?(a>=0&&(n=n.replace(/=/,"")),(l==="<"?o._start:o.endTime(o._repeat>=0))+(parseFloat(n.substr(1))||0)*(c?(a<0?o:r).totalDuration()/100:1)):a<0?(n in i||(i[n]=s),i[n]):(l=parseFloat(n.charAt(a-1)+n.substr(a+1)),c&&r&&(l=l/100*(It(r)?r[0]:r).totalDuration()),a>1?e(t,n.substr(0,a-1),r)+l:s+l)):n==null?s:+n},hi=function(t,n,r){var i=yn(n[1]),o=(i?2:1)+(t<2?0:1),s=n[o],a,l;if(i&&(s.duration=n[1]),s.parent=r,t){for(a=s,l=r;l&&!("immediateRender"in a);)a=l.vars.defaults||{},l=te(l.vars.inherit)&&l.parent;s.immediateRender=te(a.immediateRender),t<2?s.runBackwards=1:s.startAt=n[o-1]}return new gt(n[0],s,n[o+1])},er=function(t,n){return t||t===0?n(t):n},ro=function(t,n,r){return r<t?t:r>n?n:r},Nt=function(t,n){return!Ct(t)||!(n=gm.exec(t))?"":n[1]},Pm=function(t,n,r){return er(r,function(i){return ro(t,n,i)})},Sc=[].slice,F5=function(t,n){return t&&Ze(t)&&"length"in t&&(!n&&!t.length||t.length-1 in t&&Ze(t[0]))&&!t.nodeType&&t!==Ue},Dm=function(t,n,r){return r===void 0&&(r=[]),t.forEach(function(i){var o;return Ct(i)&&!n||F5(i,1)?(o=r).push.apply(o,Ee(i)):r.push(i)})||r},Ee=function(t,n,r){return it&&!n&&it.selector?it.selector(t):Ct(t)&&!r&&(yc||!P1())?Sc.call((n||Zu).querySelectorAll(t),0):It(t)?Dm(t,r):F5(t)?Sc.call(t,0):t?[t]:[]},Tc=function(t){return t=Ee(t)[0]||Fi("Invalid scope")||{},function(n){var r=t.current||t.nativeElement||t;return Ee(n,r.querySelectorAll?r:r===t?Fi("Invalid scope")||Zu.createElement("div"):t)}},B5=function(t){return t.sort(function(){return .5-Math.random()})},z5=function(t){if(ht(t))return t;var n=Ze(t)?t:{each:t},r=br(n.ease),i=n.from||0,o=parseFloat(n.base)||0,s={},a=i>0&&i<1,l=isNaN(i)||a,c=n.axis,u=i,f=i;return Ct(i)?u=f={center:.5,edges:.5,end:1}[i]||0:!a&&l&&(u=i[0],f=i[1]),function(d,p,v){var h=(v||n).length,_=s[h],m,g,y,w,x,S,T,b,C;if(!_){if(C=n.grid==="auto"?0:(n.grid||[1,Ce])[1],!C){for(T=-Ce;T<(T=v[C++].getBoundingClientRect().left)&&C<h;);C<h&&C--}for(_=s[h]=[],m=l?Math.min(C,h)*u-.5:i%C,g=C===Ce?0:l?h*f/C-.5:i/C|0,T=0,b=Ce,S=0;S<h;S++)y=S%C-m,w=g-(S/C|0),_[S]=x=c?Math.abs(c==="y"?w:y):S5(y*y+w*w),x>T&&(T=x),x<b&&(b=x);i==="random"&&B5(_),_.max=T-b,_.min=b,_.v=h=(parseFloat(n.amount)||parseFloat(n.each)*(C>h?h-1:c?c==="y"?h/C:C:Math.max(C,h/C))||0)*(i==="edges"?-1:1),_.b=h<0?o-h:o,_.u=Nt(n.amount||n.each)||0,r=r&&h<0?K5(r):r}return h=(_[d]-_.min)/_.max||0,Tt(_.b+(r?r(h):h)*_.v)+_.u}},bc=function(t){var n=Math.pow(10,((t+"").split(".")[1]||"").length);return function(r){var i=Tt(Math.round(parseFloat(r)/t)*t*n);return(i-i%1)/n+(yn(r)?0:Nt(r))}},j5=function(t,n){var r=It(t),i,o;return!r&&Ze(t)&&(i=r=t.radius||Ce,t.values?(t=Ee(t.values),(o=!yn(t[0]))&&(i*=i)):t=bc(t.increment)),er(n,r?ht(t)?function(s){return o=t(s),Math.abs(o-s)<=i?o:s}:function(s){for(var a=parseFloat(o?s.x:s),l=parseFloat(o?s.y:0),c=Ce,u=0,f=t.length,d,p;f--;)o?(d=t[f].x-a,p=t[f].y-l,d=d*d+p*p):d=Math.abs(t[f]-a),d<c&&(c=d,u=f);return u=!i||c<=i?t[u]:s,o||u===s||yn(s)?u:u+Nt(s)}:bc(t))},U5=function(t,n,r,i){return er(It(t)?!n:r===!0?!!(r=0):!i,function(){return It(t)?t[~~(Math.random()*t.length)]:(r=r||1e-5)&&(i=r<1?Math.pow(10,(r+"").length-2):1)&&Math.floor(Math.round((t-r/2+Math.random()*(n-t+r*.99))/r)*r*i)/i})},Am=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return function(i){return n.reduce(function(o,s){return s(o)},i)}},Lm=function(t,n){return function(r){return t(parseFloat(r))+(n||Nt(r))}},Om=function(t,n,r){return H5(t,n,0,1,r)},$5=function(t,n,r){return er(r,function(i){return t[~~n(i)]})},Mm=function e(t,n,r){var i=n-t;return It(t)?$5(t,e(0,t.length),n):er(r,function(o){return(i+(o-t)%i)%i+t})},Nm=function e(t,n,r){var i=n-t,o=i*2;return It(t)?$5(t,e(0,t.length-1),n):er(r,function(s){return s=(o+(s-t)%o)%o||0,t+(s>i?o-s:s)})},zi=function(t){for(var n=0,r="",i,o,s,a;~(i=t.indexOf("random(",n));)s=t.indexOf(")",i),a=t.charAt(i+7)==="[",o=t.substr(i+7,s-i-7).match(a?E5:vc),r+=t.substr(n,i-n)+U5(a?o:+o[0],a?0:+o[1],+o[2]||1e-5),n=s+1;return r+t.substr(n,t.length-n)},H5=function(t,n,r,i,o){var s=n-t,a=i-r;return er(o,function(l){return r+((l-t)/s*a||0)})},Rm=function e(t,n,r,i){var o=isNaN(t+n)?0:function(p){return(1-p)*t+p*n};if(!o){var s=Ct(t),a={},l,c,u,f,d;if(r===!0&&(i=1)&&(r=null),s)t={p:t},n={p:n};else if(It(t)&&!It(n)){for(u=[],f=t.length,d=f-2,c=1;c<f;c++)u.push(e(t[c-1],t[c]));f--,o=function(v){v*=f;var h=Math.min(d,~~v);return u[h](v-h)},r=n}else i||(t=Lr(It(t)?[]:{},t));if(!u){for(l in n)r0.call(a,t,l,"get",n[l]);o=function(v){return a0(v,a)||(s?t.p:t)}}}return er(r,o)},gd=function(t,n,r){var i=t.labels,o=Ce,s,a,l;for(s in i)a=i[s]-n,a<0==!!r&&a&&o>(a=Math.abs(a))&&(l=s,o=a);return l},de=function(t,n,r){var i=t.vars,o=i[n],s=it,a=t._ctx,l,c,u;if(o)return l=i[n+"Params"],c=i.callbackScope||t,r&&Un.length&&Es(),a&&(it=a),u=l?o.apply(c,l):o.call(c),it=s,u},ti=function(t){return Yn(t),t.scrollTrigger&&t.scrollTrigger.kill(!!Rt),t.progress()<1&&de(t,"onInterrupt"),t},n1,G5=[],W5=function(t){if(t)if(t=!t.name&&t.default||t,Ku()||t.headless){var n=t.name,r=ht(t),i=n&&!r&&t.init?function(){this._props=[]}:t,o={init:Bi,render:a0,add:r0,kill:Km,modifier:Qm,rawVars:0},s={targetTest:0,get:0,getSetter:s0,aliases:{},register:0};if(P1(),t!==i){if(se[n])return;Ae(i,Ae(ks(t,o),s)),Lr(i.prototype,Lr(o,ks(t,s))),se[i.prop=n]=i,t.targetTest&&(Qo.push(i),t0[n]=1),n=(n==="css"?"CSS":n.charAt(0).toUpperCase()+n.substr(1))+"Plugin"}D5(n,i),t.register&&t.register(ie,i,ne)}else G5.push(t)},Q=255,ei={aqua:[0,Q,Q],lime:[0,Q,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,Q],navy:[0,0,128],white:[Q,Q,Q],olive:[128,128,0],yellow:[Q,Q,0],orange:[Q,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[Q,0,0],pink:[Q,192,203],cyan:[0,Q,Q],transparent:[Q,Q,Q,0]},el=function(t,n,r){return t+=t<0?1:t>1?-1:0,(t*6<1?n+(r-n)*t*6:t<.5?r:t*3<2?n+(r-n)*(2/3-t)*6:n)*Q+.5|0},Y5=function(t,n,r){var i=t?yn(t)?[t>>16,t>>8&Q,t&Q]:0:ei.black,o,s,a,l,c,u,f,d,p,v;if(!i){if(t.substr(-1)===","&&(t=t.substr(0,t.length-1)),ei[t])i=ei[t];else if(t.charAt(0)==="#"){if(t.length<6&&(o=t.charAt(1),s=t.charAt(2),a=t.charAt(3),t="#"+o+o+s+s+a+a+(t.length===5?t.charAt(4)+t.charAt(4):"")),t.length===9)return i=parseInt(t.substr(1,6),16),[i>>16,i>>8&Q,i&Q,parseInt(t.substr(7),16)/255];t=parseInt(t.substr(1),16),i=[t>>16,t>>8&Q,t&Q]}else if(t.substr(0,3)==="hsl"){if(i=v=t.match(vc),!n)l=+i[0]%360/360,c=+i[1]/100,u=+i[2]/100,s=u<=.5?u*(c+1):u+c-u*c,o=u*2-s,i.length>3&&(i[3]*=1),i[0]=el(l+1/3,o,s),i[1]=el(l,o,s),i[2]=el(l-1/3,o,s);else if(~t.indexOf("="))return i=t.match(b5),r&&i.length<4&&(i[3]=1),i}else i=t.match(vc)||ei.transparent;i=i.map(Number)}return n&&!v&&(o=i[0]/Q,s=i[1]/Q,a=i[2]/Q,f=Math.max(o,s,a),d=Math.min(o,s,a),u=(f+d)/2,f===d?l=c=0:(p=f-d,c=u>.5?p/(2-f-d):p/(f+d),l=f===o?(s-a)/p+(s<a?6:0):f===s?(a-o)/p+2:(o-s)/p+4,l*=60),i[0]=~~(l+.5),i[1]=~~(c*100+.5),i[2]=~~(u*100+.5)),r&&i.length<4&&(i[3]=1),i},X5=function(t){var n=[],r=[],i=-1;return t.split($n).forEach(function(o){var s=o.match(e1)||[];n.push.apply(n,s),r.push(i+=s.length+1)}),n.c=r,n},vd=function(t,n,r){var i="",o=(t+i).match($n),s=n?"hsla(":"rgba(",a=0,l,c,u,f;if(!o)return t;if(o=o.map(function(d){return(d=Y5(d,n,1))&&s+(n?d[0]+","+d[1]+"%,"+d[2]+"%,"+d[3]:d.join(","))+")"}),r&&(u=X5(t),l=r.c,l.join(i)!==u.c.join(i)))for(c=t.replace($n,"1").split(e1),f=c.length-1;a<f;a++)i+=c[a]+(~l.indexOf(a)?o.shift()||s+"0,0,0,0)":(u.length?u:o.length?o:r).shift());if(!c)for(c=t.split($n),f=c.length-1;a<f;a++)i+=c[a]+o[a];return i+c[f]},$n=function(){var e="(?:\\b(?:(?:rgb|rgba|hsl|hsla)\\(.+?\\))|\\B#(?:[0-9a-f]{3,4}){1,2}\\b",t;for(t in ei)e+="|"+t+"\\b";return new RegExp(e+")","gi")}(),Im=/hsl[a]?\(/,Q5=function(t){var n=t.join(" "),r;if($n.lastIndex=0,$n.test(n))return r=Im.test(n),t[1]=vd(t[1],r),t[0]=vd(t[0],r,X5(t[1])),!0},ji,ae=function(){var e=Date.now,t=500,n=33,r=e(),i=r,o=1e3/240,s=o,a=[],l,c,u,f,d,p,v=function h(_){var m=e()-i,g=_===!0,y,w,x,S;if((m>t||m<0)&&(r+=m-n),i+=m,x=i-r,y=x-s,(y>0||g)&&(S=++f.frame,d=x-f.time*1e3,f.time=x=x/1e3,s+=y+(y>=o?4:o-y),w=1),g||(l=c(h)),w)for(p=0;p<a.length;p++)a[p](x,d,S,_)};return f={time:0,frame:0,tick:function(){v(!0)},deltaRatio:function(_){return d/(1e3/(_||60))},wake:function(){k5&&(!yc&&Ku()&&(Ue=yc=window,Zu=Ue.document||{},ve.gsap=ie,(Ue.gsapVersions||(Ue.gsapVersions=[])).push(ie.version),P5(Cs||Ue.GreenSockGlobals||!Ue.gsap&&Ue||{}),G5.forEach(W5)),u=typeof requestAnimationFrame<"u"&&requestAnimationFrame,l&&f.sleep(),c=u||function(_){return setTimeout(_,s-f.time*1e3+1|0)},ji=1,v(2))},sleep:function(){(u?cancelAnimationFrame:clearTimeout)(l),ji=0,c=Bi},lagSmoothing:function(_,m){t=_||1/0,n=Math.min(m||33,t)},fps:function(_){o=1e3/(_||240),s=f.time*1e3+o},add:function(_,m,g){var y=m?function(w,x,S,T){_(w,x,S,T),f.remove(y)}:_;return f.remove(_),a[g?"unshift":"push"](y),P1(),y},remove:function(_,m){~(m=a.indexOf(_))&&a.splice(m,1)&&p>=m&&p--},_listeners:a},f}(),P1=function(){return!ji&&ae.wake()},H={},Vm=/^[\d.\-M][\d.\-,\s]/,qm=/["']/g,Fm=function(t){for(var n={},r=t.substr(1,t.length-3).split(":"),i=r[0],o=1,s=r.length,a,l,c;o<s;o++)l=r[o],a=o!==s-1?l.lastIndexOf(","):l.length,c=l.substr(0,a),n[i]=isNaN(c)?c.replace(qm,"").trim():+c,i=l.substr(a+1).trim();return n},Bm=function(t){var n=t.indexOf("(")+1,r=t.indexOf(")"),i=t.indexOf("(",n);return t.substring(n,~i&&i<r?t.indexOf(")",r+1):r)},zm=function(t){var n=(t+"").split("("),r=H[n[0]];return r&&n.length>1&&r.config?r.config.apply(null,~t.indexOf("{")?[Fm(n[1])]:Bm(t).split(",").map(M5)):H._CE&&Vm.test(t)?H._CE("",t):r},K5=function(t){return function(n){return 1-t(1-n)}},Z5=function e(t,n){for(var r=t._first,i;r;)r instanceof zt?e(r,n):r.vars.yoyoEase&&(!r._yoyo||!r._repeat)&&r._yoyo!==n&&(r.timeline?e(r.timeline,n):(i=r._ease,r._ease=r._yEase,r._yEase=i,r._yoyo=n)),r=r._next},br=function(t,n){return t&&(ht(t)?t:H[t]||zm(t))||n},Rr=function(t,n,r,i){r===void 0&&(r=function(l){return 1-n(1-l)}),i===void 0&&(i=function(l){return l<.5?n(l*2)/2:1-n((1-l)*2)/2});var o={easeIn:n,easeOut:r,easeInOut:i},s;return ee(t,function(a){H[a]=ve[a]=o,H[s=a.toLowerCase()]=r;for(var l in o)H[s+(l==="easeIn"?".in":l==="easeOut"?".out":".inOut")]=H[a+"."+l]=o[l]}),o},J5=function(t){return function(n){return n<.5?(1-t(1-n*2))/2:.5+t((n-.5)*2)/2}},nl=function e(t,n,r){var i=n>=1?n:1,o=(r||(t?.3:.45))/(n<1?n:1),s=o/gc*(Math.asin(1/i)||0),a=function(u){return u===1?1:i*Math.pow(2,-10*u)*mm((u-s)*o)+1},l=t==="out"?a:t==="in"?function(c){return 1-a(1-c)}:J5(a);return o=gc/o,l.config=function(c,u){return e(t,c,u)},l},rl=function e(t,n){n===void 0&&(n=1.70158);var r=function(s){return s?--s*s*((n+1)*s+n)+1:0},i=t==="out"?r:t==="in"?function(o){return 1-r(1-o)}:J5(r);return i.config=function(o){return e(t,o)},i};ee("Linear,Quad,Cubic,Quart,Quint,Strong",function(e,t){var n=t<5?t+1:t;Rr(e+",Power"+(n-1),t?function(r){return Math.pow(r,n)}:function(r){return r},function(r){return 1-Math.pow(1-r,n)},function(r){return r<.5?Math.pow(r*2,n)/2:1-Math.pow((1-r)*2,n)/2})});H.Linear.easeNone=H.none=H.Linear.easeIn;Rr("Elastic",nl("in"),nl("out"),nl());(function(e,t){var n=1/t,r=2*n,i=2.5*n,o=function(a){return a<n?e*a*a:a<r?e*Math.pow(a-1.5/t,2)+.75:a<i?e*(a-=2.25/t)*a+.9375:e*Math.pow(a-2.625/t,2)+.984375};Rr("Bounce",function(s){return 1-o(1-s)},o)})(7.5625,2.75);Rr("Expo",function(e){return e?Math.pow(2,10*(e-1)):0});Rr("Circ",function(e){return-(S5(1-e*e)-1)});Rr("Sine",function(e){return e===1?1:-pm(e*dm)+1});Rr("Back",rl("in"),rl("out"),rl());H.SteppedEase=H.steps=ve.SteppedEase={config:function(t,n){t===void 0&&(t=1);var r=1/t,i=t+(n?0:1),o=n?1:0,s=1-Z;return function(a){return((i*ro(0,s,a)|0)+o)*r}}};C1.ease=H["quad.out"];ee("onComplete,onUpdate,onStart,onRepeat,onReverseComplete,onInterrupt",function(e){return e0+=e+","+e+"Params,"});var t6=function(t,n){this.id=hm++,t._gsap=this,this.target=t,this.harness=n,this.get=n?n.get:L5,this.set=n?n.getSetter:s0},Ui=function(){function e(n){this.vars=n,this._delay=+n.delay||0,(this._repeat=n.repeat===1/0?-2:n.repeat||0)&&(this._rDelay=n.repeatDelay||0,this._yoyo=!!n.yoyo||!!n.yoyoEase),this._ts=1,k1(this,+n.duration,1,1),this.data=n.data,it&&(this._ctx=it,it.data.push(this)),ji||ae.wake()}var t=e.prototype;return t.delay=function(r){return r||r===0?(this.parent&&this.parent.smoothChildTiming&&this.startTime(this._start+r-this._delay),this._delay=r,this):this._delay},t.duration=function(r){return arguments.length?this.totalDuration(this._repeat>0?r+(r+this._rDelay)*this._repeat:r):this.totalDuration()&&this._dur},t.totalDuration=function(r){return arguments.length?(this._dirty=0,k1(this,this._repeat<0?r:(r-this._repeat*this._rDelay)/(this._repeat+1))):this._tDur},t.totalTime=function(r,i){if(P1(),!arguments.length)return this._tTime;var o=this._dp;if(o&&o.smoothChildTiming&&this._ts){for(ca(this,r),!o._dp||o.parent||I5(o,this);o&&o.parent;)o.parent._time!==o._start+(o._ts>=0?o._tTime/o._ts:(o.totalDuration()-o._tTime)/-o._ts)&&o.totalTime(o._tTime,!0),o=o.parent;!this.parent&&this._dp.autoRemoveChildren&&(this._ts>0&&r<this._tDur||this._ts<0&&r>0||!this._tDur&&!r)&&He(this._dp,this,this._start-this._delay)}return(this._tTime!==r||!this._dur&&!i||this._initted&&Math.abs(this._zTime)===Z||!r&&!this._initted&&(this.add||this._ptLookup))&&(this._ts||(this._pTime=r),O5(this,r,i)),this},t.time=function(r,i){return arguments.length?this.totalTime(Math.min(this.totalDuration(),r+pd(this))%(this._dur+this._rDelay)||(r?this._dur:0),i):this._time},t.totalProgress=function(r,i){return arguments.length?this.totalTime(this.totalDuration()*r,i):this.totalDuration()?Math.min(1,this._tTime/this._tDur):this.rawTime()>0?1:0},t.progress=function(r,i){return arguments.length?this.totalTime(this.duration()*(this._yoyo&&!(this.iteration()&1)?1-r:r)+pd(this),i):this.duration()?Math.min(1,this._time/this._dur):this.rawTime()>0?1:0},t.iteration=function(r,i){var o=this.duration()+this._rDelay;return arguments.length?this.totalTime(this._time+(r-1)*o,i):this._repeat?E1(this._tTime,o)+1:1},t.timeScale=function(r,i){if(!arguments.length)return this._rts===-Z?0:this._rts;if(this._rts===r)return this;var o=this.parent&&this._ts?Ps(this.parent._time,this):this._tTime;return this._rts=+r||0,this._ts=this._ps||r===-Z?0:this._rts,this.totalTime(ro(-Math.abs(this._delay),this._tDur,o),i!==!1),la(this),Sm(this)},t.paused=function(r){return arguments.length?(this._ps!==r&&(this._ps=r,r?(this._pTime=this._tTime||Math.max(-this._delay,this.rawTime()),this._ts=this._act=0):(P1(),this._ts=this._rts,this.totalTime(this.parent&&!this.parent.smoothChildTiming?this.rawTime():this._tTime||this._pTime,this.progress()===1&&Math.abs(this._zTime)!==Z&&(this._tTime-=Z)))),this):this._ps},t.startTime=function(r){if(arguments.length){this._start=r;var i=this.parent||this._dp;return i&&(i._sort||!this.parent)&&He(i,this,r-this._delay),this}return this._start},t.endTime=function(r){return this._start+(te(r)?this.totalDuration():this.duration())/Math.abs(this._ts||1)},t.rawTime=function(r){var i=this.parent||this._dp;return i?r&&(!this._ts||this._repeat&&this._time&&this.totalProgress()<1)?this._tTime%(this._dur+this._rDelay):this._ts?Ps(i.rawTime(r),this):this._tTime:this._tTime},t.revert=function(r){r===void 0&&(r=ym);var i=Rt;return Rt=r,(this._initted||this._startAt)&&(this.timeline&&this.timeline.revert(r),this.totalTime(-.01,r.suppressEvents)),this.data!=="nested"&&r.kill!==!1&&this.kill(),Rt=i,this},t.globalTime=function(r){for(var i=this,o=arguments.length?r:i.rawTime();i;)o=i._start+o/(Math.abs(i._ts)||1),i=i._dp;return!this.parent&&this._sat?this._sat.globalTime(r):o},t.repeat=function(r){return arguments.length?(this._repeat=r===1/0?-2:r,md(this)):this._repeat===-2?1/0:this._repeat},t.repeatDelay=function(r){if(arguments.length){var i=this._time;return this._rDelay=r,md(this),i?this.time(i):this}return this._rDelay},t.yoyo=function(r){return arguments.length?(this._yoyo=r,this):this._yoyo},t.seek=function(r,i){return this.totalTime(xe(this,r),te(i))},t.restart=function(r,i){return this.play().totalTime(r?-this._delay:0,te(i))},t.play=function(r,i){return r!=null&&this.seek(r,i),this.reversed(!1).paused(!1)},t.reverse=function(r,i){return r!=null&&this.seek(r||this.totalDuration(),i),this.reversed(!0).paused(!1)},t.pause=function(r,i){return r!=null&&this.seek(r,i),this.paused(!0)},t.resume=function(){return this.paused(!1)},t.reversed=function(r){return arguments.length?(!!r!==this.reversed()&&this.timeScale(-this._rts||(r?-Z:0)),this):this._rts<0},t.invalidate=function(){return this._initted=this._act=0,this._zTime=-Z,this},t.isActive=function(){var r=this.parent||this._dp,i=this._start,o;return!!(!r||this._ts&&this._initted&&r.isActive()&&(o=r.rawTime(!0))>=i&&o<this.endTime(!0)-Z)},t.eventCallback=function(r,i,o){var s=this.vars;return arguments.length>1?(i?(s[r]=i,o&&(s[r+"Params"]=o),r==="onUpdate"&&(this._onUpdate=i)):delete s[r],this):s[r]},t.then=function(r){var i=this;return new Promise(function(o){var s=ht(r)?r:N5,a=function(){var c=i.then;i.then=null,ht(s)&&(s=s(i))&&(s.then||s===i)&&(i.then=c),o(s),i.then=c};i._initted&&i.totalProgress()===1&&i._ts>=0||!i._tTime&&i._ts<0?a():i._prom=a})},t.kill=function(){ti(this)},e}();Ae(Ui.prototype,{_time:0,_start:0,_end:0,_tTime:0,_tDur:0,_dirty:0,_repeat:0,_yoyo:!1,parent:null,_initted:!1,_rDelay:0,_ts:1,_dp:0,ratio:0,_zTime:-Z,_prom:0,_ps:!1,_rts:1});var zt=function(e){x5(t,e);function t(r,i){var o;return r===void 0&&(r={}),o=e.call(this,r)||this,o.labels={},o.smoothChildTiming=!!r.smoothChildTiming,o.autoRemoveChildren=!!r.autoRemoveChildren,o._sort=te(r.sortChildren),st&&He(r.parent||st,sn(o),i),r.reversed&&o.reverse(),r.paused&&o.paused(!0),r.scrollTrigger&&V5(sn(o),r.scrollTrigger),o}var n=t.prototype;return n.to=function(i,o,s){return hi(0,arguments,this),this},n.from=function(i,o,s){return hi(1,arguments,this),this},n.fromTo=function(i,o,s,a){return hi(2,arguments,this),this},n.set=function(i,o,s){return o.duration=0,o.parent=this,di(o).repeatDelay||(o.repeat=0),o.immediateRender=!!o.immediateRender,new gt(i,o,xe(this,s),1),this},n.call=function(i,o,s){return He(this,gt.delayedCall(0,i,o),s)},n.staggerTo=function(i,o,s,a,l,c,u){return s.duration=o,s.stagger=s.stagger||a,s.onComplete=c,s.onCompleteParams=u,s.parent=this,new gt(i,s,xe(this,l)),this},n.staggerFrom=function(i,o,s,a,l,c,u){return s.runBackwards=1,di(s).immediateRender=te(s.immediateRender),this.staggerTo(i,o,s,a,l,c,u)},n.staggerFromTo=function(i,o,s,a,l,c,u,f){return a.startAt=s,di(a).immediateRender=te(a.immediateRender),this.staggerTo(i,o,a,l,c,u,f)},n.render=function(i,o,s){var a=this._time,l=this._dirty?this.totalDuration():this._tDur,c=this._dur,u=i<=0?0:Tt(i),f=this._zTime<0!=i<0&&(this._initted||!c),d,p,v,h,_,m,g,y,w,x,S,T;if(this!==st&&u>l&&i>=0&&(u=l),u!==this._tTime||s||f){if(a!==this._time&&c&&(u+=this._time-a,i+=this._time-a),d=u,w=this._start,y=this._ts,m=!y,f&&(c||(a=this._zTime),(i||!o)&&(this._zTime=i)),this._repeat){if(S=this._yoyo,_=c+this._rDelay,this._repeat<-1&&i<0)return this.totalTime(_*100+i,o,s);if(d=Tt(u%_),u===l?(h=this._repeat,d=c):(h=~~(u/_),h&&h===u/_&&(d=c,h--),d>c&&(d=c)),x=E1(this._tTime,_),!a&&this._tTime&&x!==h&&this._tTime-x*_-this._dur<=0&&(x=h),S&&h&1&&(d=c-d,T=1),h!==x&&!this._lock){var b=S&&x&1,C=b===(S&&h&1);if(h<x&&(b=!b),a=b?0:u%c?c:u,this._lock=1,this.render(a||(T?0:Tt(h*_)),o,!c)._lock=0,this._tTime=u,!o&&this.parent&&de(this,"onRepeat"),this.vars.repeatRefresh&&!T&&(this.invalidate()._lock=1),a&&a!==this._time||m!==!this._ts||this.vars.onRepeat&&!this.parent&&!this._act)return this;if(c=this._dur,l=this._tDur,C&&(this._lock=2,a=b?c:-1e-4,this.render(a,!0),this.vars.repeatRefresh&&!T&&this.invalidate()),this._lock=0,!this._ts&&!m)return this;Z5(this,T)}}if(this._hasPause&&!this._forcing&&this._lock<2&&(g=Em(this,Tt(a),Tt(d)),g&&(u-=d-(d=g._start))),this._tTime=u,this._time=d,this._act=!y,this._initted||(this._onUpdate=this.vars.onUpdate,this._initted=1,this._zTime=i,a=0),!a&&d&&!o&&!h&&(de(this,"onStart"),this._tTime!==u))return this;if(d>=a&&i>=0)for(p=this._first;p;){if(v=p._next,(p._act||d>=p._start)&&p._ts&&g!==p){if(p.parent!==this)return this.render(i,o,s);if(p.render(p._ts>0?(d-p._start)*p._ts:(p._dirty?p.totalDuration():p._tDur)+(d-p._start)*p._ts,o,s),d!==this._time||!this._ts&&!m){g=0,v&&(u+=this._zTime=-Z);break}}p=v}else{p=this._last;for(var E=i<0?i:d;p;){if(v=p._prev,(p._act||E<=p._end)&&p._ts&&g!==p){if(p.parent!==this)return this.render(i,o,s);if(p.render(p._ts>0?(E-p._start)*p._ts:(p._dirty?p.totalDuration():p._tDur)+(E-p._start)*p._ts,o,s||Rt&&(p._initted||p._startAt)),d!==this._time||!this._ts&&!m){g=0,v&&(u+=this._zTime=E?-Z:Z);break}}p=v}}if(g&&!o&&(this.pause(),g.render(d>=a?0:-Z)._zTime=d>=a?1:-1,this._ts))return this._start=w,la(this),this.render(i,o,s);this._onUpdate&&!o&&de(this,"onUpdate",!0),(u===l&&this._tTime>=this.totalDuration()||!u&&a)&&(w===this._start||Math.abs(y)!==Math.abs(this._ts))&&(this._lock||((i||!c)&&(u===l&&this._ts>0||!u&&this._ts<0)&&Yn(this,1),!o&&!(i<0&&!a)&&(u||a||!l)&&(de(this,u===l&&i>=0?"onComplete":"onReverseComplete",!0),this._prom&&!(u<l&&this.timeScale()>0)&&this._prom())))}return this},n.add=function(i,o){var s=this;if(yn(o)||(o=xe(this,o,i)),!(i instanceof Ui)){if(It(i))return i.forEach(function(a){return s.add(a,o)}),this;if(Ct(i))return this.addLabel(i,o);if(ht(i))i=gt.delayedCall(0,i);else return this}return this!==i?He(this,i,o):this},n.getChildren=function(i,o,s,a){i===void 0&&(i=!0),o===void 0&&(o=!0),s===void 0&&(s=!0),a===void 0&&(a=-Ce);for(var l=[],c=this._first;c;)c._start>=a&&(c instanceof gt?o&&l.push(c):(s&&l.push(c),i&&l.push.apply(l,c.getChildren(!0,o,s)))),c=c._next;return l},n.getById=function(i){for(var o=this.getChildren(1,1,1),s=o.length;s--;)if(o[s].vars.id===i)return o[s]},n.remove=function(i){return Ct(i)?this.removeLabel(i):ht(i)?this.killTweensOf(i):(aa(this,i),i===this._recent&&(this._recent=this._last),Tr(this))},n.totalTime=function(i,o){return arguments.length?(this._forcing=1,!this._dp&&this._ts&&(this._start=Tt(ae.time-(this._ts>0?i/this._ts:(this.totalDuration()-i)/-this._ts))),e.prototype.totalTime.call(this,i,o),this._forcing=0,this):this._tTime},n.addLabel=function(i,o){return this.labels[i]=xe(this,o),this},n.removeLabel=function(i){return delete this.labels[i],this},n.addPause=function(i,o,s){var a=gt.delayedCall(0,o||Bi,s);return a.data="isPause",this._hasPause=1,He(this,a,xe(this,i))},n.removePause=function(i){var o=this._first;for(i=xe(this,i);o;)o._start===i&&o.data==="isPause"&&Yn(o),o=o._next},n.killTweensOf=function(i,o,s){for(var a=this.getTweensOf(i,s),l=a.length;l--;)Ln!==a[l]&&a[l].kill(i,o);return this},n.getTweensOf=function(i,o){for(var s=[],a=Ee(i),l=this._first,c=yn(o),u;l;)l instanceof gt?_m(l._targets,a)&&(c?(!Ln||l._initted&&l._ts)&&l.globalTime(0)<=o&&l.globalTime(l.totalDuration())>o:!o||l.isActive())&&s.push(l):(u=l.getTweensOf(a,o)).length&&s.push.apply(s,u),l=l._next;return s},n.tweenTo=function(i,o){o=o||{};var s=this,a=xe(s,i),l=o,c=l.startAt,u=l.onStart,f=l.onStartParams,d=l.immediateRender,p,v=gt.to(s,Ae({ease:o.ease||"none",lazy:!1,immediateRender:!1,time:a,overwrite:"auto",duration:o.duration||Math.abs((a-(c&&"time"in c?c.time:s._time))/s.timeScale())||Z,onStart:function(){if(s.pause(),!p){var _=o.duration||Math.abs((a-(c&&"time"in c?c.time:s._time))/s.timeScale());v._dur!==_&&k1(v,_,0,1).render(v._time,!0,!0),p=1}u&&u.apply(v,f||[])}},o));return d?v.render(0):v},n.tweenFromTo=function(i,o,s){return this.tweenTo(o,Ae({startAt:{time:xe(this,i)}},s))},n.recent=function(){return this._recent},n.nextLabel=function(i){return i===void 0&&(i=this._time),gd(this,xe(this,i))},n.previousLabel=function(i){return i===void 0&&(i=this._time),gd(this,xe(this,i),1)},n.currentLabel=function(i){return arguments.length?this.seek(i,!0):this.previousLabel(this._time+Z)},n.shiftChildren=function(i,o,s){s===void 0&&(s=0);for(var a=this._first,l=this.labels,c;a;)a._start>=s&&(a._start+=i,a._end+=i),a=a._next;if(o)for(c in l)l[c]>=s&&(l[c]+=i);return Tr(this)},n.invalidate=function(i){var o=this._first;for(this._lock=0;o;)o.invalidate(i),o=o._next;return e.prototype.invalidate.call(this,i)},n.clear=function(i){i===void 0&&(i=!0);for(var o=this._first,s;o;)s=o._next,this.remove(o),o=s;return this._dp&&(this._time=this._tTime=this._pTime=0),i&&(this.labels={}),Tr(this)},n.totalDuration=function(i){var o=0,s=this,a=s._last,l=Ce,c,u,f;if(arguments.length)return s.timeScale((s._repeat<0?s.duration():s.totalDuration())/(s.reversed()?-i:i));if(s._dirty){for(f=s.parent;a;)c=a._prev,a._dirty&&a.totalDuration(),u=a._start,u>l&&s._sort&&a._ts&&!s._lock?(s._lock=1,He(s,a,u-a._delay,1)._lock=0):l=u,u<0&&a._ts&&(o-=u,(!f&&!s._dp||f&&f.smoothChildTiming)&&(s._start+=u/s._ts,s._time-=u,s._tTime-=u),s.shiftChildren(-u,!1,-1/0),l=0),a._end>o&&a._ts&&(o=a._end),a=c;k1(s,s===st&&s._time>o?s._time:o,1,1),s._dirty=0}return s._tDur},t.updateRoot=function(i){if(st._ts&&(O5(st,Ps(i,st)),A5=ae.frame),ae.frame>=dd){dd+=pe.autoSleep||120;var o=st._first;if((!o||!o._ts)&&pe.autoSleep&&ae._listeners.length<2){for(;o&&!o._ts;)o=o._next;o||ae.sleep()}}},t}(Ui);Ae(zt.prototype,{_lock:0,_hasPause:0,_forcing:0});var jm=function(t,n,r,i,o,s,a){var l=new ne(this._pt,t,n,0,1,s6,null,o),c=0,u=0,f,d,p,v,h,_,m,g;for(l.b=r,l.e=i,r+="",i+="",(m=~i.indexOf("random("))&&(i=zi(i)),s&&(g=[r,i],s(g,t,n),r=g[0],i=g[1]),d=r.match(Ja)||[];f=Ja.exec(i);)v=f[0],h=i.substring(c,f.index),p?p=(p+1)%5:h.substr(-5)==="rgba("&&(p=1),v!==d[u++]&&(_=parseFloat(d[u-1])||0,l._pt={_next:l._pt,p:h||u===1?h:",",s:_,c:v.charAt(1)==="="?p1(_,v)-_:parseFloat(v)-_,m:p&&p<4?Math.round:0},c=Ja.lastIndex);return l.c=c<i.length?i.substring(c,i.length):"",l.fp=a,(C5.test(i)||m)&&(l.e=0),this._pt=l,l},r0=function(t,n,r,i,o,s,a,l,c,u){ht(i)&&(i=i(o||0,t,s));var f=t[n],d=r!=="get"?r:ht(f)?c?t[n.indexOf("set")||!ht(t["get"+n.substr(3)])?n:"get"+n.substr(3)](c):t[n]():f,p=ht(f)?c?Wm:i6:o0,v;if(Ct(i)&&(~i.indexOf("random(")&&(i=zi(i)),i.charAt(1)==="="&&(v=p1(d,i)+(Nt(d)||0),(v||v===0)&&(i=v))),!u||d!==i||Cc)return!isNaN(d*i)&&i!==""?(v=new ne(this._pt,t,n,+d||0,i-(d||0),typeof f=="boolean"?Xm:o6,0,p),c&&(v.fp=c),a&&v.modifier(a,this,t),this._pt=v):(!f&&!(n in t)&&Ju(n,i),jm.call(this,t,n,d,i,p,l||pe.stringFilter,c))},Um=function(t,n,r,i,o){if(ht(t)&&(t=pi(t,o,n,r,i)),!Ze(t)||t.style&&t.nodeType||It(t)||T5(t))return Ct(t)?pi(t,o,n,r,i):t;var s={},a;for(a in t)s[a]=pi(t[a],o,n,r,i);return s},e6=function(t,n,r,i,o,s){var a,l,c,u;if(se[t]&&(a=new se[t]).init(o,a.rawVars?n[t]:Um(n[t],i,o,s,r),r,i,s)!==!1&&(r._pt=l=new ne(r._pt,o,t,0,1,a.render,a,0,a.priority),r!==n1))for(c=r._ptLookup[r._targets.indexOf(o)],u=a._props.length;u--;)c[a._props[u]]=l;return a},Ln,Cc,i0=function e(t,n,r){var i=t.vars,o=i.ease,s=i.startAt,a=i.immediateRender,l=i.lazy,c=i.onUpdate,u=i.runBackwards,f=i.yoyoEase,d=i.keyframes,p=i.autoRevert,v=t._dur,h=t._startAt,_=t._targets,m=t.parent,g=m&&m.data==="nested"?m.vars.targets:_,y=t._overwrite==="auto"&&!Xu,w=t.timeline,x,S,T,b,C,E,L,O,B,G,I,F,z;if(w&&(!d||!o)&&(o="none"),t._ease=br(o,C1.ease),t._yEase=f?K5(br(f===!0?o:f,C1.ease)):0,f&&t._yoyo&&!t._repeat&&(f=t._yEase,t._yEase=t._ease,t._ease=f),t._from=!w&&!!i.runBackwards,!w||d&&!i.stagger){if(O=_[0]?Sr(_[0]).harness:0,F=O&&i[O.prop],x=ks(i,t0),h&&(h._zTime<0&&h.progress(1),n<0&&u&&a&&!p?h.render(-1,!0):h.revert(u&&v?Xo:vm),h._lazy=0),s){if(Yn(t._startAt=gt.set(_,Ae({data:"isStart",overwrite:!1,parent:m,immediateRender:!0,lazy:!h&&te(l),startAt:null,delay:0,onUpdate:c&&function(){return de(t,"onUpdate")},stagger:0},s))),t._startAt._dp=0,t._startAt._sat=t,n<0&&(Rt||!a&&!p)&&t._startAt.revert(Xo),a&&v&&n<=0&&r<=0){n&&(t._zTime=n);return}}else if(u&&v&&!h){if(n&&(a=!1),T=Ae({overwrite:!1,data:"isFromStart",lazy:a&&!h&&te(l),immediateRender:a,stagger:0,parent:m},x),F&&(T[O.prop]=F),Yn(t._startAt=gt.set(_,T)),t._startAt._dp=0,t._startAt._sat=t,n<0&&(Rt?t._startAt.revert(Xo):t._startAt.render(-1,!0)),t._zTime=n,!a)e(t._startAt,Z,Z);else if(!n)return}for(t._pt=t._ptCache=0,l=v&&te(l)||l&&!v,S=0;S<_.length;S++){if(C=_[S],L=C._gsap||n0(_)[S]._gsap,t._ptLookup[S]=G={},_c[L.id]&&Un.length&&Es(),I=g===_?S:g.indexOf(C),O&&(B=new O).init(C,F||x,t,I,g)!==!1&&(t._pt=b=new ne(t._pt,C,B.name,0,1,B.render,B,0,B.priority),B._props.forEach(function(P){G[P]=b}),B.priority&&(E=1)),!O||F)for(T in x)se[T]&&(B=e6(T,x,t,I,C,g))?B.priority&&(E=1):G[T]=b=r0.call(t,C,T,"get",x[T],I,g,0,i.stringFilter);t._op&&t._op[S]&&t.kill(C,t._op[S]),y&&t._pt&&(Ln=t,st.killTweensOf(C,G,t.globalTime(n)),z=!t.parent,Ln=0),t._pt&&l&&(_c[L.id]=1)}E&&a6(t),t._onInit&&t._onInit(t)}t._onUpdate=c,t._initted=(!t._op||t._pt)&&!z,d&&n<=0&&w.render(Ce,!0,!0)},$m=function(t,n,r,i,o,s,a,l){var c=(t._pt&&t._ptCache||(t._ptCache={}))[n],u,f,d,p;if(!c)for(c=t._ptCache[n]=[],d=t._ptLookup,p=t._targets.length;p--;){if(u=d[p][n],u&&u.d&&u.d._pt)for(u=u.d._pt;u&&u.p!==n&&u.fp!==n;)u=u._next;if(!u)return Cc=1,t.vars[n]="+=0",i0(t,a),Cc=0,l?Fi(n+" not eligible for reset"):1;c.push(u)}for(p=c.length;p--;)f=c[p],u=f._pt||f,u.s=(i||i===0)&&!o?i:u.s+(i||0)+s*u.c,u.c=r-u.s,f.e&&(f.e=pt(r)+Nt(f.e)),f.b&&(f.b=u.s+Nt(f.b))},Hm=function(t,n){var r=t[0]?Sr(t[0]).harness:0,i=r&&r.aliases,o,s,a,l;if(!i)return n;o=Lr({},n);for(s in i)if(s in o)for(l=i[s].split(","),a=l.length;a--;)o[l[a]]=o[s];return o},Gm=function(t,n,r,i){var o=n.ease||i||"power1.inOut",s,a;if(It(n))a=r[t]||(r[t]=[]),n.forEach(function(l,c){return a.push({t:c/(n.length-1)*100,v:l,e:o})});else for(s in n)a=r[s]||(r[s]=[]),s==="ease"||a.push({t:parseFloat(t),v:n[s],e:o})},pi=function(t,n,r,i,o){return ht(t)?t.call(n,r,i,o):Ct(t)&&~t.indexOf("random(")?zi(t):t},n6=e0+"repeat,repeatDelay,yoyo,repeatRefresh,yoyoEase,autoRevert",r6={};ee(n6+",id,stagger,delay,duration,paused,scrollTrigger",function(e){return r6[e]=1});var gt=function(e){x5(t,e);function t(r,i,o,s){var a;typeof i=="number"&&(o.duration=i,i=o,o=null),a=e.call(this,s?i:di(i))||this;var l=a.vars,c=l.duration,u=l.delay,f=l.immediateRender,d=l.stagger,p=l.overwrite,v=l.keyframes,h=l.defaults,_=l.scrollTrigger,m=l.yoyoEase,g=i.parent||st,y=(It(r)||T5(r)?yn(r[0]):"length"in i)?[r]:Ee(r),w,x,S,T,b,C,E,L;if(a._targets=y.length?n0(y):Fi("GSAP target "+r+" not found. https://gsap.com",!pe.nullTargetWarn)||[],a._ptLookup=[],a._overwrite=p,v||d||Ao(c)||Ao(u)){if(i=a.vars,w=a.timeline=new zt({data:"nested",defaults:h||{},targets:g&&g.data==="nested"?g.vars.targets:y}),w.kill(),w.parent=w._dp=sn(a),w._start=0,d||Ao(c)||Ao(u)){if(T=y.length,E=d&&z5(d),Ze(d))for(b in d)~n6.indexOf(b)&&(L||(L={}),L[b]=d[b]);for(x=0;x<T;x++)S=ks(i,r6),S.stagger=0,m&&(S.yoyoEase=m),L&&Lr(S,L),C=y[x],S.duration=+pi(c,sn(a),x,C,y),S.delay=(+pi(u,sn(a),x,C,y)||0)-a._delay,!d&&T===1&&S.delay&&(a._delay=u=S.delay,a._start+=u,S.delay=0),w.to(C,S,E?E(x,C,y):0),w._ease=H.none;w.duration()?c=u=0:a.timeline=0}else if(v){di(Ae(w.vars.defaults,{ease:"none"})),w._ease=br(v.ease||i.ease||"none");var O=0,B,G,I;if(It(v))v.forEach(function(F){return w.to(y,F,">")}),w.duration();else{S={};for(b in v)b==="ease"||b==="easeEach"||Gm(b,v[b],S,v.easeEach);for(b in S)for(B=S[b].sort(function(F,z){return F.t-z.t}),O=0,x=0;x<B.length;x++)G=B[x],I={ease:G.e,duration:(G.t-(x?B[x-1].t:0))/100*c},I[b]=G.v,w.to(y,I,O),O+=I.duration;w.duration()<c&&w.to({},{duration:c-w.duration()})}}c||a.duration(c=w.duration())}else a.timeline=0;return p===!0&&!Xu&&(Ln=sn(a),st.killTweensOf(y),Ln=0),He(g,sn(a),o),i.reversed&&a.reverse(),i.paused&&a.paused(!0),(f||!c&&!v&&a._start===Tt(g._time)&&te(f)&&Tm(sn(a))&&g.data!=="nested")&&(a._tTime=-Z,a.render(Math.max(0,-u)||0)),_&&V5(sn(a),_),a}var n=t.prototype;return n.render=function(i,o,s){var a=this._time,l=this._tDur,c=this._dur,u=i<0,f=i>l-Z&&!u?l:i<Z?0:i,d,p,v,h,_,m,g,y,w;if(!c)Cm(this,i,o,s);else if(f!==this._tTime||!i||s||!this._initted&&this._tTime||this._startAt&&this._zTime<0!==u){if(d=f,y=this.timeline,this._repeat){if(h=c+this._rDelay,this._repeat<-1&&u)return this.totalTime(h*100+i,o,s);if(d=Tt(f%h),f===l?(v=this._repeat,d=c):(v=~~(f/h),v&&v===Tt(f/h)&&(d=c,v--),d>c&&(d=c)),m=this._yoyo&&v&1,m&&(w=this._yEase,d=c-d),_=E1(this._tTime,h),d===a&&!s&&this._initted&&v===_)return this._tTime=f,this;v!==_&&(y&&this._yEase&&Z5(y,m),this.vars.repeatRefresh&&!m&&!this._lock&&this._time!==h&&this._initted&&(this._lock=s=1,this.render(Tt(h*v),!0).invalidate()._lock=0))}if(!this._initted){if(q5(this,u?i:d,s,o,f))return this._tTime=0,this;if(a!==this._time&&!(s&&this.vars.repeatRefresh&&v!==_))return this;if(c!==this._dur)return this.render(i,o,s)}if(this._tTime=f,this._time=d,!this._act&&this._ts&&(this._act=1,this._lazy=0),this.ratio=g=(w||this._ease)(d/c),this._from&&(this.ratio=g=1-g),d&&!a&&!o&&!v&&(de(this,"onStart"),this._tTime!==f))return this;for(p=this._pt;p;)p.r(g,p.d),p=p._next;y&&y.render(i<0?i:y._dur*y._ease(d/this._dur),o,s)||this._startAt&&(this._zTime=i),this._onUpdate&&!o&&(u&&wc(this,i,o,s),de(this,"onUpdate")),this._repeat&&v!==_&&this.vars.onRepeat&&!o&&this.parent&&de(this,"onRepeat"),(f===this._tDur||!f)&&this._tTime===f&&(u&&!this._onUpdate&&wc(this,i,!0,!0),(i||!c)&&(f===this._tDur&&this._ts>0||!f&&this._ts<0)&&Yn(this,1),!o&&!(u&&!a)&&(f||a||m)&&(de(this,f===l?"onComplete":"onReverseComplete",!0),this._prom&&!(f<l&&this.timeScale()>0)&&this._prom()))}return this},n.targets=function(){return this._targets},n.invalidate=function(i){return(!i||!this.vars.runBackwards)&&(this._startAt=0),this._pt=this._op=this._onUpdate=this._lazy=this.ratio=0,this._ptLookup=[],this.timeline&&this.timeline.invalidate(i),e.prototype.invalidate.call(this,i)},n.resetTo=function(i,o,s,a,l){ji||ae.wake(),this._ts||this.play();var c=Math.min(this._dur,(this._dp._time-this._start)*this._ts),u;return this._initted||i0(this,c),u=this._ease(c/this._dur),$m(this,i,o,s,a,u,c,l)?this.resetTo(i,o,s,a,1):(ca(this,0),this.parent||R5(this._dp,this,"_first","_last",this._dp._sort?"_start":0),this.render(0))},n.kill=function(i,o){if(o===void 0&&(o="all"),!i&&(!o||o==="all"))return this._lazy=this._pt=0,this.parent?ti(this):this;if(this.timeline){var s=this.timeline.totalDuration();return this.timeline.killTweensOf(i,o,Ln&&Ln.vars.overwrite!==!0)._first||ti(this),this.parent&&s!==this.timeline.totalDuration()&&k1(this,this._dur*this.timeline._tDur/s,0,1),this}var a=this._targets,l=i?Ee(i):a,c=this._ptLookup,u=this._pt,f,d,p,v,h,_,m;if((!o||o==="all")&&xm(a,l))return o==="all"&&(this._pt=0),ti(this);for(f=this._op=this._op||[],o!=="all"&&(Ct(o)&&(h={},ee(o,function(g){return h[g]=1}),o=h),o=Hm(a,o)),m=a.length;m--;)if(~l.indexOf(a[m])){d=c[m],o==="all"?(f[m]=o,v=d,p={}):(p=f[m]=f[m]||{},v=o);for(h in v)_=d&&d[h],_&&((!("kill"in _.d)||_.d.kill(h)===!0)&&aa(this,_,"_pt"),delete d[h]),p!=="all"&&(p[h]=1)}return this._initted&&!this._pt&&u&&ti(this),this},t.to=function(i,o){return new t(i,o,arguments[2])},t.from=function(i,o){return hi(1,arguments)},t.delayedCall=function(i,o,s,a){return new t(o,0,{immediateRender:!1,lazy:!1,overwrite:!1,delay:i,onComplete:o,onReverseComplete:o,onCompleteParams:s,onReverseCompleteParams:s,callbackScope:a})},t.fromTo=function(i,o,s){return hi(2,arguments)},t.set=function(i,o){return o.duration=0,o.repeatDelay||(o.repeat=0),new t(i,o)},t.killTweensOf=function(i,o,s){return st.killTweensOf(i,o,s)},t}(Ui);Ae(gt.prototype,{_targets:[],_lazy:0,_startAt:0,_op:0,_onInit:0});ee("staggerTo,staggerFrom,staggerFromTo",function(e){gt[e]=function(){var t=new zt,n=Sc.call(arguments,0);return n.splice(e==="staggerFromTo"?5:4,0,0),t[e].apply(t,n)}});var o0=function(t,n,r){return t[n]=r},i6=function(t,n,r){return t[n](r)},Wm=function(t,n,r,i){return t[n](i.fp,r)},Ym=function(t,n,r){return t.setAttribute(n,r)},s0=function(t,n){return ht(t[n])?i6:Qu(t[n])&&t.setAttribute?Ym:o0},o6=function(t,n){return n.set(n.t,n.p,Math.round((n.s+n.c*t)*1e6)/1e6,n)},Xm=function(t,n){return n.set(n.t,n.p,!!(n.s+n.c*t),n)},s6=function(t,n){var r=n._pt,i="";if(!t&&n.b)i=n.b;else if(t===1&&n.e)i=n.e;else{for(;r;)i=r.p+(r.m?r.m(r.s+r.c*t):Math.round((r.s+r.c*t)*1e4)/1e4)+i,r=r._next;i+=n.c}n.set(n.t,n.p,i,n)},a0=function(t,n){for(var r=n._pt;r;)r.r(t,r.d),r=r._next},Qm=function(t,n,r,i){for(var o=this._pt,s;o;)s=o._next,o.p===i&&o.modifier(t,n,r),o=s},Km=function(t){for(var n=this._pt,r,i;n;)i=n._next,n.p===t&&!n.op||n.op===t?aa(this,n,"_pt"):n.dep||(r=1),n=i;return!r},Zm=function(t,n,r,i){i.mSet(t,n,i.m.call(i.tween,r,i.mt),i)},a6=function(t){for(var n=t._pt,r,i,o,s;n;){for(r=n._next,i=o;i&&i.pr>n.pr;)i=i._next;(n._prev=i?i._prev:s)?n._prev._next=n:o=n,(n._next=i)?i._prev=n:s=n,n=r}t._pt=o},ne=function(){function e(n,r,i,o,s,a,l,c,u){this.t=r,this.s=o,this.c=s,this.p=i,this.r=a||o6,this.d=l||this,this.set=c||o0,this.pr=u||0,this._next=n,n&&(n._prev=this)}var t=e.prototype;return t.modifier=function(r,i,o){this.mSet=this.mSet||this.set,this.set=Zm,this.m=r,this.mt=o,this.tween=i},e}();ee(e0+"parent,duration,ease,delay,overwrite,runBackwards,startAt,yoyo,immediateRender,repeat,repeatDelay,data,paused,reversed,lazy,callbackScope,stringFilter,id,yoyoEase,stagger,inherit,repeatRefresh,keyframes,autoRevert,scrollTrigger",function(e){return t0[e]=1});ve.TweenMax=ve.TweenLite=gt;ve.TimelineLite=ve.TimelineMax=zt;st=new zt({sortChildren:!1,defaults:C1,autoRemoveChildren:!0,id:"root",smoothChildTiming:!0});pe.stringFilter=Q5;var Cr=[],Ko={},Jm=[],yd=0,t4=0,il=function(t){return(Ko[t]||Jm).map(function(n){return n()})},Ec=function(){var t=Date.now(),n=[];t-yd>2&&(il("matchMediaInit"),Cr.forEach(function(r){var i=r.queries,o=r.conditions,s,a,l,c;for(a in i)s=Ue.matchMedia(i[a]).matches,s&&(l=1),s!==o[a]&&(o[a]=s,c=1);c&&(r.revert(),l&&n.push(r))}),il("matchMediaRevert"),n.forEach(function(r){return r.onMatch(r,function(i){return r.add(null,i)})}),yd=t,il("matchMedia"))},l6=function(){function e(n,r){this.selector=r&&Tc(r),this.data=[],this._r=[],this.isReverted=!1,this.id=t4++,n&&this.add(n)}var t=e.prototype;return t.add=function(r,i,o){ht(r)&&(o=i,i=r,r=ht);var s=this,a=function(){var c=it,u=s.selector,f;return c&&c!==s&&c.data.push(s),o&&(s.selector=Tc(o)),it=s,f=i.apply(s,arguments),ht(f)&&s._r.push(f),it=c,s.selector=u,s.isReverted=!1,f};return s.last=a,r===ht?a(s,function(l){return s.add(null,l)}):r?s[r]=a:a},t.ignore=function(r){var i=it;it=null,r(this),it=i},t.getTweens=function(){var r=[];return this.data.forEach(function(i){return i instanceof e?r.push.apply(r,i.getTweens()):i instanceof gt&&!(i.parent&&i.parent.data==="nested")&&r.push(i)}),r},t.clear=function(){this._r.length=this.data.length=0},t.kill=function(r,i){var o=this;if(r?function(){for(var a=o.getTweens(),l=o.data.length,c;l--;)c=o.data[l],c.data==="isFlip"&&(c.revert(),c.getChildren(!0,!0,!1).forEach(function(u){return a.splice(a.indexOf(u),1)}));for(a.map(function(u){return{g:u._dur||u._delay||u._sat&&!u._sat.vars.immediateRender?u.globalTime(0):-1/0,t:u}}).sort(function(u,f){return f.g-u.g||-1/0}).forEach(function(u){return u.t.revert(r)}),l=o.data.length;l--;)c=o.data[l],c instanceof zt?c.data!=="nested"&&(c.scrollTrigger&&c.scrollTrigger.revert(),c.kill()):!(c instanceof gt)&&c.revert&&c.revert(r);o._r.forEach(function(u){return u(r,o)}),o.isReverted=!0}():this.data.forEach(function(a){return a.kill&&a.kill()}),this.clear(),i)for(var s=Cr.length;s--;)Cr[s].id===this.id&&Cr.splice(s,1)},t.revert=function(r){this.kill(r||{})},e}(),e4=function(){function e(n){this.contexts=[],this.scope=n,it&&it.data.push(this)}var t=e.prototype;return t.add=function(r,i,o){Ze(r)||(r={matches:r});var s=new l6(0,o||this.scope),a=s.conditions={},l,c,u;it&&!s.selector&&(s.selector=it.selector),this.contexts.push(s),i=s.add("onMatch",i),s.queries=r;for(c in r)c==="all"?u=1:(l=Ue.matchMedia(r[c]),l&&(Cr.indexOf(s)<0&&Cr.push(s),(a[c]=l.matches)&&(u=1),l.addListener?l.addListener(Ec):l.addEventListener("change",Ec)));return u&&i(s,function(f){return s.add(null,f)}),this},t.revert=function(r){this.kill(r||{})},t.kill=function(r){this.contexts.forEach(function(i){return i.kill(r,!0)})},e}(),Ds={registerPlugin:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];n.forEach(function(i){return W5(i)})},timeline:function(t){return new zt(t)},getTweensOf:function(t,n){return st.getTweensOf(t,n)},getProperty:function(t,n,r,i){Ct(t)&&(t=Ee(t)[0]);var o=Sr(t||{}).get,s=r?N5:M5;return r==="native"&&(r=""),t&&(n?s((se[n]&&se[n].get||o)(t,n,r,i)):function(a,l,c){return s((se[a]&&se[a].get||o)(t,a,l,c))})},quickSetter:function(t,n,r){if(t=Ee(t),t.length>1){var i=t.map(function(u){return ie.quickSetter(u,n,r)}),o=i.length;return function(u){for(var f=o;f--;)i[f](u)}}t=t[0]||{};var s=se[n],a=Sr(t),l=a.harness&&(a.harness.aliases||{})[n]||n,c=s?function(u){var f=new s;n1._pt=0,f.init(t,r?u+r:u,n1,0,[t]),f.render(1,f),n1._pt&&a0(1,n1)}:a.set(t,l);return s?c:function(u){return c(t,l,r?u+r:u,a,1)}},quickTo:function(t,n,r){var i,o=ie.to(t,Lr((i={},i[n]="+=0.1",i.paused=!0,i),r||{})),s=function(l,c,u){return o.resetTo(n,l,c,u)};return s.tween=o,s},isTweening:function(t){return st.getTweensOf(t,!0).length>0},defaults:function(t){return t&&t.ease&&(t.ease=br(t.ease,C1.ease)),hd(C1,t||{})},config:function(t){return hd(pe,t||{})},registerEffect:function(t){var n=t.name,r=t.effect,i=t.plugins,o=t.defaults,s=t.extendTimeline;(i||"").split(",").forEach(function(a){return a&&!se[a]&&!ve[a]&&Fi(n+" effect requires "+a+" plugin.")}),tl[n]=function(a,l,c){return r(Ee(a),Ae(l||{},o),c)},s&&(zt.prototype[n]=function(a,l,c){return this.add(tl[n](a,Ze(l)?l:(c=l)&&{},this),c)})},registerEase:function(t,n){H[t]=br(n)},parseEase:function(t,n){return arguments.length?br(t,n):H},getById:function(t){return st.getById(t)},exportRoot:function(t,n){t===void 0&&(t={});var r=new zt(t),i,o;for(r.smoothChildTiming=te(t.smoothChildTiming),st.remove(r),r._dp=0,r._time=r._tTime=st._time,i=st._first;i;)o=i._next,(n||!(!i._dur&&i instanceof gt&&i.vars.onComplete===i._targets[0]))&&He(r,i,i._start-i._delay),i=o;return He(st,r,0),r},context:function(t,n){return t?new l6(t,n):it},matchMedia:function(t){return new e4(t)},matchMediaRefresh:function(){return Cr.forEach(function(t){var n=t.conditions,r,i;for(i in n)n[i]&&(n[i]=!1,r=1);r&&t.revert()})||Ec()},addEventListener:function(t,n){var r=Ko[t]||(Ko[t]=[]);~r.indexOf(n)||r.push(n)},removeEventListener:function(t,n){var r=Ko[t],i=r&&r.indexOf(n);i>=0&&r.splice(i,1)},utils:{wrap:Mm,wrapYoyo:Nm,distribute:z5,random:U5,snap:j5,normalize:Om,getUnit:Nt,clamp:Pm,splitColor:Y5,toArray:Ee,selector:Tc,mapRange:H5,pipe:Am,unitize:Lm,interpolate:Rm,shuffle:B5},install:P5,effects:tl,ticker:ae,updateRoot:zt.updateRoot,plugins:se,globalTimeline:st,core:{PropTween:ne,globals:D5,Tween:gt,Timeline:zt,Animation:Ui,getCache:Sr,_removeLinkedListItem:aa,reverting:function(){return Rt},context:function(t){return t&&it&&(it.data.push(t),t._ctx=it),it},suppressOverwrites:function(t){return Xu=t}}};ee("to,from,fromTo,delayedCall,set,killTweensOf",function(e){return Ds[e]=gt[e]});ae.add(zt.updateRoot);n1=Ds.to({},{duration:0});var n4=function(t,n){for(var r=t._pt;r&&r.p!==n&&r.op!==n&&r.fp!==n;)r=r._next;return r},r4=function(t,n){var r=t._targets,i,o,s;for(i in n)for(o=r.length;o--;)s=t._ptLookup[o][i],s&&(s=s.d)&&(s._pt&&(s=n4(s,i)),s&&s.modifier&&s.modifier(n[i],t,r[o],i))},ol=function(t,n){return{name:t,rawVars:1,init:function(i,o,s){s._onInit=function(a){var l,c;if(Ct(o)&&(l={},ee(o,function(u){return l[u]=1}),o=l),n){l={};for(c in o)l[c]=n(o[c]);o=l}r4(a,o)}}}},ie=Ds.registerPlugin({name:"attr",init:function(t,n,r,i,o){var s,a,l;this.tween=r;for(s in n)l=t.getAttribute(s)||"",a=this.add(t,"setAttribute",(l||0)+"",n[s],i,o,0,0,s),a.op=s,a.b=l,this._props.push(s)},render:function(t,n){for(var r=n._pt;r;)Rt?r.set(r.t,r.p,r.b,r):r.r(t,r.d),r=r._next}},{name:"endArray",init:function(t,n){for(var r=n.length;r--;)this.add(t,r,t[r]||0,n[r],0,0,0,0,0,1)}},ol("roundProps",bc),ol("modifiers"),ol("snap",j5))||Ds;gt.version=zt.version=ie.version="3.12.5";k5=1;Ku()&&P1();H.Power0;H.Power1;H.Power2;H.Power3;H.Power4;H.Linear;H.Quad;H.Cubic;H.Quart;H.Quint;H.Strong;H.Elastic;H.Back;H.SteppedEase;H.Bounce;H.Sine;H.Expo;H.Circ;/*!
 * CSSPlugin 3.12.5
 * https://gsap.com
 *
 * Copyright 2008-2024, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license or for
 * Club GSAP members, the agreement issued with that membership.
 * @author: Jack Doyle, <EMAIL>
*/var _d,On,m1,l0,vr,wd,c0,i4=function(){return typeof window<"u"},_n={},ur=180/Math.PI,g1=Math.PI/180,zr=Math.atan2,xd=1e8,u0=/([A-Z])/g,o4=/(left|right|width|margin|padding|x)/i,s4=/[\s,\(]\S/,Ge={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},kc=function(t,n){return n.set(n.t,n.p,Math.round((n.s+n.c*t)*1e4)/1e4+n.u,n)},a4=function(t,n){return n.set(n.t,n.p,t===1?n.e:Math.round((n.s+n.c*t)*1e4)/1e4+n.u,n)},l4=function(t,n){return n.set(n.t,n.p,t?Math.round((n.s+n.c*t)*1e4)/1e4+n.u:n.b,n)},c4=function(t,n){var r=n.s+n.c*t;n.set(n.t,n.p,~~(r+(r<0?-.5:.5))+n.u,n)},c6=function(t,n){return n.set(n.t,n.p,t?n.e:n.b,n)},u6=function(t,n){return n.set(n.t,n.p,t!==1?n.b:n.e,n)},u4=function(t,n,r){return t.style[n]=r},f4=function(t,n,r){return t.style.setProperty(n,r)},d4=function(t,n,r){return t._gsap[n]=r},h4=function(t,n,r){return t._gsap.scaleX=t._gsap.scaleY=r},p4=function(t,n,r,i,o){var s=t._gsap;s.scaleX=s.scaleY=r,s.renderTransform(o,s)},m4=function(t,n,r,i,o){var s=t._gsap;s[n]=r,s.renderTransform(o,s)},at="transform",re=at+"Origin",g4=function e(t,n){var r=this,i=this.target,o=i.style,s=i._gsap;if(t in _n&&o){if(this.tfm=this.tfm||{},t!=="transform")t=Ge[t]||t,~t.indexOf(",")?t.split(",").forEach(function(a){return r.tfm[a]=ln(i,a)}):this.tfm[t]=s.x?s[t]:ln(i,t),t===re&&(this.tfm.zOrigin=s.zOrigin);else return Ge.transform.split(",").forEach(function(a){return e.call(r,a,n)});if(this.props.indexOf(at)>=0)return;s.svg&&(this.svgo=i.getAttribute("data-svg-origin"),this.props.push(re,n,"")),t=at}(o||n)&&this.props.push(t,n,o[t])},f6=function(t){t.translate&&(t.removeProperty("translate"),t.removeProperty("scale"),t.removeProperty("rotate"))},v4=function(){var t=this.props,n=this.target,r=n.style,i=n._gsap,o,s;for(o=0;o<t.length;o+=3)t[o+1]?n[t[o]]=t[o+2]:t[o+2]?r[t[o]]=t[o+2]:r.removeProperty(t[o].substr(0,2)==="--"?t[o]:t[o].replace(u0,"-$1").toLowerCase());if(this.tfm){for(s in this.tfm)i[s]=this.tfm[s];i.svg&&(i.renderTransform(),n.setAttribute("data-svg-origin",this.svgo||"")),o=c0(),(!o||!o.isStart)&&!r[at]&&(f6(r),i.zOrigin&&r[re]&&(r[re]+=" "+i.zOrigin+"px",i.zOrigin=0,i.renderTransform()),i.uncache=1)}},d6=function(t,n){var r={target:t,props:[],revert:v4,save:g4};return t._gsap||ie.core.getCache(t),n&&n.split(",").forEach(function(i){return r.save(i)}),r},h6,Pc=function(t,n){var r=On.createElementNS?On.createElementNS((n||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),t):On.createElement(t);return r&&r.style?r:On.createElement(t)},Xe=function e(t,n,r){var i=getComputedStyle(t);return i[n]||i.getPropertyValue(n.replace(u0,"-$1").toLowerCase())||i.getPropertyValue(n)||!r&&e(t,D1(n)||n,1)||""},Sd="O,Moz,ms,Ms,Webkit".split(","),D1=function(t,n,r){var i=n||vr,o=i.style,s=5;if(t in o&&!r)return t;for(t=t.charAt(0).toUpperCase()+t.substr(1);s--&&!(Sd[s]+t in o););return s<0?null:(s===3?"ms":s>=0?Sd[s]:"")+t},Dc=function(){i4()&&window.document&&(_d=window,On=_d.document,m1=On.documentElement,vr=Pc("div")||{style:{}},Pc("div"),at=D1(at),re=at+"Origin",vr.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",h6=!!D1("perspective"),c0=ie.core.reverting,l0=1)},sl=function e(t){var n=Pc("svg",this.ownerSVGElement&&this.ownerSVGElement.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),r=this.parentNode,i=this.nextSibling,o=this.style.cssText,s;if(m1.appendChild(n),n.appendChild(this),this.style.display="block",t)try{s=this.getBBox(),this._gsapBBox=this.getBBox,this.getBBox=e}catch{}else this._gsapBBox&&(s=this._gsapBBox());return r&&(i?r.insertBefore(this,i):r.appendChild(this)),m1.removeChild(n),this.style.cssText=o,s},Td=function(t,n){for(var r=n.length;r--;)if(t.hasAttribute(n[r]))return t.getAttribute(n[r])},p6=function(t){var n;try{n=t.getBBox()}catch{n=sl.call(t,!0)}return n&&(n.width||n.height)||t.getBBox===sl||(n=sl.call(t,!0)),n&&!n.width&&!n.x&&!n.y?{x:+Td(t,["x","cx","x1"])||0,y:+Td(t,["y","cy","y1"])||0,width:0,height:0}:n},m6=function(t){return!!(t.getCTM&&(!t.parentNode||t.ownerSVGElement)&&p6(t))},Or=function(t,n){if(n){var r=t.style,i;n in _n&&n!==re&&(n=at),r.removeProperty?(i=n.substr(0,2),(i==="ms"||n.substr(0,6)==="webkit")&&(n="-"+n),r.removeProperty(i==="--"?n:n.replace(u0,"-$1").toLowerCase())):r.removeAttribute(n)}},Mn=function(t,n,r,i,o,s){var a=new ne(t._pt,n,r,0,1,s?u6:c6);return t._pt=a,a.b=i,a.e=o,t._props.push(r),a},bd={deg:1,rad:1,turn:1},y4={grid:1,flex:1},Xn=function e(t,n,r,i){var o=parseFloat(r)||0,s=(r+"").trim().substr((o+"").length)||"px",a=vr.style,l=o4.test(n),c=t.tagName.toLowerCase()==="svg",u=(c?"client":"offset")+(l?"Width":"Height"),f=100,d=i==="px",p=i==="%",v,h,_,m;if(i===s||!o||bd[i]||bd[s])return o;if(s!=="px"&&!d&&(o=e(t,n,r,"px")),m=t.getCTM&&m6(t),(p||s==="%")&&(_n[n]||~n.indexOf("adius")))return v=m?t.getBBox()[l?"width":"height"]:t[u],pt(p?o/v*f:o/100*v);if(a[l?"width":"height"]=f+(d?s:i),h=~n.indexOf("adius")||i==="em"&&t.appendChild&&!c?t:t.parentNode,m&&(h=(t.ownerSVGElement||{}).parentNode),(!h||h===On||!h.appendChild)&&(h=On.body),_=h._gsap,_&&p&&_.width&&l&&_.time===ae.time&&!_.uncache)return pt(o/_.width*f);if(p&&(n==="height"||n==="width")){var g=t.style[n];t.style[n]=f+i,v=t[u],g?t.style[n]=g:Or(t,n)}else(p||s==="%")&&!y4[Xe(h,"display")]&&(a.position=Xe(t,"position")),h===t&&(a.position="static"),h.appendChild(vr),v=vr[u],h.removeChild(vr),a.position="absolute";return l&&p&&(_=Sr(h),_.time=ae.time,_.width=h[u]),pt(d?v*o/f:v&&o?f/v*o:0)},ln=function(t,n,r,i){var o;return l0||Dc(),n in Ge&&n!=="transform"&&(n=Ge[n],~n.indexOf(",")&&(n=n.split(",")[0])),_n[n]&&n!=="transform"?(o=Hi(t,i),o=n!=="transformOrigin"?o[n]:o.svg?o.origin:Ls(Xe(t,re))+" "+o.zOrigin+"px"):(o=t.style[n],(!o||o==="auto"||i||~(o+"").indexOf("calc("))&&(o=As[n]&&As[n](t,n,r)||Xe(t,n)||L5(t,n)||(n==="opacity"?1:0))),r&&!~(o+"").trim().indexOf(" ")?Xn(t,n,o,r)+r:o},_4=function(t,n,r,i){if(!r||r==="none"){var o=D1(n,t,1),s=o&&Xe(t,o,1);s&&s!==r?(n=o,r=s):n==="borderColor"&&(r=Xe(t,"borderTopColor"))}var a=new ne(this._pt,t.style,n,0,1,s6),l=0,c=0,u,f,d,p,v,h,_,m,g,y,w,x;if(a.b=r,a.e=i,r+="",i+="",i==="auto"&&(h=t.style[n],t.style[n]=i,i=Xe(t,n)||i,h?t.style[n]=h:Or(t,n)),u=[r,i],Q5(u),r=u[0],i=u[1],d=r.match(e1)||[],x=i.match(e1)||[],x.length){for(;f=e1.exec(i);)_=f[0],g=i.substring(l,f.index),v?v=(v+1)%5:(g.substr(-5)==="rgba("||g.substr(-5)==="hsla(")&&(v=1),_!==(h=d[c++]||"")&&(p=parseFloat(h)||0,w=h.substr((p+"").length),_.charAt(1)==="="&&(_=p1(p,_)+w),m=parseFloat(_),y=_.substr((m+"").length),l=e1.lastIndex-y.length,y||(y=y||pe.units[n]||w,l===i.length&&(i+=y,a.e+=y)),w!==y&&(p=Xn(t,n,h,y)||0),a._pt={_next:a._pt,p:g||c===1?g:",",s:p,c:m-p,m:v&&v<4||n==="zIndex"?Math.round:0});a.c=l<i.length?i.substring(l,i.length):""}else a.r=n==="display"&&i==="none"?u6:c6;return C5.test(i)&&(a.e=0),this._pt=a,a},Cd={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},w4=function(t){var n=t.split(" "),r=n[0],i=n[1]||"50%";return(r==="top"||r==="bottom"||i==="left"||i==="right")&&(t=r,r=i,i=t),n[0]=Cd[r]||r,n[1]=Cd[i]||i,n.join(" ")},x4=function(t,n){if(n.tween&&n.tween._time===n.tween._dur){var r=n.t,i=r.style,o=n.u,s=r._gsap,a,l,c;if(o==="all"||o===!0)i.cssText="",l=1;else for(o=o.split(","),c=o.length;--c>-1;)a=o[c],_n[a]&&(l=1,a=a==="transformOrigin"?re:at),Or(r,a);l&&(Or(r,at),s&&(s.svg&&r.removeAttribute("transform"),Hi(r,1),s.uncache=1,f6(i)))}},As={clearProps:function(t,n,r,i,o){if(o.data!=="isFromStart"){var s=t._pt=new ne(t._pt,n,r,0,0,x4);return s.u=i,s.pr=-10,s.tween=o,t._props.push(r),1}}},$i=[1,0,0,1,0,0],g6={},v6=function(t){return t==="matrix(1, 0, 0, 1, 0, 0)"||t==="none"||!t},Ed=function(t){var n=Xe(t,at);return v6(n)?$i:n.substr(7).match(b5).map(pt)},f0=function(t,n){var r=t._gsap||Sr(t),i=t.style,o=Ed(t),s,a,l,c;return r.svg&&t.getAttribute("transform")?(l=t.transform.baseVal.consolidate().matrix,o=[l.a,l.b,l.c,l.d,l.e,l.f],o.join(",")==="1,0,0,1,0,0"?$i:o):(o===$i&&!t.offsetParent&&t!==m1&&!r.svg&&(l=i.display,i.display="block",s=t.parentNode,(!s||!t.offsetParent)&&(c=1,a=t.nextElementSibling,m1.appendChild(t)),o=Ed(t),l?i.display=l:Or(t,"display"),c&&(a?s.insertBefore(t,a):s?s.appendChild(t):m1.removeChild(t))),n&&o.length>6?[o[0],o[1],o[4],o[5],o[12],o[13]]:o)},Ac=function(t,n,r,i,o,s){var a=t._gsap,l=o||f0(t,!0),c=a.xOrigin||0,u=a.yOrigin||0,f=a.xOffset||0,d=a.yOffset||0,p=l[0],v=l[1],h=l[2],_=l[3],m=l[4],g=l[5],y=n.split(" "),w=parseFloat(y[0])||0,x=parseFloat(y[1])||0,S,T,b,C;r?l!==$i&&(T=p*_-v*h)&&(b=w*(_/T)+x*(-h/T)+(h*g-_*m)/T,C=w*(-v/T)+x*(p/T)-(p*g-v*m)/T,w=b,x=C):(S=p6(t),w=S.x+(~y[0].indexOf("%")?w/100*S.width:w),x=S.y+(~(y[1]||y[0]).indexOf("%")?x/100*S.height:x)),i||i!==!1&&a.smooth?(m=w-c,g=x-u,a.xOffset=f+(m*p+g*h)-m,a.yOffset=d+(m*v+g*_)-g):a.xOffset=a.yOffset=0,a.xOrigin=w,a.yOrigin=x,a.smooth=!!i,a.origin=n,a.originIsAbsolute=!!r,t.style[re]="0px 0px",s&&(Mn(s,a,"xOrigin",c,w),Mn(s,a,"yOrigin",u,x),Mn(s,a,"xOffset",f,a.xOffset),Mn(s,a,"yOffset",d,a.yOffset)),t.setAttribute("data-svg-origin",w+" "+x)},Hi=function(t,n){var r=t._gsap||new t6(t);if("x"in r&&!n&&!r.uncache)return r;var i=t.style,o=r.scaleX<0,s="px",a="deg",l=getComputedStyle(t),c=Xe(t,re)||"0",u,f,d,p,v,h,_,m,g,y,w,x,S,T,b,C,E,L,O,B,G,I,F,z,P,M,N,j,q,nr,Et,Le;return u=f=d=h=_=m=g=y=w=0,p=v=1,r.svg=!!(t.getCTM&&m6(t)),l.translate&&((l.translate!=="none"||l.scale!=="none"||l.rotate!=="none")&&(i[at]=(l.translate!=="none"?"translate3d("+(l.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+(l.rotate!=="none"?"rotate("+l.rotate+") ":"")+(l.scale!=="none"?"scale("+l.scale.split(" ").join(",")+") ":"")+(l[at]!=="none"?l[at]:"")),i.scale=i.rotate=i.translate="none"),T=f0(t,r.svg),r.svg&&(r.uncache?(P=t.getBBox(),c=r.xOrigin-P.x+"px "+(r.yOrigin-P.y)+"px",z=""):z=!n&&t.getAttribute("data-svg-origin"),Ac(t,z||c,!!z||r.originIsAbsolute,r.smooth!==!1,T)),x=r.xOrigin||0,S=r.yOrigin||0,T!==$i&&(L=T[0],O=T[1],B=T[2],G=T[3],u=I=T[4],f=F=T[5],T.length===6?(p=Math.sqrt(L*L+O*O),v=Math.sqrt(G*G+B*B),h=L||O?zr(O,L)*ur:0,g=B||G?zr(B,G)*ur+h:0,g&&(v*=Math.abs(Math.cos(g*g1))),r.svg&&(u-=x-(x*L+S*B),f-=S-(x*O+S*G))):(Le=T[6],nr=T[7],N=T[8],j=T[9],q=T[10],Et=T[11],u=T[12],f=T[13],d=T[14],b=zr(Le,q),_=b*ur,b&&(C=Math.cos(-b),E=Math.sin(-b),z=I*C+N*E,P=F*C+j*E,M=Le*C+q*E,N=I*-E+N*C,j=F*-E+j*C,q=Le*-E+q*C,Et=nr*-E+Et*C,I=z,F=P,Le=M),b=zr(-B,q),m=b*ur,b&&(C=Math.cos(-b),E=Math.sin(-b),z=L*C-N*E,P=O*C-j*E,M=B*C-q*E,Et=G*E+Et*C,L=z,O=P,B=M),b=zr(O,L),h=b*ur,b&&(C=Math.cos(b),E=Math.sin(b),z=L*C+O*E,P=I*C+F*E,O=O*C-L*E,F=F*C-I*E,L=z,I=P),_&&Math.abs(_)+Math.abs(h)>359.9&&(_=h=0,m=180-m),p=pt(Math.sqrt(L*L+O*O+B*B)),v=pt(Math.sqrt(F*F+Le*Le)),b=zr(I,F),g=Math.abs(b)>2e-4?b*ur:0,w=Et?1/(Et<0?-Et:Et):0),r.svg&&(z=t.getAttribute("transform"),r.forceCSS=t.setAttribute("transform","")||!v6(Xe(t,at)),z&&t.setAttribute("transform",z))),Math.abs(g)>90&&Math.abs(g)<270&&(o?(p*=-1,g+=h<=0?180:-180,h+=h<=0?180:-180):(v*=-1,g+=g<=0?180:-180)),n=n||r.uncache,r.x=u-((r.xPercent=u&&(!n&&r.xPercent||(Math.round(t.offsetWidth/2)===Math.round(-u)?-50:0)))?t.offsetWidth*r.xPercent/100:0)+s,r.y=f-((r.yPercent=f&&(!n&&r.yPercent||(Math.round(t.offsetHeight/2)===Math.round(-f)?-50:0)))?t.offsetHeight*r.yPercent/100:0)+s,r.z=d+s,r.scaleX=pt(p),r.scaleY=pt(v),r.rotation=pt(h)+a,r.rotationX=pt(_)+a,r.rotationY=pt(m)+a,r.skewX=g+a,r.skewY=y+a,r.transformPerspective=w+s,(r.zOrigin=parseFloat(c.split(" ")[2])||!n&&r.zOrigin||0)&&(i[re]=Ls(c)),r.xOffset=r.yOffset=0,r.force3D=pe.force3D,r.renderTransform=r.svg?T4:h6?y6:S4,r.uncache=0,r},Ls=function(t){return(t=t.split(" "))[0]+" "+t[1]},al=function(t,n,r){var i=Nt(n);return pt(parseFloat(n)+parseFloat(Xn(t,"x",r+"px",i)))+i},S4=function(t,n){n.z="0px",n.rotationY=n.rotationX="0deg",n.force3D=0,y6(t,n)},ir="0deg",H1="0px",or=") ",y6=function(t,n){var r=n||this,i=r.xPercent,o=r.yPercent,s=r.x,a=r.y,l=r.z,c=r.rotation,u=r.rotationY,f=r.rotationX,d=r.skewX,p=r.skewY,v=r.scaleX,h=r.scaleY,_=r.transformPerspective,m=r.force3D,g=r.target,y=r.zOrigin,w="",x=m==="auto"&&t&&t!==1||m===!0;if(y&&(f!==ir||u!==ir)){var S=parseFloat(u)*g1,T=Math.sin(S),b=Math.cos(S),C;S=parseFloat(f)*g1,C=Math.cos(S),s=al(g,s,T*C*-y),a=al(g,a,-Math.sin(S)*-y),l=al(g,l,b*C*-y+y)}_!==H1&&(w+="perspective("+_+or),(i||o)&&(w+="translate("+i+"%, "+o+"%) "),(x||s!==H1||a!==H1||l!==H1)&&(w+=l!==H1||x?"translate3d("+s+", "+a+", "+l+") ":"translate("+s+", "+a+or),c!==ir&&(w+="rotate("+c+or),u!==ir&&(w+="rotateY("+u+or),f!==ir&&(w+="rotateX("+f+or),(d!==ir||p!==ir)&&(w+="skew("+d+", "+p+or),(v!==1||h!==1)&&(w+="scale("+v+", "+h+or),g.style[at]=w||"translate(0, 0)"},T4=function(t,n){var r=n||this,i=r.xPercent,o=r.yPercent,s=r.x,a=r.y,l=r.rotation,c=r.skewX,u=r.skewY,f=r.scaleX,d=r.scaleY,p=r.target,v=r.xOrigin,h=r.yOrigin,_=r.xOffset,m=r.yOffset,g=r.forceCSS,y=parseFloat(s),w=parseFloat(a),x,S,T,b,C;l=parseFloat(l),c=parseFloat(c),u=parseFloat(u),u&&(u=parseFloat(u),c+=u,l+=u),l||c?(l*=g1,c*=g1,x=Math.cos(l)*f,S=Math.sin(l)*f,T=Math.sin(l-c)*-d,b=Math.cos(l-c)*d,c&&(u*=g1,C=Math.tan(c-u),C=Math.sqrt(1+C*C),T*=C,b*=C,u&&(C=Math.tan(u),C=Math.sqrt(1+C*C),x*=C,S*=C)),x=pt(x),S=pt(S),T=pt(T),b=pt(b)):(x=f,b=d,S=T=0),(y&&!~(s+"").indexOf("px")||w&&!~(a+"").indexOf("px"))&&(y=Xn(p,"x",s,"px"),w=Xn(p,"y",a,"px")),(v||h||_||m)&&(y=pt(y+v-(v*x+h*T)+_),w=pt(w+h-(v*S+h*b)+m)),(i||o)&&(C=p.getBBox(),y=pt(y+i/100*C.width),w=pt(w+o/100*C.height)),C="matrix("+x+","+S+","+T+","+b+","+y+","+w+")",p.setAttribute("transform",C),g&&(p.style[at]=C)},b4=function(t,n,r,i,o){var s=360,a=Ct(o),l=parseFloat(o)*(a&&~o.indexOf("rad")?ur:1),c=l-i,u=i+c+"deg",f,d;return a&&(f=o.split("_")[1],f==="short"&&(c%=s,c!==c%(s/2)&&(c+=c<0?s:-s)),f==="cw"&&c<0?c=(c+s*xd)%s-~~(c/s)*s:f==="ccw"&&c>0&&(c=(c-s*xd)%s-~~(c/s)*s)),t._pt=d=new ne(t._pt,n,r,i,c,a4),d.e=u,d.u="deg",t._props.push(r),d},kd=function(t,n){for(var r in n)t[r]=n[r];return t},C4=function(t,n,r){var i=kd({},r._gsap),o="perspective,force3D,transformOrigin,svgOrigin",s=r.style,a,l,c,u,f,d,p,v;i.svg?(c=r.getAttribute("transform"),r.setAttribute("transform",""),s[at]=n,a=Hi(r,1),Or(r,at),r.setAttribute("transform",c)):(c=getComputedStyle(r)[at],s[at]=n,a=Hi(r,1),s[at]=c);for(l in _n)c=i[l],u=a[l],c!==u&&o.indexOf(l)<0&&(p=Nt(c),v=Nt(u),f=p!==v?Xn(r,l,c,v):parseFloat(c),d=parseFloat(u),t._pt=new ne(t._pt,a,l,f,d-f,kc),t._pt.u=v||0,t._props.push(l));kd(a,i)};ee("padding,margin,Width,Radius",function(e,t){var n="Top",r="Right",i="Bottom",o="Left",s=(t<3?[n,r,i,o]:[n+o,n+r,i+r,i+o]).map(function(a){return t<2?e+a:"border"+a+e});As[t>1?"border"+e:e]=function(a,l,c,u,f){var d,p;if(arguments.length<4)return d=s.map(function(v){return ln(a,v,c)}),p=d.join(" "),p.split(d[0]).length===5?d[0]:p;d=(u+"").split(" "),p={},s.forEach(function(v,h){return p[v]=d[h]=d[h]||d[(h-1)/2|0]}),a.init(l,p,f)}});var _6={name:"css",register:Dc,targetTest:function(t){return t.style&&t.nodeType},init:function(t,n,r,i,o){var s=this._props,a=t.style,l=r.vars.startAt,c,u,f,d,p,v,h,_,m,g,y,w,x,S,T,b;l0||Dc(),this.styles=this.styles||d6(t),b=this.styles.props,this.tween=r;for(h in n)if(h!=="autoRound"&&(u=n[h],!(se[h]&&e6(h,n,r,i,t,o)))){if(p=typeof u,v=As[h],p==="function"&&(u=u.call(r,i,t,o),p=typeof u),p==="string"&&~u.indexOf("random(")&&(u=zi(u)),v)v(this,t,h,u,r)&&(T=1);else if(h.substr(0,2)==="--")c=(getComputedStyle(t).getPropertyValue(h)+"").trim(),u+="",$n.lastIndex=0,$n.test(c)||(_=Nt(c),m=Nt(u)),m?_!==m&&(c=Xn(t,h,c,m)+m):_&&(u+=_),this.add(a,"setProperty",c,u,i,o,0,0,h),s.push(h),b.push(h,0,a[h]);else if(p!=="undefined"){if(l&&h in l?(c=typeof l[h]=="function"?l[h].call(r,i,t,o):l[h],Ct(c)&&~c.indexOf("random(")&&(c=zi(c)),Nt(c+"")||c==="auto"||(c+=pe.units[h]||Nt(ln(t,h))||""),(c+"").charAt(1)==="="&&(c=ln(t,h))):c=ln(t,h),d=parseFloat(c),g=p==="string"&&u.charAt(1)==="="&&u.substr(0,2),g&&(u=u.substr(2)),f=parseFloat(u),h in Ge&&(h==="autoAlpha"&&(d===1&&ln(t,"visibility")==="hidden"&&f&&(d=0),b.push("visibility",0,a.visibility),Mn(this,a,"visibility",d?"inherit":"hidden",f?"inherit":"hidden",!f)),h!=="scale"&&h!=="transform"&&(h=Ge[h],~h.indexOf(",")&&(h=h.split(",")[0]))),y=h in _n,y){if(this.styles.save(h),w||(x=t._gsap,x.renderTransform&&!n.parseTransform||Hi(t,n.parseTransform),S=n.smoothOrigin!==!1&&x.smooth,w=this._pt=new ne(this._pt,a,at,0,1,x.renderTransform,x,0,-1),w.dep=1),h==="scale")this._pt=new ne(this._pt,x,"scaleY",x.scaleY,(g?p1(x.scaleY,g+f):f)-x.scaleY||0,kc),this._pt.u=0,s.push("scaleY",h),h+="X";else if(h==="transformOrigin"){b.push(re,0,a[re]),u=w4(u),x.svg?Ac(t,u,0,S,0,this):(m=parseFloat(u.split(" ")[2])||0,m!==x.zOrigin&&Mn(this,x,"zOrigin",x.zOrigin,m),Mn(this,a,h,Ls(c),Ls(u)));continue}else if(h==="svgOrigin"){Ac(t,u,1,S,0,this);continue}else if(h in g6){b4(this,x,h,d,g?p1(d,g+u):u);continue}else if(h==="smoothOrigin"){Mn(this,x,"smooth",x.smooth,u);continue}else if(h==="force3D"){x[h]=u;continue}else if(h==="transform"){C4(this,u,t);continue}}else h in a||(h=D1(h)||h);if(y||(f||f===0)&&(d||d===0)&&!s4.test(u)&&h in a)_=(c+"").substr((d+"").length),f||(f=0),m=Nt(u)||(h in pe.units?pe.units[h]:_),_!==m&&(d=Xn(t,h,c,m)),this._pt=new ne(this._pt,y?x:a,h,d,(g?p1(d,g+f):f)-d,!y&&(m==="px"||h==="zIndex")&&n.autoRound!==!1?c4:kc),this._pt.u=m||0,_!==m&&m!=="%"&&(this._pt.b=c,this._pt.r=l4);else if(h in a)_4.call(this,t,h,c,g?g+u:u);else if(h in t)this.add(t,h,c||t[h],g?g+u:u,i,o);else if(h!=="parseTransform"){Ju(h,u);continue}y||(h in a?b.push(h,0,a[h]):b.push(h,1,c||t[h])),s.push(h)}}T&&a6(this)},render:function(t,n){if(n.tween._time||!c0())for(var r=n._pt;r;)r.r(t,r.d),r=r._next;else n.styles.revert()},get:ln,aliases:Ge,getSetter:function(t,n,r){var i=Ge[n];return i&&i.indexOf(",")<0&&(n=i),n in _n&&n!==re&&(t._gsap.x||ln(t,"x"))?r&&wd===r?n==="scale"?h4:d4:(wd=r||{})&&(n==="scale"?p4:m4):t.style&&!Qu(t.style[n])?u4:~n.indexOf("-")?f4:s0(t,n)},core:{_removeProperty:Or,_getMatrix:f0}};ie.utils.checkPrefix=D1;ie.core.getStyleSaver=d6;(function(e,t,n,r){var i=ee(e+","+t+","+n,function(o){_n[o]=1});ee(t,function(o){pe.units[o]="deg",g6[o]=1}),Ge[i[13]]=e+","+t,ee(r,function(o){var s=o.split(":");Ge[s[1]]=i[s[0]]})})("x,y,z,scale,scaleX,scaleY,xPercent,yPercent","rotation,rotationX,rotationY,skewX,skewY","transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective","0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY");ee("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",function(e){pe.units[e]="px"});ie.registerPlugin(_6);var dr=ie.registerPlugin(_6)||ie;dr.core.Tween;function Pd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function Dd(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Pd(Object(n),!0).forEach(function(r){D4(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Pd(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function E4(e,t){if(typeof e!="object"||!e)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function k4(e){var t=E4(e,"string");return typeof t=="symbol"?t:String(t)}function Ad(e,t,n,r,i,o,s){try{var a=e[o](s),l=a.value}catch(c){n(c);return}a.done?t(l):Promise.resolve(l).then(r,i)}function P4(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var o=e.apply(t,n);function s(l){Ad(o,r,i,s,a,"next",l)}function a(l){Ad(o,r,i,s,a,"throw",l)}s(void 0)})}}function D4(e,t,n){return t=k4(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function A4(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,o;for(o=0;o<r.length;o++)i=r[o],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function L4(e,t){if(e==null)return{};var n=A4(e,t),r,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)r=o[i],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}var O4=(e,t)=>{var n=K.useRef(null),r=K.useRef(e);return K.useEffect(()=>{r.current=e},[e]),K.useEffect(()=>{var i=()=>r.current();if(typeof t=="number")return n.current=window.setTimeout(i,t),()=>window.clearTimeout(n.current)},[t]),n},M4=["state","safeToRemove"],w6=e=>{var t=K.useContext(Yu),{state:n,safeToRemove:r}=t,i=L4(t,M4),o=x6();return O4(r,n===Gt.stopped&&Number.isFinite(void 0)?e.removeDelay*1e3:null),Dd(Dd({},i),{},{data:o,state:n,safeToRemove:r,isPlaying:n===Gt.playing,isStopped:n===Gt.stopped})},x6=e=>{var{data:t}=K.useContext(Yu),{trim:n=!0}={};return D.useMemo(()=>{if(!n)return t;var r={};for(var[i,o]of Object.entries(t))r[i]=typeof o=="string"?o.trim():o;return r},[t,n])},N4=D.forwardRef(function(t,n){var{children:r,hide:i,wait:o=i,onPlay:s,onStop:a}=t,[l]=D.useState(dr.timeline({paused:!0})),{state:c,isStopped:u,safeToRemove:f}=w6(),[d,p]=D.useState(!1),v=c>=Gt.playing&&!o;return D.useImperativeHandle(n,()=>l),D.useLayoutEffect(()=>{v&&(s&&(s(l),l.play()),p(!0))},[v]),D.useEffect(()=>{if(u)if(!a)f();else{a(l);var h=l.reversed()?"onReverseComplete":"onComplete";l.eventCallback(h,f)}},[u,f]),v||d?r:null});const d0=D.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),ua=D.createContext({});function R4(){return D.useContext(ua).visualElement}const fa=D.createContext(null),Ir=typeof document<"u",ll=Ir?D.useLayoutEffect:D.useEffect,S6=D.createContext({strict:!1});function I4(e,t,n,r){const i=R4(),o=D.useContext(S6),s=D.useContext(fa),a=D.useContext(d0).reducedMotion,l=D.useRef();r=r||o.renderer,!l.current&&r&&(l.current=r(e,{visualState:t,parent:i,props:n,presenceId:s?s.id:void 0,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:a}));const c=l.current;return ll(()=>{c&&c.render()}),ll(()=>{c&&c.animationState&&c.animationState.animateChanges()}),ll(()=>()=>c&&c.notify("Unmount"),[]),c}function r1(e){return typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function V4(e,t,n){return D.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):r1(n)&&(n.current=r))},[t])}function Gi(e){return typeof e=="string"||Array.isArray(e)}function da(e){return typeof e=="object"&&typeof e.start=="function"}const q4=["initial","animate","exit","whileHover","whileDrag","whileTap","whileFocus","whileInView"];function ha(e){return da(e.animate)||q4.some(t=>Gi(e[t]))}function T6(e){return!!(ha(e)||e.variants)}function F4(e,t){if(ha(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Gi(n)?n:void 0,animate:Gi(r)?r:void 0}}return e.inherit!==!1?t:{}}function B4(e){const{initial:t,animate:n}=F4(e,D.useContext(ua));return D.useMemo(()=>({initial:t,animate:n}),[Ld(t),Ld(n)])}function Ld(e){return Array.isArray(e)?e.join(" "):e}const en=e=>({isEnabled:t=>e.some(n=>!!t[n])}),Wi={measureLayout:en(["layout","layoutId","drag"]),animation:en(["animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView"]),exit:en(["exit"]),drag:en(["drag","dragControls"]),focus:en(["whileFocus"]),hover:en(["whileHover","onHoverStart","onHoverEnd"]),tap:en(["whileTap","onTap","onTapStart","onTapCancel"]),pan:en(["onPan","onPanStart","onPanSessionStart","onPanEnd"]),inView:en(["whileInView","onViewportEnter","onViewportLeave"])};function z4(e){for(const t in e)t==="projectionNodeConstructor"?Wi.projectionNodeConstructor=e[t]:Wi[t].Component=e[t]}function h0(e){const t=D.useRef(null);return t.current===null&&(t.current=e()),t.current}const mi={hasAnimatedSinceResize:!0,hasEverUpdated:!1};let j4=1;function U4(){return h0(()=>{if(mi.hasEverUpdated)return j4++})}const b6=D.createContext({});class $4 extends K.Component{getSnapshotBeforeUpdate(){const{visualElement:t,props:n}=this.props;return t&&t.setProps(n),null}componentDidUpdate(){}render(){return this.props.children}}const C6=D.createContext({}),H4=Symbol.for("motionComponentSymbol");function G4({preloadedFeatures:e,createVisualElement:t,projectionNodeConstructor:n,useRender:r,useVisualState:i,Component:o}){e&&z4(e);function s(l,c){const u={...D.useContext(d0),...l,layoutId:W4(l)},{isStatic:f}=u;let d=null;const p=B4(l),v=f?void 0:U4(),h=i(l,f);if(!f&&Ir){p.visualElement=I4(o,h,u,t);const _=D.useContext(S6).strict,m=D.useContext(C6);p.visualElement&&(d=p.visualElement.loadFeatures(u,_,e,v,n||Wi.projectionNodeConstructor,m))}return D.createElement($4,{visualElement:p.visualElement,props:u},d,D.createElement(ua.Provider,{value:p},r(o,l,v,V4(h,p.visualElement,c),h,f,p.visualElement)))}const a=D.forwardRef(s);return a[H4]=o,a}function W4({layoutId:e}){const t=D.useContext(b6).id;return t&&e!==void 0?t+"-"+e:e}function Y4(e){function t(r,i={}){return G4(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const X4=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function p0(e){return typeof e!="string"||e.includes("-")?!1:!!(X4.indexOf(e)>-1||/[A-Z]/.test(e))}const Os={};function Q4(e){Object.assign(Os,e)}const Ms=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Vr=new Set(Ms);function E6(e,{layout:t,layoutId:n}){return Vr.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Os[e]||e==="opacity")}const Je=e=>!!(e!=null&&e.getVelocity),K4={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Z4=(e,t)=>Ms.indexOf(e)-Ms.indexOf(t);function J4({transform:e,transformKeys:t},{enableHardwareAcceleration:n=!0,allowTransformNone:r=!0},i,o){let s="";t.sort(Z4);for(const a of t)s+=`${K4[a]||a}(${e[a]}) `;return n&&!e.z&&(s+="translateZ(0)"),s=s.trim(),o?s=o(e,i?"":s):r&&i&&(s="none"),s}function k6(e){return e.startsWith("--")}const tg=(e,t)=>t&&typeof e=="number"?t.transform(e):e,A1=(e,t,n)=>Math.min(Math.max(n,e),t),qr={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},gi={...qr,transform:e=>A1(0,1,e)},Lo={...qr,default:1},vi=e=>Math.round(e*1e5)/1e5,Yi=/(-)?([\d]*\.?[\d])+/g,Lc=/(#[0-9a-f]{6}|#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,eg=/^(#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function io(e){return typeof e=="string"}const oo=e=>({test:t=>io(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Tn=oo("deg"),Qe=oo("%"),R=oo("px"),ng=oo("vh"),rg=oo("vw"),Od={...Qe,parse:e=>Qe.parse(e)/100,transform:e=>Qe.transform(e*100)},Md={...qr,transform:Math.round},P6={borderWidth:R,borderTopWidth:R,borderRightWidth:R,borderBottomWidth:R,borderLeftWidth:R,borderRadius:R,radius:R,borderTopLeftRadius:R,borderTopRightRadius:R,borderBottomRightRadius:R,borderBottomLeftRadius:R,width:R,maxWidth:R,height:R,maxHeight:R,size:R,top:R,right:R,bottom:R,left:R,padding:R,paddingTop:R,paddingRight:R,paddingBottom:R,paddingLeft:R,margin:R,marginTop:R,marginRight:R,marginBottom:R,marginLeft:R,rotate:Tn,rotateX:Tn,rotateY:Tn,rotateZ:Tn,scale:Lo,scaleX:Lo,scaleY:Lo,scaleZ:Lo,skew:Tn,skewX:Tn,skewY:Tn,distance:R,translateX:R,translateY:R,translateZ:R,x:R,y:R,z:R,perspective:R,transformPerspective:R,opacity:gi,originX:Od,originY:Od,originZ:R,zIndex:Md,fillOpacity:gi,strokeOpacity:gi,numOctaves:Md};function m0(e,t,n,r){const{style:i,vars:o,transform:s,transformKeys:a,transformOrigin:l}=e;a.length=0;let c=!1,u=!1,f=!0;for(const d in t){const p=t[d];if(k6(d)){o[d]=p;continue}const v=P6[d],h=tg(p,v);if(Vr.has(d)){if(c=!0,s[d]=h,a.push(d),!f)continue;p!==(v.default||0)&&(f=!1)}else d.startsWith("origin")?(u=!0,l[d]=h):i[d]=h}if(t.transform||(c||r?i.transform=J4(e,n,f,r):i.transform&&(i.transform="none")),u){const{originX:d="50%",originY:p="50%",originZ:v=0}=l;i.transformOrigin=`${d} ${p} ${v}`}}const g0=()=>({style:{},transform:{},transformKeys:[],transformOrigin:{},vars:{}});function D6(e,t,n){for(const r in t)!Je(t[r])&&!E6(r,n)&&(e[r]=t[r])}function ig({transformTemplate:e},t,n){return D.useMemo(()=>{const r=g0();return m0(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function og(e,t,n){const r=e.style||{},i={};return D6(i,r,e),Object.assign(i,ig(e,t,n)),e.transformValues?e.transformValues(i):i}function sg(e,t,n){const r={},i=og(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),r.style=i,r}const ag=["animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView"],lg=["whileTap","onTap","onTapStart","onTapCancel"],cg=["onPan","onPanStart","onPanSessionStart","onPanEnd"],ug=["whileInView","onViewportEnter","onViewportLeave","viewport"],fg=new Set(["initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","layout","layoutId","layoutDependency","onLayoutAnimationStart","onLayoutAnimationComplete","onLayoutMeasure","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","drag","dragControls","dragListener","dragConstraints","dragDirectionLock","dragSnapToOrigin","_dragX","_dragY","dragElastic","dragMomentum","dragPropagation","dragTransition","onHoverStart","onHoverEnd","layoutScroll",...ug,...lg,...ag,...cg]);function Ns(e){return fg.has(e)}let A6=e=>!Ns(e);function dg(e){e&&(A6=t=>t.startsWith("on")?!Ns(t):e(t))}try{dg(require("@emotion/is-prop-valid").default)}catch{}function hg(e,t,n){const r={};for(const i in e)(A6(i)||n===!0&&Ns(i)||!t&&!Ns(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function Nd(e,t,n){return typeof e=="string"?e:R.transform(t+n*e)}function pg(e,t,n){const r=Nd(t,e.x,e.width),i=Nd(n,e.y,e.height);return`${r} ${i}`}const mg={offset:"stroke-dashoffset",array:"stroke-dasharray"},gg={offset:"strokeDashoffset",array:"strokeDasharray"};function vg(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?mg:gg;e[o.offset]=R.transform(-r);const s=R.transform(t),a=R.transform(n);e[o.array]=`${s} ${a}`}function v0(e,{attrX:t,attrY:n,originX:r,originY:i,pathLength:o,pathSpacing:s=1,pathOffset:a=0,...l},c,u,f){if(m0(e,l,c,f),u){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:d,style:p,dimensions:v}=e;d.transform&&(v&&(p.transform=d.transform),delete d.transform),v&&(r!==void 0||i!==void 0||p.transform)&&(p.transformOrigin=pg(v,r!==void 0?r:.5,i!==void 0?i:.5)),t!==void 0&&(d.x=t),n!==void 0&&(d.y=n),o!==void 0&&vg(d,o,s,a,!1)}const L6=()=>({...g0(),attrs:{}}),y0=e=>typeof e=="string"&&e.toLowerCase()==="svg";function yg(e,t,n,r){const i=D.useMemo(()=>{const o=L6();return v0(o,t,{enableHardwareAcceleration:!1},y0(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};D6(o,e.style,e),i.style={...o,...i.style}}return i}function _g(e=!1){return(n,r,i,o,{latestValues:s},a)=>{const c=(p0(n)?yg:sg)(r,s,a,n),f={...hg(r,typeof n=="string",e),...c,ref:o};return i&&(f["data-projection-id"]=i),D.createElement(n,f)}}const _0=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();function O6(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const M6=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function N6(e,t,n,r){O6(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(M6.has(i)?i:_0(i),t.attrs[i])}function w0(e){const{style:t}=e,n={};for(const r in t)(Je(t[r])||E6(r,e))&&(n[r]=t[r]);return n}function R6(e){const t=w0(e);for(const n in e)if(Je(e[n])){const r=n==="x"||n==="y"?"attr"+n.toUpperCase():n;t[r]=e[n]}return t}function x0(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}const Rs=e=>Array.isArray(e),wg=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),xg=e=>Rs(e)?e[e.length-1]||0:e;function Zo(e){const t=Je(e)?e.get():e;return wg(t)?t.toValue():t}function Sg({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,o){const s={latestValues:Tg(r,i,o,e),renderState:t()};return n&&(s.mount=a=>n(r,a,s)),s}const I6=e=>(t,n)=>{const r=D.useContext(ua),i=D.useContext(fa),o=()=>Sg(e,t,r,i);return n?o():h0(o)};function Tg(e,t,n,r){const i={},o=r(e);for(const d in o)i[d]=Zo(o[d]);let{initial:s,animate:a}=e;const l=ha(e),c=T6(e);t&&c&&!l&&e.inherit!==!1&&(s===void 0&&(s=t.initial),a===void 0&&(a=t.animate));let u=n?n.initial===!1:!1;u=u||s===!1;const f=u?a:s;return f&&typeof f!="boolean"&&!da(f)&&(Array.isArray(f)?f:[f]).forEach(p=>{const v=x0(e,p);if(!v)return;const{transitionEnd:h,transition:_,...m}=v;for(const g in m){let y=m[g];if(Array.isArray(y)){const w=u?y.length-1:0;y=y[w]}y!==null&&(i[g]=y)}for(const g in h)i[g]=h[g]}),i}const bg={useVisualState:I6({scrapeMotionValuesFromProps:R6,createRenderState:L6,onMount:(e,t,{renderState:n,latestValues:r})=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}v0(n,r,{enableHardwareAcceleration:!1},y0(t.tagName),e.transformTemplate),N6(t,n)}})},Cg={useVisualState:I6({scrapeMotionValuesFromProps:w0,createRenderState:g0})};function Eg(e,{forwardMotionProps:t=!1},n,r,i){return{...p0(e)?bg:Cg,preloadedFeatures:n,useRender:_g(t),createVisualElement:r,projectionNodeConstructor:i,Component:e}}var J;(function(e){e.Animate="animate",e.Hover="whileHover",e.Tap="whileTap",e.Drag="whileDrag",e.Focus="whileFocus",e.InView="whileInView",e.Exit="exit"})(J||(J={}));function pa(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function Oc(e,t,n,r){D.useEffect(()=>{const i=e.current;if(n&&i)return pa(i,t,n,r)},[e,t,n,r])}function kg({whileFocus:e,visualElement:t}){const{animationState:n}=t,r=()=>{n&&n.setActive(J.Focus,!0)},i=()=>{n&&n.setActive(J.Focus,!1)};Oc(t,"focus",e?r:void 0),Oc(t,"blur",e?i:void 0)}function V6(e){return typeof PointerEvent<"u"&&e instanceof PointerEvent?e.pointerType==="mouse":e instanceof MouseEvent}function q6(e){return!!e.touches}function Pg(e){return t=>{const n=t instanceof MouseEvent;(!n||n&&t.button===0)&&e(t)}}const Dg={pageX:0,pageY:0};function Ag(e,t="page"){const r=e.touches[0]||e.changedTouches[0]||Dg;return{x:r[t+"X"],y:r[t+"Y"]}}function Lg(e,t="page"){return{x:e[t+"X"],y:e[t+"Y"]}}function S0(e,t="page"){return{point:q6(e)?Ag(e,t):Lg(e,t)}}const F6=(e,t=!1)=>{const n=r=>e(r,S0(r));return t?Pg(n):n},Og=()=>Ir&&window.onpointerdown===null,Mg=()=>Ir&&window.ontouchstart===null,Ng=()=>Ir&&window.onmousedown===null,Rg={pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointercancel:"mousecancel",pointerover:"mouseover",pointerout:"mouseout",pointerenter:"mouseenter",pointerleave:"mouseleave"},Ig={pointerdown:"touchstart",pointermove:"touchmove",pointerup:"touchend",pointercancel:"touchcancel"};function B6(e){return Og()?e:Mg()?Ig[e]:Ng()?Rg[e]:e}function v1(e,t,n,r){return pa(e,B6(t),F6(n,t==="pointerdown"),r)}function Is(e,t,n,r){return Oc(e,B6(t),n&&F6(n,t==="pointerdown"),r)}function z6(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const Rd=z6("dragHorizontal"),Id=z6("dragVertical");function j6(e){let t=!1;if(e==="y")t=Id();else if(e==="x")t=Rd();else{const n=Rd(),r=Id();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function U6(){const e=j6(!0);return e?(e(),!1):!0}function Vd(e,t,n){return(r,i)=>{!V6(r)||U6()||(e.animationState&&e.animationState.setActive(J.Hover,t),n&&n(r,i))}}function Vg({onHoverStart:e,onHoverEnd:t,whileHover:n,visualElement:r}){Is(r,"pointerenter",e||n?Vd(r,!0,e):void 0,{passive:!e}),Is(r,"pointerleave",t||n?Vd(r,!1,t):void 0,{passive:!t})}const $6=(e,t)=>t?e===t?!0:$6(e,t.parentElement):!1;function H6(e){return D.useEffect(()=>()=>e(),[])}const qg=(e,t)=>n=>t(e(n)),ma=(...e)=>e.reduce(qg);function Fg({onTap:e,onTapStart:t,onTapCancel:n,whileTap:r,visualElement:i}){const o=e||t||n||r,s=D.useRef(!1),a=D.useRef(null),l={passive:!(t||e||n||p)};function c(){a.current&&a.current(),a.current=null}function u(){return c(),s.current=!1,i.animationState&&i.animationState.setActive(J.Tap,!1),!U6()}function f(v,h){u()&&($6(i.current,v.target)?e&&e(v,h):n&&n(v,h))}function d(v,h){u()&&n&&n(v,h)}function p(v,h){c(),!s.current&&(s.current=!0,a.current=ma(v1(window,"pointerup",f,l),v1(window,"pointercancel",d,l)),i.animationState&&i.animationState.setActive(J.Tap,!0),t&&t(v,h))}Is(i,"pointerdown",o?p:void 0,l),H6(c)}var Bg={};const zg="production",jg=typeof process>"u"||Bg===void 0?zg:"production",qd=new Set;function Ug(e,t,n){qd.has(t)||(console.warn(t),qd.add(t))}const Mc=new WeakMap,cl=new WeakMap,$g=e=>{const t=Mc.get(e.target);t&&t(e)},Hg=e=>{e.forEach($g)};function Gg({root:e,...t}){const n=e||document;cl.has(n)||cl.set(n,{});const r=cl.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(Hg,{root:e,...t})),r[i]}function Wg(e,t,n){const r=Gg(t);return Mc.set(e,n),r.observe(e),()=>{Mc.delete(e),r.unobserve(e)}}function Yg({visualElement:e,whileInView:t,onViewportEnter:n,onViewportLeave:r,viewport:i={}}){const o=D.useRef({hasEnteredView:!1,isInView:!1});let s=!!(t||n||r);i.once&&o.current.hasEnteredView&&(s=!1),(typeof IntersectionObserver>"u"?Kg:Qg)(s,o.current,e,i)}const Xg={some:0,all:1};function Qg(e,t,n,{root:r,margin:i,amount:o="some",once:s}){D.useEffect(()=>{if(!e||!n.current)return;const a={root:r==null?void 0:r.current,rootMargin:i,threshold:typeof o=="number"?o:Xg[o]},l=c=>{const{isIntersecting:u}=c;if(t.isInView===u||(t.isInView=u,s&&!u&&t.hasEnteredView))return;u&&(t.hasEnteredView=!0),n.animationState&&n.animationState.setActive(J.InView,u);const f=n.getProps(),d=u?f.onViewportEnter:f.onViewportLeave;d&&d(c)};return Wg(n.current,a,l)},[e,r,i,o])}function Kg(e,t,n,{fallback:r=!0}){D.useEffect(()=>{!e||!r||(jg!=="production"&&Ug(!1,"IntersectionObserver not available on this device. whileInView animations will trigger on mount."),requestAnimationFrame(()=>{t.hasEnteredView=!0;const{onViewportEnter:i}=n.getProps();i&&i(null),n.animationState&&n.animationState.setActive(J.InView,!0)}))},[e])}const Nn=e=>t=>(e(t),null),Zg={inView:Nn(Yg),tap:Nn(Fg),focus:Nn(kg),hover:Nn(Vg)};function G6(){const e=D.useContext(fa);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=D.useId();return D.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}function W6(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}const Jg=e=>/^\-?\d*\.?\d+$/.test(e),tv=e=>/^0[^.\s]+$/.test(e),dn={delta:0,timestamp:0},Y6=1/60*1e3,ev=typeof performance<"u"?()=>performance.now():()=>Date.now(),X6=typeof window<"u"?e=>window.requestAnimationFrame(e):e=>setTimeout(()=>e(ev()),Y6);function nv(e){let t=[],n=[],r=0,i=!1,o=!1;const s=new WeakSet,a={schedule:(l,c=!1,u=!1)=>{const f=u&&i,d=f?t:n;return c&&s.add(l),d.indexOf(l)===-1&&(d.push(l),f&&i&&(r=t.length)),l},cancel:l=>{const c=n.indexOf(l);c!==-1&&n.splice(c,1),s.delete(l)},process:l=>{if(i){o=!0;return}if(i=!0,[t,n]=[n,t],n.length=0,r=t.length,r)for(let c=0;c<r;c++){const u=t[c];u(l),s.has(u)&&(a.schedule(u),e())}i=!1,o&&(o=!1,a.process(l))}};return a}const rv=40;let Nc=!0,Xi=!1,Rc=!1;const so=["read","update","preRender","render","postRender"],ga=so.reduce((e,t)=>(e[t]=nv(()=>Xi=!0),e),{}),me=so.reduce((e,t)=>{const n=ga[t];return e[t]=(r,i=!1,o=!1)=>(Xi||ov(),n.schedule(r,i,o)),e},{}),Qn=so.reduce((e,t)=>(e[t]=ga[t].cancel,e),{}),ul=so.reduce((e,t)=>(e[t]=()=>ga[t].process(dn),e),{}),iv=e=>ga[e].process(dn),Q6=e=>{Xi=!1,dn.delta=Nc?Y6:Math.max(Math.min(e-dn.timestamp,rv),1),dn.timestamp=e,Rc=!0,so.forEach(iv),Rc=!1,Xi&&(Nc=!1,X6(Q6))},ov=()=>{Xi=!0,Nc=!0,Rc||X6(Q6)};function T0(e,t){e.indexOf(t)===-1&&e.push(t)}function b0(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class C0{constructor(){this.subscriptions=[]}add(t){return T0(this.subscriptions,t),()=>b0(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function E0(e,t){return t?e*(1e3/t):0}const sv=e=>!isNaN(parseFloat(e));class av{constructor(t,n={}){this.version="7.10.3",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:o,timestamp:s}=dn;this.lastUpdated!==s&&(this.timeDelta=o,this.lastUpdated=s,me.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>me.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=sv(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){return this.events[t]||(this.events[t]=new C0),this.events[t].add(n)}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t){this.passiveEffect=t}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?E0(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.stopAnimation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.stopAnimation&&(this.stopAnimation(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.stopAnimation}clearAnimation(){this.stopAnimation=null}destroy(){this.clearListeners(),this.stop()}}function L1(e,t){return new av(e,t)}const k0=(e,t)=>n=>!!(io(n)&&eg.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),K6=(e,t,n)=>r=>{if(!io(r))return r;const[i,o,s,a]=r.match(Yi);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:a!==void 0?parseFloat(a):1}},lv=e=>A1(0,255,e),fl={...qr,transform:e=>Math.round(lv(e))},yr={test:k0("rgb","red"),parse:K6("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+fl.transform(e)+", "+fl.transform(t)+", "+fl.transform(n)+", "+vi(gi.transform(r))+")"};function cv(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Ic={test:k0("#"),parse:cv,transform:yr.transform},i1={test:k0("hsl","hue"),parse:K6("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+Qe.transform(vi(t))+", "+Qe.transform(vi(n))+", "+vi(gi.transform(r))+")"},qt={test:e=>yr.test(e)||Ic.test(e)||i1.test(e),parse:e=>yr.test(e)?yr.parse(e):i1.test(e)?i1.parse(e):Ic.parse(e),transform:e=>io(e)?e:e.hasOwnProperty("red")?yr.transform(e):i1.transform(e)},Z6="${c}",J6="${n}";function uv(e){var t,n;return isNaN(e)&&io(e)&&(((t=e.match(Yi))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Lc))===null||n===void 0?void 0:n.length)||0)>0}function Vs(e){typeof e=="number"&&(e=`${e}`);const t=[];let n=0,r=0;const i=e.match(Lc);i&&(n=i.length,e=e.replace(Lc,Z6),t.push(...i.map(qt.parse)));const o=e.match(Yi);return o&&(r=o.length,e=e.replace(Yi,J6),t.push(...o.map(qr.parse))),{values:t,numColors:n,numNumbers:r,tokenised:e}}function th(e){return Vs(e).values}function eh(e){const{values:t,numColors:n,tokenised:r}=Vs(e),i=t.length;return o=>{let s=r;for(let a=0;a<i;a++)s=s.replace(a<n?Z6:J6,a<n?qt.transform(o[a]):vi(o[a]));return s}}const fv=e=>typeof e=="number"?0:e;function dv(e){const t=th(e);return eh(e)(t.map(fv))}const Kn={test:uv,parse:th,createTransformer:eh,getAnimatableNone:dv},hv=new Set(["brightness","contrast","saturate","opacity"]);function pv(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Yi)||[];if(!r)return e;const i=n.replace(r,"");let o=hv.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const mv=/([a-z-]*)\(.*?\)/g,Vc={...Kn,getAnimatableNone:e=>{const t=e.match(mv);return t?t.map(pv).join(" "):e}},gv={...P6,color:qt,backgroundColor:qt,outlineColor:qt,fill:qt,stroke:qt,borderColor:qt,borderTopColor:qt,borderRightColor:qt,borderBottomColor:qt,borderLeftColor:qt,filter:Vc,WebkitFilter:Vc},P0=e=>gv[e];function D0(e,t){var n;let r=P0(e);return r!==Vc&&(r=Kn),(n=r.getAnimatableNone)===null||n===void 0?void 0:n.call(r,t)}const nh=e=>t=>t.test(e),vv={test:e=>e==="auto",parse:e=>e},rh=[qr,R,Qe,Tn,rg,ng,vv],G1=e=>rh.find(nh(e)),yv=[...rh,qt,Kn],_v=e=>yv.find(nh(e));function wv(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function xv(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function va(e,t,n){const r=e.getProps();return x0(r,t,n!==void 0?n:r.custom,wv(e),xv(e))}function Sv(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,L1(n))}function Tv(e,t){const n=va(e,t);let{transitionEnd:r={},transition:i={},...o}=n?e.makeTargetAnimatable(n,!1):{};o={...o,...r};for(const s in o){const a=xg(o[s]);Sv(e,s,a)}}function bv(e,t,n){var r,i;const o=Object.keys(t).filter(a=>!e.hasValue(a)),s=o.length;if(s)for(let a=0;a<s;a++){const l=o[a],c=t[l];let u=null;Array.isArray(c)&&(u=c[0]),u===null&&(u=(i=(r=n[l])!==null&&r!==void 0?r:e.readValue(l))!==null&&i!==void 0?i:t[l]),u!=null&&(typeof u=="string"&&(Jg(u)||tv(u))?u=parseFloat(u):!_v(u)&&Kn.test(c)&&(u=D0(l,c)),e.addValue(l,L1(u,{owner:e})),n[l]===void 0&&(n[l]=u),u!==null&&e.setBaseTarget(l,u))}}function Cv(e,t){return t?(t[e]||t.default||t).from:void 0}function Ev(e,t,n){var r;const i={};for(const o in e){const s=Cv(o,t);i[o]=s!==void 0?s:(r=n.getValue(o))===null||r===void 0?void 0:r.get()}return i}function qs(e){return!!(Je(e)&&e.add)}const kv=(e,t)=>`${e}: ${t}`;function Pv(e,t){const{MotionAppearAnimations:n}=window,r=kv(e,Vr.has(t)?"transform":t),i=n&&n.get(r);return i?(me.render(()=>{try{i.cancel(),n.delete(r)}catch{}}),i.currentTime||0):0}const Dv="framerAppearId",Av="data-"+_0(Dv);var Qi=function(){};const Jo=e=>e*1e3,Lv={current:!1},A0=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,L0=e=>t=>1-e(1-t),O0=e=>e*e,Ov=L0(O0),M0=A0(O0),dt=(e,t,n)=>-n*e+n*t+e;function dl(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Mv({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;i=dl(l,a,e+1/3),o=dl(l,a,e),s=dl(l,a,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}const hl=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},Nv=[Ic,yr,i1],Rv=e=>Nv.find(t=>t.test(e));function Fd(e){const t=Rv(e);let n=t.parse(e);return t===i1&&(n=Mv(n)),n}const ih=(e,t)=>{const n=Fd(e),r=Fd(t),i={...n};return o=>(i.red=hl(n.red,r.red,o),i.green=hl(n.green,r.green,o),i.blue=hl(n.blue,r.blue,o),i.alpha=dt(n.alpha,r.alpha,o),yr.transform(i))};function oh(e,t){return typeof e=="number"?n=>dt(e,t,n):qt.test(e)?ih(e,t):ah(e,t)}const sh=(e,t)=>{const n=[...e],r=n.length,i=e.map((o,s)=>oh(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}},Iv=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=oh(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}},ah=(e,t)=>{const n=Kn.createTransformer(t),r=Vs(e),i=Vs(t);return r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?ma(sh(r.values,i.values),n):s=>`${s>0?t:e}`},Fs=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Bd=(e,t)=>n=>dt(e,t,n);function Vv(e){return typeof e=="number"?Bd:typeof e=="string"?qt.test(e)?ih:ah:Array.isArray(e)?sh:typeof e=="object"?Iv:Bd}function qv(e,t,n){const r=[],i=n||Vv(e[0]),o=e.length-1;for(let s=0;s<o;s++){let a=i(e[s],e[s+1]);if(t){const l=Array.isArray(t)?t[s]:t;a=ma(l,a)}r.push(a)}return r}function lh(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;Qi(o===t.length),Qi(!r||!Array.isArray(r)||r.length===o-1),e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=qv(t,r,i),a=s.length,l=c=>{let u=0;if(a>1)for(;u<e.length-2&&!(c<e[u+1]);u++);const f=Fs(e[u],e[u+1],c);return s[u](f)};return n?c=>l(A1(e[0],e[o-1],c)):l}const N0=e=>e,ch=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,Fv=1e-7,Bv=12;function zv(e,t,n,r,i){let o,s,a=0;do s=t+(n-t)/2,o=ch(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>Fv&&++a<Bv);return s}function uh(e,t,n,r){if(e===t&&n===r)return N0;const i=o=>zv(o,0,1,e,n);return o=>o===0||o===1?o:ch(i(o),t,r)}const fh=e=>1-Math.sin(Math.acos(e)),R0=L0(fh),jv=A0(R0),dh=uh(.33,1.53,.69,.99),I0=L0(dh),Uv=A0(I0),$v=e=>(e*=2)<1?.5*I0(e):.5*(2-Math.pow(2,-10*(e-1))),zd={linear:N0,easeIn:O0,easeInOut:M0,easeOut:Ov,circIn:fh,circInOut:jv,circOut:R0,backIn:I0,backInOut:Uv,backOut:dh,anticipate:$v},jd=e=>{if(Array.isArray(e)){Qi(e.length===4);const[t,n,r,i]=e;return uh(t,n,r,i)}else if(typeof e=="string")return Qi(zd[e]!==void 0),zd[e];return e},Hv=e=>Array.isArray(e)&&typeof e[0]!="number";function Gv(e,t){return e.map(()=>t||M0).splice(0,e.length-1)}function Wv(e){const t=e.length;return e.map((n,r)=>r!==0?r/(t-1):0)}function Yv(e,t){return e.map(n=>n*t)}function Bs({keyframes:e,ease:t=M0,times:n,duration:r=300}){e=[...e];const i=Bs[0],o=Hv(t)?t.map(jd):jd(t),s={done:!1,value:i},a=Yv(n&&n.length===Bs.length?n:Wv(e),r);function l(){return lh(a,e,{ease:Array.isArray(o)?o:Gv(e,o)})}let c=l();return{next:u=>(s.value=c(u),s.done=u>=r,s),flipTarget:()=>{e.reverse(),c=l()}}}const pl=.001,Xv=.01,Qv=10,Kv=.05,Zv=1;function Jv({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,o,s=1-t;s=A1(Kv,Zv,s),e=A1(Xv,Qv,e/1e3),s<1?(i=c=>{const u=c*s,f=u*e,d=u-n,p=qc(c,s),v=Math.exp(-f);return pl-d/p*v},o=c=>{const f=c*s*e,d=f*n+n,p=Math.pow(s,2)*Math.pow(c,2)*e,v=Math.exp(-f),h=qc(Math.pow(c,2),s);return(-i(c)+pl>0?-1:1)*((d-p)*v)/h}):(i=c=>{const u=Math.exp(-c*e),f=(c-n)*e+1;return-pl+u*f},o=c=>{const u=Math.exp(-c*e),f=(n-c)*(e*e);return u*f});const a=5/e,l=ey(i,o,a);if(e=e*1e3,isNaN(l))return{stiffness:100,damping:10,duration:e};{const c=Math.pow(l,2)*r;return{stiffness:c,damping:s*2*Math.sqrt(r*c),duration:e}}}const ty=12;function ey(e,t,n){let r=n;for(let i=1;i<ty;i++)r=r-e(r)/t(r);return r}function qc(e,t){return e*Math.sqrt(1-t*t)}const ny=["duration","bounce"],ry=["stiffness","damping","mass"];function Ud(e,t){return t.some(n=>e[n]!==void 0)}function iy(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Ud(e,ry)&&Ud(e,ny)){const n=Jv(e);t={...t,...n,velocity:0,mass:1},t.isResolvedFromDuration=!0}return t}const oy=5;function hh({keyframes:e,restSpeed:t=2,restDelta:n=.01,...r}){let i=e[0],o=e[e.length-1];const s={done:!1,value:i},{stiffness:a,damping:l,mass:c,velocity:u,duration:f,isResolvedFromDuration:d}=iy(r);let p=sy,v=u?-(u/1e3):0;const h=l/(2*Math.sqrt(a*c));function _(){const m=o-i,g=Math.sqrt(a/c)/1e3;if(n===void 0&&(n=Math.min(Math.abs(o-i)/100,.4)),h<1){const y=qc(g,h);p=w=>{const x=Math.exp(-h*g*w);return o-x*((v+h*g*m)/y*Math.sin(y*w)+m*Math.cos(y*w))}}else if(h===1)p=y=>o-Math.exp(-g*y)*(m+(v+g*m)*y);else{const y=g*Math.sqrt(h*h-1);p=w=>{const x=Math.exp(-h*g*w),S=Math.min(y*w,300);return o-x*((v+h*g*m)*Math.sinh(S)+y*m*Math.cosh(S))/y}}}return _(),{next:m=>{const g=p(m);if(d)s.done=m>=f;else{let y=v;if(m!==0)if(h<1){const S=Math.max(0,m-oy);y=E0(g-p(S),m-S)}else y=0;const w=Math.abs(y)<=t,x=Math.abs(o-g)<=n;s.done=w&&x}return s.value=s.done?o:g,s},flipTarget:()=>{v=-v,[i,o]=[o,i],_()}}}hh.needsInterpolation=(e,t)=>typeof e=="string"||typeof t=="string";const sy=e=>0;function ay({keyframes:e=[0],velocity:t=0,power:n=.8,timeConstant:r=350,restDelta:i=.5,modifyTarget:o}){const s=e[0],a={done:!1,value:s};let l=n*t;const c=s+l,u=o===void 0?c:o(c);return u!==c&&(l=u-s),{next:f=>{const d=-l*Math.exp(-f/r);return a.done=!(d>i||d<-i),a.value=a.done?u:u+d,a},flipTarget:()=>{}}}const ly={decay:ay,keyframes:Bs,tween:Bs,spring:hh};function ph(e,t,n=0){return e-t-n}function cy(e,t=0,n=0,r=!0){return r?ph(t+-e,t,n):t-(e-t)+n}function uy(e,t,n,r){return r?e>=t+n:e<=-n}const fy=e=>{const t=({delta:n})=>e(n);return{start:()=>me.update(t,!0),stop:()=>Qn.update(t)}};function zs({duration:e,driver:t=fy,elapsed:n=0,repeat:r=0,repeatType:i="loop",repeatDelay:o=0,keyframes:s,autoplay:a=!0,onPlay:l,onStop:c,onComplete:u,onRepeat:f,onUpdate:d,type:p="keyframes",...v}){var h,_;let m,g=0,y=e,w,x=!1,S=!0,T;const b=ly[s.length>2?"keyframes":p],C=s[0],E=s[s.length-1];!((_=(h=b).needsInterpolation)===null||_===void 0)&&_.call(h,C,E)&&(T=lh([0,100],[C,E],{clamp:!1}),s=[0,100]);const L=b({...v,duration:e,keyframes:s});function O(){g++,i==="reverse"?(S=g%2===0,n=cy(n,y,o,S)):(n=ph(n,y,o),i==="mirror"&&L.flipTarget()),x=!1,f&&f()}function B(){m.stop(),u&&u()}function G(F){if(S||(F=-F),n+=F,!x){const z=L.next(Math.max(0,n));w=z.value,T&&(w=T(w)),x=S?z.done:n<=0}d&&d(w),x&&(g===0&&(y=y!==void 0?y:n),g<r?uy(n,y,o,S)&&O():B())}function I(){l&&l(),m=t(G),m.start()}return a&&I(),{stop:()=>{c&&c(),m.stop()},sample:F=>L.next(Math.max(0,F))}}function dy(e){return!e||Array.isArray(e)||typeof e=="string"&&mh[e]}const ni=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,mh={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ni([0,.65,.55,1]),circOut:ni([.55,0,1,.45]),backIn:ni([.31,.01,.66,-.59]),backOut:ni([.33,1.53,.69,.99])};function hy(e){if(e)return Array.isArray(e)?ni(e):mh[e]}function py(e,t,n,{delay:r=0,duration:i,repeat:o=0,repeatType:s="loop",ease:a,times:l}={}){return e.animate({[t]:n,offset:l},{delay:r,duration:i,easing:hy(a),fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}const Oo=10;function my(e,t,{onUpdate:n,onComplete:r,...i}){let{keyframes:o,duration:s=.3,elapsed:a=0,ease:l}=i;if(i.type==="spring"||!dy(i.ease)){const u=zs(i);let f={done:!1,value:o[0]};const d=[];let p=0;for(;!f.done;)f=u.sample(p),d.push(f.value),p+=Oo;o=d,s=p-Oo,l="linear"}const c=py(e.owner.current,t,o,{...i,delay:-a,duration:s,ease:l});return c.onfinish=()=>{e.set(o[o.length-1]),r&&r()},()=>{const{currentTime:u}=c;if(u){const f=zs(i);e.setWithVelocity(f.sample(u-Oo).value,f.sample(u).value,Oo)}me.update(()=>c.cancel())}}function gh(e,t){const n=performance.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(Qn.read(r),e(o-t))};return me.read(r,!0),()=>Qn.read(r)}function gy({keyframes:e,elapsed:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),()=>{});return t?gh(i,-t):i()}function vy({keyframes:e,velocity:t=0,min:n,max:r,power:i=.8,timeConstant:o=750,bounceStiffness:s=500,bounceDamping:a=10,restDelta:l=1,modifyTarget:c,driver:u,onUpdate:f,onComplete:d,onStop:p}){const v=e[0];let h;function _(w){return n!==void 0&&w<n||r!==void 0&&w>r}function m(w){return n===void 0?r:r===void 0||Math.abs(n-w)<Math.abs(r-w)?n:r}function g(w){h==null||h.stop(),h=zs({keyframes:[0,1],velocity:0,...w,driver:u,onUpdate:x=>{var S;f==null||f(x),(S=w.onUpdate)===null||S===void 0||S.call(w,x)},onComplete:d,onStop:p})}function y(w){g({type:"spring",stiffness:s,damping:a,restDelta:l,...w})}if(_(v))y({velocity:t,keyframes:[v,m(v)]});else{let w=i*t+v;typeof c<"u"&&(w=c(w));const x=m(w),S=x===n?-1:1;let T,b;const C=E=>{T=b,b=E,t=E0(E-T,dn.delta),(S===1&&E>x||S===-1&&E<x)&&y({keyframes:[E,x],velocity:t})};g({type:"decay",keyframes:[v,0],velocity:t,timeConstant:o,power:i,restDelta:l,modifyTarget:c,onUpdate:_(w)?C:void 0})}return{stop:()=>h==null?void 0:h.stop()}}const sr=()=>({type:"spring",stiffness:500,damping:25,restSpeed:10}),Mo=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),ml=()=>({type:"keyframes",ease:"linear",duration:.3}),yy={type:"keyframes",duration:.8},$d={x:sr,y:sr,z:sr,rotate:sr,rotateX:sr,rotateY:sr,rotateZ:sr,scaleX:Mo,scaleY:Mo,scale:Mo,opacity:ml,backgroundColor:ml,color:ml,default:Mo},_y=(e,{keyframes:t})=>t.length>2?yy:($d[e]||$d.default)(t[1]),Fc=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&Kn.test(t)&&!t.startsWith("url("));function wy({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,...c}){return!!Object.keys(c).length}function Hd(e){return e===0||typeof e=="string"&&parseFloat(e)===0&&e.indexOf(" ")===-1}function Gd(e){return typeof e=="number"?0:D0("",e)}function vh(e,t){return e[t]||e.default||e}function xy(e,t,n,r){const i=Fc(t,n);let o=r.from!==void 0?r.from:e.get();return o==="none"&&i&&typeof n=="string"?o=D0(t,n):Hd(o)&&typeof n=="string"?o=Gd(n):!Array.isArray(n)&&Hd(n)&&typeof o=="string"&&(n=Gd(o)),Array.isArray(n)?(n[0]===null&&(n[0]=o),n):[o,n]}const Wd={waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate")},gl={},yh={};for(const e in Wd)yh[e]=()=>(gl[e]===void 0&&(gl[e]=Wd[e]()),gl[e]);const Sy=new Set(["opacity"]),V0=(e,t,n,r={})=>i=>{const o=vh(r,e)||{},s=o.delay||r.delay||0;let{elapsed:a=0}=r;a=a-Jo(s);const l=xy(t,e,n,o),c=l[0],u=l[l.length-1],f=Fc(e,c),d=Fc(e,u);let p={keyframes:l,velocity:t.getVelocity(),...o,elapsed:a,onUpdate:m=>{t.set(m),o.onUpdate&&o.onUpdate(m)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(!f||!d||Lv.current||o.type===!1)return gy(p);if(o.type==="inertia"){const m=vy(p);return()=>m.stop()}wy(o)||(p={...p,..._y(e,p)}),p.duration&&(p.duration=Jo(p.duration)),p.repeatDelay&&(p.repeatDelay=Jo(p.repeatDelay));const v=t.owner,h=v&&v.current;if(yh.waapi()&&Sy.has(e)&&!p.repeatDelay&&p.repeatType!=="mirror"&&p.damping!==0&&v&&h instanceof HTMLElement&&!v.getProps().onUpdate)return my(t,e,p);{const m=zs(p);return()=>m.stop()}};function Ty(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>Bc(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=Bc(e,t,n);else{const i=typeof t=="function"?va(e,t,n.custom):t;r=_h(e,i,n)}return r.then(()=>e.notify("AnimationComplete",t))}function Bc(e,t,n={}){var r;const i=va(e,t,n.custom);let{transition:o=e.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(o=n.transitionOverride);const s=i?()=>_h(e,i,n):()=>Promise.resolve(),a=!((r=e.variantChildren)===null||r===void 0)&&r.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:f,staggerDirection:d}=o;return by(e,t,u+c,f,d,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[c,u]=l==="beforeChildren"?[s,a]:[a,s];return c().then(u)}else return Promise.all([s(),a(n.delay)])}function _h(e,t,{delay:n=0,transitionOverride:r,type:i}={}){var o;let{transition:s=e.getDefaultTransition(),transitionEnd:a,...l}=e.makeTargetAnimatable(t);const c=e.getValue("willChange");r&&(s=r);const u=[],f=i&&((o=e.animationState)===null||o===void 0?void 0:o.getState()[i]);for(const d in l){const p=e.getValue(d),v=l[d];if(!p||v===void 0||f&&Ey(f,d))continue;let h={delay:n,elapsed:0,...s};if(e.shouldReduceMotion&&Vr.has(d)&&(h={...h,type:!1,delay:0}),!p.hasAnimated){const m=e.getProps()[Av];m&&(h.elapsed=Pv(m,d))}let _=p.start(V0(d,p,v,h));qs(c)&&(c.add(d),_=_.then(()=>c.remove(d))),u.push(_)}return Promise.all(u).then(()=>{a&&Tv(e,a)})}function by(e,t,n=0,r=0,i=1,o){const s=[],a=(e.variantChildren.size-1)*r,l=i===1?(c=0)=>c*r:(c=0)=>a-c*r;return Array.from(e.variantChildren).sort(Cy).forEach((c,u)=>{s.push(Bc(c,t,{...o,delay:n+l(u)}).then(()=>c.notify("AnimationComplete",t)))}),Promise.all(s)}function Cy(e,t){return e.sortNodePosition(t)}function Ey({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}const q0=[J.Animate,J.InView,J.Focus,J.Hover,J.Tap,J.Drag,J.Exit],ky=[...q0].reverse(),Py=q0.length;function Dy(e){return t=>Promise.all(t.map(({animation:n,options:r})=>Ty(e,n,r)))}function Ay(e){let t=Dy(e);const n=Oy();let r=!0;const i=(l,c)=>{const u=va(e,c);if(u){const{transition:f,transitionEnd:d,...p}=u;l={...l,...p,...d}}return l};function o(l){t=l(e)}function s(l,c){const u=e.getProps(),f=e.getVariantContext(!0)||{},d=[],p=new Set;let v={},h=1/0;for(let m=0;m<Py;m++){const g=ky[m],y=n[g],w=u[g]!==void 0?u[g]:f[g],x=Gi(w),S=g===c?y.isActive:null;S===!1&&(h=m);let T=w===f[g]&&w!==u[g]&&x;if(T&&r&&e.manuallyAnimateOnMount&&(T=!1),y.protectedKeys={...v},!y.isActive&&S===null||!w&&!y.prevProp||da(w)||typeof w=="boolean")continue;const b=Ly(y.prevProp,w);let C=b||g===c&&y.isActive&&!T&&x||m>h&&x;const E=Array.isArray(w)?w:[w];let L=E.reduce(i,{});S===!1&&(L={});const{prevResolvedValues:O={}}=y,B={...O,...L},G=I=>{C=!0,p.delete(I),y.needsAnimating[I]=!0};for(const I in B){const F=L[I],z=O[I];v.hasOwnProperty(I)||(F!==z?Rs(F)&&Rs(z)?!W6(F,z)||b?G(I):y.protectedKeys[I]=!0:F!==void 0?G(I):p.add(I):F!==void 0&&p.has(I)?G(I):y.protectedKeys[I]=!0)}y.prevProp=w,y.prevResolvedValues=L,y.isActive&&(v={...v,...L}),r&&e.blockInitialAnimation&&(C=!1),C&&!T&&d.push(...E.map(I=>({animation:I,options:{type:g,...l}})))}if(p.size){const m={};p.forEach(g=>{const y=e.getBaseTarget(g);y!==void 0&&(m[g]=y)}),d.push({animation:m})}let _=!!d.length;return r&&u.initial===!1&&!e.manuallyAnimateOnMount&&(_=!1),r=!1,_?t(d):Promise.resolve()}function a(l,c,u){var f;if(n[l].isActive===c)return Promise.resolve();(f=e.variantChildren)===null||f===void 0||f.forEach(p=>{var v;return(v=p.animationState)===null||v===void 0?void 0:v.setActive(l,c)}),n[l].isActive=c;const d=s(u,l);for(const p in n)n[p].protectedKeys={};return d}return{animateChanges:s,setActive:a,setAnimateFunction:o,getState:()=>n}}function Ly(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!W6(t,e):!1}function ar(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Oy(){return{[J.Animate]:ar(!0),[J.InView]:ar(),[J.Hover]:ar(),[J.Tap]:ar(),[J.Drag]:ar(),[J.Focus]:ar(),[J.Exit]:ar()}}const My={animation:Nn(({visualElement:e,animate:t})=>{e.animationState||(e.animationState=Ay(e)),da(t)&&D.useEffect(()=>t.subscribe(e),[t])}),exit:Nn(e=>{const{custom:t,visualElement:n}=e,[r,i]=G6(),o=D.useContext(fa);D.useEffect(()=>{n.isPresent=r;const s=n.animationState&&n.animationState.setActive(J.Exit,!r,{custom:o&&o.custom||t});s&&!r&&s.then(i)},[r])})},Yd=(e,t)=>Math.abs(e-t);function Ny(e,t){const n=Yd(e.x,t.x),r=Yd(e.y,t.y);return Math.sqrt(n**2+r**2)}class wh{constructor(t,n,{transformPagePoint:r}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const c=yl(this.lastMoveEventInfo,this.history),u=this.startEvent!==null,f=Ny(c.offset,{x:0,y:0})>=3;if(!u&&!f)return;const{point:d}=c,{timestamp:p}=dn;this.history.push({...d,timestamp:p});const{onStart:v,onMove:h}=this.handlers;u||(v&&v(this.lastMoveEvent,c),this.startEvent=this.lastMoveEvent),h&&h(this.lastMoveEvent,c)},this.handlePointerMove=(c,u)=>{if(this.lastMoveEvent=c,this.lastMoveEventInfo=vl(u,this.transformPagePoint),V6(c)&&c.buttons===0){this.handlePointerUp(c,u);return}me.update(this.updatePoint,!0)},this.handlePointerUp=(c,u)=>{this.end();const{onEnd:f,onSessionEnd:d}=this.handlers,p=yl(vl(u,this.transformPagePoint),this.history);this.startEvent&&f&&f(c,p),d&&d(c,p)},q6(t)&&t.touches.length>1)return;this.handlers=n,this.transformPagePoint=r;const i=S0(t),o=vl(i,this.transformPagePoint),{point:s}=o,{timestamp:a}=dn;this.history=[{...s,timestamp:a}];const{onSessionStart:l}=n;l&&l(t,yl(o,this.history)),this.removeListeners=ma(v1(window,"pointermove",this.handlePointerMove),v1(window,"pointerup",this.handlePointerUp),v1(window,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Qn.update(this.updatePoint)}}function vl(e,t){return t?{point:t(e.point)}:e}function Xd(e,t){return{x:e.x-t.x,y:e.y-t.y}}function yl({point:e},t){return{point:e,delta:Xd(e,xh(t)),offset:Xd(e,Ry(t)),velocity:Iy(t,.1)}}function Ry(e){return e[0]}function xh(e){return e[e.length-1]}function Iy(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=xh(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>Jo(t)));)n--;if(!r)return{x:0,y:0};const o=(i.timestamp-r.timestamp)/1e3;if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function ye(e){return e.max-e.min}function zc(e,t=0,n=.01){return Math.abs(e-t)<=n}function Qd(e,t,n,r=.5){e.origin=r,e.originPoint=dt(t.min,t.max,e.origin),e.scale=ye(n)/ye(t),(zc(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=dt(n.min,n.max,e.origin)-e.originPoint,(zc(e.translate)||isNaN(e.translate))&&(e.translate=0)}function yi(e,t,n,r){Qd(e.x,t.x,n.x,r==null?void 0:r.originX),Qd(e.y,t.y,n.y,r==null?void 0:r.originY)}function Kd(e,t,n){e.min=n.min+t.min,e.max=e.min+ye(t)}function Vy(e,t,n){Kd(e.x,t.x,n.x),Kd(e.y,t.y,n.y)}function Zd(e,t,n){e.min=t.min-n.min,e.max=e.min+ye(t)}function _i(e,t,n){Zd(e.x,t.x,n.x),Zd(e.y,t.y,n.y)}function qy(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?dt(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?dt(n,e,r.max):Math.min(e,n)),e}function Jd(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function Fy(e,{top:t,left:n,bottom:r,right:i}){return{x:Jd(e.x,n,i),y:Jd(e.y,t,r)}}function t8(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function By(e,t){return{x:t8(e.x,t.x),y:t8(e.y,t.y)}}function zy(e,t){let n=.5;const r=ye(e),i=ye(t);return i>r?n=Fs(t.min,t.max-r,e.min):r>i&&(n=Fs(e.min,e.max-i,t.min)),A1(0,1,n)}function jy(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const jc=.35;function Uy(e=jc){return e===!1?e=0:e===!0&&(e=jc),{x:e8(e,"left","right"),y:e8(e,"top","bottom")}}function e8(e,t,n){return{min:n8(e,t),max:n8(e,n)}}function n8(e,t){return typeof e=="number"?e:e[t]||0}const r8=()=>({translate:0,scale:1,origin:0,originPoint:0}),wi=()=>({x:r8(),y:r8()}),i8=()=>({min:0,max:0}),yt=()=>({x:i8(),y:i8()});function je(e){return[e("x"),e("y")]}function Sh({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function $y({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function Hy(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function _l(e){return e===void 0||e===1}function Uc({scale:e,scaleX:t,scaleY:n}){return!_l(e)||!_l(t)||!_l(n)}function fr(e){return Uc(e)||Th(e)||e.z||e.rotate||e.rotateX||e.rotateY}function Th(e){return o8(e.x)||o8(e.y)}function o8(e){return e&&e!=="0%"}function js(e,t,n){const r=e-n,i=t*r;return n+i}function s8(e,t,n,r,i){return i!==void 0&&(e=js(e,i,r)),js(e,n,r)+t}function $c(e,t=0,n=1,r,i){e.min=s8(e.min,t,n,r,i),e.max=s8(e.max,t,n,r,i)}function bh(e,{x:t,y:n}){$c(e.x,t.translate,t.scale,t.originPoint),$c(e.y,n.translate,n.scale,n.originPoint)}function Gy(e,t,n,r=!1){var i,o;const s=n.length;if(!s)return;t.x=t.y=1;let a,l;for(let c=0;c<s;c++)a=n[c],l=a.projectionDelta,((o=(i=a.instance)===null||i===void 0?void 0:i.style)===null||o===void 0?void 0:o.display)!=="contents"&&(r&&a.options.layoutScroll&&a.scroll&&a!==a.root&&o1(e,{x:-a.scroll.offset.x,y:-a.scroll.offset.y}),l&&(t.x*=l.x.scale,t.y*=l.y.scale,bh(e,l)),r&&fr(a.latestValues)&&o1(e,a.latestValues));t.x=a8(t.x),t.y=a8(t.y)}function a8(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function En(e,t){e.min=e.min+t,e.max=e.max+t}function l8(e,t,[n,r,i]){const o=t[i]!==void 0?t[i]:.5,s=dt(e.min,e.max,o);$c(e,t[n],t[r],s,t.scale)}const Wy=["x","scaleX","originX"],Yy=["y","scaleY","originY"];function o1(e,t){l8(e.x,t,Wy),l8(e.y,t,Yy)}function Ch(e,t){return Sh(Hy(e.getBoundingClientRect(),t))}function Xy(e,t,n){const r=Ch(e,n),{scroll:i}=t;return i&&(En(r.x,i.offset.x),En(r.y,i.offset.y)),r}const Qy=new WeakMap;class Ky{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=yt(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){if(this.visualElement.isPresent===!1)return;const r=a=>{this.stopAnimation(),n&&this.snapToCursor(S0(a,"page").point)},i=(a,l)=>{var c;const{drag:u,dragPropagation:f,onDragStart:d}=this.getProps();u&&!f&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=j6(u),!this.openGlobalLock)||(this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),je(p=>{var v,h;let _=this.getAxisMotionValue(p).get()||0;if(Qe.test(_)){const m=(h=(v=this.visualElement.projection)===null||v===void 0?void 0:v.layout)===null||h===void 0?void 0:h.layoutBox[p];m&&(_=ye(m)*(parseFloat(_)/100))}this.originPoint[p]=_}),d==null||d(a,l),(c=this.visualElement.animationState)===null||c===void 0||c.setActive(J.Drag,!0))},o=(a,l)=>{const{dragPropagation:c,dragDirectionLock:u,onDirectionLock:f,onDrag:d}=this.getProps();if(!c&&!this.openGlobalLock)return;const{offset:p}=l;if(u&&this.currentDirection===null){this.currentDirection=Zy(p),this.currentDirection!==null&&(f==null||f(this.currentDirection));return}this.updateAxis("x",l.point,p),this.updateAxis("y",l.point,p),this.visualElement.render(),d==null||d(a,l)},s=(a,l)=>this.stop(a,l);this.panSession=new wh(t,{onSessionStart:r,onStart:i,onMove:o,onSessionEnd:s},{transformPagePoint:this.visualElement.getTransformPagePoint()})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o==null||o(t,n)}cancel(){var t,n;this.isDragging=!1,this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!1),(t=this.panSession)===null||t===void 0||t.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),(n=this.visualElement.animationState)===null||n===void 0||n.setActive(J.Drag,!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!No(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=qy(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){const{dragConstraints:t,dragElastic:n}=this.getProps(),{layout:r}=this.visualElement.projection||{},i=this.constraints;t&&r1(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=Fy(r.layoutBox,t):this.constraints=!1,this.elastic=Uy(n),i!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&je(o=>{this.getAxisMotionValue(o)&&(this.constraints[o]=jy(r.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!r1(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=Xy(r,i.root,this.visualElement.getTransformPagePoint());let s=By(i.layout.layoutBox,o);if(n){const a=n($y(s));this.hasMutatedConstraints=!!a,a&&(s=Sh(a))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=je(u=>{if(!No(u,n,this.currentDirection))return;let f=(l==null?void 0:l[u])||{};s&&(f={min:0,max:0});const d=i?200:1e6,p=i?40:1e7,v={type:"inertia",velocity:r?t[u]:0,bounceStiffness:d,bounceDamping:p,timeConstant:750,restDelta:1,restSpeed:10,...o,...f};return this.startAxisValueAnimation(u,v)});return Promise.all(c).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(V0(t,r,0,n))}stopAnimation(){je(t=>this.getAxisMotionValue(t).stop())}getAxisMotionValue(t){var n;const r="_drag"+t.toUpperCase(),i=this.visualElement.getProps()[r];return i||this.visualElement.getValue(t,((n=this.visualElement.getProps().initial)===null||n===void 0?void 0:n[t])||0)}snapToCursor(t){je(n=>{const{drag:r}=this.getProps();if(!No(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:a}=i.layout.layoutBox[n];o.set(t[n]-dt(s,a,.5))}})}scalePositionWithinConstraints(){var t;if(!this.visualElement.current)return;const{drag:n,dragConstraints:r}=this.getProps(),{projection:i}=this.visualElement;if(!r1(r)||!i||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};je(a=>{const l=this.getAxisMotionValue(a);if(l){const c=l.get();o[a]=zy({min:c,max:c},this.constraints[a])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",(t=i.root)===null||t===void 0||t.updateScroll(),i.updateLayout(),this.resolveConstraints(),je(a=>{if(!No(a,n,null))return;const l=this.getAxisMotionValue(a),{min:c,max:u}=this.constraints[a];l.set(dt(c,u,o[a]))})}addListeners(){var t;if(!this.visualElement.current)return;Qy.set(this.visualElement,this);const n=this.visualElement.current,r=v1(n,"pointerdown",c=>{const{drag:u,dragListener:f=!0}=this.getProps();u&&f&&this.start(c)}),i=()=>{const{dragConstraints:c}=this.getProps();r1(c)&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,s=o.addEventListener("measure",i);o&&!o.layout&&((t=o.root)===null||t===void 0||t.updateScroll(),o.updateLayout()),i();const a=pa(window,"resize",()=>this.scalePositionWithinConstraints()),l=o.addEventListener("didUpdate",({delta:c,hasLayoutChanged:u})=>{this.isDragging&&u&&(je(f=>{const d=this.getAxisMotionValue(f);d&&(this.originPoint[f]+=c[f].translate,d.set(d.get()+c[f].translate))}),this.visualElement.render())});return()=>{a(),r(),s(),l==null||l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=jc,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:a}}}function No(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function Zy(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}function Jy(e){const{dragControls:t,visualElement:n}=e,r=h0(()=>new Ky(n));D.useEffect(()=>t&&t.subscribe(r),[r,t]),D.useEffect(()=>r.addListeners(),[r])}function t_({onPan:e,onPanStart:t,onPanEnd:n,onPanSessionStart:r,visualElement:i}){const o=e||t||n||r,s=D.useRef(null),{transformPagePoint:a}=D.useContext(d0),l={onSessionStart:r,onStart:t,onMove:e,onEnd:(u,f)=>{s.current=null,n&&n(u,f)}};D.useEffect(()=>{s.current!==null&&s.current.updateHandlers(l)});function c(u){s.current=new wh(u,l,{transformPagePoint:a})}Is(i,"pointerdown",o&&c),H6(()=>s.current&&s.current.end())}const e_={pan:Nn(t_),drag:Nn(Jy)};function Hc(e){return typeof e=="string"&&e.startsWith("var(--")}const Eh=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function n_(e){const t=Eh.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function Gc(e,t,n=1){const[r,i]=n_(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);return o?o.trim():Hc(i)?Gc(i,t,n+1):i}function r_(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const o=i.get();if(!Hc(o))return;const s=Gc(o,r);s&&i.set(s)});for(const i in t){const o=t[i];if(!Hc(o))continue;const s=Gc(o,r);s&&(t[i]=s,n&&n[i]===void 0&&(n[i]=o))}return{target:t,transitionEnd:n}}const i_=new Set(["width","height","top","left","right","bottom","x","y"]),kh=e=>i_.has(e),o_=e=>Object.keys(e).some(kh),Ph=(e,t)=>{e.set(t,!1),e.set(t)},c8=e=>e===qr||e===R;var u8;(function(e){e.width="width",e.height="height",e.left="left",e.right="right",e.top="top",e.bottom="bottom"})(u8||(u8={}));const f8=(e,t)=>parseFloat(e.split(", ")[t]),d8=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return f8(i[1],t);{const o=r.match(/^matrix\((.+)\)$/);return o?f8(o[1],e):0}},s_=new Set(["x","y","z"]),a_=Ms.filter(e=>!s_.has(e));function l_(e){const t=[];return a_.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const h8={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:d8(4,13),y:d8(5,14)},c_=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:s}=o,a={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(c=>{a[c]=h8[c](r,o)}),t.render();const l=t.measureViewportBox();return n.forEach(c=>{const u=t.getValue(c);Ph(u,a[c]),e[c]=h8[c](l,o)}),e},u_=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(kh);let o=[],s=!1;const a=[];if(i.forEach(l=>{const c=e.getValue(l);if(!e.hasValue(l))return;let u=n[l],f=G1(u);const d=t[l];let p;if(Rs(d)){const v=d.length,h=d[0]===null?1:0;u=d[h],f=G1(u);for(let _=h;_<v;_++)p?Qi(G1(d[_])===p):p=G1(d[_])}else p=G1(d);if(f!==p)if(c8(f)&&c8(p)){const v=c.get();typeof v=="string"&&c.set(parseFloat(v)),typeof d=="string"?t[l]=parseFloat(d):Array.isArray(d)&&p===R&&(t[l]=d.map(parseFloat))}else f!=null&&f.transform&&(p!=null&&p.transform)&&(u===0||d===0)?u===0?c.set(p.transform(u)):t[l]=f.transform(d):(s||(o=l_(e),s=!0),a.push(l),r[l]=r[l]!==void 0?r[l]:t[l],Ph(c,d))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,c=c_(t,e,a);return o.length&&o.forEach(([u,f])=>{e.getValue(u).set(f)}),e.render(),Ir&&l!==null&&window.scrollTo({top:l}),{target:c,transitionEnd:r}}else return{target:t,transitionEnd:r}};function f_(e,t,n,r){return o_(t)?u_(e,t,n,r):{target:t,transitionEnd:r}}const d_=(e,t,n,r)=>{const i=r_(e,t,r);return t=i.target,r=i.transitionEnd,f_(e,t,n,r)},Wc={current:null},Dh={current:!1};function h_(){if(Dh.current=!0,!!Ir)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Wc.current=e.matches;e.addListener(t),t()}else Wc.current=!1}function p_(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],s=n[i];if(Je(o))e.addValue(i,o),qs(r)&&r.add(i);else if(Je(s))e.addValue(i,L1(o,{owner:e})),qs(r)&&r.remove(i);else if(s!==o)if(e.hasValue(i)){const a=e.getValue(i);!a.hasAnimated&&a.set(o)}else{const a=e.getStaticValue(i);e.addValue(i,L1(a!==void 0?a:o))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const Ah=Object.keys(Wi),m_=Ah.length,p8=["AnimationStart","AnimationComplete","Update","Unmount","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class g_{constructor({parent:t,props:n,reducedMotionConfig:r,visualState:i},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.isPresent=!0,this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>me.render(this.render,!1,!0);const{latestValues:s,renderState:a}=i;this.latestValues=s,this.baseTarget={...s},this.initialValues=n.initial?{...s}:{},this.renderState=a,this.parent=t,this.props=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.isControllingVariants=ha(n),this.isVariantNode=T6(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:l,...c}=this.scrapeMotionValuesFromProps(n);for(const u in c){const f=c[u];s[u]!==void 0&&Je(f)&&(f.set(s[u],!1),qs(l)&&l.add(u))}}scrapeMotionValuesFromProps(t){return{}}mount(t){var n;this.current=t,this.projection&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=(n=this.parent)===null||n===void 0?void 0:n.addVariantChild(this)),this.values.forEach((r,i)=>this.bindToMotionValue(i,r)),Dh.current||h_(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Wc.current,this.parent&&this.parent.children.add(this),this.setProps(this.props)}unmount(){var t,n,r;(t=this.projection)===null||t===void 0||t.unmount(),Qn.update(this.notifyUpdate),Qn.render(this.render),this.valueSubscriptions.forEach(i=>i()),(n=this.removeFromVariantTree)===null||n===void 0||n.call(this),(r=this.parent)===null||r===void 0||r.children.delete(this);for(const i in this.events)this.events[i].clear();this.current=null}bindToMotionValue(t,n){const r=Vr.has(t),i=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&me.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),o()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures(t,n,r,i,o,s){const a=[];for(let l=0;l<m_;l++){const c=Ah[l],{isEnabled:u,Component:f}=Wi[c];u(t)&&f&&a.push(D.createElement(f,{key:c,...t,visualElement:this}))}if(!this.projection&&o){this.projection=new o(i,this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:c,drag:u,dragConstraints:f,layoutScroll:d}=t;this.projection.setOptions({layoutId:l,layout:c,alwaysMeasureLayout:!!u||f&&r1(f),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof c=="string"?c:"both",initialPromotionConfig:s,layoutScroll:d})}return a}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):yt()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}setProps(t){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.props=t;for(let n=0;n<p8.length;n++){const r=p8[n];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);const i=t["on"+r];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=p_(this,this.scrapeMotionValuesFromProps(t),this.prevMotionValues)}getProps(){return this.props}getVariant(t){var n;return(n=this.props.variants)===null||n===void 0?void 0:n[t]}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){var t;return this.isVariantNode?this:(t=this.parent)===null||t===void 0?void 0:t.getClosestVariantNode()}getVariantContext(t=!1){var n,r;if(t)return(n=this.parent)===null||n===void 0?void 0:n.getVariantContext();if(!this.isControllingVariants){const o=((r=this.parent)===null||r===void 0?void 0:r.getVariantContext())||{};return this.props.initial!==void 0&&(o.initial=this.props.initial),o}const i={};for(let o=0;o<v_;o++){const s=Lh[o],a=this.props[s];(Gi(a)||a===!1)&&(i[s]=a)}return i}addVariantChild(t){var n;const r=this.getClosestVariantNode();if(r)return(n=r.variantChildren)===null||n===void 0||n.add(t),()=>r.variantChildren.delete(t)}addValue(t,n){this.hasValue(t)&&this.removeValue(t),this.values.set(t,n),this.latestValues[t]=n.get(),this.bindToMotionValue(t,n)}removeValue(t){var n;this.values.delete(t),(n=this.valueSubscriptions.get(t))===null||n===void 0||n(),this.valueSubscriptions.delete(t),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=L1(n,{owner:this}),this.addValue(t,r)),r}readValue(t){return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=x0(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!Je(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new C0),this.events[t].add(n)}notify(t,...n){var r;(r=this.events[t])===null||r===void 0||r.notify(...n)}}const Lh=["initial",...q0],v_=Lh.length;class Oh extends g_{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){var r;return(r=t.style)===null||r===void 0?void 0:r[n]}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},o){let s=Ev(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),s&&(s=i(s))),o){bv(this,r,s);const a=d_(this,r,s,n);n=a.transitionEnd,r=a.target}return{transition:t,transitionEnd:n,...r}}}function y_(e){return window.getComputedStyle(e)}class __ extends Oh{readValueFromInstance(t,n){if(Vr.has(n)){const r=P0(n);return r&&r.default||0}else{const r=y_(t),i=(k6(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Ch(t,n)}build(t,n,r,i){m0(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t){return w0(t)}renderInstance(t,n,r,i){O6(t,n,r,i)}}class w_ extends Oh{constructor(){super(...arguments),this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){var r;return Vr.has(n)?((r=P0(n))===null||r===void 0?void 0:r.default)||0:(n=M6.has(n)?n:_0(n),t.getAttribute(n))}measureInstanceViewportBox(){return yt()}scrapeMotionValuesFromProps(t){return R6(t)}build(t,n,r,i){v0(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){N6(t,n,r,i)}mount(t){this.isSVGTag=y0(t.tagName),super.mount(t)}}const x_=(e,t)=>p0(e)?new w_(t,{enableHardwareAcceleration:!1}):new __(t,{enableHardwareAcceleration:!0});function m8(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const W1={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(R.test(e))e=parseFloat(e);else return e;const n=m8(e,t.target.x),r=m8(e,t.target.y);return`${n}% ${r}%`}},g8="_$css",S_={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=e.includes("var("),o=[];i&&(e=e.replace(Eh,p=>(o.push(p),g8)));const s=Kn.parse(e);if(s.length>5)return r;const a=Kn.createTransformer(e),l=typeof s[0]!="number"?1:0,c=n.x.scale*t.x,u=n.y.scale*t.y;s[0+l]/=c,s[1+l]/=u;const f=dt(c,u,.5);typeof s[2+l]=="number"&&(s[2+l]/=f),typeof s[3+l]=="number"&&(s[3+l]/=f);let d=a(s);if(i){let p=0;d=d.replace(g8,()=>{const v=o[p];return p++,v})}return d}};class T_ extends K.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;Q4(C_),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),mi.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||me.postRender(()=>{var a;!((a=s.getStack())===null||a===void 0)&&a.members.length||this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),!t.currentAnimation&&t.isLead()&&this.safeToRemove())}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n!=null&&n.group&&n.group.remove(i),r!=null&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t==null||t()}render(){return null}}function b_(e){const[t,n]=G6(),r=D.useContext(b6);return K.createElement(T_,{...e,layoutGroup:r,switchLayoutGroup:D.useContext(C6),isPresent:t,safeToRemove:n})}const C_={borderRadius:{...W1,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:W1,borderTopRightRadius:W1,borderBottomLeftRadius:W1,borderBottomRightRadius:W1,boxShadow:S_},E_={measureLayout:b_};function k_(e,t,n={}){const r=Je(e)?e:L1(e);return r.start(V0("",r,t,n)),{stop:()=>r.stop(),isAnimating:()=>r.isAnimating()}}const Mh=["TopLeft","TopRight","BottomLeft","BottomRight"],P_=Mh.length,v8=e=>typeof e=="string"?parseFloat(e):e,y8=e=>typeof e=="number"||R.test(e);function D_(e,t,n,r,i,o){i?(e.opacity=dt(0,n.opacity!==void 0?n.opacity:1,A_(r)),e.opacityExit=dt(t.opacity!==void 0?t.opacity:1,0,L_(r))):o&&(e.opacity=dt(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<P_;s++){const a=`border${Mh[s]}Radius`;let l=_8(t,a),c=_8(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||y8(l)===y8(c)?(e[a]=Math.max(dt(v8(l),v8(c),r),0),(Qe.test(c)||Qe.test(l))&&(e[a]+="%")):e[a]=c}(t.rotate||n.rotate)&&(e.rotate=dt(t.rotate||0,n.rotate||0,r))}function _8(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const A_=Nh(0,.5,R0),L_=Nh(.5,.95,N0);function Nh(e,t,n){return r=>r<e?0:r>t?1:n(Fs(e,t,r))}function w8(e,t){e.min=t.min,e.max=t.max}function Me(e,t){w8(e.x,t.x),w8(e.y,t.y)}function x8(e,t,n,r,i){return e-=t,e=js(e,1/n,r),i!==void 0&&(e=js(e,1/i,r)),e}function O_(e,t=0,n=1,r=.5,i,o=e,s=e){if(Qe.test(t)&&(t=parseFloat(t),t=dt(s.min,s.max,t/100)-s.min),typeof t!="number")return;let a=dt(o.min,o.max,r);e===o&&(a-=t),e.min=x8(e.min,t,n,a,i),e.max=x8(e.max,t,n,a,i)}function S8(e,t,[n,r,i],o,s){O_(e,t[n],t[r],t[i],t.scale,o,s)}const M_=["x","scaleX","originX"],N_=["y","scaleY","originY"];function T8(e,t,n,r){S8(e.x,t,M_,n==null?void 0:n.x,r==null?void 0:r.x),S8(e.y,t,N_,n==null?void 0:n.y,r==null?void 0:r.y)}function b8(e){return e.translate===0&&e.scale===1}function Rh(e){return b8(e.x)&&b8(e.y)}function Ih(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function C8(e){return ye(e.x)/ye(e.y)}class R_{constructor(){this.members=[]}add(t){T0(this.members,t),t.scheduleRender()}remove(t){if(b0(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){var r;const i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,n&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),!((r=t.root)===null||r===void 0)&&r.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:o}=t.options;o===!1&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{var n,r,i,o,s;(r=(n=t.options).onExitComplete)===null||r===void 0||r.call(n),(s=(i=t.resumingFrom)===null||i===void 0?void 0:(o=i.options).onExitComplete)===null||s===void 0||s.call(o)})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function E8(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r=`translate3d(${i}px, ${o}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:l,rotateX:c,rotateY:u}=n;l&&(r+=`rotate(${l}deg) `),c&&(r+=`rotateX(${c}deg) `),u&&(r+=`rotateY(${u}deg) `)}const s=e.x.scale*t.x,a=e.y.scale*t.y;return(s!==1||a!==1)&&(r+=`scale(${s}, ${a})`),r||"none"}const I_=(e,t)=>e.depth-t.depth;class V_{constructor(){this.children=[],this.isDirty=!1}add(t){T0(this.children,t),this.isDirty=!0}remove(t){b0(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(I_),this.isDirty=!1,this.children.forEach(t)}}const k8=["","X","Y","Z"],P8=1e3;let q_=0;function Vh({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s,a={},l=t==null?void 0:t()){this.id=q_++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isTransformDirty=!1,this.isProjectionDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.potentialNodes=new Map,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.nodes.forEach(z_),this.nodes.forEach($_),this.nodes.forEach(H_)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.elementId=s,this.latestValues=a,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0,s&&this.root.registerPotentialNode(s,this);for(let c=0;c<this.path.length;c++)this.path[c].shouldResetTransform=!0;this.root===this&&(this.nodes=new V_)}addEventListener(s,a){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new C0),this.eventHandlers.get(s).add(a)}notifyListeners(s,...a){const l=this.eventHandlers.get(s);l==null||l.notify(...a)}hasListeners(s){return this.eventHandlers.has(s)}registerPotentialNode(s,a){this.potentialNodes.set(s,a)}mount(s,a=!1){var l;if(this.instance)return;this.isSVG=s instanceof SVGElement&&s.tagName!=="svg",this.instance=s;const{layoutId:c,layout:u,visualElement:f}=this.options;if(f&&!f.current&&f.mount(s),this.root.nodes.add(this),(l=this.parent)===null||l===void 0||l.children.add(this),this.elementId&&this.root.potentialNodes.delete(this.elementId),a&&(u||c)&&(this.isLayoutDirty=!0),e){let d;const p=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=gh(p,250),mi.hasAnimatedSinceResize&&(mi.hasAnimatedSinceResize=!1,this.nodes.forEach(A8))})}c&&this.root.registerSharedNode(c,this),this.options.animate!==!1&&f&&(c||u)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:p,hasRelativeTargetChanged:v,layout:h})=>{var _,m,g,y,w;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=(m=(_=this.options.transition)!==null&&_!==void 0?_:f.getDefaultTransition())!==null&&m!==void 0?m:Q_,{onLayoutAnimationStart:S,onLayoutAnimationComplete:T}=f.getProps(),b=!this.targetLayout||!Ih(this.targetLayout,h)||v,C=!p&&v;if(!((g=this.resumeFrom)===null||g===void 0)&&g.instance||C||p&&(b||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,C);const E={...vh(x,"layout"),onPlay:S,onComplete:T};f.shouldReduceMotion&&(E.delay=0,E.type=!1),this.startAnimation(E)}else!p&&this.animationProgress===0&&A8(this),this.isLead()&&((w=(y=this.options).onExitComplete)===null||w===void 0||w.call(y));this.targetLayout=h})}unmount(){var s,a;this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this),(s=this.getStack())===null||s===void 0||s.remove(this),(a=this.parent)===null||a===void 0||a.children.delete(this),this.instance=void 0,Qn.preRender(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){var s;return this.isAnimationBlocked||((s=this.parent)===null||s===void 0?void 0:s.isTreeAnimationBlocked())||!1}startUpdate(){var s;this.isUpdateBlocked()||(this.isUpdating=!0,(s=this.nodes)===null||s===void 0||s.forEach(G_),this.animationId++)}willUpdate(s=!0){var a,l,c;if(this.root.isUpdateBlocked()){(l=(a=this.options).onExitComplete)===null||l===void 0||l.call(a);return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let p=0;p<this.path.length;p++){const v=this.path[p];v.shouldResetTransform=!0,v.updateScroll("snapshot")}const{layoutId:u,layout:f}=this.options;if(u===void 0&&!f)return;const d=(c=this.options.visualElement)===null||c===void 0?void 0:c.getProps().transformTemplate;this.prevTransformTemplateValue=d==null?void 0:d(this.latestValues,""),this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}didUpdate(){if(this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(D8);return}this.isUpdating&&(this.isUpdating=!1,this.potentialNodes.size&&(this.potentialNodes.forEach(K_),this.potentialNodes.clear()),this.nodes.forEach(U_),this.nodes.forEach(F_),this.nodes.forEach(B_),this.clearAllSnapshots(),ul.update(),ul.preRender(),ul.render())}clearAllSnapshots(){this.nodes.forEach(j_),this.sharedNodes.forEach(W_)}scheduleUpdateProjection(){me.preRender(this.updateProjection,!1,!0)}scheduleCheckAfterUnmount(){me.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){var s;if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=yt(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox),(s=this.options.visualElement)===null||s===void 0||s.notify("LayoutMeasure",this.layout.layoutBox,a==null?void 0:a.layoutBox)}updateScroll(s="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){var s;if(!i)return;const a=this.isLayoutDirty||this.shouldResetTransform,l=this.projectionDelta&&!Rh(this.projectionDelta),c=(s=this.options.visualElement)===null||s===void 0?void 0:s.getProps().transformTemplate,u=c==null?void 0:c(this.latestValues,""),f=u!==this.prevTransformTemplateValue;a&&(l||fr(this.latestValues)||f)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return s&&(l=this.removeTransform(l)),Z_(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return yt();const a=s.measureViewportBox(),{scroll:l}=this.root;return l&&(En(a.x,l.offset.x),En(a.y,l.offset.y)),a}removeElementScroll(s){const a=yt();Me(a,s);for(let l=0;l<this.path.length;l++){const c=this.path[l],{scroll:u,options:f}=c;if(c!==this.root&&u&&f.layoutScroll){if(u.isRoot){Me(a,s);const{scroll:d}=this.root;d&&(En(a.x,-d.offset.x),En(a.y,-d.offset.y))}En(a.x,u.offset.x),En(a.y,u.offset.y)}}return a}applyTransform(s,a=!1){const l=yt();Me(l,s);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&o1(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),fr(u.latestValues)&&o1(l,u.latestValues)}return fr(this.latestValues)&&o1(l,this.latestValues),l}removeTransform(s){var a;const l=yt();Me(l,s);for(let c=0;c<this.path.length;c++){const u=this.path[c];if(!u.instance||!fr(u.latestValues))continue;Uc(u.latestValues)&&u.updateSnapshot();const f=yt(),d=u.measurePageBox();Me(f,d),T8(l,u.latestValues,(a=u.snapshot)===null||a===void 0?void 0:a.layoutBox,f)}return fr(this.latestValues)&&T8(l,this.latestValues),l}setTargetDelta(s){this.targetDelta=s,this.isProjectionDirty=!0,this.root.scheduleUpdateProjection()}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}resolveTargetDelta(){var s;const a=this.getLead();if(this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),!this.isProjectionDirty&&!this.attemptToResolveRelativeTarget)return;const{layout:l,layoutId:c}=this.options;if(!(!this.layout||!(l||c))){if(!this.targetDelta&&!this.relativeTarget){const u=this.getClosestProjectingParent();u&&u.layout?(this.relativeParent=u,this.relativeTarget=yt(),this.relativeTargetOrigin=yt(),_i(this.relativeTargetOrigin,this.layout.layoutBox,u.layout.layoutBox),Me(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=yt(),this.targetWithTransforms=yt()),this.relativeTarget&&this.relativeTargetOrigin&&(!((s=this.relativeParent)===null||s===void 0)&&s.target)?Vy(this.target,this.relativeTarget,this.relativeParent.target):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Me(this.target,this.layout.layoutBox),bh(this.target,this.targetDelta)):Me(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const u=this.getClosestProjectingParent();u&&!!u.resumingFrom==!!this.resumingFrom&&!u.options.layoutScroll&&u.target?(this.relativeParent=u,this.relativeTarget=yt(),this.relativeTargetOrigin=yt(),_i(this.relativeTargetOrigin,this.target,u.target),Me(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Uc(this.parent.latestValues)||Th(this.parent.latestValues)))return(this.parent.relativeTarget||this.parent.targetDelta)&&this.parent.layout?this.parent:this.parent.getClosestProjectingParent()}calcProjection(){var s;const{isProjectionDirty:a,isTransformDirty:l}=this;this.isProjectionDirty=this.isTransformDirty=!1;const c=this.getLead(),u=!!this.resumingFrom||this!==c;let f=!0;if(a&&(f=!1),u&&l&&(f=!1),f)return;const{layout:d,layoutId:p}=this.options;if(this.isTreeAnimating=!!(!((s=this.parent)===null||s===void 0)&&s.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||p))return;Me(this.layoutCorrected,this.layout.layoutBox),Gy(this.layoutCorrected,this.treeScale,this.path,u);const{target:v}=c;if(!v)return;this.projectionDelta||(this.projectionDelta=wi(),this.projectionDeltaWithTransform=wi());const h=this.treeScale.x,_=this.treeScale.y,m=this.projectionTransform;yi(this.projectionDelta,this.layoutCorrected,v,this.latestValues),this.projectionTransform=E8(this.projectionDelta,this.treeScale),(this.projectionTransform!==m||this.treeScale.x!==h||this.treeScale.y!==_)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){var a,l,c;(l=(a=this.options).scheduleRender)===null||l===void 0||l.call(a),s&&((c=this.getStack())===null||c===void 0||c.scheduleRender()),this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,a=!1){var l,c;const u=this.snapshot,f=(u==null?void 0:u.latestValues)||{},d={...this.latestValues},p=wi();this.relativeTarget=this.relativeTargetOrigin=void 0,this.attemptToResolveRelativeTarget=!a;const v=yt(),h=(u==null?void 0:u.source)!==((l=this.layout)===null||l===void 0?void 0:l.source),_=(((c=this.getStack())===null||c===void 0?void 0:c.members.length)||0)<=1,m=!!(h&&!_&&this.options.crossfade===!0&&!this.path.some(X_));this.animationProgress=0,this.mixTargetDelta=g=>{var y;const w=g/1e3;L8(p.x,s.x,w),L8(p.y,s.y,w),this.setTargetDelta(p),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&(!((y=this.relativeParent)===null||y===void 0)&&y.layout)&&(_i(v,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Y_(this.relativeTarget,this.relativeTargetOrigin,v,w)),h&&(this.animationValues=d,D_(d,f,this.latestValues,w,m,_)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=w},this.mixTargetDelta(0)}startAnimation(s){var a,l;this.notifyListeners("animationStart"),(a=this.currentAnimation)===null||a===void 0||a.stop(),this.resumingFrom&&((l=this.resumingFrom.currentAnimation)===null||l===void 0||l.stop()),this.pendingAnimation&&(Qn.update(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=me.update(()=>{mi.hasAnimatedSinceResize=!0,this.currentAnimation=k_(0,P8,{...s,onUpdate:c=>{var u;this.mixTargetDelta(c),(u=s.onUpdate)===null||u===void 0||u.call(s,c)},onComplete:()=>{var c;(c=s.onComplete)===null||c===void 0||c.call(s),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){var s;this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0),(s=this.getStack())===null||s===void 0||s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){var s;this.currentAnimation&&((s=this.mixTargetDelta)===null||s===void 0||s.call(this,P8),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=s;if(!(!a||!l||!c)){if(this!==s&&this.layout&&c&&qh(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||yt();const f=ye(this.layout.layoutBox.x);l.x.min=s.target.x.min,l.x.max=l.x.min+f;const d=ye(this.layout.layoutBox.y);l.y.min=s.target.y.min,l.y.max=l.y.min+d}Me(a,l),o1(a,u),yi(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(s,a){var l,c,u;this.sharedNodes.has(s)||this.sharedNodes.set(s,new R_),this.sharedNodes.get(s).add(a),a.promote({transition:(l=a.options.initialPromotionConfig)===null||l===void 0?void 0:l.transition,preserveFollowOpacity:(u=(c=a.options.initialPromotionConfig)===null||c===void 0?void 0:c.shouldPreserveFollowOpacity)===null||u===void 0?void 0:u.call(c,a)})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:a}=this.options;return a?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:a}=this.options;return a?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),s&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let a=!1;const{latestValues:l}=s;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const c={};for(let u=0;u<k8.length;u++){const f="rotate"+k8[u];l[f]&&(c[f]=l[f],s.setStaticValue(f,0))}s==null||s.render();for(const u in c)s.setStaticValue(u,c[u]);s.scheduleRender()}getProjectionStyles(s={}){var a,l,c;const u={};if(!this.instance||this.isSVG)return u;if(this.isVisible)u.visibility="";else return{visibility:"hidden"};const f=(a=this.options.visualElement)===null||a===void 0?void 0:a.getProps().transformTemplate;if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Zo(s.pointerEvents)||"",u.transform=f?f(this.latestValues,""):"none",u;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const _={};return this.options.layoutId&&(_.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,_.pointerEvents=Zo(s.pointerEvents)||""),this.hasProjected&&!fr(this.latestValues)&&(_.transform=f?f({},""):"none",this.hasProjected=!1),_}const p=d.animationValues||d.latestValues;this.applyTransformsToTarget(),u.transform=E8(this.projectionDeltaWithTransform,this.treeScale,p),f&&(u.transform=f(p,u.transform));const{x:v,y:h}=this.projectionDelta;u.transformOrigin=`${v.origin*100}% ${h.origin*100}% 0`,d.animationValues?u.opacity=d===this?(c=(l=p.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&c!==void 0?c:1:this.preserveOpacity?this.latestValues.opacity:p.opacityExit:u.opacity=d===this?p.opacity!==void 0?p.opacity:"":p.opacityExit!==void 0?p.opacityExit:0;for(const _ in Os){if(p[_]===void 0)continue;const{correct:m,applyTo:g}=Os[_],y=m(p[_],d);if(g){const w=g.length;for(let x=0;x<w;x++)u[g[x]]=y}else u[_]=y}return this.options.layoutId&&(u.pointerEvents=d===this?Zo(s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var a;return(a=s.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(D8),this.root.sharedNodes.clear()}}}function F_(e){e.updateLayout()}function B_(e){var t,n,r;const i=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&i&&e.hasListeners("didUpdate")){const{layoutBox:o,measuredBox:s}=e.layout,{animationType:a}=e.options,l=i.source!==e.layout.source;a==="size"?je(p=>{const v=l?i.measuredBox[p]:i.layoutBox[p],h=ye(v);v.min=o[p].min,v.max=v.min+h}):qh(a,i.layoutBox,o)&&je(p=>{const v=l?i.measuredBox[p]:i.layoutBox[p],h=ye(o[p]);v.max=v.min+h});const c=wi();yi(c,o,i.layoutBox);const u=wi();l?yi(u,e.applyTransform(s,!0),i.measuredBox):yi(u,o,i.layoutBox);const f=!Rh(c);let d=!1;if(!e.resumeFrom){const p=e.getClosestProjectingParent();if(p&&!p.resumeFrom){const{snapshot:v,layout:h}=p;if(v&&h){const _=yt();_i(_,i.layoutBox,v.layoutBox);const m=yt();_i(m,o,h.layoutBox),Ih(_,m)||(d=!0)}}}e.notifyListeners("didUpdate",{layout:o,snapshot:i,delta:u,layoutDelta:c,hasLayoutChanged:f,hasRelativeTargetChanged:d})}else e.isLead()&&((r=(n=e.options).onExitComplete)===null||r===void 0||r.call(n));e.options.transition=void 0}function z_(e){e.isProjectionDirty||(e.isProjectionDirty=!!(e.parent&&e.parent.isProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=!!(e.parent&&e.parent.isTransformDirty))}function j_(e){e.clearSnapshot()}function D8(e){e.clearMeasurements()}function U_(e){const{visualElement:t}=e.options;t!=null&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function A8(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0}function $_(e){e.resolveTargetDelta()}function H_(e){e.calcProjection()}function G_(e){e.resetRotation()}function W_(e){e.removeLeadSnapshot()}function L8(e,t,n){e.translate=dt(t.translate,0,n),e.scale=dt(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function O8(e,t,n,r){e.min=dt(t.min,n.min,r),e.max=dt(t.max,n.max,r)}function Y_(e,t,n,r){O8(e.x,t.x,n.x,r),O8(e.y,t.y,n.y,r)}function X_(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const Q_={duration:.45,ease:[.4,0,.1,1]};function K_(e,t){let n=e.root;for(let o=e.path.length-1;o>=0;o--)if(e.path[o].instance){n=e.path[o];break}const i=(n&&n!==e.root?n.instance:document).querySelector(`[data-projection-id="${t}"]`);i&&e.mount(i,!0)}function M8(e){e.min=Math.round(e.min),e.max=Math.round(e.max)}function Z_(e){M8(e.x),M8(e.y)}function qh(e,t,n){return e==="position"||e==="preserve-aspect"&&!zc(C8(t),C8(n),.2)}const J_=Vh({attachResizeListener:(e,t)=>pa(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),wl={current:void 0},tw=Vh({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!wl.current){const e=new J_(0,{});e.mount(window),e.setOptions({layoutScroll:!0}),wl.current=e}return wl.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),ew={...My,...Zg,...e_,...E_},nw=Y4((e,t)=>Eg(e,t,ew,x_,tw));var rw=K.memo(e=>{var{play:t,items:n}=e,[r,i]=K.useState(t);return!r&&t&&i(!0),!(n!=null&&n.length)||!r?null:K.createElement(iw,e)}),N8=0,iw=e=>{var{items:t,renderItem:n,pixelsPerFrame:r=5,frameRate:i=25,play:o,loop:s=!0,onExit:a}=e,l=K.createRef(),[c,u]=K.useState(),[f,d]=K.useState([[N8,t[0]]]);K.useLayoutEffect(()=>{u(l.current.getBoundingClientRect())},[]);var p=h=>{var _=t.findIndex(y=>{var{id:w}=y;return w===h.id}),m=(_+1)%t.length,g=t[m];d(y=>[...y,[++N8,g]])},v=()=>{f.length===1&&a&&a(),d(h=>h.slice(1))};return K.createElement("div",{ref:l,style:{position:"relative",width:"100%",height:"100%"}},c!=null&&o&&f.map(h=>{var[_,m]=h,g=t.findIndex(y=>{var{id:w}=y;return w===m.id});return K.createElement(ow,{key:_,item:m,offset:Math.round(c.width),pixelsPerSecond:r*i,onEntered:p,onExited:v},n(m,{showSeparator:s||g<t.length-1}))}))},ow=e=>{var{item:t,children:n,offset:r,pixelsPerSecond:i,onEntered:o,onExited:s}=e,a=K.useRef(),[l,c]=K.useState();K.useEffect(()=>{c(a.current.getBoundingClientRect().width)},[]);var u=r+(l!=null?l:0);return K.createElement(nw.div,{ref:a,style:{position:"absolute",left:r},animate:l?{x:-u,transition:{duration:u/i,ease:"linear"}}:!1,onAnimationStart:()=>{window.setTimeout(()=>{o(t)},l/i*1e3)},onAnimationComplete:()=>{s(t)}},n)},Fh={},ao={},ya={},_a={};Object.defineProperty(_a,"__esModule",{value:!0});var sw=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),aw=String.fromCodePoint||function(e){var t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|e&1023),t+=String.fromCharCode(e),t};function lw(e){var t;return e>=55296&&e<=57343||e>1114111?"�":aw((t=sw.get(e))!==null&&t!==void 0?t:e)}_a.default=lw;var Bh={},F0={};Object.defineProperty(F0,"__esModule",{value:!0});F0.default=new Uint16Array([14866,60,237,340,721,1312,1562,1654,1838,1957,2183,2239,2301,2958,3037,3893,4123,4298,4330,4801,5191,5395,5752,5903,5943,5972,6050,0,0,0,0,0,0,6135,6565,7422,8183,8738,9242,9503,9938,10189,10573,10637,10715,11950,12246,13539,13950,14445,14533,15364,16514,16980,17390,17763,17849,18036,18125,4096,69,77,97,98,99,102,103,108,109,110,111,112,114,115,116,117,92,100,106,115,122,137,142,151,157,163,167,182,196,204,220,229,108,105,103,33024,198,59,32768,198,80,33024,38,59,32768,38,99,117,116,101,33024,193,59,32768,193,114,101,118,101,59,32768,258,512,105,121,127,134,114,99,33024,194,59,32768,194,59,32768,1040,114,59,32896,55349,56580,114,97,118,101,33024,192,59,32768,192,112,104,97,59,32768,913,97,99,114,59,32768,256,100,59,32768,10835,512,103,112,172,177,111,110,59,32768,260,102,59,32896,55349,56632,112,108,121,70,117,110,99,116,105,111,110,59,32768,8289,105,110,103,33024,197,59,32768,197,512,99,115,209,214,114,59,32896,55349,56476,105,103,110,59,32768,8788,105,108,100,101,33024,195,59,32768,195,109,108,33024,196,59,32768,196,2048,97,99,101,102,111,114,115,117,253,278,282,310,315,321,327,332,512,99,114,258,267,107,115,108,97,115,104,59,32768,8726,583,271,274,59,32768,10983,101,100,59,32768,8966,121,59,32768,1041,768,99,114,116,289,296,306,97,117,115,101,59,32768,8757,110,111,117,108,108,105,115,59,32768,8492,97,59,32768,914,114,59,32896,55349,56581,112,102,59,32896,55349,56633,101,118,101,59,32768,728,99,114,59,32768,8492,109,112,101,113,59,32768,8782,3584,72,79,97,99,100,101,102,104,105,108,111,114,115,117,368,373,380,426,461,466,487,491,495,533,593,695,701,707,99,121,59,32768,1063,80,89,33024,169,59,32768,169,768,99,112,121,387,393,419,117,116,101,59,32768,262,512,59,105,398,400,32768,8914,116,97,108,68,105,102,102,101,114,101,110,116,105,97,108,68,59,32768,8517,108,101,121,115,59,32768,8493,1024,97,101,105,111,435,441,449,454,114,111,110,59,32768,268,100,105,108,33024,199,59,32768,199,114,99,59,32768,264,110,105,110,116,59,32768,8752,111,116,59,32768,266,512,100,110,471,478,105,108,108,97,59,32768,184,116,101,114,68,111,116,59,32768,183,114,59,32768,8493,105,59,32768,935,114,99,108,101,1024,68,77,80,84,508,513,520,526,111,116,59,32768,8857,105,110,117,115,59,32768,8854,108,117,115,59,32768,8853,105,109,101,115,59,32768,8855,111,512,99,115,539,562,107,119,105,115,101,67,111,110,116,111,117,114,73,110,116,101,103,114,97,108,59,32768,8754,101,67,117,114,108,121,512,68,81,573,586,111,117,98,108,101,81,117,111,116,101,59,32768,8221,117,111,116,101,59,32768,8217,1024,108,110,112,117,602,614,648,664,111,110,512,59,101,609,611,32768,8759,59,32768,10868,768,103,105,116,621,629,634,114,117,101,110,116,59,32768,8801,110,116,59,32768,8751,111,117,114,73,110,116,101,103,114,97,108,59,32768,8750,512,102,114,653,656,59,32768,8450,111,100,117,99,116,59,32768,8720,110,116,101,114,67,108,111,99,107,119,105,115,101,67,111,110,116,111,117,114,73,110,116,101,103,114,97,108,59,32768,8755,111,115,115,59,32768,10799,99,114,59,32896,55349,56478,112,512,59,67,713,715,32768,8915,97,112,59,32768,8781,2816,68,74,83,90,97,99,101,102,105,111,115,743,758,763,768,773,795,809,821,826,910,1295,512,59,111,748,750,32768,8517,116,114,97,104,100,59,32768,10513,99,121,59,32768,1026,99,121,59,32768,1029,99,121,59,32768,1039,768,103,114,115,780,786,790,103,101,114,59,32768,8225,114,59,32768,8609,104,118,59,32768,10980,512,97,121,800,806,114,111,110,59,32768,270,59,32768,1044,108,512,59,116,815,817,32768,8711,97,59,32768,916,114,59,32896,55349,56583,512,97,102,831,897,512,99,109,836,891,114,105,116,105,99,97,108,1024,65,68,71,84,852,859,877,884,99,117,116,101,59,32768,180,111,581,864,867,59,32768,729,98,108,101,65,99,117,116,101,59,32768,733,114,97,118,101,59,32768,96,105,108,100,101,59,32768,732,111,110,100,59,32768,8900,102,101,114,101,110,116,105,97,108,68,59,32768,8518,2113,920,0,0,0,925,946,0,1139,102,59,32896,55349,56635,768,59,68,69,931,933,938,32768,168,111,116,59,32768,8412,113,117,97,108,59,32768,8784,98,108,101,1536,67,68,76,82,85,86,961,978,996,1080,1101,1125,111,110,116,111,117,114,73,110,116,101,103,114,97,108,59,32768,8751,111,1093,985,0,0,988,59,32768,168,110,65,114,114,111,119,59,32768,8659,512,101,111,1001,1034,102,116,768,65,82,84,1010,1017,1029,114,114,111,119,59,32768,8656,105,103,104,116,65,114,114,111,119,59,32768,8660,101,101,59,32768,10980,110,103,512,76,82,1041,1068,101,102,116,512,65,82,1049,1056,114,114,111,119,59,32768,10232,105,103,104,116,65,114,114,111,119,59,32768,10234,105,103,104,116,65,114,114,111,119,59,32768,10233,105,103,104,116,512,65,84,1089,1096,114,114,111,119,59,32768,8658,101,101,59,32768,8872,112,1042,1108,0,0,1115,114,114,111,119,59,32768,8657,111,119,110,65,114,114,111,119,59,32768,8661,101,114,116,105,99,97,108,66,97,114,59,32768,8741,110,1536,65,66,76,82,84,97,1152,1179,1186,1236,1272,1288,114,114,111,119,768,59,66,85,1163,1165,1170,32768,8595,97,114,59,32768,10515,112,65,114,114,111,119,59,32768,8693,114,101,118,101,59,32768,785,101,102,116,1315,1196,0,1209,0,1220,105,103,104,116,86,101,99,116,111,114,59,32768,10576,101,101,86,101,99,116,111,114,59,32768,10590,101,99,116,111,114,512,59,66,1229,1231,32768,8637,97,114,59,32768,10582,105,103,104,116,805,1245,0,1256,101,101,86,101,99,116,111,114,59,32768,10591,101,99,116,111,114,512,59,66,1265,1267,32768,8641,97,114,59,32768,10583,101,101,512,59,65,1279,1281,32768,8868,114,114,111,119,59,32768,8615,114,114,111,119,59,32768,8659,512,99,116,1300,1305,114,59,32896,55349,56479,114,111,107,59,32768,272,4096,78,84,97,99,100,102,103,108,109,111,112,113,115,116,117,120,1344,1348,1354,1363,1386,1391,1396,1405,1413,1460,1475,1483,1514,1527,1531,1538,71,59,32768,330,72,33024,208,59,32768,208,99,117,116,101,33024,201,59,32768,201,768,97,105,121,1370,1376,1383,114,111,110,59,32768,282,114,99,33024,202,59,32768,202,59,32768,1069,111,116,59,32768,278,114,59,32896,55349,56584,114,97,118,101,33024,200,59,32768,200,101,109,101,110,116,59,32768,8712,512,97,112,1418,1423,99,114,59,32768,274,116,121,1060,1431,0,0,1444,109,97,108,108,83,113,117,97,114,101,59,32768,9723,101,114,121,83,109,97,108,108,83,113,117,97,114,101,59,32768,9643,512,103,112,1465,1470,111,110,59,32768,280,102,59,32896,55349,56636,115,105,108,111,110,59,32768,917,117,512,97,105,1489,1504,108,512,59,84,1495,1497,32768,10869,105,108,100,101,59,32768,8770,108,105,98,114,105,117,109,59,32768,8652,512,99,105,1519,1523,114,59,32768,8496,109,59,32768,10867,97,59,32768,919,109,108,33024,203,59,32768,203,512,105,112,1543,1549,115,116,115,59,32768,8707,111,110,101,110,116,105,97,108,69,59,32768,8519,1280,99,102,105,111,115,1572,1576,1581,1620,1648,121,59,32768,1060,114,59,32896,55349,56585,108,108,101,100,1060,1591,0,0,1604,109,97,108,108,83,113,117,97,114,101,59,32768,9724,101,114,121,83,109,97,108,108,83,113,117,97,114,101,59,32768,9642,1601,1628,0,1633,0,0,1639,102,59,32896,55349,56637,65,108,108,59,32768,8704,114,105,101,114,116,114,102,59,32768,8497,99,114,59,32768,8497,3072,74,84,97,98,99,100,102,103,111,114,115,116,1678,1683,1688,1701,1708,1729,1734,1739,1742,1748,1828,1834,99,121,59,32768,1027,33024,62,59,32768,62,109,109,97,512,59,100,1696,1698,32768,915,59,32768,988,114,101,118,101,59,32768,286,768,101,105,121,1715,1721,1726,100,105,108,59,32768,290,114,99,59,32768,284,59,32768,1043,111,116,59,32768,288,114,59,32896,55349,56586,59,32768,8921,112,102,59,32896,55349,56638,101,97,116,101,114,1536,69,70,71,76,83,84,1766,1783,1794,1803,1809,1821,113,117,97,108,512,59,76,1775,1777,32768,8805,101,115,115,59,32768,8923,117,108,108,69,113,117,97,108,59,32768,8807,114,101,97,116,101,114,59,32768,10914,101,115,115,59,32768,8823,108,97,110,116,69,113,117,97,108,59,32768,10878,105,108,100,101,59,32768,8819,99,114,59,32896,55349,56482,59,32768,8811,2048,65,97,99,102,105,111,115,117,1854,1861,1874,1880,1884,1897,1919,1934,82,68,99,121,59,32768,1066,512,99,116,1866,1871,101,107,59,32768,711,59,32768,94,105,114,99,59,32768,292,114,59,32768,8460,108,98,101,114,116,83,112,97,99,101,59,32768,8459,833,1902,0,1906,102,59,32768,8461,105,122,111,110,116,97,108,76,105,110,101,59,32768,9472,512,99,116,1924,1928,114,59,32768,8459,114,111,107,59,32768,294,109,112,533,1940,1950,111,119,110,72,117,109,112,59,32768,8782,113,117,97,108,59,32768,8783,3584,69,74,79,97,99,100,102,103,109,110,111,115,116,117,1985,1990,1996,2001,2010,2025,2030,2034,2043,2077,2134,2155,2160,2167,99,121,59,32768,1045,108,105,103,59,32768,306,99,121,59,32768,1025,99,117,116,101,33024,205,59,32768,205,512,105,121,2015,2022,114,99,33024,206,59,32768,206,59,32768,1048,111,116,59,32768,304,114,59,32768,8465,114,97,118,101,33024,204,59,32768,204,768,59,97,112,2050,2052,2070,32768,8465,512,99,103,2057,2061,114,59,32768,298,105,110,97,114,121,73,59,32768,8520,108,105,101,115,59,32768,8658,837,2082,0,2110,512,59,101,2086,2088,32768,8748,512,103,114,2093,2099,114,97,108,59,32768,8747,115,101,99,116,105,111,110,59,32768,8898,105,115,105,98,108,101,512,67,84,2120,2127,111,109,109,97,59,32768,8291,105,109,101,115,59,32768,8290,768,103,112,116,2141,2146,2151,111,110,59,32768,302,102,59,32896,55349,56640,97,59,32768,921,99,114,59,32768,8464,105,108,100,101,59,32768,296,828,2172,0,2177,99,121,59,32768,1030,108,33024,207,59,32768,207,1280,99,102,111,115,117,2193,2206,2211,2217,2232,512,105,121,2198,2203,114,99,59,32768,308,59,32768,1049,114,59,32896,55349,56589,112,102,59,32896,55349,56641,820,2222,0,2227,114,59,32896,55349,56485,114,99,121,59,32768,1032,107,99,121,59,32768,1028,1792,72,74,97,99,102,111,115,2253,2258,2263,2269,2283,2288,2294,99,121,59,32768,1061,99,121,59,32768,1036,112,112,97,59,32768,922,512,101,121,2274,2280,100,105,108,59,32768,310,59,32768,1050,114,59,32896,55349,56590,112,102,59,32896,55349,56642,99,114,59,32896,55349,56486,2816,74,84,97,99,101,102,108,109,111,115,116,2323,2328,2333,2374,2396,2775,2780,2797,2804,2934,2954,99,121,59,32768,1033,33024,60,59,32768,60,1280,99,109,110,112,114,2344,2350,2356,2360,2370,117,116,101,59,32768,313,98,100,97,59,32768,923,103,59,32768,10218,108,97,99,101,116,114,102,59,32768,8466,114,59,32768,8606,768,97,101,121,2381,2387,2393,114,111,110,59,32768,317,100,105,108,59,32768,315,59,32768,1051,512,102,115,2401,2702,116,2560,65,67,68,70,82,84,85,86,97,114,2423,2470,2479,2530,2537,2561,2618,2666,2683,2690,512,110,114,2428,2441,103,108,101,66,114,97,99,107,101,116,59,32768,10216,114,111,119,768,59,66,82,2451,2453,2458,32768,8592,97,114,59,32768,8676,105,103,104,116,65,114,114,111,119,59,32768,8646,101,105,108,105,110,103,59,32768,8968,111,838,2485,0,2498,98,108,101,66,114,97,99,107,101,116,59,32768,10214,110,805,2503,0,2514,101,101,86,101,99,116,111,114,59,32768,10593,101,99,116,111,114,512,59,66,2523,2525,32768,8643,97,114,59,32768,10585,108,111,111,114,59,32768,8970,105,103,104,116,512,65,86,2546,2553,114,114,111,119,59,32768,8596,101,99,116,111,114,59,32768,10574,512,101,114,2566,2591,101,768,59,65,86,2574,2576,2583,32768,8867,114,114,111,119,59,32768,8612,101,99,116,111,114,59,32768,10586,105,97,110,103,108,101,768,59,66,69,2604,2606,2611,32768,8882,97,114,59,32768,10703,113,117,97,108,59,32768,8884,112,768,68,84,86,2626,2638,2649,111,119,110,86,101,99,116,111,114,59,32768,10577,101,101,86,101,99,116,111,114,59,32768,10592,101,99,116,111,114,512,59,66,2659,2661,32768,8639,97,114,59,32768,10584,101,99,116,111,114,512,59,66,2676,2678,32768,8636,97,114,59,32768,10578,114,114,111,119,59,32768,8656,105,103,104,116,97,114,114,111,119,59,32768,8660,115,1536,69,70,71,76,83,84,2716,2730,2741,2750,2756,2768,113,117,97,108,71,114,101,97,116,101,114,59,32768,8922,117,108,108,69,113,117,97,108,59,32768,8806,114,101,97,116,101,114,59,32768,8822,101,115,115,59,32768,10913,108,97,110,116,69,113,117,97,108,59,32768,10877,105,108,100,101,59,32768,8818,114,59,32896,55349,56591,512,59,101,2785,2787,32768,8920,102,116,97,114,114,111,119,59,32768,8666,105,100,111,116,59,32768,319,768,110,112,119,2811,2899,2904,103,1024,76,82,108,114,2821,2848,2860,2887,101,102,116,512,65,82,2829,2836,114,114,111,119,59,32768,10229,105,103,104,116,65,114,114,111,119,59,32768,10231,105,103,104,116,65,114,114,111,119,59,32768,10230,101,102,116,512,97,114,2868,2875,114,114,111,119,59,32768,10232,105,103,104,116,97,114,114,111,119,59,32768,10234,105,103,104,116,97,114,114,111,119,59,32768,10233,102,59,32896,55349,56643,101,114,512,76,82,2911,2922,101,102,116,65,114,114,111,119,59,32768,8601,105,103,104,116,65,114,114,111,119,59,32768,8600,768,99,104,116,2941,2945,2948,114,59,32768,8466,59,32768,8624,114,111,107,59,32768,321,59,32768,8810,2048,97,99,101,102,105,111,115,117,2974,2978,2982,3007,3012,3022,3028,3033,112,59,32768,10501,121,59,32768,1052,512,100,108,2987,2998,105,117,109,83,112,97,99,101,59,32768,8287,108,105,110,116,114,102,59,32768,8499,114,59,32896,55349,56592,110,117,115,80,108,117,115,59,32768,8723,112,102,59,32896,55349,56644,99,114,59,32768,8499,59,32768,924,2304,74,97,99,101,102,111,115,116,117,3055,3060,3067,3089,3201,3206,3874,3880,3889,99,121,59,32768,1034,99,117,116,101,59,32768,323,768,97,101,121,3074,3080,3086,114,111,110,59,32768,327,100,105,108,59,32768,325,59,32768,1053,768,103,115,119,3096,3160,3194,97,116,105,118,101,768,77,84,86,3108,3121,3145,101,100,105,117,109,83,112,97,99,101,59,32768,8203,104,105,512,99,110,3128,3137,107,83,112,97,99,101,59,32768,8203,83,112,97,99,101,59,32768,8203,101,114,121,84,104,105,110,83,112,97,99,101,59,32768,8203,116,101,100,512,71,76,3168,3184,114,101,97,116,101,114,71,114,101,97,116,101,114,59,32768,8811,101,115,115,76,101,115,115,59,32768,8810,76,105,110,101,59,32768,10,114,59,32896,55349,56593,1024,66,110,112,116,3215,3222,3238,3242,114,101,97,107,59,32768,8288,66,114,101,97,107,105,110,103,83,112,97,99,101,59,32768,160,102,59,32768,8469,3328,59,67,68,69,71,72,76,78,80,82,83,84,86,3269,3271,3293,3312,3352,3430,3455,3551,3589,3625,3678,3821,3861,32768,10988,512,111,117,3276,3286,110,103,114,117,101,110,116,59,32768,8802,112,67,97,112,59,32768,8813,111,117,98,108,101,86,101,114,116,105,99,97,108,66,97,114,59,32768,8742,768,108,113,120,3319,3327,3345,101,109,101,110,116,59,32768,8713,117,97,108,512,59,84,3335,3337,32768,8800,105,108,100,101,59,32896,8770,824,105,115,116,115,59,32768,8708,114,101,97,116,101,114,1792,59,69,70,71,76,83,84,3373,3375,3382,3394,3404,3410,3423,32768,8815,113,117,97,108,59,32768,8817,117,108,108,69,113,117,97,108,59,32896,8807,824,114,101,97,116,101,114,59,32896,8811,824,101,115,115,59,32768,8825,108,97,110,116,69,113,117,97,108,59,32896,10878,824,105,108,100,101,59,32768,8821,117,109,112,533,3437,3448,111,119,110,72,117,109,112,59,32896,8782,824,113,117,97,108,59,32896,8783,824,101,512,102,115,3461,3492,116,84,114,105,97,110,103,108,101,768,59,66,69,3477,3479,3485,32768,8938,97,114,59,32896,10703,824,113,117,97,108,59,32768,8940,115,1536,59,69,71,76,83,84,3506,3508,3515,3524,3531,3544,32768,8814,113,117,97,108,59,32768,8816,114,101,97,116,101,114,59,32768,8824,101,115,115,59,32896,8810,824,108,97,110,116,69,113,117,97,108,59,32896,10877,824,105,108,100,101,59,32768,8820,101,115,116,101,100,512,71,76,3561,3578,114,101,97,116,101,114,71,114,101,97,116,101,114,59,32896,10914,824,101,115,115,76,101,115,115,59,32896,10913,824,114,101,99,101,100,101,115,768,59,69,83,3603,3605,3613,32768,8832,113,117,97,108,59,32896,10927,824,108,97,110,116,69,113,117,97,108,59,32768,8928,512,101,105,3630,3645,118,101,114,115,101,69,108,101,109,101,110,116,59,32768,8716,103,104,116,84,114,105,97,110,103,108,101,768,59,66,69,3663,3665,3671,32768,8939,97,114,59,32896,10704,824,113,117,97,108,59,32768,8941,512,113,117,3683,3732,117,97,114,101,83,117,512,98,112,3694,3712,115,101,116,512,59,69,3702,3705,32896,8847,824,113,117,97,108,59,32768,8930,101,114,115,101,116,512,59,69,3722,3725,32896,8848,824,113,117,97,108,59,32768,8931,768,98,99,112,3739,3757,3801,115,101,116,512,59,69,3747,3750,32896,8834,8402,113,117,97,108,59,32768,8840,99,101,101,100,115,1024,59,69,83,84,3771,3773,3781,3793,32768,8833,113,117,97,108,59,32896,10928,824,108,97,110,116,69,113,117,97,108,59,32768,8929,105,108,100,101,59,32896,8831,824,101,114,115,101,116,512,59,69,3811,3814,32896,8835,8402,113,117,97,108,59,32768,8841,105,108,100,101,1024,59,69,70,84,3834,3836,3843,3854,32768,8769,113,117,97,108,59,32768,8772,117,108,108,69,113,117,97,108,59,32768,8775,105,108,100,101,59,32768,8777,101,114,116,105,99,97,108,66,97,114,59,32768,8740,99,114,59,32896,55349,56489,105,108,100,101,33024,209,59,32768,209,59,32768,925,3584,69,97,99,100,102,103,109,111,112,114,115,116,117,118,3921,3927,3936,3951,3958,3963,3972,3996,4002,4034,4037,4055,4071,4078,108,105,103,59,32768,338,99,117,116,101,33024,211,59,32768,211,512,105,121,3941,3948,114,99,33024,212,59,32768,212,59,32768,1054,98,108,97,99,59,32768,336,114,59,32896,55349,56594,114,97,118,101,33024,210,59,32768,210,768,97,101,105,3979,3984,3989,99,114,59,32768,332,103,97,59,32768,937,99,114,111,110,59,32768,927,112,102,59,32896,55349,56646,101,110,67,117,114,108,121,512,68,81,4014,4027,111,117,98,108,101,81,117,111,116,101,59,32768,8220,117,111,116,101,59,32768,8216,59,32768,10836,512,99,108,4042,4047,114,59,32896,55349,56490,97,115,104,33024,216,59,32768,216,105,573,4060,4067,100,101,33024,213,59,32768,213,101,115,59,32768,10807,109,108,33024,214,59,32768,214,101,114,512,66,80,4085,4109,512,97,114,4090,4094,114,59,32768,8254,97,99,512,101,107,4101,4104,59,32768,9182,101,116,59,32768,9140,97,114,101,110,116,104,101,115,105,115,59,32768,9180,2304,97,99,102,104,105,108,111,114,115,4141,4150,4154,4159,4163,4166,4176,4198,4284,114,116,105,97,108,68,59,32768,8706,121,59,32768,1055,114,59,32896,55349,56595,105,59,32768,934,59,32768,928,117,115,77,105,110,117,115,59,32768,177,512,105,112,4181,4194,110,99,97,114,101,112,108,97,110,101,59,32768,8460,102,59,32768,8473,1024,59,101,105,111,4207,4209,4251,4256,32768,10939,99,101,100,101,115,1024,59,69,83,84,4223,4225,4232,4244,32768,8826,113,117,97,108,59,32768,10927,108,97,110,116,69,113,117,97,108,59,32768,8828,105,108,100,101,59,32768,8830,109,101,59,32768,8243,512,100,112,4261,4267,117,99,116,59,32768,8719,111,114,116,105,111,110,512,59,97,4278,4280,32768,8759,108,59,32768,8733,512,99,105,4289,4294,114,59,32896,55349,56491,59,32768,936,1024,85,102,111,115,4306,4313,4318,4323,79,84,33024,34,59,32768,34,114,59,32896,55349,56596,112,102,59,32768,8474,99,114,59,32896,55349,56492,3072,66,69,97,99,101,102,104,105,111,114,115,117,4354,4360,4366,4395,4417,4473,4477,4481,4743,4764,4776,4788,97,114,114,59,32768,10512,71,33024,174,59,32768,174,768,99,110,114,4373,4379,4383,117,116,101,59,32768,340,103,59,32768,10219,114,512,59,116,4389,4391,32768,8608,108,59,32768,10518,768,97,101,121,4402,4408,4414,114,111,110,59,32768,344,100,105,108,59,32768,342,59,32768,1056,512,59,118,4422,4424,32768,8476,101,114,115,101,512,69,85,4433,4458,512,108,113,4438,4446,101,109,101,110,116,59,32768,8715,117,105,108,105,98,114,105,117,109,59,32768,8651,112,69,113,117,105,108,105,98,114,105,117,109,59,32768,10607,114,59,32768,8476,111,59,32768,929,103,104,116,2048,65,67,68,70,84,85,86,97,4501,4547,4556,4607,4614,4671,4719,4736,512,110,114,4506,4519,103,108,101,66,114,97,99,107,101,116,59,32768,10217,114,111,119,768,59,66,76,4529,4531,4536,32768,8594,97,114,59,32768,8677,101,102,116,65,114,114,111,119,59,32768,8644,101,105,108,105,110,103,59,32768,8969,111,838,4562,0,4575,98,108,101,66,114,97,99,107,101,116,59,32768,10215,110,805,4580,0,4591,101,101,86,101,99,116,111,114,59,32768,10589,101,99,116,111,114,512,59,66,4600,4602,32768,8642,97,114,59,32768,10581,108,111,111,114,59,32768,8971,512,101,114,4619,4644,101,768,59,65,86,4627,4629,4636,32768,8866,114,114,111,119,59,32768,8614,101,99,116,111,114,59,32768,10587,105,97,110,103,108,101,768,59,66,69,4657,4659,4664,32768,8883,97,114,59,32768,10704,113,117,97,108,59,32768,8885,112,768,68,84,86,4679,4691,4702,111,119,110,86,101,99,116,111,114,59,32768,10575,101,101,86,101,99,116,111,114,59,32768,10588,101,99,116,111,114,512,59,66,4712,4714,32768,8638,97,114,59,32768,10580,101,99,116,111,114,512,59,66,4729,4731,32768,8640,97,114,59,32768,10579,114,114,111,119,59,32768,8658,512,112,117,4748,4752,102,59,32768,8477,110,100,73,109,112,108,105,101,115,59,32768,10608,105,103,104,116,97,114,114,111,119,59,32768,8667,512,99,104,4781,4785,114,59,32768,8475,59,32768,8625,108,101,68,101,108,97,121,101,100,59,32768,10740,3328,72,79,97,99,102,104,105,109,111,113,115,116,117,4827,4842,4849,4856,4889,4894,4949,4955,4967,4973,5059,5065,5070,512,67,99,4832,4838,72,99,121,59,32768,1065,121,59,32768,1064,70,84,99,121,59,32768,1068,99,117,116,101,59,32768,346,1280,59,97,101,105,121,4867,4869,4875,4881,4886,32768,10940,114,111,110,59,32768,352,100,105,108,59,32768,350,114,99,59,32768,348,59,32768,1057,114,59,32896,55349,56598,111,114,116,1024,68,76,82,85,4906,4917,4928,4940,111,119,110,65,114,114,111,119,59,32768,8595,101,102,116,65,114,114,111,119,59,32768,8592,105,103,104,116,65,114,114,111,119,59,32768,8594,112,65,114,114,111,119,59,32768,8593,103,109,97,59,32768,931,97,108,108,67,105,114,99,108,101,59,32768,8728,112,102,59,32896,55349,56650,1091,4979,0,0,4983,116,59,32768,8730,97,114,101,1024,59,73,83,85,4994,4996,5010,5052,32768,9633,110,116,101,114,115,101,99,116,105,111,110,59,32768,8851,117,512,98,112,5016,5033,115,101,116,512,59,69,5024,5026,32768,8847,113,117,97,108,59,32768,8849,101,114,115,101,116,512,59,69,5043,5045,32768,8848,113,117,97,108,59,32768,8850,110,105,111,110,59,32768,8852,99,114,59,32896,55349,56494,97,114,59,32768,8902,1024,98,99,109,112,5079,5102,5155,5158,512,59,115,5084,5086,32768,8912,101,116,512,59,69,5093,5095,32768,8912,113,117,97,108,59,32768,8838,512,99,104,5107,5148,101,101,100,115,1024,59,69,83,84,5120,5122,5129,5141,32768,8827,113,117,97,108,59,32768,10928,108,97,110,116,69,113,117,97,108,59,32768,8829,105,108,100,101,59,32768,8831,84,104,97,116,59,32768,8715,59,32768,8721,768,59,101,115,5165,5167,5185,32768,8913,114,115,101,116,512,59,69,5176,5178,32768,8835,113,117,97,108,59,32768,8839,101,116,59,32768,8913,2816,72,82,83,97,99,102,104,105,111,114,115,5213,5221,5227,5241,5252,5274,5279,5323,5362,5368,5378,79,82,78,33024,222,59,32768,222,65,68,69,59,32768,8482,512,72,99,5232,5237,99,121,59,32768,1035,121,59,32768,1062,512,98,117,5246,5249,59,32768,9,59,32768,932,768,97,101,121,5259,5265,5271,114,111,110,59,32768,356,100,105,108,59,32768,354,59,32768,1058,114,59,32896,55349,56599,512,101,105,5284,5300,835,5289,0,5297,101,102,111,114,101,59,32768,8756,97,59,32768,920,512,99,110,5305,5315,107,83,112,97,99,101,59,32896,8287,8202,83,112,97,99,101,59,32768,8201,108,100,101,1024,59,69,70,84,5335,5337,5344,5355,32768,8764,113,117,97,108,59,32768,8771,117,108,108,69,113,117,97,108,59,32768,8773,105,108,100,101,59,32768,8776,112,102,59,32896,55349,56651,105,112,108,101,68,111,116,59,32768,8411,512,99,116,5383,5388,114,59,32896,55349,56495,114,111,107,59,32768,358,5426,5417,5444,5458,5473,0,5480,5485,0,0,0,0,0,5494,5500,5564,5579,0,5726,5732,5738,5745,512,99,114,5421,5429,117,116,101,33024,218,59,32768,218,114,512,59,111,5435,5437,32768,8607,99,105,114,59,32768,10569,114,820,5449,0,5453,121,59,32768,1038,118,101,59,32768,364,512,105,121,5462,5469,114,99,33024,219,59,32768,219,59,32768,1059,98,108,97,99,59,32768,368,114,59,32896,55349,56600,114,97,118,101,33024,217,59,32768,217,97,99,114,59,32768,362,512,100,105,5504,5548,101,114,512,66,80,5511,5535,512,97,114,5516,5520,114,59,32768,95,97,99,512,101,107,5527,5530,59,32768,9183,101,116,59,32768,9141,97,114,101,110,116,104,101,115,105,115,59,32768,9181,111,110,512,59,80,5555,5557,32768,8899,108,117,115,59,32768,8846,512,103,112,5568,5573,111,110,59,32768,370,102,59,32896,55349,56652,2048,65,68,69,84,97,100,112,115,5595,5624,5635,5648,5664,5671,5682,5712,114,114,111,119,768,59,66,68,5606,5608,5613,32768,8593,97,114,59,32768,10514,111,119,110,65,114,114,111,119,59,32768,8645,111,119,110,65,114,114,111,119,59,32768,8597,113,117,105,108,105,98,114,105,117,109,59,32768,10606,101,101,512,59,65,5655,5657,32768,8869,114,114,111,119,59,32768,8613,114,114,111,119,59,32768,8657,111,119,110,97,114,114,111,119,59,32768,8661,101,114,512,76,82,5689,5700,101,102,116,65,114,114,111,119,59,32768,8598,105,103,104,116,65,114,114,111,119,59,32768,8599,105,512,59,108,5718,5720,32768,978,111,110,59,32768,933,105,110,103,59,32768,366,99,114,59,32896,55349,56496,105,108,100,101,59,32768,360,109,108,33024,220,59,32768,220,2304,68,98,99,100,101,102,111,115,118,5770,5776,5781,5785,5798,5878,5883,5889,5895,97,115,104,59,32768,8875,97,114,59,32768,10987,121,59,32768,1042,97,115,104,512,59,108,5793,5795,32768,8873,59,32768,10982,512,101,114,5803,5806,59,32768,8897,768,98,116,121,5813,5818,5866,97,114,59,32768,8214,512,59,105,5823,5825,32768,8214,99,97,108,1024,66,76,83,84,5837,5842,5848,5859,97,114,59,32768,8739,105,110,101,59,32768,124,101,112,97,114,97,116,111,114,59,32768,10072,105,108,100,101,59,32768,8768,84,104,105,110,83,112,97,99,101,59,32768,8202,114,59,32896,55349,56601,112,102,59,32896,55349,56653,99,114,59,32896,55349,56497,100,97,115,104,59,32768,8874,1280,99,101,102,111,115,5913,5919,5925,5930,5936,105,114,99,59,32768,372,100,103,101,59,32768,8896,114,59,32896,55349,56602,112,102,59,32896,55349,56654,99,114,59,32896,55349,56498,1024,102,105,111,115,5951,5956,5959,5965,114,59,32896,55349,56603,59,32768,926,112,102,59,32896,55349,56655,99,114,59,32896,55349,56499,2304,65,73,85,97,99,102,111,115,117,5990,5995,6e3,6005,6014,6027,6032,6038,6044,99,121,59,32768,1071,99,121,59,32768,1031,99,121,59,32768,1070,99,117,116,101,33024,221,59,32768,221,512,105,121,6019,6024,114,99,59,32768,374,59,32768,1067,114,59,32896,55349,56604,112,102,59,32896,55349,56656,99,114,59,32896,55349,56500,109,108,59,32768,376,2048,72,97,99,100,101,102,111,115,6066,6071,6078,6092,6097,6119,6123,6128,99,121,59,32768,1046,99,117,116,101,59,32768,377,512,97,121,6083,6089,114,111,110,59,32768,381,59,32768,1047,111,116,59,32768,379,835,6102,0,6116,111,87,105,100,116,104,83,112,97,99,101,59,32768,8203,97,59,32768,918,114,59,32768,8488,112,102,59,32768,8484,99,114,59,32896,55349,56501,5938,6159,6168,6175,0,6214,6222,6233,0,0,0,0,6242,6267,6290,6429,6444,0,6495,6503,6531,6540,0,6547,99,117,116,101,33024,225,59,32768,225,114,101,118,101,59,32768,259,1536,59,69,100,105,117,121,6187,6189,6193,6196,6203,6210,32768,8766,59,32896,8766,819,59,32768,8767,114,99,33024,226,59,32768,226,116,101,33024,180,59,32768,180,59,32768,1072,108,105,103,33024,230,59,32768,230,512,59,114,6226,6228,32768,8289,59,32896,55349,56606,114,97,118,101,33024,224,59,32768,224,512,101,112,6246,6261,512,102,112,6251,6257,115,121,109,59,32768,8501,104,59,32768,8501,104,97,59,32768,945,512,97,112,6271,6284,512,99,108,6276,6280,114,59,32768,257,103,59,32768,10815,33024,38,59,32768,38,1077,6295,0,0,6326,1280,59,97,100,115,118,6305,6307,6312,6315,6322,32768,8743,110,100,59,32768,10837,59,32768,10844,108,111,112,101,59,32768,10840,59,32768,10842,1792,59,101,108,109,114,115,122,6340,6342,6345,6349,6391,6410,6422,32768,8736,59,32768,10660,101,59,32768,8736,115,100,512,59,97,6356,6358,32768,8737,2098,6368,6371,6374,6377,6380,6383,6386,6389,59,32768,10664,59,32768,10665,59,32768,10666,59,32768,10667,59,32768,10668,59,32768,10669,59,32768,10670,59,32768,10671,116,512,59,118,6397,6399,32768,8735,98,512,59,100,6405,6407,32768,8894,59,32768,10653,512,112,116,6415,6419,104,59,32768,8738,59,32768,197,97,114,114,59,32768,9084,512,103,112,6433,6438,111,110,59,32768,261,102,59,32896,55349,56658,1792,59,69,97,101,105,111,112,6458,6460,6463,6469,6472,6476,6480,32768,8776,59,32768,10864,99,105,114,59,32768,10863,59,32768,8778,100,59,32768,8779,115,59,32768,39,114,111,120,512,59,101,6488,6490,32768,8776,113,59,32768,8778,105,110,103,33024,229,59,32768,229,768,99,116,121,6509,6514,6517,114,59,32896,55349,56502,59,32768,42,109,112,512,59,101,6524,6526,32768,8776,113,59,32768,8781,105,108,100,101,33024,227,59,32768,227,109,108,33024,228,59,32768,228,512,99,105,6551,6559,111,110,105,110,116,59,32768,8755,110,116,59,32768,10769,4096,78,97,98,99,100,101,102,105,107,108,110,111,112,114,115,117,6597,6602,6673,6688,6701,6707,6768,6773,6891,6898,6999,7023,7309,7316,7334,7383,111,116,59,32768,10989,512,99,114,6607,6652,107,1024,99,101,112,115,6617,6623,6632,6639,111,110,103,59,32768,8780,112,115,105,108,111,110,59,32768,1014,114,105,109,101,59,32768,8245,105,109,512,59,101,6646,6648,32768,8765,113,59,32768,8909,583,6656,6661,101,101,59,32768,8893,101,100,512,59,103,6667,6669,32768,8965,101,59,32768,8965,114,107,512,59,116,6680,6682,32768,9141,98,114,107,59,32768,9142,512,111,121,6693,6698,110,103,59,32768,8780,59,32768,1073,113,117,111,59,32768,8222,1280,99,109,112,114,116,6718,6731,6738,6743,6749,97,117,115,512,59,101,6726,6728,32768,8757,59,32768,8757,112,116,121,118,59,32768,10672,115,105,59,32768,1014,110,111,117,59,32768,8492,768,97,104,119,6756,6759,6762,59,32768,946,59,32768,8502,101,101,110,59,32768,8812,114,59,32896,55349,56607,103,1792,99,111,115,116,117,118,119,6789,6809,6834,6850,6872,6879,6884,768,97,105,117,6796,6800,6805,112,59,32768,8898,114,99,59,32768,9711,112,59,32768,8899,768,100,112,116,6816,6821,6827,111,116,59,32768,10752,108,117,115,59,32768,10753,105,109,101,115,59,32768,10754,1090,6840,0,0,6846,99,117,112,59,32768,10758,97,114,59,32768,9733,114,105,97,110,103,108,101,512,100,117,6862,6868,111,119,110,59,32768,9661,112,59,32768,9651,112,108,117,115,59,32768,10756,101,101,59,32768,8897,101,100,103,101,59,32768,8896,97,114,111,119,59,32768,10509,768,97,107,111,6905,6976,6994,512,99,110,6910,6972,107,768,108,115,116,6918,6927,6935,111,122,101,110,103,101,59,32768,10731,113,117,97,114,101,59,32768,9642,114,105,97,110,103,108,101,1024,59,100,108,114,6951,6953,6959,6965,32768,9652,111,119,110,59,32768,9662,101,102,116,59,32768,9666,105,103,104,116,59,32768,9656,107,59,32768,9251,770,6981,0,6991,771,6985,0,6988,59,32768,9618,59,32768,9617,52,59,32768,9619,99,107,59,32768,9608,512,101,111,7004,7019,512,59,113,7009,7012,32896,61,8421,117,105,118,59,32896,8801,8421,116,59,32768,8976,1024,112,116,119,120,7032,7037,7049,7055,102,59,32896,55349,56659,512,59,116,7042,7044,32768,8869,111,109,59,32768,8869,116,105,101,59,32768,8904,3072,68,72,85,86,98,100,104,109,112,116,117,118,7080,7101,7126,7147,7182,7187,7208,7233,7240,7246,7253,7274,1024,76,82,108,114,7089,7092,7095,7098,59,32768,9559,59,32768,9556,59,32768,9558,59,32768,9555,1280,59,68,85,100,117,7112,7114,7117,7120,7123,32768,9552,59,32768,9574,59,32768,9577,59,32768,9572,59,32768,9575,1024,76,82,108,114,7135,7138,7141,7144,59,32768,9565,59,32768,9562,59,32768,9564,59,32768,9561,1792,59,72,76,82,104,108,114,7162,7164,7167,7170,7173,7176,7179,32768,9553,59,32768,9580,59,32768,9571,59,32768,9568,59,32768,9579,59,32768,9570,59,32768,9567,111,120,59,32768,10697,1024,76,82,108,114,7196,7199,7202,7205,59,32768,9557,59,32768,9554,59,32768,9488,59,32768,9484,1280,59,68,85,100,117,7219,7221,7224,7227,7230,32768,9472,59,32768,9573,59,32768,9576,59,32768,9516,59,32768,9524,105,110,117,115,59,32768,8863,108,117,115,59,32768,8862,105,109,101,115,59,32768,8864,1024,76,82,108,114,7262,7265,7268,7271,59,32768,9563,59,32768,9560,59,32768,9496,59,32768,9492,1792,59,72,76,82,104,108,114,7289,7291,7294,7297,7300,7303,7306,32768,9474,59,32768,9578,59,32768,9569,59,32768,9566,59,32768,9532,59,32768,9508,59,32768,9500,114,105,109,101,59,32768,8245,512,101,118,7321,7326,118,101,59,32768,728,98,97,114,33024,166,59,32768,166,1024,99,101,105,111,7343,7348,7353,7364,114,59,32896,55349,56503,109,105,59,32768,8271,109,512,59,101,7359,7361,32768,8765,59,32768,8909,108,768,59,98,104,7372,7374,7377,32768,92,59,32768,10693,115,117,98,59,32768,10184,573,7387,7399,108,512,59,101,7392,7394,32768,8226,116,59,32768,8226,112,768,59,69,101,7406,7408,7411,32768,8782,59,32768,10926,512,59,113,7416,7418,32768,8783,59,32768,8783,6450,7448,0,7523,7571,7576,7613,0,7618,7647,0,0,7764,0,0,7779,0,0,7899,7914,7949,7955,0,8158,0,8176,768,99,112,114,7454,7460,7509,117,116,101,59,32768,263,1536,59,97,98,99,100,115,7473,7475,7480,7487,7500,7505,32768,8745,110,100,59,32768,10820,114,99,117,112,59,32768,10825,512,97,117,7492,7496,112,59,32768,10827,112,59,32768,10823,111,116,59,32768,10816,59,32896,8745,65024,512,101,111,7514,7518,116,59,32768,8257,110,59,32768,711,1024,97,101,105,117,7531,7544,7552,7557,833,7536,0,7540,115,59,32768,10829,111,110,59,32768,269,100,105,108,33024,231,59,32768,231,114,99,59,32768,265,112,115,512,59,115,7564,7566,32768,10828,109,59,32768,10832,111,116,59,32768,267,768,100,109,110,7582,7589,7596,105,108,33024,184,59,32768,184,112,116,121,118,59,32768,10674,116,33280,162,59,101,7603,7605,32768,162,114,100,111,116,59,32768,183,114,59,32896,55349,56608,768,99,101,105,7624,7628,7643,121,59,32768,1095,99,107,512,59,109,7635,7637,32768,10003,97,114,107,59,32768,10003,59,32768,967,114,1792,59,69,99,101,102,109,115,7662,7664,7667,7742,7745,7752,7757,32768,9675,59,32768,10691,768,59,101,108,7674,7676,7680,32768,710,113,59,32768,8791,101,1074,7687,0,0,7709,114,114,111,119,512,108,114,7695,7701,101,102,116,59,32768,8634,105,103,104,116,59,32768,8635,1280,82,83,97,99,100,7719,7722,7725,7730,7736,59,32768,174,59,32768,9416,115,116,59,32768,8859,105,114,99,59,32768,8858,97,115,104,59,32768,8861,59,32768,8791,110,105,110,116,59,32768,10768,105,100,59,32768,10991,99,105,114,59,32768,10690,117,98,115,512,59,117,7771,7773,32768,9827,105,116,59,32768,9827,1341,7785,7804,7850,0,7871,111,110,512,59,101,7791,7793,32768,58,512,59,113,7798,7800,32768,8788,59,32768,8788,1086,7809,0,0,7820,97,512,59,116,7814,7816,32768,44,59,32768,64,768,59,102,108,7826,7828,7832,32768,8705,110,59,32768,8728,101,512,109,120,7838,7844,101,110,116,59,32768,8705,101,115,59,32768,8450,824,7854,0,7866,512,59,100,7858,7860,32768,8773,111,116,59,32768,10861,110,116,59,32768,8750,768,102,114,121,7877,7881,7886,59,32896,55349,56660,111,100,59,32768,8720,33280,169,59,115,7892,7894,32768,169,114,59,32768,8471,512,97,111,7903,7908,114,114,59,32768,8629,115,115,59,32768,10007,512,99,117,7918,7923,114,59,32896,55349,56504,512,98,112,7928,7938,512,59,101,7933,7935,32768,10959,59,32768,10961,512,59,101,7943,7945,32768,10960,59,32768,10962,100,111,116,59,32768,8943,1792,100,101,108,112,114,118,119,7969,7983,7996,8009,8057,8147,8152,97,114,114,512,108,114,7977,7980,59,32768,10552,59,32768,10549,1089,7989,0,0,7993,114,59,32768,8926,99,59,32768,8927,97,114,114,512,59,112,8004,8006,32768,8630,59,32768,10557,1536,59,98,99,100,111,115,8022,8024,8031,8044,8049,8053,32768,8746,114,99,97,112,59,32768,10824,512,97,117,8036,8040,112,59,32768,10822,112,59,32768,10826,111,116,59,32768,8845,114,59,32768,10821,59,32896,8746,65024,1024,97,108,114,118,8066,8078,8116,8123,114,114,512,59,109,8073,8075,32768,8631,59,32768,10556,121,768,101,118,119,8086,8104,8109,113,1089,8093,0,0,8099,114,101,99,59,32768,8926,117,99,99,59,32768,8927,101,101,59,32768,8910,101,100,103,101,59,32768,8911,101,110,33024,164,59,32768,164,101,97,114,114,111,119,512,108,114,8134,8140,101,102,116,59,32768,8630,105,103,104,116,59,32768,8631,101,101,59,32768,8910,101,100,59,32768,8911,512,99,105,8162,8170,111,110,105,110,116,59,32768,8754,110,116,59,32768,8753,108,99,116,121,59,32768,9005,4864,65,72,97,98,99,100,101,102,104,105,106,108,111,114,115,116,117,119,122,8221,8226,8231,8267,8282,8296,8327,8351,8366,8379,8466,8471,8487,8621,8647,8676,8697,8712,8720,114,114,59,32768,8659,97,114,59,32768,10597,1024,103,108,114,115,8240,8246,8252,8256,103,101,114,59,32768,8224,101,116,104,59,32768,8504,114,59,32768,8595,104,512,59,118,8262,8264,32768,8208,59,32768,8867,572,8271,8278,97,114,111,119,59,32768,10511,97,99,59,32768,733,512,97,121,8287,8293,114,111,110,59,32768,271,59,32768,1076,768,59,97,111,8303,8305,8320,32768,8518,512,103,114,8310,8316,103,101,114,59,32768,8225,114,59,32768,8650,116,115,101,113,59,32768,10871,768,103,108,109,8334,8339,8344,33024,176,59,32768,176,116,97,59,32768,948,112,116,121,118,59,32768,10673,512,105,114,8356,8362,115,104,116,59,32768,10623,59,32896,55349,56609,97,114,512,108,114,8373,8376,59,32768,8643,59,32768,8642,1280,97,101,103,115,118,8390,8418,8421,8428,8433,109,768,59,111,115,8398,8400,8415,32768,8900,110,100,512,59,115,8407,8409,32768,8900,117,105,116,59,32768,9830,59,32768,9830,59,32768,168,97,109,109,97,59,32768,989,105,110,59,32768,8946,768,59,105,111,8440,8442,8461,32768,247,100,101,33280,247,59,111,8450,8452,32768,247,110,116,105,109,101,115,59,32768,8903,110,120,59,32768,8903,99,121,59,32768,1106,99,1088,8478,0,0,8483,114,110,59,32768,8990,111,112,59,32768,8973,1280,108,112,116,117,119,8498,8504,8509,8556,8570,108,97,114,59,32768,36,102,59,32896,55349,56661,1280,59,101,109,112,115,8520,8522,8535,8542,8548,32768,729,113,512,59,100,8528,8530,32768,8784,111,116,59,32768,8785,105,110,117,115,59,32768,8760,108,117,115,59,32768,8724,113,117,97,114,101,59,32768,8865,98,108,101,98,97,114,119,101,100,103,101,59,32768,8966,110,768,97,100,104,8578,8585,8597,114,114,111,119,59,32768,8595,111,119,110,97,114,114,111,119,115,59,32768,8650,97,114,112,111,111,110,512,108,114,8608,8614,101,102,116,59,32768,8643,105,103,104,116,59,32768,8642,563,8625,8633,107,97,114,111,119,59,32768,10512,1088,8638,0,0,8643,114,110,59,32768,8991,111,112,59,32768,8972,768,99,111,116,8654,8666,8670,512,114,121,8659,8663,59,32896,55349,56505,59,32768,1109,108,59,32768,10742,114,111,107,59,32768,273,512,100,114,8681,8686,111,116,59,32768,8945,105,512,59,102,8692,8694,32768,9663,59,32768,9662,512,97,104,8702,8707,114,114,59,32768,8693,97,114,59,32768,10607,97,110,103,108,101,59,32768,10662,512,99,105,8725,8729,121,59,32768,1119,103,114,97,114,114,59,32768,10239,4608,68,97,99,100,101,102,103,108,109,110,111,112,113,114,115,116,117,120,8774,8788,8807,8844,8849,8852,8866,8895,8929,8977,8989,9004,9046,9136,9151,9171,9184,9199,512,68,111,8779,8784,111,116,59,32768,10871,116,59,32768,8785,512,99,115,8793,8801,117,116,101,33024,233,59,32768,233,116,101,114,59,32768,10862,1024,97,105,111,121,8816,8822,8835,8841,114,111,110,59,32768,283,114,512,59,99,8828,8830,32768,8790,33024,234,59,32768,234,108,111,110,59,32768,8789,59,32768,1101,111,116,59,32768,279,59,32768,8519,512,68,114,8857,8862,111,116,59,32768,8786,59,32896,55349,56610,768,59,114,115,8873,8875,8883,32768,10906,97,118,101,33024,232,59,32768,232,512,59,100,8888,8890,32768,10902,111,116,59,32768,10904,1024,59,105,108,115,8904,8906,8914,8917,32768,10905,110,116,101,114,115,59,32768,9191,59,32768,8467,512,59,100,8922,8924,32768,10901,111,116,59,32768,10903,768,97,112,115,8936,8941,8960,99,114,59,32768,275,116,121,768,59,115,118,8950,8952,8957,32768,8709,101,116,59,32768,8709,59,32768,8709,112,512,49,59,8966,8975,516,8970,8973,59,32768,8196,59,32768,8197,32768,8195,512,103,115,8982,8985,59,32768,331,112,59,32768,8194,512,103,112,8994,8999,111,110,59,32768,281,102,59,32896,55349,56662,768,97,108,115,9011,9023,9028,114,512,59,115,9017,9019,32768,8917,108,59,32768,10723,117,115,59,32768,10865,105,768,59,108,118,9036,9038,9043,32768,949,111,110,59,32768,949,59,32768,1013,1024,99,115,117,118,9055,9071,9099,9128,512,105,111,9060,9065,114,99,59,32768,8790,108,111,110,59,32768,8789,1082,9077,0,0,9081,109,59,32768,8770,97,110,116,512,103,108,9088,9093,116,114,59,32768,10902,101,115,115,59,32768,10901,768,97,101,105,9106,9111,9116,108,115,59,32768,61,115,116,59,32768,8799,118,512,59,68,9122,9124,32768,8801,68,59,32768,10872,112,97,114,115,108,59,32768,10725,512,68,97,9141,9146,111,116,59,32768,8787,114,114,59,32768,10609,768,99,100,105,9158,9162,9167,114,59,32768,8495,111,116,59,32768,8784,109,59,32768,8770,512,97,104,9176,9179,59,32768,951,33024,240,59,32768,240,512,109,114,9189,9195,108,33024,235,59,32768,235,111,59,32768,8364,768,99,105,112,9206,9210,9215,108,59,32768,33,115,116,59,32768,8707,512,101,111,9220,9230,99,116,97,116,105,111,110,59,32768,8496,110,101,110,116,105,97,108,101,59,32768,8519,4914,9262,0,9276,0,9280,9287,0,0,9318,9324,0,9331,0,9352,9357,9386,0,9395,9497,108,108,105,110,103,100,111,116,115,101,113,59,32768,8786,121,59,32768,1092,109,97,108,101,59,32768,9792,768,105,108,114,9293,9299,9313,108,105,103,59,32768,64259,1082,9305,0,0,9309,103,59,32768,64256,105,103,59,32768,64260,59,32896,55349,56611,108,105,103,59,32768,64257,108,105,103,59,32896,102,106,768,97,108,116,9337,9341,9346,116,59,32768,9837,105,103,59,32768,64258,110,115,59,32768,9649,111,102,59,32768,402,833,9361,0,9366,102,59,32896,55349,56663,512,97,107,9370,9375,108,108,59,32768,8704,512,59,118,9380,9382,32768,8916,59,32768,10969,97,114,116,105,110,116,59,32768,10765,512,97,111,9399,9491,512,99,115,9404,9487,1794,9413,9443,9453,9470,9474,0,9484,1795,9421,9426,9429,9434,9437,0,9440,33024,189,59,32768,189,59,32768,8531,33024,188,59,32768,188,59,32768,8533,59,32768,8537,59,32768,8539,772,9447,0,9450,59,32768,8532,59,32768,8534,1285,9459,9464,0,0,9467,33024,190,59,32768,190,59,32768,8535,59,32768,8540,53,59,32768,8536,775,9478,0,9481,59,32768,8538,59,32768,8541,56,59,32768,8542,108,59,32768,8260,119,110,59,32768,8994,99,114,59,32896,55349,56507,4352,69,97,98,99,100,101,102,103,105,106,108,110,111,114,115,116,118,9537,9547,9575,9582,9595,9600,9679,9684,9694,9700,9705,9725,9773,9779,9785,9810,9917,512,59,108,9542,9544,32768,8807,59,32768,10892,768,99,109,112,9554,9560,9572,117,116,101,59,32768,501,109,97,512,59,100,9567,9569,32768,947,59,32768,989,59,32768,10886,114,101,118,101,59,32768,287,512,105,121,9587,9592,114,99,59,32768,285,59,32768,1075,111,116,59,32768,289,1024,59,108,113,115,9609,9611,9614,9633,32768,8805,59,32768,8923,768,59,113,115,9621,9623,9626,32768,8805,59,32768,8807,108,97,110,116,59,32768,10878,1024,59,99,100,108,9642,9644,9648,9667,32768,10878,99,59,32768,10921,111,116,512,59,111,9655,9657,32768,10880,512,59,108,9662,9664,32768,10882,59,32768,10884,512,59,101,9672,9675,32896,8923,65024,115,59,32768,10900,114,59,32896,55349,56612,512,59,103,9689,9691,32768,8811,59,32768,8921,109,101,108,59,32768,8503,99,121,59,32768,1107,1024,59,69,97,106,9714,9716,9719,9722,32768,8823,59,32768,10898,59,32768,10917,59,32768,10916,1024,69,97,101,115,9734,9737,9751,9768,59,32768,8809,112,512,59,112,9743,9745,32768,10890,114,111,120,59,32768,10890,512,59,113,9756,9758,32768,10888,512,59,113,9763,9765,32768,10888,59,32768,8809,105,109,59,32768,8935,112,102,59,32896,55349,56664,97,118,101,59,32768,96,512,99,105,9790,9794,114,59,32768,8458,109,768,59,101,108,9802,9804,9807,32768,8819,59,32768,10894,59,32768,10896,34304,62,59,99,100,108,113,114,9824,9826,9838,9843,9849,9856,32768,62,512,99,105,9831,9834,59,32768,10919,114,59,32768,10874,111,116,59,32768,8919,80,97,114,59,32768,10645,117,101,115,116,59,32768,10876,1280,97,100,101,108,115,9867,9882,9887,9906,9912,833,9872,0,9879,112,114,111,120,59,32768,10886,114,59,32768,10616,111,116,59,32768,8919,113,512,108,113,9893,9899,101,115,115,59,32768,8923,108,101,115,115,59,32768,10892,101,115,115,59,32768,8823,105,109,59,32768,8819,512,101,110,9922,9932,114,116,110,101,113,113,59,32896,8809,65024,69,59,32896,8809,65024,2560,65,97,98,99,101,102,107,111,115,121,9958,9963,10015,10020,10026,10060,10065,10085,10147,10171,114,114,59,32768,8660,1024,105,108,109,114,9972,9978,9982,9988,114,115,112,59,32768,8202,102,59,32768,189,105,108,116,59,32768,8459,512,100,114,9993,9998,99,121,59,32768,1098,768,59,99,119,10005,10007,10012,32768,8596,105,114,59,32768,10568,59,32768,8621,97,114,59,32768,8463,105,114,99,59,32768,293,768,97,108,114,10033,10048,10054,114,116,115,512,59,117,10041,10043,32768,9829,105,116,59,32768,9829,108,105,112,59,32768,8230,99,111,110,59,32768,8889,114,59,32896,55349,56613,115,512,101,119,10071,10078,97,114,111,119,59,32768,10533,97,114,111,119,59,32768,10534,1280,97,109,111,112,114,10096,10101,10107,10136,10141,114,114,59,32768,8703,116,104,116,59,32768,8763,107,512,108,114,10113,10124,101,102,116,97,114,114,111,119,59,32768,8617,105,103,104,116,97,114,114,111,119,59,32768,8618,102,59,32896,55349,56665,98,97,114,59,32768,8213,768,99,108,116,10154,10159,10165,114,59,32896,55349,56509,97,115,104,59,32768,8463,114,111,107,59,32768,295,512,98,112,10176,10182,117,108,108,59,32768,8259,104,101,110,59,32768,8208,5426,10211,0,10220,0,10239,10255,10267,0,10276,10312,0,0,10318,10371,10458,10485,10491,0,10500,10545,10558,99,117,116,101,33024,237,59,32768,237,768,59,105,121,10226,10228,10235,32768,8291,114,99,33024,238,59,32768,238,59,32768,1080,512,99,120,10243,10247,121,59,32768,1077,99,108,33024,161,59,32768,161,512,102,114,10259,10262,59,32768,8660,59,32896,55349,56614,114,97,118,101,33024,236,59,32768,236,1024,59,105,110,111,10284,10286,10300,10306,32768,8520,512,105,110,10291,10296,110,116,59,32768,10764,116,59,32768,8749,102,105,110,59,32768,10716,116,97,59,32768,8489,108,105,103,59,32768,307,768,97,111,112,10324,10361,10365,768,99,103,116,10331,10335,10357,114,59,32768,299,768,101,108,112,10342,10345,10351,59,32768,8465,105,110,101,59,32768,8464,97,114,116,59,32768,8465,104,59,32768,305,102,59,32768,8887,101,100,59,32768,437,1280,59,99,102,111,116,10381,10383,10389,10403,10409,32768,8712,97,114,101,59,32768,8453,105,110,512,59,116,10396,10398,32768,8734,105,101,59,32768,10717,100,111,116,59,32768,305,1280,59,99,101,108,112,10420,10422,10427,10444,10451,32768,8747,97,108,59,32768,8890,512,103,114,10432,10438,101,114,115,59,32768,8484,99,97,108,59,32768,8890,97,114,104,107,59,32768,10775,114,111,100,59,32768,10812,1024,99,103,112,116,10466,10470,10475,10480,121,59,32768,1105,111,110,59,32768,303,102,59,32896,55349,56666,97,59,32768,953,114,111,100,59,32768,10812,117,101,115,116,33024,191,59,32768,191,512,99,105,10504,10509,114,59,32896,55349,56510,110,1280,59,69,100,115,118,10521,10523,10526,10531,10541,32768,8712,59,32768,8953,111,116,59,32768,8949,512,59,118,10536,10538,32768,8948,59,32768,8947,59,32768,8712,512,59,105,10549,10551,32768,8290,108,100,101,59,32768,297,828,10562,0,10567,99,121,59,32768,1110,108,33024,239,59,32768,239,1536,99,102,109,111,115,117,10585,10598,10603,10609,10615,10630,512,105,121,10590,10595,114,99,59,32768,309,59,32768,1081,114,59,32896,55349,56615,97,116,104,59,32768,567,112,102,59,32896,55349,56667,820,10620,0,10625,114,59,32896,55349,56511,114,99,121,59,32768,1112,107,99,121,59,32768,1108,2048,97,99,102,103,104,106,111,115,10653,10666,10680,10685,10692,10697,10702,10708,112,112,97,512,59,118,10661,10663,32768,954,59,32768,1008,512,101,121,10671,10677,100,105,108,59,32768,311,59,32768,1082,114,59,32896,55349,56616,114,101,101,110,59,32768,312,99,121,59,32768,1093,99,121,59,32768,1116,112,102,59,32896,55349,56668,99,114,59,32896,55349,56512,5888,65,66,69,72,97,98,99,100,101,102,103,104,106,108,109,110,111,112,114,115,116,117,118,10761,10783,10789,10799,10804,10957,11011,11047,11094,11349,11372,11382,11409,11414,11451,11478,11526,11698,11711,11755,11823,11910,11929,768,97,114,116,10768,10773,10777,114,114,59,32768,8666,114,59,32768,8656,97,105,108,59,32768,10523,97,114,114,59,32768,10510,512,59,103,10794,10796,32768,8806,59,32768,10891,97,114,59,32768,10594,4660,10824,0,10830,0,10838,0,0,0,0,0,10844,10850,0,10867,10870,10877,0,10933,117,116,101,59,32768,314,109,112,116,121,118,59,32768,10676,114,97,110,59,32768,8466,98,100,97,59,32768,955,103,768,59,100,108,10857,10859,10862,32768,10216,59,32768,10641,101,59,32768,10216,59,32768,10885,117,111,33024,171,59,32768,171,114,2048,59,98,102,104,108,112,115,116,10894,10896,10907,10911,10915,10919,10923,10928,32768,8592,512,59,102,10901,10903,32768,8676,115,59,32768,10527,115,59,32768,10525,107,59,32768,8617,112,59,32768,8619,108,59,32768,10553,105,109,59,32768,10611,108,59,32768,8610,768,59,97,101,10939,10941,10946,32768,10923,105,108,59,32768,10521,512,59,115,10951,10953,32768,10925,59,32896,10925,65024,768,97,98,114,10964,10969,10974,114,114,59,32768,10508,114,107,59,32768,10098,512,97,107,10979,10991,99,512,101,107,10985,10988,59,32768,123,59,32768,91,512,101,115,10996,10999,59,32768,10635,108,512,100,117,11005,11008,59,32768,10639,59,32768,10637,1024,97,101,117,121,11020,11026,11040,11044,114,111,110,59,32768,318,512,100,105,11031,11036,105,108,59,32768,316,108,59,32768,8968,98,59,32768,123,59,32768,1083,1024,99,113,114,115,11056,11060,11072,11090,97,59,32768,10550,117,111,512,59,114,11067,11069,32768,8220,59,32768,8222,512,100,117,11077,11083,104,97,114,59,32768,10599,115,104,97,114,59,32768,10571,104,59,32768,8626,1280,59,102,103,113,115,11105,11107,11228,11231,11250,32768,8804,116,1280,97,104,108,114,116,11119,11136,11157,11169,11216,114,114,111,119,512,59,116,11128,11130,32768,8592,97,105,108,59,32768,8610,97,114,112,111,111,110,512,100,117,11147,11153,111,119,110,59,32768,8637,112,59,32768,8636,101,102,116,97,114,114,111,119,115,59,32768,8647,105,103,104,116,768,97,104,115,11180,11194,11204,114,114,111,119,512,59,115,11189,11191,32768,8596,59,32768,8646,97,114,112,111,111,110,115,59,32768,8651,113,117,105,103,97,114,114,111,119,59,32768,8621,104,114,101,101,116,105,109,101,115,59,32768,8907,59,32768,8922,768,59,113,115,11238,11240,11243,32768,8804,59,32768,8806,108,97,110,116,59,32768,10877,1280,59,99,100,103,115,11261,11263,11267,11286,11298,32768,10877,99,59,32768,10920,111,116,512,59,111,11274,11276,32768,10879,512,59,114,11281,11283,32768,10881,59,32768,10883,512,59,101,11291,11294,32896,8922,65024,115,59,32768,10899,1280,97,100,101,103,115,11309,11317,11322,11339,11344,112,112,114,111,120,59,32768,10885,111,116,59,32768,8918,113,512,103,113,11328,11333,116,114,59,32768,8922,103,116,114,59,32768,10891,116,114,59,32768,8822,105,109,59,32768,8818,768,105,108,114,11356,11362,11368,115,104,116,59,32768,10620,111,111,114,59,32768,8970,59,32896,55349,56617,512,59,69,11377,11379,32768,8822,59,32768,10897,562,11386,11405,114,512,100,117,11391,11394,59,32768,8637,512,59,108,11399,11401,32768,8636,59,32768,10602,108,107,59,32768,9604,99,121,59,32768,1113,1280,59,97,99,104,116,11425,11427,11432,11440,11446,32768,8810,114,114,59,32768,8647,111,114,110,101,114,59,32768,8990,97,114,100,59,32768,10603,114,105,59,32768,9722,512,105,111,11456,11462,100,111,116,59,32768,320,117,115,116,512,59,97,11470,11472,32768,9136,99,104,101,59,32768,9136,1024,69,97,101,115,11487,11490,11504,11521,59,32768,8808,112,512,59,112,11496,11498,32768,10889,114,111,120,59,32768,10889,512,59,113,11509,11511,32768,10887,512,59,113,11516,11518,32768,10887,59,32768,8808,105,109,59,32768,8934,2048,97,98,110,111,112,116,119,122,11543,11556,11561,11616,11640,11660,11667,11680,512,110,114,11548,11552,103,59,32768,10220,114,59,32768,8701,114,107,59,32768,10214,103,768,108,109,114,11569,11596,11604,101,102,116,512,97,114,11577,11584,114,114,111,119,59,32768,10229,105,103,104,116,97,114,114,111,119,59,32768,10231,97,112,115,116,111,59,32768,10236,105,103,104,116,97,114,114,111,119,59,32768,10230,112,97,114,114,111,119,512,108,114,11627,11633,101,102,116,59,32768,8619,105,103,104,116,59,32768,8620,768,97,102,108,11647,11651,11655,114,59,32768,10629,59,32896,55349,56669,117,115,59,32768,10797,105,109,101,115,59,32768,10804,562,11671,11676,115,116,59,32768,8727,97,114,59,32768,95,768,59,101,102,11687,11689,11695,32768,9674,110,103,101,59,32768,9674,59,32768,10731,97,114,512,59,108,11705,11707,32768,40,116,59,32768,10643,1280,97,99,104,109,116,11722,11727,11735,11747,11750,114,114,59,32768,8646,111,114,110,101,114,59,32768,8991,97,114,512,59,100,11742,11744,32768,8651,59,32768,10605,59,32768,8206,114,105,59,32768,8895,1536,97,99,104,105,113,116,11768,11774,11779,11782,11798,11817,113,117,111,59,32768,8249,114,59,32896,55349,56513,59,32768,8624,109,768,59,101,103,11790,11792,11795,32768,8818,59,32768,10893,59,32768,10895,512,98,117,11803,11806,59,32768,91,111,512,59,114,11812,11814,32768,8216,59,32768,8218,114,111,107,59,32768,322,34816,60,59,99,100,104,105,108,113,114,11841,11843,11855,11860,11866,11872,11878,11885,32768,60,512,99,105,11848,11851,59,32768,10918,114,59,32768,10873,111,116,59,32768,8918,114,101,101,59,32768,8907,109,101,115,59,32768,8905,97,114,114,59,32768,10614,117,101,115,116,59,32768,10875,512,80,105,11890,11895,97,114,59,32768,10646,768,59,101,102,11902,11904,11907,32768,9667,59,32768,8884,59,32768,9666,114,512,100,117,11916,11923,115,104,97,114,59,32768,10570,104,97,114,59,32768,10598,512,101,110,11934,11944,114,116,110,101,113,113,59,32896,8808,65024,69,59,32896,8808,65024,3584,68,97,99,100,101,102,104,105,108,110,111,112,115,117,11978,11984,12061,12075,12081,12095,12100,12104,12170,12181,12188,12204,12207,12223,68,111,116,59,32768,8762,1024,99,108,112,114,11993,11999,12019,12055,114,33024,175,59,32768,175,512,101,116,12004,12007,59,32768,9794,512,59,101,12012,12014,32768,10016,115,101,59,32768,10016,512,59,115,12024,12026,32768,8614,116,111,1024,59,100,108,117,12037,12039,12045,12051,32768,8614,111,119,110,59,32768,8615,101,102,116,59,32768,8612,112,59,32768,8613,107,101,114,59,32768,9646,512,111,121,12066,12072,109,109,97,59,32768,10793,59,32768,1084,97,115,104,59,32768,8212,97,115,117,114,101,100,97,110,103,108,101,59,32768,8737,114,59,32896,55349,56618,111,59,32768,8487,768,99,100,110,12111,12118,12146,114,111,33024,181,59,32768,181,1024,59,97,99,100,12127,12129,12134,12139,32768,8739,115,116,59,32768,42,105,114,59,32768,10992,111,116,33024,183,59,32768,183,117,115,768,59,98,100,12155,12157,12160,32768,8722,59,32768,8863,512,59,117,12165,12167,32768,8760,59,32768,10794,564,12174,12178,112,59,32768,10971,114,59,32768,8230,112,108,117,115,59,32768,8723,512,100,112,12193,12199,101,108,115,59,32768,8871,102,59,32896,55349,56670,59,32768,8723,512,99,116,12212,12217,114,59,32896,55349,56514,112,111,115,59,32768,8766,768,59,108,109,12230,12232,12240,32768,956,116,105,109,97,112,59,32768,8888,97,112,59,32768,8888,6144,71,76,82,86,97,98,99,100,101,102,103,104,105,106,108,109,111,112,114,115,116,117,118,119,12294,12315,12364,12376,12393,12472,12496,12547,12553,12636,12641,12703,12725,12747,12752,12876,12881,12957,13033,13089,13294,13359,13384,13499,512,103,116,12299,12303,59,32896,8921,824,512,59,118,12308,12311,32896,8811,8402,59,32896,8811,824,768,101,108,116,12322,12348,12352,102,116,512,97,114,12329,12336,114,114,111,119,59,32768,8653,105,103,104,116,97,114,114,111,119,59,32768,8654,59,32896,8920,824,512,59,118,12357,12360,32896,8810,8402,59,32896,8810,824,105,103,104,116,97,114,114,111,119,59,32768,8655,512,68,100,12381,12387,97,115,104,59,32768,8879,97,115,104,59,32768,8878,1280,98,99,110,112,116,12404,12409,12415,12420,12452,108,97,59,32768,8711,117,116,101,59,32768,324,103,59,32896,8736,8402,1280,59,69,105,111,112,12431,12433,12437,12442,12446,32768,8777,59,32896,10864,824,100,59,32896,8779,824,115,59,32768,329,114,111,120,59,32768,8777,117,114,512,59,97,12459,12461,32768,9838,108,512,59,115,12467,12469,32768,9838,59,32768,8469,836,12477,0,12483,112,33024,160,59,32768,160,109,112,512,59,101,12489,12492,32896,8782,824,59,32896,8783,824,1280,97,101,111,117,121,12507,12519,12525,12540,12544,833,12512,0,12515,59,32768,10819,111,110,59,32768,328,100,105,108,59,32768,326,110,103,512,59,100,12532,12534,32768,8775,111,116,59,32896,10861,824,112,59,32768,10818,59,32768,1085,97,115,104,59,32768,8211,1792,59,65,97,100,113,115,120,12568,12570,12575,12596,12602,12608,12623,32768,8800,114,114,59,32768,8663,114,512,104,114,12581,12585,107,59,32768,10532,512,59,111,12590,12592,32768,8599,119,59,32768,8599,111,116,59,32896,8784,824,117,105,118,59,32768,8802,512,101,105,12613,12618,97,114,59,32768,10536,109,59,32896,8770,824,105,115,116,512,59,115,12631,12633,32768,8708,59,32768,8708,114,59,32896,55349,56619,1024,69,101,115,116,12650,12654,12688,12693,59,32896,8807,824,768,59,113,115,12661,12663,12684,32768,8817,768,59,113,115,12670,12672,12676,32768,8817,59,32896,8807,824,108,97,110,116,59,32896,10878,824,59,32896,10878,824,105,109,59,32768,8821,512,59,114,12698,12700,32768,8815,59,32768,8815,768,65,97,112,12710,12715,12720,114,114,59,32768,8654,114,114,59,32768,8622,97,114,59,32768,10994,768,59,115,118,12732,12734,12744,32768,8715,512,59,100,12739,12741,32768,8956,59,32768,8954,59,32768,8715,99,121,59,32768,1114,1792,65,69,97,100,101,115,116,12767,12772,12776,12781,12785,12853,12858,114,114,59,32768,8653,59,32896,8806,824,114,114,59,32768,8602,114,59,32768,8229,1024,59,102,113,115,12794,12796,12821,12842,32768,8816,116,512,97,114,12802,12809,114,114,111,119,59,32768,8602,105,103,104,116,97,114,114,111,119,59,32768,8622,768,59,113,115,12828,12830,12834,32768,8816,59,32896,8806,824,108,97,110,116,59,32896,10877,824,512,59,115,12847,12850,32896,10877,824,59,32768,8814,105,109,59,32768,8820,512,59,114,12863,12865,32768,8814,105,512,59,101,12871,12873,32768,8938,59,32768,8940,105,100,59,32768,8740,512,112,116,12886,12891,102,59,32896,55349,56671,33536,172,59,105,110,12899,12901,12936,32768,172,110,1024,59,69,100,118,12911,12913,12917,12923,32768,8713,59,32896,8953,824,111,116,59,32896,8949,824,818,12928,12931,12934,59,32768,8713,59,32768,8951,59,32768,8950,105,512,59,118,12942,12944,32768,8716,818,12949,12952,12955,59,32768,8716,59,32768,8958,59,32768,8957,768,97,111,114,12964,12992,12999,114,1024,59,97,115,116,12974,12976,12983,12988,32768,8742,108,108,101,108,59,32768,8742,108,59,32896,11005,8421,59,32896,8706,824,108,105,110,116,59,32768,10772,768,59,99,101,13006,13008,13013,32768,8832,117,101,59,32768,8928,512,59,99,13018,13021,32896,10927,824,512,59,101,13026,13028,32768,8832,113,59,32896,10927,824,1024,65,97,105,116,13042,13047,13066,13077,114,114,59,32768,8655,114,114,768,59,99,119,13056,13058,13062,32768,8603,59,32896,10547,824,59,32896,8605,824,103,104,116,97,114,114,111,119,59,32768,8603,114,105,512,59,101,13084,13086,32768,8939,59,32768,8941,1792,99,104,105,109,112,113,117,13104,13128,13151,13169,13174,13179,13194,1024,59,99,101,114,13113,13115,13120,13124,32768,8833,117,101,59,32768,8929,59,32896,10928,824,59,32896,55349,56515,111,114,116,1086,13137,0,0,13142,105,100,59,32768,8740,97,114,97,108,108,101,108,59,32768,8742,109,512,59,101,13157,13159,32768,8769,512,59,113,13164,13166,32768,8772,59,32768,8772,105,100,59,32768,8740,97,114,59,32768,8742,115,117,512,98,112,13186,13190,101,59,32768,8930,101,59,32768,8931,768,98,99,112,13201,13241,13254,1024,59,69,101,115,13210,13212,13216,13219,32768,8836,59,32896,10949,824,59,32768,8840,101,116,512,59,101,13226,13229,32896,8834,8402,113,512,59,113,13235,13237,32768,8840,59,32896,10949,824,99,512,59,101,13247,13249,32768,8833,113,59,32896,10928,824,1024,59,69,101,115,13263,13265,13269,13272,32768,8837,59,32896,10950,824,59,32768,8841,101,116,512,59,101,13279,13282,32896,8835,8402,113,512,59,113,13288,13290,32768,8841,59,32896,10950,824,1024,103,105,108,114,13303,13307,13315,13319,108,59,32768,8825,108,100,101,33024,241,59,32768,241,103,59,32768,8824,105,97,110,103,108,101,512,108,114,13330,13344,101,102,116,512,59,101,13338,13340,32768,8938,113,59,32768,8940,105,103,104,116,512,59,101,13353,13355,32768,8939,113,59,32768,8941,512,59,109,13364,13366,32768,957,768,59,101,115,13373,13375,13380,32768,35,114,111,59,32768,8470,112,59,32768,8199,2304,68,72,97,100,103,105,108,114,115,13403,13409,13415,13420,13426,13439,13446,13476,13493,97,115,104,59,32768,8877,97,114,114,59,32768,10500,112,59,32896,8781,8402,97,115,104,59,32768,8876,512,101,116,13431,13435,59,32896,8805,8402,59,32896,62,8402,110,102,105,110,59,32768,10718,768,65,101,116,13453,13458,13462,114,114,59,32768,10498,59,32896,8804,8402,512,59,114,13467,13470,32896,60,8402,105,101,59,32896,8884,8402,512,65,116,13481,13486,114,114,59,32768,10499,114,105,101,59,32896,8885,8402,105,109,59,32896,8764,8402,768,65,97,110,13506,13511,13532,114,114,59,32768,8662,114,512,104,114,13517,13521,107,59,32768,10531,512,59,111,13526,13528,32768,8598,119,59,32768,8598,101,97,114,59,32768,10535,9252,13576,0,0,0,0,0,0,0,0,0,0,0,0,0,13579,0,13596,13617,13653,13659,13673,13695,13708,0,0,13713,13750,0,13788,13794,0,13815,13890,13913,13937,13944,59,32768,9416,512,99,115,13583,13591,117,116,101,33024,243,59,32768,243,116,59,32768,8859,512,105,121,13600,13613,114,512,59,99,13606,13608,32768,8858,33024,244,59,32768,244,59,32768,1086,1280,97,98,105,111,115,13627,13632,13638,13642,13646,115,104,59,32768,8861,108,97,99,59,32768,337,118,59,32768,10808,116,59,32768,8857,111,108,100,59,32768,10684,108,105,103,59,32768,339,512,99,114,13663,13668,105,114,59,32768,10687,59,32896,55349,56620,1600,13680,0,0,13684,0,13692,110,59,32768,731,97,118,101,33024,242,59,32768,242,59,32768,10689,512,98,109,13699,13704,97,114,59,32768,10677,59,32768,937,110,116,59,32768,8750,1024,97,99,105,116,13721,13726,13741,13746,114,114,59,32768,8634,512,105,114,13731,13735,114,59,32768,10686,111,115,115,59,32768,10683,110,101,59,32768,8254,59,32768,10688,768,97,101,105,13756,13761,13766,99,114,59,32768,333,103,97,59,32768,969,768,99,100,110,13773,13779,13782,114,111,110,59,32768,959,59,32768,10678,117,115,59,32768,8854,112,102,59,32896,55349,56672,768,97,101,108,13800,13804,13809,114,59,32768,10679,114,112,59,32768,10681,117,115,59,32768,8853,1792,59,97,100,105,111,115,118,13829,13831,13836,13869,13875,13879,13886,32768,8744,114,114,59,32768,8635,1024,59,101,102,109,13845,13847,13859,13864,32768,10845,114,512,59,111,13853,13855,32768,8500,102,59,32768,8500,33024,170,59,32768,170,33024,186,59,32768,186,103,111,102,59,32768,8886,114,59,32768,10838,108,111,112,101,59,32768,10839,59,32768,10843,768,99,108,111,13896,13900,13908,114,59,32768,8500,97,115,104,33024,248,59,32768,248,108,59,32768,8856,105,573,13917,13924,100,101,33024,245,59,32768,245,101,115,512,59,97,13930,13932,32768,8855,115,59,32768,10806,109,108,33024,246,59,32768,246,98,97,114,59,32768,9021,5426,13972,0,14013,0,14017,14053,0,14058,14086,0,0,14107,14199,0,14202,0,0,14229,14425,0,14438,114,1024,59,97,115,116,13981,13983,13997,14009,32768,8741,33280,182,59,108,13989,13991,32768,182,108,101,108,59,32768,8741,1082,14003,0,0,14007,109,59,32768,10995,59,32768,11005,59,32768,8706,121,59,32768,1087,114,1280,99,105,109,112,116,14028,14033,14038,14043,14046,110,116,59,32768,37,111,100,59,32768,46,105,108,59,32768,8240,59,32768,8869,101,110,107,59,32768,8241,114,59,32896,55349,56621,768,105,109,111,14064,14074,14080,512,59,118,14069,14071,32768,966,59,32768,981,109,97,116,59,32768,8499,110,101,59,32768,9742,768,59,116,118,14092,14094,14103,32768,960,99,104,102,111,114,107,59,32768,8916,59,32768,982,512,97,117,14111,14132,110,512,99,107,14117,14128,107,512,59,104,14123,14125,32768,8463,59,32768,8462,118,59,32768,8463,115,2304,59,97,98,99,100,101,109,115,116,14152,14154,14160,14163,14168,14179,14182,14188,14193,32768,43,99,105,114,59,32768,10787,59,32768,8862,105,114,59,32768,10786,512,111,117,14173,14176,59,32768,8724,59,32768,10789,59,32768,10866,110,33024,177,59,32768,177,105,109,59,32768,10790,119,111,59,32768,10791,59,32768,177,768,105,112,117,14208,14216,14221,110,116,105,110,116,59,32768,10773,102,59,32896,55349,56673,110,100,33024,163,59,32768,163,2560,59,69,97,99,101,105,110,111,115,117,14249,14251,14254,14258,14263,14336,14348,14367,14413,14418,32768,8826,59,32768,10931,112,59,32768,10935,117,101,59,32768,8828,512,59,99,14268,14270,32768,10927,1536,59,97,99,101,110,115,14283,14285,14293,14302,14306,14331,32768,8826,112,112,114,111,120,59,32768,10935,117,114,108,121,101,113,59,32768,8828,113,59,32768,10927,768,97,101,115,14313,14321,14326,112,112,114,111,120,59,32768,10937,113,113,59,32768,10933,105,109,59,32768,8936,105,109,59,32768,8830,109,101,512,59,115,14343,14345,32768,8242,59,32768,8473,768,69,97,115,14355,14358,14362,59,32768,10933,112,59,32768,10937,105,109,59,32768,8936,768,100,102,112,14374,14377,14402,59,32768,8719,768,97,108,115,14384,14390,14396,108,97,114,59,32768,9006,105,110,101,59,32768,8978,117,114,102,59,32768,8979,512,59,116,14407,14409,32768,8733,111,59,32768,8733,105,109,59,32768,8830,114,101,108,59,32768,8880,512,99,105,14429,14434,114,59,32896,55349,56517,59,32768,968,110,99,115,112,59,32768,8200,1536,102,105,111,112,115,117,14457,14462,14467,14473,14480,14486,114,59,32896,55349,56622,110,116,59,32768,10764,112,102,59,32896,55349,56674,114,105,109,101,59,32768,8279,99,114,59,32896,55349,56518,768,97,101,111,14493,14513,14526,116,512,101,105,14499,14508,114,110,105,111,110,115,59,32768,8461,110,116,59,32768,10774,115,116,512,59,101,14520,14522,32768,63,113,59,32768,8799,116,33024,34,59,32768,34,5376,65,66,72,97,98,99,100,101,102,104,105,108,109,110,111,112,114,115,116,117,120,14575,14597,14603,14608,14775,14829,14865,14901,14943,14966,15e3,15139,15159,15176,15182,15236,15261,15267,15309,15352,15360,768,97,114,116,14582,14587,14591,114,114,59,32768,8667,114,59,32768,8658,97,105,108,59,32768,10524,97,114,114,59,32768,10511,97,114,59,32768,10596,1792,99,100,101,110,113,114,116,14623,14637,14642,14650,14672,14679,14751,512,101,117,14628,14632,59,32896,8765,817,116,101,59,32768,341,105,99,59,32768,8730,109,112,116,121,118,59,32768,10675,103,1024,59,100,101,108,14660,14662,14665,14668,32768,10217,59,32768,10642,59,32768,10661,101,59,32768,10217,117,111,33024,187,59,32768,187,114,2816,59,97,98,99,102,104,108,112,115,116,119,14703,14705,14709,14720,14723,14727,14731,14735,14739,14744,14748,32768,8594,112,59,32768,10613,512,59,102,14714,14716,32768,8677,115,59,32768,10528,59,32768,10547,115,59,32768,10526,107,59,32768,8618,112,59,32768,8620,108,59,32768,10565,105,109,59,32768,10612,108,59,32768,8611,59,32768,8605,512,97,105,14756,14761,105,108,59,32768,10522,111,512,59,110,14767,14769,32768,8758,97,108,115,59,32768,8474,768,97,98,114,14782,14787,14792,114,114,59,32768,10509,114,107,59,32768,10099,512,97,107,14797,14809,99,512,101,107,14803,14806,59,32768,125,59,32768,93,512,101,115,14814,14817,59,32768,10636,108,512,100,117,14823,14826,59,32768,10638,59,32768,10640,1024,97,101,117,121,14838,14844,14858,14862,114,111,110,59,32768,345,512,100,105,14849,14854,105,108,59,32768,343,108,59,32768,8969,98,59,32768,125,59,32768,1088,1024,99,108,113,115,14874,14878,14885,14897,97,59,32768,10551,100,104,97,114,59,32768,10601,117,111,512,59,114,14892,14894,32768,8221,59,32768,8221,104,59,32768,8627,768,97,99,103,14908,14934,14938,108,1024,59,105,112,115,14918,14920,14925,14931,32768,8476,110,101,59,32768,8475,97,114,116,59,32768,8476,59,32768,8477,116,59,32768,9645,33024,174,59,32768,174,768,105,108,114,14950,14956,14962,115,104,116,59,32768,10621,111,111,114,59,32768,8971,59,32896,55349,56623,512,97,111,14971,14990,114,512,100,117,14977,14980,59,32768,8641,512,59,108,14985,14987,32768,8640,59,32768,10604,512,59,118,14995,14997,32768,961,59,32768,1009,768,103,110,115,15007,15123,15127,104,116,1536,97,104,108,114,115,116,15022,15039,15060,15086,15099,15111,114,114,111,119,512,59,116,15031,15033,32768,8594,97,105,108,59,32768,8611,97,114,112,111,111,110,512,100,117,15050,15056,111,119,110,59,32768,8641,112,59,32768,8640,101,102,116,512,97,104,15068,15076,114,114,111,119,115,59,32768,8644,97,114,112,111,111,110,115,59,32768,8652,105,103,104,116,97,114,114,111,119,115,59,32768,8649,113,117,105,103,97,114,114,111,119,59,32768,8605,104,114,101,101,116,105,109,101,115,59,32768,8908,103,59,32768,730,105,110,103,100,111,116,115,101,113,59,32768,8787,768,97,104,109,15146,15151,15156,114,114,59,32768,8644,97,114,59,32768,8652,59,32768,8207,111,117,115,116,512,59,97,15168,15170,32768,9137,99,104,101,59,32768,9137,109,105,100,59,32768,10990,1024,97,98,112,116,15191,15204,15209,15229,512,110,114,15196,15200,103,59,32768,10221,114,59,32768,8702,114,107,59,32768,10215,768,97,102,108,15216,15220,15224,114,59,32768,10630,59,32896,55349,56675,117,115,59,32768,10798,105,109,101,115,59,32768,10805,512,97,112,15241,15253,114,512,59,103,15247,15249,32768,41,116,59,32768,10644,111,108,105,110,116,59,32768,10770,97,114,114,59,32768,8649,1024,97,99,104,113,15276,15282,15287,15290,113,117,111,59,32768,8250,114,59,32896,55349,56519,59,32768,8625,512,98,117,15295,15298,59,32768,93,111,512,59,114,15304,15306,32768,8217,59,32768,8217,768,104,105,114,15316,15322,15328,114,101,101,59,32768,8908,109,101,115,59,32768,8906,105,1024,59,101,102,108,15338,15340,15343,15346,32768,9657,59,32768,8885,59,32768,9656,116,114,105,59,32768,10702,108,117,104,97,114,59,32768,10600,59,32768,8478,6706,15391,15398,15404,15499,15516,15592,0,15606,15660,0,0,15752,15758,0,15827,15863,15886,16e3,16006,16038,16086,0,16467,0,0,16506,99,117,116,101,59,32768,347,113,117,111,59,32768,8218,2560,59,69,97,99,101,105,110,112,115,121,15424,15426,15429,15441,15446,15458,15463,15482,15490,15495,32768,8827,59,32768,10932,833,15434,0,15437,59,32768,10936,111,110,59,32768,353,117,101,59,32768,8829,512,59,100,15451,15453,32768,10928,105,108,59,32768,351,114,99,59,32768,349,768,69,97,115,15470,15473,15477,59,32768,10934,112,59,32768,10938,105,109,59,32768,8937,111,108,105,110,116,59,32768,10771,105,109,59,32768,8831,59,32768,1089,111,116,768,59,98,101,15507,15509,15512,32768,8901,59,32768,8865,59,32768,10854,1792,65,97,99,109,115,116,120,15530,15535,15556,15562,15566,15572,15587,114,114,59,32768,8664,114,512,104,114,15541,15545,107,59,32768,10533,512,59,111,15550,15552,32768,8600,119,59,32768,8600,116,33024,167,59,32768,167,105,59,32768,59,119,97,114,59,32768,10537,109,512,105,110,15578,15584,110,117,115,59,32768,8726,59,32768,8726,116,59,32768,10038,114,512,59,111,15597,15600,32896,55349,56624,119,110,59,32768,8994,1024,97,99,111,121,15614,15619,15632,15654,114,112,59,32768,9839,512,104,121,15624,15629,99,121,59,32768,1097,59,32768,1096,114,116,1086,15640,0,0,15645,105,100,59,32768,8739,97,114,97,108,108,101,108,59,32768,8741,33024,173,59,32768,173,512,103,109,15664,15681,109,97,768,59,102,118,15673,15675,15678,32768,963,59,32768,962,59,32768,962,2048,59,100,101,103,108,110,112,114,15698,15700,15705,15715,15725,15735,15739,15745,32768,8764,111,116,59,32768,10858,512,59,113,15710,15712,32768,8771,59,32768,8771,512,59,69,15720,15722,32768,10910,59,32768,10912,512,59,69,15730,15732,32768,10909,59,32768,10911,101,59,32768,8774,108,117,115,59,32768,10788,97,114,114,59,32768,10610,97,114,114,59,32768,8592,1024,97,101,105,116,15766,15788,15796,15808,512,108,115,15771,15783,108,115,101,116,109,105,110,117,115,59,32768,8726,104,112,59,32768,10803,112,97,114,115,108,59,32768,10724,512,100,108,15801,15804,59,32768,8739,101,59,32768,8995,512,59,101,15813,15815,32768,10922,512,59,115,15820,15822,32768,10924,59,32896,10924,65024,768,102,108,112,15833,15839,15857,116,99,121,59,32768,1100,512,59,98,15844,15846,32768,47,512,59,97,15851,15853,32768,10692,114,59,32768,9023,102,59,32896,55349,56676,97,512,100,114,15868,15882,101,115,512,59,117,15875,15877,32768,9824,105,116,59,32768,9824,59,32768,8741,768,99,115,117,15892,15921,15977,512,97,117,15897,15909,112,512,59,115,15903,15905,32768,8851,59,32896,8851,65024,112,512,59,115,15915,15917,32768,8852,59,32896,8852,65024,117,512,98,112,15927,15952,768,59,101,115,15934,15936,15939,32768,8847,59,32768,8849,101,116,512,59,101,15946,15948,32768,8847,113,59,32768,8849,768,59,101,115,15959,15961,15964,32768,8848,59,32768,8850,101,116,512,59,101,15971,15973,32768,8848,113,59,32768,8850,768,59,97,102,15984,15986,15996,32768,9633,114,566,15991,15994,59,32768,9633,59,32768,9642,59,32768,9642,97,114,114,59,32768,8594,1024,99,101,109,116,16014,16019,16025,16031,114,59,32896,55349,56520,116,109,110,59,32768,8726,105,108,101,59,32768,8995,97,114,102,59,32768,8902,512,97,114,16042,16053,114,512,59,102,16048,16050,32768,9734,59,32768,9733,512,97,110,16058,16081,105,103,104,116,512,101,112,16067,16076,112,115,105,108,111,110,59,32768,1013,104,105,59,32768,981,115,59,32768,175,1280,98,99,109,110,112,16096,16221,16288,16291,16295,2304,59,69,100,101,109,110,112,114,115,16115,16117,16120,16125,16137,16143,16154,16160,16166,32768,8834,59,32768,10949,111,116,59,32768,10941,512,59,100,16130,16132,32768,8838,111,116,59,32768,10947,117,108,116,59,32768,10945,512,69,101,16148,16151,59,32768,10955,59,32768,8842,108,117,115,59,32768,10943,97,114,114,59,32768,10617,768,101,105,117,16173,16206,16210,116,768,59,101,110,16181,16183,16194,32768,8834,113,512,59,113,16189,16191,32768,8838,59,32768,10949,101,113,512,59,113,16201,16203,32768,8842,59,32768,10955,109,59,32768,10951,512,98,112,16215,16218,59,32768,10965,59,32768,10963,99,1536,59,97,99,101,110,115,16235,16237,16245,16254,16258,16283,32768,8827,112,112,114,111,120,59,32768,10936,117,114,108,121,101,113,59,32768,8829,113,59,32768,10928,768,97,101,115,16265,16273,16278,112,112,114,111,120,59,32768,10938,113,113,59,32768,10934,105,109,59,32768,8937,105,109,59,32768,8831,59,32768,8721,103,59,32768,9834,3328,49,50,51,59,69,100,101,104,108,109,110,112,115,16322,16327,16332,16337,16339,16342,16356,16368,16382,16388,16394,16405,16411,33024,185,59,32768,185,33024,178,59,32768,178,33024,179,59,32768,179,32768,8835,59,32768,10950,512,111,115,16347,16351,116,59,32768,10942,117,98,59,32768,10968,512,59,100,16361,16363,32768,8839,111,116,59,32768,10948,115,512,111,117,16374,16378,108,59,32768,10185,98,59,32768,10967,97,114,114,59,32768,10619,117,108,116,59,32768,10946,512,69,101,16399,16402,59,32768,10956,59,32768,8843,108,117,115,59,32768,10944,768,101,105,117,16418,16451,16455,116,768,59,101,110,16426,16428,16439,32768,8835,113,512,59,113,16434,16436,32768,8839,59,32768,10950,101,113,512,59,113,16446,16448,32768,8843,59,32768,10956,109,59,32768,10952,512,98,112,16460,16463,59,32768,10964,59,32768,10966,768,65,97,110,16473,16478,16499,114,114,59,32768,8665,114,512,104,114,16484,16488,107,59,32768,10534,512,59,111,16493,16495,32768,8601,119,59,32768,8601,119,97,114,59,32768,10538,108,105,103,33024,223,59,32768,223,5938,16538,16552,16557,16579,16584,16591,0,16596,16692,0,0,0,0,0,16731,16780,0,16787,16908,0,0,0,16938,1091,16543,0,0,16549,103,101,116,59,32768,8982,59,32768,964,114,107,59,32768,9140,768,97,101,121,16563,16569,16575,114,111,110,59,32768,357,100,105,108,59,32768,355,59,32768,1090,111,116,59,32768,8411,108,114,101,99,59,32768,8981,114,59,32896,55349,56625,1024,101,105,107,111,16604,16641,16670,16684,835,16609,0,16624,101,512,52,102,16614,16617,59,32768,8756,111,114,101,59,32768,8756,97,768,59,115,118,16631,16633,16638,32768,952,121,109,59,32768,977,59,32768,977,512,99,110,16646,16665,107,512,97,115,16652,16660,112,112,114,111,120,59,32768,8776,105,109,59,32768,8764,115,112,59,32768,8201,512,97,115,16675,16679,112,59,32768,8776,105,109,59,32768,8764,114,110,33024,254,59,32768,254,829,16696,16701,16727,100,101,59,32768,732,101,115,33536,215,59,98,100,16710,16712,16723,32768,215,512,59,97,16717,16719,32768,8864,114,59,32768,10801,59,32768,10800,116,59,32768,8749,768,101,112,115,16737,16741,16775,97,59,32768,10536,1024,59,98,99,102,16750,16752,16757,16762,32768,8868,111,116,59,32768,9014,105,114,59,32768,10993,512,59,111,16767,16770,32896,55349,56677,114,107,59,32768,10970,97,59,32768,10537,114,105,109,101,59,32768,8244,768,97,105,112,16793,16798,16899,100,101,59,32768,8482,1792,97,100,101,109,112,115,116,16813,16868,16873,16876,16883,16889,16893,110,103,108,101,1280,59,100,108,113,114,16828,16830,16836,16850,16853,32768,9653,111,119,110,59,32768,9663,101,102,116,512,59,101,16844,16846,32768,9667,113,59,32768,8884,59,32768,8796,105,103,104,116,512,59,101,16862,16864,32768,9657,113,59,32768,8885,111,116,59,32768,9708,59,32768,8796,105,110,117,115,59,32768,10810,108,117,115,59,32768,10809,98,59,32768,10701,105,109,101,59,32768,10811,101,122,105,117,109,59,32768,9186,768,99,104,116,16914,16926,16931,512,114,121,16919,16923,59,32896,55349,56521,59,32768,1094,99,121,59,32768,1115,114,111,107,59,32768,359,512,105,111,16942,16947,120,116,59,32768,8812,104,101,97,100,512,108,114,16956,16967,101,102,116,97,114,114,111,119,59,32768,8606,105,103,104,116,97,114,114,111,119,59,32768,8608,4608,65,72,97,98,99,100,102,103,104,108,109,111,112,114,115,116,117,119,17016,17021,17026,17043,17057,17072,17095,17110,17119,17139,17172,17187,17202,17290,17330,17336,17365,17381,114,114,59,32768,8657,97,114,59,32768,10595,512,99,114,17031,17039,117,116,101,33024,250,59,32768,250,114,59,32768,8593,114,820,17049,0,17053,121,59,32768,1118,118,101,59,32768,365,512,105,121,17062,17069,114,99,33024,251,59,32768,251,59,32768,1091,768,97,98,104,17079,17084,17090,114,114,59,32768,8645,108,97,99,59,32768,369,97,114,59,32768,10606,512,105,114,17100,17106,115,104,116,59,32768,10622,59,32896,55349,56626,114,97,118,101,33024,249,59,32768,249,562,17123,17135,114,512,108,114,17128,17131,59,32768,8639,59,32768,8638,108,107,59,32768,9600,512,99,116,17144,17167,1088,17150,0,0,17163,114,110,512,59,101,17156,17158,32768,8988,114,59,32768,8988,111,112,59,32768,8975,114,105,59,32768,9720,512,97,108,17177,17182,99,114,59,32768,363,33024,168,59,32768,168,512,103,112,17192,17197,111,110,59,32768,371,102,59,32896,55349,56678,1536,97,100,104,108,115,117,17215,17222,17233,17257,17262,17280,114,114,111,119,59,32768,8593,111,119,110,97,114,114,111,119,59,32768,8597,97,114,112,111,111,110,512,108,114,17244,17250,101,102,116,59,32768,8639,105,103,104,116,59,32768,8638,117,115,59,32768,8846,105,768,59,104,108,17270,17272,17275,32768,965,59,32768,978,111,110,59,32768,965,112,97,114,114,111,119,115,59,32768,8648,768,99,105,116,17297,17320,17325,1088,17303,0,0,17316,114,110,512,59,101,17309,17311,32768,8989,114,59,32768,8989,111,112,59,32768,8974,110,103,59,32768,367,114,105,59,32768,9721,99,114,59,32896,55349,56522,768,100,105,114,17343,17348,17354,111,116,59,32768,8944,108,100,101,59,32768,361,105,512,59,102,17360,17362,32768,9653,59,32768,9652,512,97,109,17370,17375,114,114,59,32768,8648,108,33024,252,59,32768,252,97,110,103,108,101,59,32768,10663,3840,65,66,68,97,99,100,101,102,108,110,111,112,114,115,122,17420,17425,17437,17443,17613,17617,17623,17667,17672,17678,17693,17699,17705,17711,17754,114,114,59,32768,8661,97,114,512,59,118,17432,17434,32768,10984,59,32768,10985,97,115,104,59,32768,8872,512,110,114,17448,17454,103,114,116,59,32768,10652,1792,101,107,110,112,114,115,116,17469,17478,17485,17494,17515,17526,17578,112,115,105,108,111,110,59,32768,1013,97,112,112,97,59,32768,1008,111,116,104,105,110,103,59,32768,8709,768,104,105,114,17501,17505,17508,105,59,32768,981,59,32768,982,111,112,116,111,59,32768,8733,512,59,104,17520,17522,32768,8597,111,59,32768,1009,512,105,117,17531,17537,103,109,97,59,32768,962,512,98,112,17542,17560,115,101,116,110,101,113,512,59,113,17553,17556,32896,8842,65024,59,32896,10955,65024,115,101,116,110,101,113,512,59,113,17571,17574,32896,8843,65024,59,32896,10956,65024,512,104,114,17583,17589,101,116,97,59,32768,977,105,97,110,103,108,101,512,108,114,17600,17606,101,102,116,59,32768,8882,105,103,104,116,59,32768,8883,121,59,32768,1074,97,115,104,59,32768,8866,768,101,108,114,17630,17648,17654,768,59,98,101,17637,17639,17644,32768,8744,97,114,59,32768,8891,113,59,32768,8794,108,105,112,59,32768,8942,512,98,116,17659,17664,97,114,59,32768,124,59,32768,124,114,59,32896,55349,56627,116,114,105,59,32768,8882,115,117,512,98,112,17685,17689,59,32896,8834,8402,59,32896,8835,8402,112,102,59,32896,55349,56679,114,111,112,59,32768,8733,116,114,105,59,32768,8883,512,99,117,17716,17721,114,59,32896,55349,56523,512,98,112,17726,17740,110,512,69,101,17732,17736,59,32896,10955,65024,59,32896,8842,65024,110,512,69,101,17746,17750,59,32896,10956,65024,59,32896,8843,65024,105,103,122,97,103,59,32768,10650,1792,99,101,102,111,112,114,115,17777,17783,17815,17820,17826,17829,17842,105,114,99,59,32768,373,512,100,105,17788,17809,512,98,103,17793,17798,97,114,59,32768,10847,101,512,59,113,17804,17806,32768,8743,59,32768,8793,101,114,112,59,32768,8472,114,59,32896,55349,56628,112,102,59,32896,55349,56680,59,32768,8472,512,59,101,17834,17836,32768,8768,97,116,104,59,32768,8768,99,114,59,32896,55349,56524,5428,17871,17891,0,17897,0,17902,17917,0,0,17920,17935,17940,17945,0,0,17977,17992,0,18008,18024,18029,768,97,105,117,17877,17881,17886,112,59,32768,8898,114,99,59,32768,9711,112,59,32768,8899,116,114,105,59,32768,9661,114,59,32896,55349,56629,512,65,97,17906,17911,114,114,59,32768,10234,114,114,59,32768,10231,59,32768,958,512,65,97,17924,17929,114,114,59,32768,10232,114,114,59,32768,10229,97,112,59,32768,10236,105,115,59,32768,8955,768,100,112,116,17951,17956,17970,111,116,59,32768,10752,512,102,108,17961,17965,59,32896,55349,56681,117,115,59,32768,10753,105,109,101,59,32768,10754,512,65,97,17981,17986,114,114,59,32768,10233,114,114,59,32768,10230,512,99,113,17996,18001,114,59,32896,55349,56525,99,117,112,59,32768,10758,512,112,116,18012,18018,108,117,115,59,32768,10756,114,105,59,32768,9651,101,101,59,32768,8897,101,100,103,101,59,32768,8896,2048,97,99,101,102,105,111,115,117,18052,18068,18081,18087,18092,18097,18103,18109,99,512,117,121,18058,18065,116,101,33024,253,59,32768,253,59,32768,1103,512,105,121,18073,18078,114,99,59,32768,375,59,32768,1099,110,33024,165,59,32768,165,114,59,32896,55349,56630,99,121,59,32768,1111,112,102,59,32896,55349,56682,99,114,59,32896,55349,56526,512,99,109,18114,18118,121,59,32768,1102,108,33024,255,59,32768,255,2560,97,99,100,101,102,104,105,111,115,119,18145,18152,18166,18171,18186,18191,18196,18204,18210,18216,99,117,116,101,59,32768,378,512,97,121,18157,18163,114,111,110,59,32768,382,59,32768,1079,111,116,59,32768,380,512,101,116,18176,18182,116,114,102,59,32768,8488,97,59,32768,950,114,59,32896,55349,56631,99,121,59,32768,1078,103,114,97,114,114,59,32768,8669,112,102,59,32896,55349,56683,99,114,59,32896,55349,56527,512,106,110,18221,18224,59,32768,8205,106,59,32768,8204]);var B0={};Object.defineProperty(B0,"__esModule",{value:!0});B0.default=new Uint16Array([1024,97,103,108,113,9,23,27,31,1086,15,0,0,19,112,59,32768,38,111,115,59,32768,39,116,59,32768,62,116,59,32768,60,117,111,116,59,32768,34]);(function(e){var t=V&&V.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(e,"__esModule",{value:!0}),e.decodeXML=e.decodeHTMLStrict=e.decodeHTML=e.determineBranch=e.JUMP_OFFSET_BASE=e.BinTrieFlags=e.xmlDecodeTree=e.htmlDecodeTree=void 0;var n=t(F0);e.htmlDecodeTree=n.default;var r=t(B0);e.xmlDecodeTree=r.default;var i=t(_a),o;(function(p){p[p.HAS_VALUE=32768]="HAS_VALUE",p[p.BRANCH_LENGTH=32512]="BRANCH_LENGTH",p[p.MULTI_BYTE=128]="MULTI_BYTE",p[p.JUMP_TABLE=127]="JUMP_TABLE"})(o=e.BinTrieFlags||(e.BinTrieFlags={})),e.JUMP_OFFSET_BASE=47;function s(p){return function(h,_){for(var m="",g=0,y=0;(y=h.indexOf("&",y))>=0;){if(m+=h.slice(g,y),g=y,y+=1,h.charCodeAt(y)===35){var w=y+1,x=10,S=h.charCodeAt(w);for((S|32)===120&&(x=16,y+=1,w+=1);(S=h.charCodeAt(++y))>=48&&S<=57||x===16&&(S|32)>=97&&(S|32)<=102;);if(w!==y){var T=h.substring(w,y),b=parseInt(T,x);if(h.charCodeAt(y)===59)y+=1;else if(_)continue;m+=i.default(b),g=y}continue}for(var C=null,E=1,L=0,O=p[L];y<h.length&&(L=a(p,O,L+1,h.charCodeAt(y)),!(L<0));y++,E++)O=p[L],O&o.HAS_VALUE&&(_&&h.charCodeAt(y)!==59?L+=1:(C=O&o.MULTI_BYTE?String.fromCharCode(p[++L],p[++L]):String.fromCharCode(p[++L]),E=0));C!=null&&(m+=C,g=y-E+1)}return m+h.slice(g)}}function a(p,v,h,_){if(v<=128)return _===v?h:-1;var m=(v&o.BRANCH_LENGTH)>>8;if(m===0)return-1;if(m===1)return _===p[h]?h+1:-1;var g=v&o.JUMP_TABLE;if(g){var y=_-e.JUMP_OFFSET_BASE-g;return y<0||y>m?-1:p[h+y]-1}for(var w=h,x=w+m-1;w<=x;){var S=w+x>>>1,T=p[S];if(T<_)w=S+1;else if(T>_)x=S-1;else return p[S+m]}return-1}e.determineBranch=a;var l=s(n.default),c=s(r.default);function u(p){return l(p,!1)}e.decodeHTML=u;function f(p){return l(p,!0)}e.decodeHTMLStrict=f;function d(p){return c(p,!0)}e.decodeXML=d})(Bh);var cw=V&&V.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ya,"__esModule",{value:!0});var uw=cw(_a),Y1=Bh;function rn(e){return e===32||e===10||e===9||e===12||e===13}function Ro(e){return e===47||e===62||rn(e)}function R8(e){return e>=48&&e<=57}function fw(e){return e>=97&&e<=122||e>=65&&e<=90}var Ht={Cdata:new Uint16Array([67,68,65,84,65,91]),CdataEnd:new Uint16Array([93,93,62]),CommentEnd:new Uint16Array([45,45,62]),ScriptEnd:new Uint16Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint16Array([60,47,115,116,121,108,101]),TitleEnd:new Uint16Array([60,47,116,105,116,108,101])},dw=function(){function e(t,n){var r=t.xmlMode,i=r===void 0?!1:r,o=t.decodeEntities,s=o===void 0?!0:o;this.cbs=n,this._state=1,this.buffer="",this.sectionStart=0,this._index=0,this.bufferOffset=0,this.baseState=1,this.isSpecial=!1,this.running=!0,this.ended=!1,this.sequenceIndex=0,this.trieIndex=0,this.trieCurrent=0,this.trieResult=null,this.entityExcess=0,this.xmlMode=i,this.decodeEntities=s,this.entityTrie=i?Y1.xmlDecodeTree:Y1.htmlDecodeTree}return e.prototype.reset=function(){this._state=1,this.buffer="",this.sectionStart=0,this._index=0,this.bufferOffset=0,this.baseState=1,this.currentSequence=void 0,this.running=!0,this.ended=!1},e.prototype.write=function(t){if(this.ended)return this.cbs.onerror(Error(".write() after done!"));this.buffer+=t,this.parse()},e.prototype.end=function(t){if(this.ended)return this.cbs.onerror(Error(".end() after done!"));t&&this.write(t),this.ended=!0,this.running&&this.finish()},e.prototype.pause=function(){this.running=!1},e.prototype.resume=function(){this.running=!0,this._index<this.buffer.length&&this.parse(),this.ended&&this.finish()},e.prototype.getAbsoluteSectionStart=function(){return this.sectionStart+this.bufferOffset},e.prototype.getAbsoluteIndex=function(){return this.bufferOffset+this._index},e.prototype.stateText=function(t){t===60||!this.decodeEntities&&this.fastForwardTo(60)?(this._index>this.sectionStart&&this.cbs.ontext(this.getSection()),this._state=2,this.sectionStart=this._index):this.decodeEntities&&t===38&&(this._state=25)},e.prototype.stateSpecialStartSequence=function(t){var n=this.sequenceIndex===this.currentSequence.length,r=n?Ro(t):(t|32)===this.currentSequence[this.sequenceIndex];if(!r)this.isSpecial=!1;else if(!n){this.sequenceIndex++;return}this.sequenceIndex=0,this._state=3,this.stateInTagName(t)},e.prototype.stateInSpecialTag=function(t){if(this.sequenceIndex===this.currentSequence.length){if(t===62||rn(t)){var n=this._index-this.currentSequence.length;if(this.sectionStart<n){var r=this._index;this._index=n,this.cbs.ontext(this.getSection()),this._index=r}this.isSpecial=!1,this.sectionStart=n+2,this.stateInClosingTagName(t);return}this.sequenceIndex=0}(t|32)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:this.sequenceIndex===0?this.currentSequence===Ht.TitleEnd?this.decodeEntities&&t===38&&(this._state=25):this.fastForwardTo(60)&&(this.sequenceIndex=1):this.sequenceIndex=+(t===60)},e.prototype.stateCDATASequence=function(t){t===Ht.Cdata[this.sequenceIndex]?++this.sequenceIndex===Ht.Cdata.length&&(this._state=21,this.currentSequence=Ht.CdataEnd,this.sequenceIndex=0,this.sectionStart=this._index+1):(this.sequenceIndex=0,this._state=16,this.stateInDeclaration(t))},e.prototype.fastForwardTo=function(t){for(;++this._index<this.buffer.length;)if(this.buffer.charCodeAt(this._index)===t)return!0;return this._index=this.buffer.length-1,!1},e.prototype.stateInCommentLike=function(t){if(t===this.currentSequence[this.sequenceIndex]){if(++this.sequenceIndex===this.currentSequence.length){var n=this.buffer.slice(this.sectionStart,this._index-2);this.currentSequence===Ht.CdataEnd?this.cbs.oncdata(n):this.cbs.oncomment(n),this.sequenceIndex=0,this.sectionStart=this._index+1,this._state=1}}else this.sequenceIndex===0?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):t!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)},e.prototype.isTagStartChar=function(t){return this.xmlMode?!Ro(t):fw(t)},e.prototype.startSpecial=function(t,n){this.isSpecial=!0,this.currentSequence=t,this.sequenceIndex=n,this._state=23},e.prototype.stateBeforeTagName=function(t){if(t===33)this._state=15,this.sectionStart=this._index+1;else if(t===63)this._state=17,this.sectionStart=this._index+1;else if(this.isTagStartChar(t)){var n=t|32;this.sectionStart=this._index,!this.xmlMode&&n===Ht.TitleEnd[2]?this.startSpecial(Ht.TitleEnd,3):this._state=!this.xmlMode&&n===Ht.ScriptEnd[2]?22:3}else t===47?this._state=5:(this._state=1,this.stateText(t))},e.prototype.stateInTagName=function(t){Ro(t)&&(this.cbs.onopentagname(this.getSection()),this.sectionStart=-1,this._state=8,this.stateBeforeAttributeName(t))},e.prototype.stateBeforeClosingTagName=function(t){rn(t)||(t===62?this._state=1:(this._state=this.isTagStartChar(t)?6:20,this.sectionStart=this._index))},e.prototype.stateInClosingTagName=function(t){(t===62||rn(t))&&(this.cbs.onclosetag(this.getSection()),this.sectionStart=-1,this._state=7,this.stateAfterClosingTagName(t))},e.prototype.stateAfterClosingTagName=function(t){(t===62||this.fastForwardTo(62))&&(this._state=1,this.sectionStart=this._index+1)},e.prototype.stateBeforeAttributeName=function(t){t===62?(this.cbs.onopentagend(),this.isSpecial?(this._state=24,this.sequenceIndex=0):this._state=1,this.baseState=this._state,this.sectionStart=this._index+1):t===47?this._state=4:rn(t)||(this._state=9,this.sectionStart=this._index)},e.prototype.stateInSelfClosingTag=function(t){t===62?(this.cbs.onselfclosingtag(),this._state=1,this.baseState=1,this.sectionStart=this._index+1,this.isSpecial=!1):rn(t)||(this._state=8,this.stateBeforeAttributeName(t))},e.prototype.stateInAttributeName=function(t){(t===61||Ro(t))&&(this.cbs.onattribname(this.getSection()),this.sectionStart=-1,this._state=10,this.stateAfterAttributeName(t))},e.prototype.stateAfterAttributeName=function(t){t===61?this._state=11:t===47||t===62?(this.cbs.onattribend(void 0),this._state=8,this.stateBeforeAttributeName(t)):rn(t)||(this.cbs.onattribend(void 0),this._state=9,this.sectionStart=this._index)},e.prototype.stateBeforeAttributeValue=function(t){t===34?(this._state=12,this.sectionStart=this._index+1):t===39?(this._state=13,this.sectionStart=this._index+1):rn(t)||(this.sectionStart=this._index,this._state=14,this.stateInAttributeValueNoQuotes(t))},e.prototype.handleInAttributeValue=function(t,n){t===n||!this.decodeEntities&&this.fastForwardTo(n)?(this.cbs.onattribdata(this.getSection()),this.sectionStart=-1,this.cbs.onattribend(String.fromCharCode(n)),this._state=8):this.decodeEntities&&t===38&&(this.baseState=this._state,this._state=25)},e.prototype.stateInAttributeValueDoubleQuotes=function(t){this.handleInAttributeValue(t,34)},e.prototype.stateInAttributeValueSingleQuotes=function(t){this.handleInAttributeValue(t,39)},e.prototype.stateInAttributeValueNoQuotes=function(t){rn(t)||t===62?(this.cbs.onattribdata(this.getSection()),this.sectionStart=-1,this.cbs.onattribend(null),this._state=8,this.stateBeforeAttributeName(t)):this.decodeEntities&&t===38&&(this.baseState=this._state,this._state=25)},e.prototype.stateBeforeDeclaration=function(t){t===91?(this._state=19,this.sequenceIndex=0):this._state=t===45?18:16},e.prototype.stateInDeclaration=function(t){(t===62||this.fastForwardTo(62))&&(this.cbs.ondeclaration(this.getSection()),this._state=1,this.sectionStart=this._index+1)},e.prototype.stateInProcessingInstruction=function(t){(t===62||this.fastForwardTo(62))&&(this.cbs.onprocessinginstruction(this.getSection()),this._state=1,this.sectionStart=this._index+1)},e.prototype.stateBeforeComment=function(t){t===45?(this._state=21,this.currentSequence=Ht.CommentEnd,this.sequenceIndex=2,this.sectionStart=this._index+1):this._state=16},e.prototype.stateInSpecialComment=function(t){(t===62||this.fastForwardTo(62))&&(this.cbs.oncomment(this.getSection()),this._state=1,this.sectionStart=this._index+1)},e.prototype.stateBeforeSpecialS=function(t){var n=t|32;n===Ht.ScriptEnd[3]?this.startSpecial(Ht.ScriptEnd,4):n===Ht.StyleEnd[3]?this.startSpecial(Ht.StyleEnd,4):(this._state=3,this.stateInTagName(t))},e.prototype.stateBeforeEntity=function(t){this.entityExcess=1,t===35?this._state=26:t===38||(this.trieIndex=0,this.trieCurrent=this.entityTrie[0],this.trieResult=null,this._state=27,this.stateInNamedEntity(t))},e.prototype.stateInNamedEntity=function(t){if(this.entityExcess+=1,this.trieIndex=(0,Y1.determineBranch)(this.entityTrie,this.trieCurrent,this.trieIndex+1,t),this.trieIndex<0){this.emitNamedEntity(),this._index--;return}if(this.trieCurrent=this.entityTrie[this.trieIndex],this.trieCurrent&Y1.BinTrieFlags.HAS_VALUE)if(!this.allowLegacyEntity()&&t!==59)this.trieIndex+=1;else{var n=this._index-this.entityExcess+1;n>this.sectionStart&&this.emitPartial(this.buffer.substring(this.sectionStart,n)),this.trieResult=this.trieCurrent&Y1.BinTrieFlags.MULTI_BYTE?String.fromCharCode(this.entityTrie[++this.trieIndex],this.entityTrie[++this.trieIndex]):String.fromCharCode(this.entityTrie[++this.trieIndex]),this.entityExcess=0,this.sectionStart=this._index+1}},e.prototype.emitNamedEntity=function(){this.trieResult&&this.emitPartial(this.trieResult),this._state=this.baseState},e.prototype.stateBeforeNumericEntity=function(t){(t|32)===120?(this.entityExcess++,this._state=29):(this._state=28,this.stateInNumericEntity(t))},e.prototype.decodeNumericEntity=function(t,n){var r=this._index-this.entityExcess-1,i=r+2+(t>>4);if(i!==this._index){r>this.sectionStart&&this.emitPartial(this.buffer.substring(this.sectionStart,r));var o=this.buffer.substring(i,this._index),s=parseInt(o,t);this.emitPartial((0,uw.default)(s)),this.sectionStart=this._index+Number(n)}this._state=this.baseState},e.prototype.stateInNumericEntity=function(t){t===59?this.decodeNumericEntity(10,!0):R8(t)?this.entityExcess++:(this.allowLegacyEntity()?this.decodeNumericEntity(10,!1):this._state=this.baseState,this._index--)},e.prototype.stateInHexEntity=function(t){t===59?this.decodeNumericEntity(16,!0):(t<97||t>102)&&(t<65||t>70)&&!R8(t)?(this.allowLegacyEntity()?this.decodeNumericEntity(16,!1):this._state=this.baseState,this._index--):this.entityExcess++},e.prototype.allowLegacyEntity=function(){return!this.xmlMode&&(this.baseState===1||this.baseState===24)},e.prototype.cleanup=function(){this.running&&this.sectionStart!==this._index&&(this._state===1||this._state===24&&this.sequenceIndex===0)&&(this.cbs.ontext(this.buffer.substr(this.sectionStart)),this.sectionStart=this._index);var t=this.sectionStart<0?this._index:this.sectionStart;this.buffer=t===this.buffer.length?"":this.buffer.substr(t),this._index-=t,this.bufferOffset+=t,this.sectionStart>0&&(this.sectionStart=0)},e.prototype.shouldContinue=function(){return this._index<this.buffer.length&&this.running},e.prototype.parse=function(){for(;this.shouldContinue();){var t=this.buffer.charCodeAt(this._index);this._state===1?this.stateText(t):this._state===23?this.stateSpecialStartSequence(t):this._state===24?this.stateInSpecialTag(t):this._state===19?this.stateCDATASequence(t):this._state===12?this.stateInAttributeValueDoubleQuotes(t):this._state===9?this.stateInAttributeName(t):this._state===21?this.stateInCommentLike(t):this._state===20?this.stateInSpecialComment(t):this._state===8?this.stateBeforeAttributeName(t):this._state===3?this.stateInTagName(t):this._state===6?this.stateInClosingTagName(t):this._state===2?this.stateBeforeTagName(t):this._state===10?this.stateAfterAttributeName(t):this._state===13?this.stateInAttributeValueSingleQuotes(t):this._state===11?this.stateBeforeAttributeValue(t):this._state===5?this.stateBeforeClosingTagName(t):this._state===7?this.stateAfterClosingTagName(t):this._state===22?this.stateBeforeSpecialS(t):this._state===14?this.stateInAttributeValueNoQuotes(t):this._state===4?this.stateInSelfClosingTag(t):this._state===16?this.stateInDeclaration(t):this._state===15?this.stateBeforeDeclaration(t):this._state===18?this.stateBeforeComment(t):this._state===17?this.stateInProcessingInstruction(t):this._state===27?this.stateInNamedEntity(t):this._state===25?this.stateBeforeEntity(t):this._state===29?this.stateInHexEntity(t):this._state===28?this.stateInNumericEntity(t):this.stateBeforeNumericEntity(t),this._index++}this.cleanup()},e.prototype.finish=function(){this._state===27&&this.emitNamedEntity(),this.sectionStart<this._index&&this.handleTrailingData(),this.cbs.onend()},e.prototype.handleTrailingData=function(){var t=this.buffer.substr(this.sectionStart);this._state===21?this.currentSequence===Ht.CdataEnd?this.cbs.oncdata(t):this.cbs.oncomment(t):this._state===28&&this.allowLegacyEntity()?this.decodeNumericEntity(10,!1):this._state===29&&this.allowLegacyEntity()?this.decodeNumericEntity(16,!1):this._state===3||this._state===8||this._state===11||this._state===10||this._state===9||this._state===13||this._state===12||this._state===14||this._state===6||this.cbs.ontext(t)},e.prototype.getSection=function(){return this.buffer.substring(this.sectionStart,this._index)},e.prototype.emitPartial=function(t){this.baseState!==1&&this.baseState!==24?this.cbs.onattribdata(t):this.cbs.ontext(t)},e}();ya.default=dw;var hw=V&&V.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ao,"__esModule",{value:!0});ao.Parser=void 0;var pw=hw(ya),jr=new Set(["input","option","optgroup","select","button","datalist","textarea"]),X=new Set(["p"]),I8=new Set(["thead","tbody"]),V8=new Set(["dd","dt"]),q8=new Set(["rt","rp"]),mw=new Map([["tr",new Set(["tr","th","td"])],["th",new Set(["th"])],["td",new Set(["thead","th","td"])],["body",new Set(["head","link","script"])],["li",new Set(["li"])],["p",X],["h1",X],["h2",X],["h3",X],["h4",X],["h5",X],["h6",X],["select",jr],["input",jr],["output",jr],["button",jr],["datalist",jr],["textarea",jr],["option",new Set(["option"])],["optgroup",new Set(["optgroup","option"])],["dd",V8],["dt",V8],["address",X],["article",X],["aside",X],["blockquote",X],["details",X],["div",X],["dl",X],["fieldset",X],["figcaption",X],["figure",X],["footer",X],["form",X],["header",X],["hr",X],["main",X],["nav",X],["ol",X],["pre",X],["section",X],["table",X],["ul",X],["rt",q8],["rp",q8],["tbody",I8],["tfoot",I8]]),gw=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]),F8=new Set(["math","svg"]),B8=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignobject","desc","title"]),vw=/\s|\//,yw=function(){function e(t,n){n===void 0&&(n={});var r,i,o,s,a;this.options=n,this.startIndex=0,this.endIndex=0,this.openTagStart=0,this.tagname="",this.attribname="",this.attribvalue="",this.attribs=null,this.stack=[],this.foreignContext=[],this.cbs=t!=null?t:{},this.lowerCaseTagNames=(r=n.lowerCaseTags)!==null&&r!==void 0?r:!n.xmlMode,this.lowerCaseAttributeNames=(i=n.lowerCaseAttributeNames)!==null&&i!==void 0?i:!n.xmlMode,this.tokenizer=new((o=n.Tokenizer)!==null&&o!==void 0?o:pw.default)(this.options,this),(a=(s=this.cbs).onparserinit)===null||a===void 0||a.call(s,this)}return e.prototype.ontext=function(t){var n,r,i=this.tokenizer.getAbsoluteIndex();this.endIndex=i-1,(r=(n=this.cbs).ontext)===null||r===void 0||r.call(n,t),this.startIndex=i},e.prototype.isVoidElement=function(t){return!this.options.xmlMode&&gw.has(t)},e.prototype.onopentagname=function(t){this.endIndex=this.tokenizer.getAbsoluteIndex(),this.lowerCaseTagNames&&(t=t.toLowerCase()),this.emitOpenTag(t)},e.prototype.emitOpenTag=function(t){var n,r,i,o;this.openTagStart=this.startIndex,this.tagname=t;var s=!this.options.xmlMode&&mw.get(t);if(s)for(;this.stack.length>0&&s.has(this.stack[this.stack.length-1]);){var a=this.stack.pop();(r=(n=this.cbs).onclosetag)===null||r===void 0||r.call(n,a,!0)}this.isVoidElement(t)||(this.stack.push(t),F8.has(t)?this.foreignContext.push(!0):B8.has(t)&&this.foreignContext.push(!1)),(o=(i=this.cbs).onopentagname)===null||o===void 0||o.call(i,t),this.cbs.onopentag&&(this.attribs={})},e.prototype.endOpenTag=function(t){var n,r;this.startIndex=this.openTagStart,this.endIndex=this.tokenizer.getAbsoluteIndex(),this.attribs&&((r=(n=this.cbs).onopentag)===null||r===void 0||r.call(n,this.tagname,this.attribs,t),this.attribs=null),this.cbs.onclosetag&&this.isVoidElement(this.tagname)&&this.cbs.onclosetag(this.tagname,!0),this.tagname=""},e.prototype.onopentagend=function(){this.endOpenTag(!1),this.startIndex=this.endIndex+1},e.prototype.onclosetag=function(t){var n,r,i,o,s,a;if(this.endIndex=this.tokenizer.getAbsoluteIndex(),this.lowerCaseTagNames&&(t=t.toLowerCase()),(F8.has(t)||B8.has(t))&&this.foreignContext.pop(),this.isVoidElement(t))!this.options.xmlMode&&t==="br"&&((r=(n=this.cbs).onopentagname)===null||r===void 0||r.call(n,t),(o=(i=this.cbs).onopentag)===null||o===void 0||o.call(i,t,{},!0),(a=(s=this.cbs).onclosetag)===null||a===void 0||a.call(s,t,!1));else{var l=this.stack.lastIndexOf(t);if(l!==-1)if(this.cbs.onclosetag)for(var c=this.stack.length-l;c--;)this.cbs.onclosetag(this.stack.pop(),c!==0);else this.stack.length=l;else!this.options.xmlMode&&t==="p"&&(this.emitOpenTag(t),this.closeCurrentTag(!0))}this.startIndex=this.endIndex+1},e.prototype.onselfclosingtag=function(){this.options.xmlMode||this.options.recognizeSelfClosing||this.foreignContext[this.foreignContext.length-1]?(this.closeCurrentTag(!1),this.startIndex=this.endIndex+1):this.onopentagend()},e.prototype.closeCurrentTag=function(t){var n,r,i=this.tagname;this.endOpenTag(t),this.stack[this.stack.length-1]===i&&((r=(n=this.cbs).onclosetag)===null||r===void 0||r.call(n,i,!t),this.stack.pop())},e.prototype.onattribname=function(t){this.startIndex=this.tokenizer.getAbsoluteSectionStart(),this.lowerCaseAttributeNames&&(t=t.toLowerCase()),this.attribname=t},e.prototype.onattribdata=function(t){this.attribvalue+=t},e.prototype.onattribend=function(t){var n,r;this.endIndex=this.tokenizer.getAbsoluteIndex(),(r=(n=this.cbs).onattribute)===null||r===void 0||r.call(n,this.attribname,this.attribvalue,t),this.attribs&&!Object.prototype.hasOwnProperty.call(this.attribs,this.attribname)&&(this.attribs[this.attribname]=this.attribvalue),this.attribname="",this.attribvalue=""},e.prototype.getInstructionName=function(t){var n=t.search(vw),r=n<0?t:t.substr(0,n);return this.lowerCaseTagNames&&(r=r.toLowerCase()),r},e.prototype.ondeclaration=function(t){if(this.endIndex=this.tokenizer.getAbsoluteIndex(),this.cbs.onprocessinginstruction){var n=this.getInstructionName(t);this.cbs.onprocessinginstruction("!"+n,"!"+t)}this.startIndex=this.endIndex+1},e.prototype.onprocessinginstruction=function(t){if(this.endIndex=this.tokenizer.getAbsoluteIndex(),this.cbs.onprocessinginstruction){var n=this.getInstructionName(t);this.cbs.onprocessinginstruction("?"+n,"?"+t)}this.startIndex=this.endIndex+1},e.prototype.oncomment=function(t){var n,r,i,o;this.endIndex=this.tokenizer.getAbsoluteIndex(),(r=(n=this.cbs).oncomment)===null||r===void 0||r.call(n,t),(o=(i=this.cbs).oncommentend)===null||o===void 0||o.call(i),this.startIndex=this.endIndex+1},e.prototype.oncdata=function(t){var n,r,i,o,s,a,l,c,u,f;this.endIndex=this.tokenizer.getAbsoluteIndex(),this.options.xmlMode||this.options.recognizeCDATA?((r=(n=this.cbs).oncdatastart)===null||r===void 0||r.call(n),(o=(i=this.cbs).ontext)===null||o===void 0||o.call(i,t),(a=(s=this.cbs).oncdataend)===null||a===void 0||a.call(s)):((c=(l=this.cbs).oncomment)===null||c===void 0||c.call(l,"[CDATA["+t+"]]"),(f=(u=this.cbs).oncommentend)===null||f===void 0||f.call(u)),this.startIndex=this.endIndex+1},e.prototype.onerror=function(t){var n,r;(r=(n=this.cbs).onerror)===null||r===void 0||r.call(n,t)},e.prototype.onend=function(){var t,n;if(this.cbs.onclosetag){this.endIndex=this.startIndex;for(var r=this.stack.length;r>0;this.cbs.onclosetag(this.stack[--r],!0));}(n=(t=this.cbs).onend)===null||n===void 0||n.call(t)},e.prototype.reset=function(){var t,n,r,i;(n=(t=this.cbs).onreset)===null||n===void 0||n.call(t),this.tokenizer.reset(),this.tagname="",this.attribname="",this.attribs=null,this.stack=[],this.startIndex=0,this.endIndex=0,(i=(r=this.cbs).onparserinit)===null||i===void 0||i.call(r,this)},e.prototype.parseComplete=function(t){this.reset(),this.end(t)},e.prototype.write=function(t){this.tokenizer.write(t)},e.prototype.end=function(t){this.tokenizer.end(t)},e.prototype.pause=function(){this.tokenizer.pause()},e.prototype.resume=function(){this.tokenizer.resume()},e.prototype.parseChunk=function(t){this.write(t)},e.prototype.done=function(t){this.end(t)},e}();ao.Parser=yw;var xn={},z0={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.Doctype=e.CDATA=e.Tag=e.Style=e.Script=e.Comment=e.Directive=e.Text=e.Root=e.isTag=e.ElementType=void 0;var t;(function(r){r.Root="root",r.Text="text",r.Directive="directive",r.Comment="comment",r.Script="script",r.Style="style",r.Tag="tag",r.CDATA="cdata",r.Doctype="doctype"})(t=e.ElementType||(e.ElementType={}));function n(r){return r.type===t.Tag||r.type===t.Script||r.type===t.Style}e.isTag=n,e.Root=t.Root,e.Text=t.Text,e.Directive=t.Directive,e.Comment=t.Comment,e.Script=t.Script,e.Style=t.Style,e.Tag=t.Tag,e.CDATA=t.CDATA,e.Doctype=t.Doctype})(z0);var $={},Fr=V&&V.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(r[o]=i[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),xi=V&&V.__assign||function(){return xi=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++){t=arguments[n];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e},xi.apply(this,arguments)};Object.defineProperty($,"__esModule",{value:!0});$.cloneNode=$.hasChildren=$.isDocument=$.isDirective=$.isComment=$.isText=$.isCDATA=$.isTag=$.Element=$.Document=$.NodeWithChildren=$.ProcessingInstruction=$.Comment=$.Text=$.DataNode=$.Node=void 0;var ft=z0,_w=new Map([[ft.ElementType.Tag,1],[ft.ElementType.Script,1],[ft.ElementType.Style,1],[ft.ElementType.Directive,1],[ft.ElementType.Text,3],[ft.ElementType.CDATA,4],[ft.ElementType.Comment,8],[ft.ElementType.Root,9]]),j0=function(){function e(t){this.type=t,this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}return Object.defineProperty(e.prototype,"nodeType",{get:function(){var t;return(t=_w.get(this.type))!==null&&t!==void 0?t:1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"parentNode",{get:function(){return this.parent},set:function(t){this.parent=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"previousSibling",{get:function(){return this.prev},set:function(t){this.prev=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"nextSibling",{get:function(){return this.next},set:function(t){this.next=t},enumerable:!1,configurable:!0}),e.prototype.cloneNode=function(t){return t===void 0&&(t=!1),U0(this,t)},e}();$.Node=j0;var wa=function(e){Fr(t,e);function t(n,r){var i=e.call(this,n)||this;return i.data=r,i}return Object.defineProperty(t.prototype,"nodeValue",{get:function(){return this.data},set:function(n){this.data=n},enumerable:!1,configurable:!0}),t}(j0);$.DataNode=wa;var zh=function(e){Fr(t,e);function t(n){return e.call(this,ft.ElementType.Text,n)||this}return t}(wa);$.Text=zh;var jh=function(e){Fr(t,e);function t(n){return e.call(this,ft.ElementType.Comment,n)||this}return t}(wa);$.Comment=jh;var Uh=function(e){Fr(t,e);function t(n,r){var i=e.call(this,ft.ElementType.Directive,r)||this;return i.name=n,i}return t}(wa);$.ProcessingInstruction=Uh;var xa=function(e){Fr(t,e);function t(n,r){var i=e.call(this,n)||this;return i.children=r,i}return Object.defineProperty(t.prototype,"firstChild",{get:function(){var n;return(n=this.children[0])!==null&&n!==void 0?n:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lastChild",{get:function(){return this.children.length>0?this.children[this.children.length-1]:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"childNodes",{get:function(){return this.children},set:function(n){this.children=n},enumerable:!1,configurable:!0}),t}(j0);$.NodeWithChildren=xa;var $h=function(e){Fr(t,e);function t(n){return e.call(this,ft.ElementType.Root,n)||this}return t}(xa);$.Document=$h;var Hh=function(e){Fr(t,e);function t(n,r,i,o){i===void 0&&(i=[]),o===void 0&&(o=n==="script"?ft.ElementType.Script:n==="style"?ft.ElementType.Style:ft.ElementType.Tag);var s=e.call(this,o,i)||this;return s.name=n,s.attribs=r,s}return Object.defineProperty(t.prototype,"tagName",{get:function(){return this.name},set:function(n){this.name=n},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"attributes",{get:function(){var n=this;return Object.keys(this.attribs).map(function(r){var i,o;return{name:r,value:n.attribs[r],namespace:(i=n["x-attribsNamespace"])===null||i===void 0?void 0:i[r],prefix:(o=n["x-attribsPrefix"])===null||o===void 0?void 0:o[r]}})},enumerable:!1,configurable:!0}),t}(xa);$.Element=Hh;function Gh(e){return(0,ft.isTag)(e)}$.isTag=Gh;function Wh(e){return e.type===ft.ElementType.CDATA}$.isCDATA=Wh;function Yh(e){return e.type===ft.ElementType.Text}$.isText=Yh;function Xh(e){return e.type===ft.ElementType.Comment}$.isComment=Xh;function Qh(e){return e.type===ft.ElementType.Directive}$.isDirective=Qh;function Kh(e){return e.type===ft.ElementType.Root}$.isDocument=Kh;function ww(e){return Object.prototype.hasOwnProperty.call(e,"children")}$.hasChildren=ww;function U0(e,t){t===void 0&&(t=!1);var n;if(Yh(e))n=new zh(e.data);else if(Xh(e))n=new jh(e.data);else if(Gh(e)){var r=t?xl(e.children):[],i=new Hh(e.name,xi({},e.attribs),r);r.forEach(function(l){return l.parent=i}),e.namespace!=null&&(i.namespace=e.namespace),e["x-attribsNamespace"]&&(i["x-attribsNamespace"]=xi({},e["x-attribsNamespace"])),e["x-attribsPrefix"]&&(i["x-attribsPrefix"]=xi({},e["x-attribsPrefix"])),n=i}else if(Wh(e)){var r=t?xl(e.children):[],o=new xa(ft.ElementType.CDATA,r);r.forEach(function(c){return c.parent=o}),n=o}else if(Kh(e)){var r=t?xl(e.children):[],s=new $h(r);r.forEach(function(c){return c.parent=s}),e["x-mode"]&&(s["x-mode"]=e["x-mode"]),n=s}else if(Qh(e)){var a=new Uh(e.name,e.data);e["x-name"]!=null&&(a["x-name"]=e["x-name"],a["x-publicId"]=e["x-publicId"],a["x-systemId"]=e["x-systemId"]),n=a}else throw new Error("Not implemented yet: ".concat(e.type));return n.startIndex=e.startIndex,n.endIndex=e.endIndex,e.sourceCodeLocation!=null&&(n.sourceCodeLocation=e.sourceCodeLocation),n}$.cloneNode=U0;function xl(e){for(var t=e.map(function(r){return U0(r,!0)}),n=1;n<t.length;n++)t[n].prev=t[n-1],t[n-1].next=t[n];return t}(function(e){var t=V&&V.__createBinding||(Object.create?function(l,c,u,f){f===void 0&&(f=u);var d=Object.getOwnPropertyDescriptor(c,u);(!d||("get"in d?!c.__esModule:d.writable||d.configurable))&&(d={enumerable:!0,get:function(){return c[u]}}),Object.defineProperty(l,f,d)}:function(l,c,u,f){f===void 0&&(f=u),l[f]=c[u]}),n=V&&V.__exportStar||function(l,c){for(var u in l)u!=="default"&&!Object.prototype.hasOwnProperty.call(c,u)&&t(c,l,u)};Object.defineProperty(e,"__esModule",{value:!0}),e.DomHandler=void 0;var r=z0,i=$;n($,e);var o=/\s+/g,s={normalizeWhitespace:!1,withStartIndices:!1,withEndIndices:!1,xmlMode:!1},a=function(){function l(c,u,f){this.dom=[],this.root=new i.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,typeof u=="function"&&(f=u,u=s),typeof c=="object"&&(u=c,c=void 0),this.callback=c!=null?c:null,this.options=u!=null?u:s,this.elementCB=f!=null?f:null}return l.prototype.onparserinit=function(c){this.parser=c},l.prototype.onreset=function(){this.dom=[],this.root=new i.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null},l.prototype.onend=function(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))},l.prototype.onerror=function(c){this.handleCallback(c)},l.prototype.onclosetag=function(){this.lastNode=null;var c=this.tagStack.pop();this.options.withEndIndices&&(c.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(c)},l.prototype.onopentag=function(c,u){var f=this.options.xmlMode?r.ElementType.Tag:void 0,d=new i.Element(c,u,void 0,f);this.addNode(d),this.tagStack.push(d)},l.prototype.ontext=function(c){var u=this.options.normalizeWhitespace,f=this.lastNode;if(f&&f.type===r.ElementType.Text)u?f.data=(f.data+c).replace(o," "):f.data+=c,this.options.withEndIndices&&(f.endIndex=this.parser.endIndex);else{u&&(c=c.replace(o," "));var d=new i.Text(c);this.addNode(d),this.lastNode=d}},l.prototype.oncomment=function(c){if(this.lastNode&&this.lastNode.type===r.ElementType.Comment){this.lastNode.data+=c;return}var u=new i.Comment(c);this.addNode(u),this.lastNode=u},l.prototype.oncommentend=function(){this.lastNode=null},l.prototype.oncdatastart=function(){var c=new i.Text(""),u=new i.NodeWithChildren(r.ElementType.CDATA,[c]);this.addNode(u),c.parent=u,this.lastNode=c},l.prototype.oncdataend=function(){this.lastNode=null},l.prototype.onprocessinginstruction=function(c,u){var f=new i.ProcessingInstruction(c,u);this.addNode(f)},l.prototype.handleCallback=function(c){if(typeof this.callback=="function")this.callback(c,this.dom);else if(c)throw c},l.prototype.addNode=function(c){var u=this.tagStack[this.tagStack.length-1],f=u.children[u.children.length-1];this.options.withStartIndices&&(c.startIndex=this.parser.startIndex),this.options.withEndIndices&&(c.endIndex=this.parser.endIndex),u.children.push(c),f&&(c.prev=f,f.next=c),c.parent=u,this.lastNode=null},l}();e.DomHandler=a,e.default=a})(xn);var Zh={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.Doctype=e.CDATA=e.Tag=e.Style=e.Script=e.Comment=e.Directive=e.Text=e.Root=e.isTag=e.ElementType=void 0;var t;(function(r){r.Root="root",r.Text="text",r.Directive="directive",r.Comment="comment",r.Script="script",r.Style="style",r.Tag="tag",r.CDATA="cdata",r.Doctype="doctype"})(t=e.ElementType||(e.ElementType={}));function n(r){return r.type===t.Tag||r.type===t.Script||r.type===t.Style}e.isTag=n,e.Root=t.Root,e.Text=t.Text,e.Directive=t.Directive,e.Comment=t.Comment,e.Script=t.Script,e.Style=t.Style,e.Tag=t.Tag,e.CDATA=t.CDATA,e.Doctype=t.Doctype})(Zh);var Yc={},$0={},le={},H0={},G0={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.Doctype=e.CDATA=e.Tag=e.Style=e.Script=e.Comment=e.Directive=e.Text=e.Root=e.isTag=e.ElementType=void 0;var t;(function(r){r.Root="root",r.Text="text",r.Directive="directive",r.Comment="comment",r.Script="script",r.Style="style",r.Tag="tag",r.CDATA="cdata",r.Doctype="doctype"})(t=e.ElementType||(e.ElementType={}));function n(r){return r.type===t.Tag||r.type===t.Script||r.type===t.Style}e.isTag=n,e.Root=t.Root,e.Text=t.Text,e.Directive=t.Directive,e.Comment=t.Comment,e.Script=t.Script,e.Style=t.Style,e.Tag=t.Tag,e.CDATA=t.CDATA,e.Doctype=t.Doctype})(G0);var Jh={},hn={};const xw="Á",Sw="á",Tw="Ă",bw="ă",Cw="∾",Ew="∿",kw="∾̳",Pw="Â",Dw="â",Aw="´",Lw="А",Ow="а",Mw="Æ",Nw="æ",Rw="⁡",Iw="𝔄",Vw="𝔞",qw="À",Fw="à",Bw="ℵ",zw="ℵ",jw="Α",Uw="α",$w="Ā",Hw="ā",Gw="⨿",Ww="&",Yw="&",Xw="⩕",Qw="⩓",Kw="∧",Zw="⩜",Jw="⩘",tx="⩚",ex="∠",nx="⦤",rx="∠",ix="⦨",ox="⦩",sx="⦪",ax="⦫",lx="⦬",cx="⦭",ux="⦮",fx="⦯",dx="∡",hx="∟",px="⊾",mx="⦝",gx="∢",vx="Å",yx="⍼",_x="Ą",wx="ą",xx="𝔸",Sx="𝕒",Tx="⩯",bx="≈",Cx="⩰",Ex="≊",kx="≋",Px="'",Dx="⁡",Ax="≈",Lx="≊",Ox="Å",Mx="å",Nx="𝒜",Rx="𝒶",Ix="≔",Vx="*",qx="≈",Fx="≍",Bx="Ã",zx="ã",jx="Ä",Ux="ä",$x="∳",Hx="⨑",Gx="≌",Wx="϶",Yx="‵",Xx="∽",Qx="⋍",Kx="∖",Zx="⫧",Jx="⊽",tS="⌅",eS="⌆",nS="⌅",rS="⎵",iS="⎶",oS="≌",sS="Б",aS="б",lS="„",cS="∵",uS="∵",fS="∵",dS="⦰",hS="϶",pS="ℬ",mS="ℬ",gS="Β",vS="β",yS="ℶ",_S="≬",wS="𝔅",xS="𝔟",SS="⋂",TS="◯",bS="⋃",CS="⨀",ES="⨁",kS="⨂",PS="⨆",DS="★",AS="▽",LS="△",OS="⨄",MS="⋁",NS="⋀",RS="⤍",IS="⧫",VS="▪",qS="▴",FS="▾",BS="◂",zS="▸",jS="␣",US="▒",$S="░",HS="▓",GS="█",WS="=⃥",YS="≡⃥",XS="⫭",QS="⌐",KS="𝔹",ZS="𝕓",JS="⊥",tT="⊥",eT="⋈",nT="⧉",rT="┐",iT="╕",oT="╖",sT="╗",aT="┌",lT="╒",cT="╓",uT="╔",fT="─",dT="═",hT="┬",pT="╤",mT="╥",gT="╦",vT="┴",yT="╧",_T="╨",wT="╩",xT="⊟",ST="⊞",TT="⊠",bT="┘",CT="╛",ET="╜",kT="╝",PT="└",DT="╘",AT="╙",LT="╚",OT="│",MT="║",NT="┼",RT="╪",IT="╫",VT="╬",qT="┤",FT="╡",BT="╢",zT="╣",jT="├",UT="╞",$T="╟",HT="╠",GT="‵",WT="˘",YT="˘",XT="¦",QT="𝒷",KT="ℬ",ZT="⁏",JT="∽",tb="⋍",eb="⧅",nb="\\",rb="⟈",ib="•",ob="•",sb="≎",ab="⪮",lb="≏",cb="≎",ub="≏",fb="Ć",db="ć",hb="⩄",pb="⩉",mb="⩋",gb="∩",vb="⋒",yb="⩇",_b="⩀",wb="ⅅ",xb="∩︀",Sb="⁁",Tb="ˇ",bb="ℭ",Cb="⩍",Eb="Č",kb="č",Pb="Ç",Db="ç",Ab="Ĉ",Lb="ĉ",Ob="∰",Mb="⩌",Nb="⩐",Rb="Ċ",Ib="ċ",Vb="¸",qb="¸",Fb="⦲",Bb="¢",zb="·",jb="·",Ub="𝔠",$b="ℭ",Hb="Ч",Gb="ч",Wb="✓",Yb="✓",Xb="Χ",Qb="χ",Kb="ˆ",Zb="≗",Jb="↺",tC="↻",eC="⊛",nC="⊚",rC="⊝",iC="⊙",oC="®",sC="Ⓢ",aC="⊖",lC="⊕",cC="⊗",uC="○",fC="⧃",dC="≗",hC="⨐",pC="⫯",mC="⧂",gC="∲",vC="”",yC="’",_C="♣",wC="♣",xC=":",SC="∷",TC="⩴",bC="≔",CC="≔",EC=",",kC="@",PC="∁",DC="∘",AC="∁",LC="ℂ",OC="≅",MC="⩭",NC="≡",RC="∮",IC="∯",VC="∮",qC="𝕔",FC="ℂ",BC="∐",zC="∐",jC="©",UC="©",$C="℗",HC="∳",GC="↵",WC="✗",YC="⨯",XC="𝒞",QC="𝒸",KC="⫏",ZC="⫑",JC="⫐",tE="⫒",eE="⋯",nE="⤸",rE="⤵",iE="⋞",oE="⋟",sE="↶",aE="⤽",lE="⩈",cE="⩆",uE="≍",fE="∪",dE="⋓",hE="⩊",pE="⊍",mE="⩅",gE="∪︀",vE="↷",yE="⤼",_E="⋞",wE="⋟",xE="⋎",SE="⋏",TE="¤",bE="↶",CE="↷",EE="⋎",kE="⋏",PE="∲",DE="∱",AE="⌭",LE="†",OE="‡",ME="ℸ",NE="↓",RE="↡",IE="⇓",VE="‐",qE="⫤",FE="⊣",BE="⤏",zE="˝",jE="Ď",UE="ď",$E="Д",HE="д",GE="‡",WE="⇊",YE="ⅅ",XE="ⅆ",QE="⤑",KE="⩷",ZE="°",JE="∇",tk="Δ",ek="δ",nk="⦱",rk="⥿",ik="𝔇",ok="𝔡",sk="⥥",ak="⇃",lk="⇂",ck="´",uk="˙",fk="˝",dk="`",hk="˜",pk="⋄",mk="⋄",gk="⋄",vk="♦",yk="♦",_k="¨",wk="ⅆ",xk="ϝ",Sk="⋲",Tk="÷",bk="÷",Ck="⋇",Ek="⋇",kk="Ђ",Pk="ђ",Dk="⌞",Ak="⌍",Lk="$",Ok="𝔻",Mk="𝕕",Nk="¨",Rk="˙",Ik="⃜",Vk="≐",qk="≑",Fk="≐",Bk="∸",zk="∔",jk="⊡",Uk="⌆",$k="∯",Hk="¨",Gk="⇓",Wk="⇐",Yk="⇔",Xk="⫤",Qk="⟸",Kk="⟺",Zk="⟹",Jk="⇒",tP="⊨",eP="⇑",nP="⇕",rP="∥",iP="⤓",oP="↓",sP="↓",aP="⇓",lP="⇵",cP="̑",uP="⇊",fP="⇃",dP="⇂",hP="⥐",pP="⥞",mP="⥖",gP="↽",vP="⥟",yP="⥗",_P="⇁",wP="↧",xP="⊤",SP="⤐",TP="⌟",bP="⌌",CP="𝒟",EP="𝒹",kP="Ѕ",PP="ѕ",DP="⧶",AP="Đ",LP="đ",OP="⋱",MP="▿",NP="▾",RP="⇵",IP="⥯",VP="⦦",qP="Џ",FP="џ",BP="⟿",zP="É",jP="é",UP="⩮",$P="Ě",HP="ě",GP="Ê",WP="ê",YP="≖",XP="≕",QP="Э",KP="э",ZP="⩷",JP="Ė",tD="ė",eD="≑",nD="ⅇ",rD="≒",iD="𝔈",oD="𝔢",sD="⪚",aD="È",lD="è",cD="⪖",uD="⪘",fD="⪙",dD="∈",hD="⏧",pD="ℓ",mD="⪕",gD="⪗",vD="Ē",yD="ē",_D="∅",wD="∅",xD="◻",SD="∅",TD="▫",bD=" ",CD=" ",ED=" ",kD="Ŋ",PD="ŋ",DD=" ",AD="Ę",LD="ę",OD="𝔼",MD="𝕖",ND="⋕",RD="⧣",ID="⩱",VD="ε",qD="Ε",FD="ε",BD="ϵ",zD="≖",jD="≕",UD="≂",$D="⪖",HD="⪕",GD="⩵",WD="=",YD="≂",XD="≟",QD="⇌",KD="≡",ZD="⩸",JD="⧥",tA="⥱",eA="≓",nA="ℯ",rA="ℰ",iA="≐",oA="⩳",sA="≂",aA="Η",lA="η",cA="Ð",uA="ð",fA="Ë",dA="ë",hA="€",pA="!",mA="∃",gA="∃",vA="ℰ",yA="ⅇ",_A="ⅇ",wA="≒",xA="Ф",SA="ф",TA="♀",bA="ﬃ",CA="ﬀ",EA="ﬄ",kA="𝔉",PA="𝔣",DA="ﬁ",AA="◼",LA="▪",OA="fj",MA="♭",NA="ﬂ",RA="▱",IA="ƒ",VA="𝔽",qA="𝕗",FA="∀",BA="∀",zA="⋔",jA="⫙",UA="ℱ",$A="⨍",HA="½",GA="⅓",WA="¼",YA="⅕",XA="⅙",QA="⅛",KA="⅔",ZA="⅖",JA="¾",tL="⅗",eL="⅜",nL="⅘",rL="⅚",iL="⅝",oL="⅞",sL="⁄",aL="⌢",lL="𝒻",cL="ℱ",uL="ǵ",fL="Γ",dL="γ",hL="Ϝ",pL="ϝ",mL="⪆",gL="Ğ",vL="ğ",yL="Ģ",_L="Ĝ",wL="ĝ",xL="Г",SL="г",TL="Ġ",bL="ġ",CL="≥",EL="≧",kL="⪌",PL="⋛",DL="≥",AL="≧",LL="⩾",OL="⪩",ML="⩾",NL="⪀",RL="⪂",IL="⪄",VL="⋛︀",qL="⪔",FL="𝔊",BL="𝔤",zL="≫",jL="⋙",UL="⋙",$L="ℷ",HL="Ѓ",GL="ѓ",WL="⪥",YL="≷",XL="⪒",QL="⪤",KL="⪊",ZL="⪊",JL="⪈",tO="≩",eO="⪈",nO="≩",rO="⋧",iO="𝔾",oO="𝕘",sO="`",aO="≥",lO="⋛",cO="≧",uO="⪢",fO="≷",dO="⩾",hO="≳",pO="𝒢",mO="ℊ",gO="≳",vO="⪎",yO="⪐",_O="⪧",wO="⩺",xO=">",SO=">",TO="≫",bO="⋗",CO="⦕",EO="⩼",kO="⪆",PO="⥸",DO="⋗",AO="⋛",LO="⪌",OO="≷",MO="≳",NO="≩︀",RO="≩︀",IO="ˇ",VO=" ",qO="½",FO="ℋ",BO="Ъ",zO="ъ",jO="⥈",UO="↔",$O="⇔",HO="↭",GO="^",WO="ℏ",YO="Ĥ",XO="ĥ",QO="♥",KO="♥",ZO="…",JO="⊹",tM="𝔥",eM="ℌ",nM="ℋ",rM="⤥",iM="⤦",oM="⇿",sM="∻",aM="↩",lM="↪",cM="𝕙",uM="ℍ",fM="―",dM="─",hM="𝒽",pM="ℋ",mM="ℏ",gM="Ħ",vM="ħ",yM="≎",_M="≏",wM="⁃",xM="‐",SM="Í",TM="í",bM="⁣",CM="Î",EM="î",kM="И",PM="и",DM="İ",AM="Е",LM="е",OM="¡",MM="⇔",NM="𝔦",RM="ℑ",IM="Ì",VM="ì",qM="ⅈ",FM="⨌",BM="∭",zM="⧜",jM="℩",UM="Ĳ",$M="ĳ",HM="Ī",GM="ī",WM="ℑ",YM="ⅈ",XM="ℐ",QM="ℑ",KM="ı",ZM="ℑ",JM="⊷",tN="Ƶ",eN="⇒",nN="℅",rN="∞",iN="⧝",oN="ı",sN="⊺",aN="∫",lN="∬",cN="ℤ",uN="∫",fN="⊺",dN="⋂",hN="⨗",pN="⨼",mN="⁣",gN="⁢",vN="Ё",yN="ё",_N="Į",wN="į",xN="𝕀",SN="𝕚",TN="Ι",bN="ι",CN="⨼",EN="¿",kN="𝒾",PN="ℐ",DN="∈",AN="⋵",LN="⋹",ON="⋴",MN="⋳",NN="∈",RN="⁢",IN="Ĩ",VN="ĩ",qN="І",FN="і",BN="Ï",zN="ï",jN="Ĵ",UN="ĵ",$N="Й",HN="й",GN="𝔍",WN="𝔧",YN="ȷ",XN="𝕁",QN="𝕛",KN="𝒥",ZN="𝒿",JN="Ј",tR="ј",eR="Є",nR="є",rR="Κ",iR="κ",oR="ϰ",sR="Ķ",aR="ķ",lR="К",cR="к",uR="𝔎",fR="𝔨",dR="ĸ",hR="Х",pR="х",mR="Ќ",gR="ќ",vR="𝕂",yR="𝕜",_R="𝒦",wR="𝓀",xR="⇚",SR="Ĺ",TR="ĺ",bR="⦴",CR="ℒ",ER="Λ",kR="λ",PR="⟨",DR="⟪",AR="⦑",LR="⟨",OR="⪅",MR="ℒ",NR="«",RR="⇤",IR="⤟",VR="←",qR="↞",FR="⇐",BR="⤝",zR="↩",jR="↫",UR="⤹",$R="⥳",HR="↢",GR="⤙",WR="⤛",YR="⪫",XR="⪭",QR="⪭︀",KR="⤌",ZR="⤎",JR="❲",tI="{",eI="[",nI="⦋",rI="⦏",iI="⦍",oI="Ľ",sI="ľ",aI="Ļ",lI="ļ",cI="⌈",uI="{",fI="Л",dI="л",hI="⤶",pI="“",mI="„",gI="⥧",vI="⥋",yI="↲",_I="≤",wI="≦",xI="⟨",SI="⇤",TI="←",bI="←",CI="⇐",EI="⇆",kI="↢",PI="⌈",DI="⟦",AI="⥡",LI="⥙",OI="⇃",MI="⌊",NI="↽",RI="↼",II="⇇",VI="↔",qI="↔",FI="⇔",BI="⇆",zI="⇋",jI="↭",UI="⥎",$I="↤",HI="⊣",GI="⥚",WI="⋋",YI="⧏",XI="⊲",QI="⊴",KI="⥑",ZI="⥠",JI="⥘",tV="↿",eV="⥒",nV="↼",rV="⪋",iV="⋚",oV="≤",sV="≦",aV="⩽",lV="⪨",cV="⩽",uV="⩿",fV="⪁",dV="⪃",hV="⋚︀",pV="⪓",mV="⪅",gV="⋖",vV="⋚",yV="⪋",_V="⋚",wV="≦",xV="≶",SV="≶",TV="⪡",bV="≲",CV="⩽",EV="≲",kV="⥼",PV="⌊",DV="𝔏",AV="𝔩",LV="≶",OV="⪑",MV="⥢",NV="↽",RV="↼",IV="⥪",VV="▄",qV="Љ",FV="љ",BV="⇇",zV="≪",jV="⋘",UV="⌞",$V="⇚",HV="⥫",GV="◺",WV="Ŀ",YV="ŀ",XV="⎰",QV="⎰",KV="⪉",ZV="⪉",JV="⪇",tq="≨",eq="⪇",nq="≨",rq="⋦",iq="⟬",oq="⇽",sq="⟦",aq="⟵",lq="⟵",cq="⟸",uq="⟷",fq="⟷",dq="⟺",hq="⟼",pq="⟶",mq="⟶",gq="⟹",vq="↫",yq="↬",_q="⦅",wq="𝕃",xq="𝕝",Sq="⨭",Tq="⨴",bq="∗",Cq="_",Eq="↙",kq="↘",Pq="◊",Dq="◊",Aq="⧫",Lq="(",Oq="⦓",Mq="⇆",Nq="⌟",Rq="⇋",Iq="⥭",Vq="‎",qq="⊿",Fq="‹",Bq="𝓁",zq="ℒ",jq="↰",Uq="↰",$q="≲",Hq="⪍",Gq="⪏",Wq="[",Yq="‘",Xq="‚",Qq="Ł",Kq="ł",Zq="⪦",Jq="⩹",tF="<",eF="<",nF="≪",rF="⋖",iF="⋋",oF="⋉",sF="⥶",aF="⩻",lF="◃",cF="⊴",uF="◂",fF="⦖",dF="⥊",hF="⥦",pF="≨︀",mF="≨︀",gF="¯",vF="♂",yF="✠",_F="✠",wF="↦",xF="↦",SF="↧",TF="↤",bF="↥",CF="▮",EF="⨩",kF="М",PF="м",DF="—",AF="∺",LF="∡",OF=" ",MF="ℳ",NF="𝔐",RF="𝔪",IF="℧",VF="µ",qF="*",FF="⫰",BF="∣",zF="·",jF="⊟",UF="−",$F="∸",HF="⨪",GF="∓",WF="⫛",YF="…",XF="∓",QF="⊧",KF="𝕄",ZF="𝕞",JF="∓",tB="𝓂",eB="ℳ",nB="∾",rB="Μ",iB="μ",oB="⊸",sB="⊸",aB="∇",lB="Ń",cB="ń",uB="∠⃒",fB="≉",dB="⩰̸",hB="≋̸",pB="ŉ",mB="≉",gB="♮",vB="ℕ",yB="♮",_B=" ",wB="≎̸",xB="≏̸",SB="⩃",TB="Ň",bB="ň",CB="Ņ",EB="ņ",kB="≇",PB="⩭̸",DB="⩂",AB="Н",LB="н",OB="–",MB="⤤",NB="↗",RB="⇗",IB="↗",VB="≠",qB="≐̸",FB="​",BB="​",zB="​",jB="​",UB="≢",$B="⤨",HB="≂̸",GB="≫",WB="≪",YB=`
`,XB="∄",QB="∄",KB="𝔑",ZB="𝔫",JB="≧̸",tz="≱",ez="≱",nz="≧̸",rz="⩾̸",iz="⩾̸",oz="⋙̸",sz="≵",az="≫⃒",lz="≯",cz="≯",uz="≫̸",fz="↮",dz="⇎",hz="⫲",pz="∋",mz="⋼",gz="⋺",vz="∋",yz="Њ",_z="њ",wz="↚",xz="⇍",Sz="‥",Tz="≦̸",bz="≰",Cz="↚",Ez="⇍",kz="↮",Pz="⇎",Dz="≰",Az="≦̸",Lz="⩽̸",Oz="⩽̸",Mz="≮",Nz="⋘̸",Rz="≴",Iz="≪⃒",Vz="≮",qz="⋪",Fz="⋬",Bz="≪̸",zz="∤",jz="⁠",Uz=" ",$z="𝕟",Hz="ℕ",Gz="⫬",Wz="¬",Yz="≢",Xz="≭",Qz="∦",Kz="∉",Zz="≠",Jz="≂̸",tj="∄",ej="≯",nj="≱",rj="≧̸",ij="≫̸",oj="≹",sj="⩾̸",aj="≵",lj="≎̸",cj="≏̸",uj="∉",fj="⋵̸",dj="⋹̸",hj="∉",pj="⋷",mj="⋶",gj="⧏̸",vj="⋪",yj="⋬",_j="≮",wj="≰",xj="≸",Sj="≪̸",Tj="⩽̸",bj="≴",Cj="⪢̸",Ej="⪡̸",kj="∌",Pj="∌",Dj="⋾",Aj="⋽",Lj="⊀",Oj="⪯̸",Mj="⋠",Nj="∌",Rj="⧐̸",Ij="⋫",Vj="⋭",qj="⊏̸",Fj="⋢",Bj="⊐̸",zj="⋣",jj="⊂⃒",Uj="⊈",$j="⊁",Hj="⪰̸",Gj="⋡",Wj="≿̸",Yj="⊃⃒",Xj="⊉",Qj="≁",Kj="≄",Zj="≇",Jj="≉",tU="∤",eU="∦",nU="∦",rU="⫽⃥",iU="∂̸",oU="⨔",sU="⊀",aU="⋠",lU="⊀",cU="⪯̸",uU="⪯̸",fU="⤳̸",dU="↛",hU="⇏",pU="↝̸",mU="↛",gU="⇏",vU="⋫",yU="⋭",_U="⊁",wU="⋡",xU="⪰̸",SU="𝒩",TU="𝓃",bU="∤",CU="∦",EU="≁",kU="≄",PU="≄",DU="∤",AU="∦",LU="⋢",OU="⋣",MU="⊄",NU="⫅̸",RU="⊈",IU="⊂⃒",VU="⊈",qU="⫅̸",FU="⊁",BU="⪰̸",zU="⊅",jU="⫆̸",UU="⊉",$U="⊃⃒",HU="⊉",GU="⫆̸",WU="≹",YU="Ñ",XU="ñ",QU="≸",KU="⋪",ZU="⋬",JU="⋫",t$="⋭",e$="Ν",n$="ν",r$="#",i$="№",o$=" ",s$="≍⃒",a$="⊬",l$="⊭",c$="⊮",u$="⊯",f$="≥⃒",d$=">⃒",h$="⤄",p$="⧞",m$="⤂",g$="≤⃒",v$="<⃒",y$="⊴⃒",_$="⤃",w$="⊵⃒",x$="∼⃒",S$="⤣",T$="↖",b$="⇖",C$="↖",E$="⤧",k$="Ó",P$="ó",D$="⊛",A$="Ô",L$="ô",O$="⊚",M$="О",N$="о",R$="⊝",I$="Ő",V$="ő",q$="⨸",F$="⊙",B$="⦼",z$="Œ",j$="œ",U$="⦿",$$="𝔒",H$="𝔬",G$="˛",W$="Ò",Y$="ò",X$="⧁",Q$="⦵",K$="Ω",Z$="∮",J$="↺",tH="⦾",eH="⦻",nH="‾",rH="⧀",iH="Ō",oH="ō",sH="Ω",aH="ω",lH="Ο",cH="ο",uH="⦶",fH="⊖",dH="𝕆",hH="𝕠",pH="⦷",mH="“",gH="‘",vH="⦹",yH="⊕",_H="↻",wH="⩔",xH="∨",SH="⩝",TH="ℴ",bH="ℴ",CH="ª",EH="º",kH="⊶",PH="⩖",DH="⩗",AH="⩛",LH="Ⓢ",OH="𝒪",MH="ℴ",NH="Ø",RH="ø",IH="⊘",VH="Õ",qH="õ",FH="⨶",BH="⨷",zH="⊗",jH="Ö",UH="ö",$H="⌽",HH="‾",GH="⏞",WH="⎴",YH="⏜",XH="¶",QH="∥",KH="∥",ZH="⫳",JH="⫽",tG="∂",eG="∂",nG="П",rG="п",iG="%",oG=".",sG="‰",aG="⊥",lG="‱",cG="𝔓",uG="𝔭",fG="Φ",dG="φ",hG="ϕ",pG="ℳ",mG="☎",gG="Π",vG="π",yG="⋔",_G="ϖ",wG="ℏ",xG="ℎ",SG="ℏ",TG="⨣",bG="⊞",CG="⨢",EG="+",kG="∔",PG="⨥",DG="⩲",AG="±",LG="±",OG="⨦",MG="⨧",NG="±",RG="ℌ",IG="⨕",VG="𝕡",qG="ℙ",FG="£",BG="⪷",zG="⪻",jG="≺",UG="≼",$G="⪷",HG="≺",GG="≼",WG="≺",YG="⪯",XG="≼",QG="≾",KG="⪯",ZG="⪹",JG="⪵",tW="⋨",eW="⪯",nW="⪳",rW="≾",iW="′",oW="″",sW="ℙ",aW="⪹",lW="⪵",cW="⋨",uW="∏",fW="∏",dW="⌮",hW="⌒",pW="⌓",mW="∝",gW="∝",vW="∷",yW="∝",_W="≾",wW="⊰",xW="𝒫",SW="𝓅",TW="Ψ",bW="ψ",CW=" ",EW="𝔔",kW="𝔮",PW="⨌",DW="𝕢",AW="ℚ",LW="⁗",OW="𝒬",MW="𝓆",NW="ℍ",RW="⨖",IW="?",VW="≟",qW='"',FW='"',BW="⇛",zW="∽̱",jW="Ŕ",UW="ŕ",$W="√",HW="⦳",GW="⟩",WW="⟫",YW="⦒",XW="⦥",QW="⟩",KW="»",ZW="⥵",JW="⇥",tY="⤠",eY="⤳",nY="→",rY="↠",iY="⇒",oY="⤞",sY="↪",aY="↬",lY="⥅",cY="⥴",uY="⤖",fY="↣",dY="↝",hY="⤚",pY="⤜",mY="∶",gY="ℚ",vY="⤍",yY="⤏",_Y="⤐",wY="❳",xY="}",SY="]",TY="⦌",bY="⦎",CY="⦐",EY="Ř",kY="ř",PY="Ŗ",DY="ŗ",AY="⌉",LY="}",OY="Р",MY="р",NY="⤷",RY="⥩",IY="”",VY="”",qY="↳",FY="ℜ",BY="ℛ",zY="ℜ",jY="ℝ",UY="ℜ",$Y="▭",HY="®",GY="®",WY="∋",YY="⇋",XY="⥯",QY="⥽",KY="⌋",ZY="𝔯",JY="ℜ",tX="⥤",eX="⇁",nX="⇀",rX="⥬",iX="Ρ",oX="ρ",sX="ϱ",aX="⟩",lX="⇥",cX="→",uX="→",fX="⇒",dX="⇄",hX="↣",pX="⌉",mX="⟧",gX="⥝",vX="⥕",yX="⇂",_X="⌋",wX="⇁",xX="⇀",SX="⇄",TX="⇌",bX="⇉",CX="↝",EX="↦",kX="⊢",PX="⥛",DX="⋌",AX="⧐",LX="⊳",OX="⊵",MX="⥏",NX="⥜",RX="⥔",IX="↾",VX="⥓",qX="⇀",FX="˚",BX="≓",zX="⇄",jX="⇌",UX="‏",$X="⎱",HX="⎱",GX="⫮",WX="⟭",YX="⇾",XX="⟧",QX="⦆",KX="𝕣",ZX="ℝ",JX="⨮",tQ="⨵",eQ="⥰",nQ=")",rQ="⦔",iQ="⨒",oQ="⇉",sQ="⇛",aQ="›",lQ="𝓇",cQ="ℛ",uQ="↱",fQ="↱",dQ="]",hQ="’",pQ="’",mQ="⋌",gQ="⋊",vQ="▹",yQ="⊵",_Q="▸",wQ="⧎",xQ="⧴",SQ="⥨",TQ="℞",bQ="Ś",CQ="ś",EQ="‚",kQ="⪸",PQ="Š",DQ="š",AQ="⪼",LQ="≻",OQ="≽",MQ="⪰",NQ="⪴",RQ="Ş",IQ="ş",VQ="Ŝ",qQ="ŝ",FQ="⪺",BQ="⪶",zQ="⋩",jQ="⨓",UQ="≿",$Q="С",HQ="с",GQ="⊡",WQ="⋅",YQ="⩦",XQ="⤥",QQ="↘",KQ="⇘",ZQ="↘",JQ="§",tK=";",eK="⤩",nK="∖",rK="∖",iK="✶",oK="𝔖",sK="𝔰",aK="⌢",lK="♯",cK="Щ",uK="щ",fK="Ш",dK="ш",hK="↓",pK="←",mK="∣",gK="∥",vK="→",yK="↑",_K="­",wK="Σ",xK="σ",SK="ς",TK="ς",bK="∼",CK="⩪",EK="≃",kK="≃",PK="⪞",DK="⪠",AK="⪝",LK="⪟",OK="≆",MK="⨤",NK="⥲",RK="←",IK="∘",VK="∖",qK="⨳",FK="⧤",BK="∣",zK="⌣",jK="⪪",UK="⪬",$K="⪬︀",HK="Ь",GK="ь",WK="⌿",YK="⧄",XK="/",QK="𝕊",KK="𝕤",ZK="♠",JK="♠",tZ="∥",eZ="⊓",nZ="⊓︀",rZ="⊔",iZ="⊔︀",oZ="√",sZ="⊏",aZ="⊑",lZ="⊏",cZ="⊑",uZ="⊐",fZ="⊒",dZ="⊐",hZ="⊒",pZ="□",mZ="□",gZ="⊓",vZ="⊏",yZ="⊑",_Z="⊐",wZ="⊒",xZ="⊔",SZ="▪",TZ="□",bZ="▪",CZ="→",EZ="𝒮",kZ="𝓈",PZ="∖",DZ="⌣",AZ="⋆",LZ="⋆",OZ="☆",MZ="★",NZ="ϵ",RZ="ϕ",IZ="¯",VZ="⊂",qZ="⋐",FZ="⪽",BZ="⫅",zZ="⊆",jZ="⫃",UZ="⫁",$Z="⫋",HZ="⊊",GZ="⪿",WZ="⥹",YZ="⊂",XZ="⋐",QZ="⊆",KZ="⫅",ZZ="⊆",JZ="⊊",tJ="⫋",eJ="⫇",nJ="⫕",rJ="⫓",iJ="⪸",oJ="≻",sJ="≽",aJ="≻",lJ="⪰",cJ="≽",uJ="≿",fJ="⪰",dJ="⪺",hJ="⪶",pJ="⋩",mJ="≿",gJ="∋",vJ="∑",yJ="∑",_J="♪",wJ="¹",xJ="²",SJ="³",TJ="⊃",bJ="⋑",CJ="⪾",EJ="⫘",kJ="⫆",PJ="⊇",DJ="⫄",AJ="⊃",LJ="⊇",OJ="⟉",MJ="⫗",NJ="⥻",RJ="⫂",IJ="⫌",VJ="⊋",qJ="⫀",FJ="⊃",BJ="⋑",zJ="⊇",jJ="⫆",UJ="⊋",$J="⫌",HJ="⫈",GJ="⫔",WJ="⫖",YJ="⤦",XJ="↙",QJ="⇙",KJ="↙",ZJ="⤪",JJ="ß",ttt="	",ett="⌖",ntt="Τ",rtt="τ",itt="⎴",ott="Ť",stt="ť",att="Ţ",ltt="ţ",ctt="Т",utt="т",ftt="⃛",dtt="⌕",htt="𝔗",ptt="𝔱",mtt="∴",gtt="∴",vtt="∴",ytt="Θ",_tt="θ",wtt="ϑ",xtt="ϑ",Stt="≈",Ttt="∼",btt="  ",Ctt=" ",Ett=" ",ktt="≈",Ptt="∼",Dtt="Þ",Att="þ",Ltt="˜",Ott="∼",Mtt="≃",Ntt="≅",Rtt="≈",Itt="⨱",Vtt="⊠",qtt="×",Ftt="⨰",Btt="∭",ztt="⤨",jtt="⌶",Utt="⫱",$tt="⊤",Htt="𝕋",Gtt="𝕥",Wtt="⫚",Ytt="⤩",Xtt="‴",Qtt="™",Ktt="™",Ztt="▵",Jtt="▿",tet="◃",eet="⊴",net="≜",ret="▹",iet="⊵",oet="◬",set="≜",aet="⨺",cet="⃛",uet="⨹",fet="⧍",det="⨻",het="⏢",pet="𝒯",met="𝓉",get="Ц",vet="ц",yet="Ћ",_et="ћ",wet="Ŧ",xet="ŧ",Tet="≬",bet="↞",Cet="↠",Eet="Ú",ket="ú",Pet="↑",Det="↟",Aet="⇑",Let="⥉",Oet="Ў",Met="ў",Net="Ŭ",Ret="ŭ",Iet="Û",Vet="û",qet="У",Fet="у",Bet="⇅",zet="Ű",jet="ű",Uet="⥮",$et="⥾",Het="𝔘",Get="𝔲",Wet="Ù",Yet="ù",Xet="⥣",Qet="↿",Ket="↾",Zet="▀",Jet="⌜",tnt="⌜",ent="⌏",nnt="◸",rnt="Ū",int="ū",ont="¨",snt="_",ant="⏟",lnt="⎵",cnt="⏝",unt="⋃",fnt="⊎",dnt="Ų",hnt="ų",pnt="𝕌",mnt="𝕦",gnt="⤒",vnt="↑",ynt="↑",_nt="⇑",wnt="⇅",xnt="↕",Snt="↕",Tnt="⇕",bnt="⥮",Cnt="↿",Ent="↾",knt="⊎",Pnt="↖",Dnt="↗",Ant="υ",Lnt="ϒ",Ont="ϒ",Mnt="Υ",Nnt="υ",Rnt="↥",Int="⊥",Vnt="⇈",qnt="⌝",Fnt="⌝",Bnt="⌎",znt="Ů",jnt="ů",Unt="◹",$nt="𝒰",Hnt="𝓊",Gnt="⋰",Wnt="Ũ",Ynt="ũ",Xnt="▵",Qnt="▴",Knt="⇈",Znt="Ü",Jnt="ü",trt="⦧",ert="⦜",nrt="ϵ",rrt="ϰ",irt="∅",ort="ϕ",srt="ϖ",art="∝",lrt="↕",crt="⇕",urt="ϱ",frt="ς",drt="⊊︀",hrt="⫋︀",prt="⊋︀",mrt="⫌︀",grt="ϑ",vrt="⊲",yrt="⊳",_rt="⫨",wrt="⫫",xrt="⫩",Srt="В",Trt="в",brt="⊢",Crt="⊨",Ert="⊩",krt="⊫",Prt="⫦",Drt="⊻",Art="∨",Lrt="⋁",Ort="≚",Mrt="⋮",Nrt="|",Rrt="‖",Irt="|",Vrt="‖",qrt="∣",Frt="|",Brt="❘",zrt="≀",jrt=" ",Urt="𝔙",$rt="𝔳",Hrt="⊲",Grt="⊂⃒",Wrt="⊃⃒",Yrt="𝕍",Xrt="𝕧",Qrt="∝",Krt="⊳",Zrt="𝒱",Jrt="𝓋",t1t="⫋︀",e1t="⊊︀",n1t="⫌︀",r1t="⊋︀",i1t="⊪",o1t="⦚",s1t="Ŵ",a1t="ŵ",l1t="⩟",c1t="∧",u1t="⋀",f1t="≙",d1t="℘",h1t="𝔚",p1t="𝔴",m1t="𝕎",g1t="𝕨",v1t="℘",y1t="≀",_1t="≀",w1t="𝒲",x1t="𝓌",S1t="⋂",T1t="◯",b1t="⋃",C1t="▽",E1t="𝔛",k1t="𝔵",P1t="⟷",D1t="⟺",A1t="Ξ",L1t="ξ",O1t="⟵",M1t="⟸",N1t="⟼",R1t="⋻",I1t="⨀",V1t="𝕏",q1t="𝕩",F1t="⨁",B1t="⨂",z1t="⟶",j1t="⟹",U1t="𝒳",$1t="𝓍",H1t="⨆",G1t="⨄",W1t="△",Y1t="⋁",X1t="⋀",Q1t="Ý",K1t="ý",Z1t="Я",J1t="я",tit="Ŷ",eit="ŷ",nit="Ы",rit="ы",iit="¥",oit="𝔜",sit="𝔶",ait="Ї",lit="ї",cit="𝕐",uit="𝕪",fit="𝒴",dit="𝓎",hit="Ю",pit="ю",mit="ÿ",git="Ÿ",vit="Ź",yit="ź",_it="Ž",wit="ž",xit="З",Sit="з",Tit="Ż",bit="ż",Cit="ℨ",Eit="​",kit="Ζ",Pit="ζ",Dit="𝔷",Ait="ℨ",Lit="Ж",Oit="ж",Mit="⇝",Nit="𝕫",Rit="ℤ",Iit="𝒵",Vit="𝓏",qit="‍",Fit="‌",tp={Aacute:xw,aacute:Sw,Abreve:Tw,abreve:bw,ac:Cw,acd:Ew,acE:kw,Acirc:Pw,acirc:Dw,acute:Aw,Acy:Lw,acy:Ow,AElig:Mw,aelig:Nw,af:Rw,Afr:Iw,afr:Vw,Agrave:qw,agrave:Fw,alefsym:Bw,aleph:zw,Alpha:jw,alpha:Uw,Amacr:$w,amacr:Hw,amalg:Gw,amp:Ww,AMP:Yw,andand:Xw,And:Qw,and:Kw,andd:Zw,andslope:Jw,andv:tx,ang:ex,ange:nx,angle:rx,angmsdaa:ix,angmsdab:ox,angmsdac:sx,angmsdad:ax,angmsdae:lx,angmsdaf:cx,angmsdag:ux,angmsdah:fx,angmsd:dx,angrt:hx,angrtvb:px,angrtvbd:mx,angsph:gx,angst:vx,angzarr:yx,Aogon:_x,aogon:wx,Aopf:xx,aopf:Sx,apacir:Tx,ap:bx,apE:Cx,ape:Ex,apid:kx,apos:Px,ApplyFunction:Dx,approx:Ax,approxeq:Lx,Aring:Ox,aring:Mx,Ascr:Nx,ascr:Rx,Assign:Ix,ast:Vx,asymp:qx,asympeq:Fx,Atilde:Bx,atilde:zx,Auml:jx,auml:Ux,awconint:$x,awint:Hx,backcong:Gx,backepsilon:Wx,backprime:Yx,backsim:Xx,backsimeq:Qx,Backslash:Kx,Barv:Zx,barvee:Jx,barwed:tS,Barwed:eS,barwedge:nS,bbrk:rS,bbrktbrk:iS,bcong:oS,Bcy:sS,bcy:aS,bdquo:lS,becaus:cS,because:uS,Because:fS,bemptyv:dS,bepsi:hS,bernou:pS,Bernoullis:mS,Beta:gS,beta:vS,beth:yS,between:_S,Bfr:wS,bfr:xS,bigcap:SS,bigcirc:TS,bigcup:bS,bigodot:CS,bigoplus:ES,bigotimes:kS,bigsqcup:PS,bigstar:DS,bigtriangledown:AS,bigtriangleup:LS,biguplus:OS,bigvee:MS,bigwedge:NS,bkarow:RS,blacklozenge:IS,blacksquare:VS,blacktriangle:qS,blacktriangledown:FS,blacktriangleleft:BS,blacktriangleright:zS,blank:jS,blk12:US,blk14:$S,blk34:HS,block:GS,bne:WS,bnequiv:YS,bNot:XS,bnot:QS,Bopf:KS,bopf:ZS,bot:JS,bottom:tT,bowtie:eT,boxbox:nT,boxdl:rT,boxdL:iT,boxDl:oT,boxDL:sT,boxdr:aT,boxdR:lT,boxDr:cT,boxDR:uT,boxh:fT,boxH:dT,boxhd:hT,boxHd:pT,boxhD:mT,boxHD:gT,boxhu:vT,boxHu:yT,boxhU:_T,boxHU:wT,boxminus:xT,boxplus:ST,boxtimes:TT,boxul:bT,boxuL:CT,boxUl:ET,boxUL:kT,boxur:PT,boxuR:DT,boxUr:AT,boxUR:LT,boxv:OT,boxV:MT,boxvh:NT,boxvH:RT,boxVh:IT,boxVH:VT,boxvl:qT,boxvL:FT,boxVl:BT,boxVL:zT,boxvr:jT,boxvR:UT,boxVr:$T,boxVR:HT,bprime:GT,breve:WT,Breve:YT,brvbar:XT,bscr:QT,Bscr:KT,bsemi:ZT,bsim:JT,bsime:tb,bsolb:eb,bsol:nb,bsolhsub:rb,bull:ib,bullet:ob,bump:sb,bumpE:ab,bumpe:lb,Bumpeq:cb,bumpeq:ub,Cacute:fb,cacute:db,capand:hb,capbrcup:pb,capcap:mb,cap:gb,Cap:vb,capcup:yb,capdot:_b,CapitalDifferentialD:wb,caps:xb,caret:Sb,caron:Tb,Cayleys:bb,ccaps:Cb,Ccaron:Eb,ccaron:kb,Ccedil:Pb,ccedil:Db,Ccirc:Ab,ccirc:Lb,Cconint:Ob,ccups:Mb,ccupssm:Nb,Cdot:Rb,cdot:Ib,cedil:Vb,Cedilla:qb,cemptyv:Fb,cent:Bb,centerdot:zb,CenterDot:jb,cfr:Ub,Cfr:$b,CHcy:Hb,chcy:Gb,check:Wb,checkmark:Yb,Chi:Xb,chi:Qb,circ:Kb,circeq:Zb,circlearrowleft:Jb,circlearrowright:tC,circledast:eC,circledcirc:nC,circleddash:rC,CircleDot:iC,circledR:oC,circledS:sC,CircleMinus:aC,CirclePlus:lC,CircleTimes:cC,cir:uC,cirE:fC,cire:dC,cirfnint:hC,cirmid:pC,cirscir:mC,ClockwiseContourIntegral:gC,CloseCurlyDoubleQuote:vC,CloseCurlyQuote:yC,clubs:_C,clubsuit:wC,colon:xC,Colon:SC,Colone:TC,colone:bC,coloneq:CC,comma:EC,commat:kC,comp:PC,compfn:DC,complement:AC,complexes:LC,cong:OC,congdot:MC,Congruent:NC,conint:RC,Conint:IC,ContourIntegral:VC,copf:qC,Copf:FC,coprod:BC,Coproduct:zC,copy:jC,COPY:UC,copysr:$C,CounterClockwiseContourIntegral:HC,crarr:GC,cross:WC,Cross:YC,Cscr:XC,cscr:QC,csub:KC,csube:ZC,csup:JC,csupe:tE,ctdot:eE,cudarrl:nE,cudarrr:rE,cuepr:iE,cuesc:oE,cularr:sE,cularrp:aE,cupbrcap:lE,cupcap:cE,CupCap:uE,cup:fE,Cup:dE,cupcup:hE,cupdot:pE,cupor:mE,cups:gE,curarr:vE,curarrm:yE,curlyeqprec:_E,curlyeqsucc:wE,curlyvee:xE,curlywedge:SE,curren:TE,curvearrowleft:bE,curvearrowright:CE,cuvee:EE,cuwed:kE,cwconint:PE,cwint:DE,cylcty:AE,dagger:LE,Dagger:OE,daleth:ME,darr:NE,Darr:RE,dArr:IE,dash:VE,Dashv:qE,dashv:FE,dbkarow:BE,dblac:zE,Dcaron:jE,dcaron:UE,Dcy:$E,dcy:HE,ddagger:GE,ddarr:WE,DD:YE,dd:XE,DDotrahd:QE,ddotseq:KE,deg:ZE,Del:JE,Delta:tk,delta:ek,demptyv:nk,dfisht:rk,Dfr:ik,dfr:ok,dHar:sk,dharl:ak,dharr:lk,DiacriticalAcute:ck,DiacriticalDot:uk,DiacriticalDoubleAcute:fk,DiacriticalGrave:dk,DiacriticalTilde:hk,diam:pk,diamond:mk,Diamond:gk,diamondsuit:vk,diams:yk,die:_k,DifferentialD:wk,digamma:xk,disin:Sk,div:Tk,divide:bk,divideontimes:Ck,divonx:Ek,DJcy:kk,djcy:Pk,dlcorn:Dk,dlcrop:Ak,dollar:Lk,Dopf:Ok,dopf:Mk,Dot:Nk,dot:Rk,DotDot:Ik,doteq:Vk,doteqdot:qk,DotEqual:Fk,dotminus:Bk,dotplus:zk,dotsquare:jk,doublebarwedge:Uk,DoubleContourIntegral:$k,DoubleDot:Hk,DoubleDownArrow:Gk,DoubleLeftArrow:Wk,DoubleLeftRightArrow:Yk,DoubleLeftTee:Xk,DoubleLongLeftArrow:Qk,DoubleLongLeftRightArrow:Kk,DoubleLongRightArrow:Zk,DoubleRightArrow:Jk,DoubleRightTee:tP,DoubleUpArrow:eP,DoubleUpDownArrow:nP,DoubleVerticalBar:rP,DownArrowBar:iP,downarrow:oP,DownArrow:sP,Downarrow:aP,DownArrowUpArrow:lP,DownBreve:cP,downdownarrows:uP,downharpoonleft:fP,downharpoonright:dP,DownLeftRightVector:hP,DownLeftTeeVector:pP,DownLeftVectorBar:mP,DownLeftVector:gP,DownRightTeeVector:vP,DownRightVectorBar:yP,DownRightVector:_P,DownTeeArrow:wP,DownTee:xP,drbkarow:SP,drcorn:TP,drcrop:bP,Dscr:CP,dscr:EP,DScy:kP,dscy:PP,dsol:DP,Dstrok:AP,dstrok:LP,dtdot:OP,dtri:MP,dtrif:NP,duarr:RP,duhar:IP,dwangle:VP,DZcy:qP,dzcy:FP,dzigrarr:BP,Eacute:zP,eacute:jP,easter:UP,Ecaron:$P,ecaron:HP,Ecirc:GP,ecirc:WP,ecir:YP,ecolon:XP,Ecy:QP,ecy:KP,eDDot:ZP,Edot:JP,edot:tD,eDot:eD,ee:nD,efDot:rD,Efr:iD,efr:oD,eg:sD,Egrave:aD,egrave:lD,egs:cD,egsdot:uD,el:fD,Element:dD,elinters:hD,ell:pD,els:mD,elsdot:gD,Emacr:vD,emacr:yD,empty:_D,emptyset:wD,EmptySmallSquare:xD,emptyv:SD,EmptyVerySmallSquare:TD,emsp13:bD,emsp14:CD,emsp:ED,ENG:kD,eng:PD,ensp:DD,Eogon:AD,eogon:LD,Eopf:OD,eopf:MD,epar:ND,eparsl:RD,eplus:ID,epsi:VD,Epsilon:qD,epsilon:FD,epsiv:BD,eqcirc:zD,eqcolon:jD,eqsim:UD,eqslantgtr:$D,eqslantless:HD,Equal:GD,equals:WD,EqualTilde:YD,equest:XD,Equilibrium:QD,equiv:KD,equivDD:ZD,eqvparsl:JD,erarr:tA,erDot:eA,escr:nA,Escr:rA,esdot:iA,Esim:oA,esim:sA,Eta:aA,eta:lA,ETH:cA,eth:uA,Euml:fA,euml:dA,euro:hA,excl:pA,exist:mA,Exists:gA,expectation:vA,exponentiale:yA,ExponentialE:_A,fallingdotseq:wA,Fcy:xA,fcy:SA,female:TA,ffilig:bA,fflig:CA,ffllig:EA,Ffr:kA,ffr:PA,filig:DA,FilledSmallSquare:AA,FilledVerySmallSquare:LA,fjlig:OA,flat:MA,fllig:NA,fltns:RA,fnof:IA,Fopf:VA,fopf:qA,forall:FA,ForAll:BA,fork:zA,forkv:jA,Fouriertrf:UA,fpartint:$A,frac12:HA,frac13:GA,frac14:WA,frac15:YA,frac16:XA,frac18:QA,frac23:KA,frac25:ZA,frac34:JA,frac35:tL,frac38:eL,frac45:nL,frac56:rL,frac58:iL,frac78:oL,frasl:sL,frown:aL,fscr:lL,Fscr:cL,gacute:uL,Gamma:fL,gamma:dL,Gammad:hL,gammad:pL,gap:mL,Gbreve:gL,gbreve:vL,Gcedil:yL,Gcirc:_L,gcirc:wL,Gcy:xL,gcy:SL,Gdot:TL,gdot:bL,ge:CL,gE:EL,gEl:kL,gel:PL,geq:DL,geqq:AL,geqslant:LL,gescc:OL,ges:ML,gesdot:NL,gesdoto:RL,gesdotol:IL,gesl:VL,gesles:qL,Gfr:FL,gfr:BL,gg:zL,Gg:jL,ggg:UL,gimel:$L,GJcy:HL,gjcy:GL,gla:WL,gl:YL,glE:XL,glj:QL,gnap:KL,gnapprox:ZL,gne:JL,gnE:tO,gneq:eO,gneqq:nO,gnsim:rO,Gopf:iO,gopf:oO,grave:sO,GreaterEqual:aO,GreaterEqualLess:lO,GreaterFullEqual:cO,GreaterGreater:uO,GreaterLess:fO,GreaterSlantEqual:dO,GreaterTilde:hO,Gscr:pO,gscr:mO,gsim:gO,gsime:vO,gsiml:yO,gtcc:_O,gtcir:wO,gt:xO,GT:SO,Gt:TO,gtdot:bO,gtlPar:CO,gtquest:EO,gtrapprox:kO,gtrarr:PO,gtrdot:DO,gtreqless:AO,gtreqqless:LO,gtrless:OO,gtrsim:MO,gvertneqq:NO,gvnE:RO,Hacek:IO,hairsp:VO,half:qO,hamilt:FO,HARDcy:BO,hardcy:zO,harrcir:jO,harr:UO,hArr:$O,harrw:HO,Hat:GO,hbar:WO,Hcirc:YO,hcirc:XO,hearts:QO,heartsuit:KO,hellip:ZO,hercon:JO,hfr:tM,Hfr:eM,HilbertSpace:nM,hksearow:rM,hkswarow:iM,hoarr:oM,homtht:sM,hookleftarrow:aM,hookrightarrow:lM,hopf:cM,Hopf:uM,horbar:fM,HorizontalLine:dM,hscr:hM,Hscr:pM,hslash:mM,Hstrok:gM,hstrok:vM,HumpDownHump:yM,HumpEqual:_M,hybull:wM,hyphen:xM,Iacute:SM,iacute:TM,ic:bM,Icirc:CM,icirc:EM,Icy:kM,icy:PM,Idot:DM,IEcy:AM,iecy:LM,iexcl:OM,iff:MM,ifr:NM,Ifr:RM,Igrave:IM,igrave:VM,ii:qM,iiiint:FM,iiint:BM,iinfin:zM,iiota:jM,IJlig:UM,ijlig:$M,Imacr:HM,imacr:GM,image:WM,ImaginaryI:YM,imagline:XM,imagpart:QM,imath:KM,Im:ZM,imof:JM,imped:tN,Implies:eN,incare:nN,in:"∈",infin:rN,infintie:iN,inodot:oN,intcal:sN,int:aN,Int:lN,integers:cN,Integral:uN,intercal:fN,Intersection:dN,intlarhk:hN,intprod:pN,InvisibleComma:mN,InvisibleTimes:gN,IOcy:vN,iocy:yN,Iogon:_N,iogon:wN,Iopf:xN,iopf:SN,Iota:TN,iota:bN,iprod:CN,iquest:EN,iscr:kN,Iscr:PN,isin:DN,isindot:AN,isinE:LN,isins:ON,isinsv:MN,isinv:NN,it:RN,Itilde:IN,itilde:VN,Iukcy:qN,iukcy:FN,Iuml:BN,iuml:zN,Jcirc:jN,jcirc:UN,Jcy:$N,jcy:HN,Jfr:GN,jfr:WN,jmath:YN,Jopf:XN,jopf:QN,Jscr:KN,jscr:ZN,Jsercy:JN,jsercy:tR,Jukcy:eR,jukcy:nR,Kappa:rR,kappa:iR,kappav:oR,Kcedil:sR,kcedil:aR,Kcy:lR,kcy:cR,Kfr:uR,kfr:fR,kgreen:dR,KHcy:hR,khcy:pR,KJcy:mR,kjcy:gR,Kopf:vR,kopf:yR,Kscr:_R,kscr:wR,lAarr:xR,Lacute:SR,lacute:TR,laemptyv:bR,lagran:CR,Lambda:ER,lambda:kR,lang:PR,Lang:DR,langd:AR,langle:LR,lap:OR,Laplacetrf:MR,laquo:NR,larrb:RR,larrbfs:IR,larr:VR,Larr:qR,lArr:FR,larrfs:BR,larrhk:zR,larrlp:jR,larrpl:UR,larrsim:$R,larrtl:HR,latail:GR,lAtail:WR,lat:YR,late:XR,lates:QR,lbarr:KR,lBarr:ZR,lbbrk:JR,lbrace:tI,lbrack:eI,lbrke:nI,lbrksld:rI,lbrkslu:iI,Lcaron:oI,lcaron:sI,Lcedil:aI,lcedil:lI,lceil:cI,lcub:uI,Lcy:fI,lcy:dI,ldca:hI,ldquo:pI,ldquor:mI,ldrdhar:gI,ldrushar:vI,ldsh:yI,le:_I,lE:wI,LeftAngleBracket:xI,LeftArrowBar:SI,leftarrow:TI,LeftArrow:bI,Leftarrow:CI,LeftArrowRightArrow:EI,leftarrowtail:kI,LeftCeiling:PI,LeftDoubleBracket:DI,LeftDownTeeVector:AI,LeftDownVectorBar:LI,LeftDownVector:OI,LeftFloor:MI,leftharpoondown:NI,leftharpoonup:RI,leftleftarrows:II,leftrightarrow:VI,LeftRightArrow:qI,Leftrightarrow:FI,leftrightarrows:BI,leftrightharpoons:zI,leftrightsquigarrow:jI,LeftRightVector:UI,LeftTeeArrow:$I,LeftTee:HI,LeftTeeVector:GI,leftthreetimes:WI,LeftTriangleBar:YI,LeftTriangle:XI,LeftTriangleEqual:QI,LeftUpDownVector:KI,LeftUpTeeVector:ZI,LeftUpVectorBar:JI,LeftUpVector:tV,LeftVectorBar:eV,LeftVector:nV,lEg:rV,leg:iV,leq:oV,leqq:sV,leqslant:aV,lescc:lV,les:cV,lesdot:uV,lesdoto:fV,lesdotor:dV,lesg:hV,lesges:pV,lessapprox:mV,lessdot:gV,lesseqgtr:vV,lesseqqgtr:yV,LessEqualGreater:_V,LessFullEqual:wV,LessGreater:xV,lessgtr:SV,LessLess:TV,lesssim:bV,LessSlantEqual:CV,LessTilde:EV,lfisht:kV,lfloor:PV,Lfr:DV,lfr:AV,lg:LV,lgE:OV,lHar:MV,lhard:NV,lharu:RV,lharul:IV,lhblk:VV,LJcy:qV,ljcy:FV,llarr:BV,ll:zV,Ll:jV,llcorner:UV,Lleftarrow:$V,llhard:HV,lltri:GV,Lmidot:WV,lmidot:YV,lmoustache:XV,lmoust:QV,lnap:KV,lnapprox:ZV,lne:JV,lnE:tq,lneq:eq,lneqq:nq,lnsim:rq,loang:iq,loarr:oq,lobrk:sq,longleftarrow:aq,LongLeftArrow:lq,Longleftarrow:cq,longleftrightarrow:uq,LongLeftRightArrow:fq,Longleftrightarrow:dq,longmapsto:hq,longrightarrow:pq,LongRightArrow:mq,Longrightarrow:gq,looparrowleft:vq,looparrowright:yq,lopar:_q,Lopf:wq,lopf:xq,loplus:Sq,lotimes:Tq,lowast:bq,lowbar:Cq,LowerLeftArrow:Eq,LowerRightArrow:kq,loz:Pq,lozenge:Dq,lozf:Aq,lpar:Lq,lparlt:Oq,lrarr:Mq,lrcorner:Nq,lrhar:Rq,lrhard:Iq,lrm:Vq,lrtri:qq,lsaquo:Fq,lscr:Bq,Lscr:zq,lsh:jq,Lsh:Uq,lsim:$q,lsime:Hq,lsimg:Gq,lsqb:Wq,lsquo:Yq,lsquor:Xq,Lstrok:Qq,lstrok:Kq,ltcc:Zq,ltcir:Jq,lt:tF,LT:eF,Lt:nF,ltdot:rF,lthree:iF,ltimes:oF,ltlarr:sF,ltquest:aF,ltri:lF,ltrie:cF,ltrif:uF,ltrPar:fF,lurdshar:dF,luruhar:hF,lvertneqq:pF,lvnE:mF,macr:gF,male:vF,malt:yF,maltese:_F,Map:"⤅",map:wF,mapsto:xF,mapstodown:SF,mapstoleft:TF,mapstoup:bF,marker:CF,mcomma:EF,Mcy:kF,mcy:PF,mdash:DF,mDDot:AF,measuredangle:LF,MediumSpace:OF,Mellintrf:MF,Mfr:NF,mfr:RF,mho:IF,micro:VF,midast:qF,midcir:FF,mid:BF,middot:zF,minusb:jF,minus:UF,minusd:$F,minusdu:HF,MinusPlus:GF,mlcp:WF,mldr:YF,mnplus:XF,models:QF,Mopf:KF,mopf:ZF,mp:JF,mscr:tB,Mscr:eB,mstpos:nB,Mu:rB,mu:iB,multimap:oB,mumap:sB,nabla:aB,Nacute:lB,nacute:cB,nang:uB,nap:fB,napE:dB,napid:hB,napos:pB,napprox:mB,natural:gB,naturals:vB,natur:yB,nbsp:_B,nbump:wB,nbumpe:xB,ncap:SB,Ncaron:TB,ncaron:bB,Ncedil:CB,ncedil:EB,ncong:kB,ncongdot:PB,ncup:DB,Ncy:AB,ncy:LB,ndash:OB,nearhk:MB,nearr:NB,neArr:RB,nearrow:IB,ne:VB,nedot:qB,NegativeMediumSpace:FB,NegativeThickSpace:BB,NegativeThinSpace:zB,NegativeVeryThinSpace:jB,nequiv:UB,nesear:$B,nesim:HB,NestedGreaterGreater:GB,NestedLessLess:WB,NewLine:YB,nexist:XB,nexists:QB,Nfr:KB,nfr:ZB,ngE:JB,nge:tz,ngeq:ez,ngeqq:nz,ngeqslant:rz,nges:iz,nGg:oz,ngsim:sz,nGt:az,ngt:lz,ngtr:cz,nGtv:uz,nharr:fz,nhArr:dz,nhpar:hz,ni:pz,nis:mz,nisd:gz,niv:vz,NJcy:yz,njcy:_z,nlarr:wz,nlArr:xz,nldr:Sz,nlE:Tz,nle:bz,nleftarrow:Cz,nLeftarrow:Ez,nleftrightarrow:kz,nLeftrightarrow:Pz,nleq:Dz,nleqq:Az,nleqslant:Lz,nles:Oz,nless:Mz,nLl:Nz,nlsim:Rz,nLt:Iz,nlt:Vz,nltri:qz,nltrie:Fz,nLtv:Bz,nmid:zz,NoBreak:jz,NonBreakingSpace:Uz,nopf:$z,Nopf:Hz,Not:Gz,not:Wz,NotCongruent:Yz,NotCupCap:Xz,NotDoubleVerticalBar:Qz,NotElement:Kz,NotEqual:Zz,NotEqualTilde:Jz,NotExists:tj,NotGreater:ej,NotGreaterEqual:nj,NotGreaterFullEqual:rj,NotGreaterGreater:ij,NotGreaterLess:oj,NotGreaterSlantEqual:sj,NotGreaterTilde:aj,NotHumpDownHump:lj,NotHumpEqual:cj,notin:uj,notindot:fj,notinE:dj,notinva:hj,notinvb:pj,notinvc:mj,NotLeftTriangleBar:gj,NotLeftTriangle:vj,NotLeftTriangleEqual:yj,NotLess:_j,NotLessEqual:wj,NotLessGreater:xj,NotLessLess:Sj,NotLessSlantEqual:Tj,NotLessTilde:bj,NotNestedGreaterGreater:Cj,NotNestedLessLess:Ej,notni:kj,notniva:Pj,notnivb:Dj,notnivc:Aj,NotPrecedes:Lj,NotPrecedesEqual:Oj,NotPrecedesSlantEqual:Mj,NotReverseElement:Nj,NotRightTriangleBar:Rj,NotRightTriangle:Ij,NotRightTriangleEqual:Vj,NotSquareSubset:qj,NotSquareSubsetEqual:Fj,NotSquareSuperset:Bj,NotSquareSupersetEqual:zj,NotSubset:jj,NotSubsetEqual:Uj,NotSucceeds:$j,NotSucceedsEqual:Hj,NotSucceedsSlantEqual:Gj,NotSucceedsTilde:Wj,NotSuperset:Yj,NotSupersetEqual:Xj,NotTilde:Qj,NotTildeEqual:Kj,NotTildeFullEqual:Zj,NotTildeTilde:Jj,NotVerticalBar:tU,nparallel:eU,npar:nU,nparsl:rU,npart:iU,npolint:oU,npr:sU,nprcue:aU,nprec:lU,npreceq:cU,npre:uU,nrarrc:fU,nrarr:dU,nrArr:hU,nrarrw:pU,nrightarrow:mU,nRightarrow:gU,nrtri:vU,nrtrie:yU,nsc:_U,nsccue:wU,nsce:xU,Nscr:SU,nscr:TU,nshortmid:bU,nshortparallel:CU,nsim:EU,nsime:kU,nsimeq:PU,nsmid:DU,nspar:AU,nsqsube:LU,nsqsupe:OU,nsub:MU,nsubE:NU,nsube:RU,nsubset:IU,nsubseteq:VU,nsubseteqq:qU,nsucc:FU,nsucceq:BU,nsup:zU,nsupE:jU,nsupe:UU,nsupset:$U,nsupseteq:HU,nsupseteqq:GU,ntgl:WU,Ntilde:YU,ntilde:XU,ntlg:QU,ntriangleleft:KU,ntrianglelefteq:ZU,ntriangleright:JU,ntrianglerighteq:t$,Nu:e$,nu:n$,num:r$,numero:i$,numsp:o$,nvap:s$,nvdash:a$,nvDash:l$,nVdash:c$,nVDash:u$,nvge:f$,nvgt:d$,nvHarr:h$,nvinfin:p$,nvlArr:m$,nvle:g$,nvlt:v$,nvltrie:y$,nvrArr:_$,nvrtrie:w$,nvsim:x$,nwarhk:S$,nwarr:T$,nwArr:b$,nwarrow:C$,nwnear:E$,Oacute:k$,oacute:P$,oast:D$,Ocirc:A$,ocirc:L$,ocir:O$,Ocy:M$,ocy:N$,odash:R$,Odblac:I$,odblac:V$,odiv:q$,odot:F$,odsold:B$,OElig:z$,oelig:j$,ofcir:U$,Ofr:$$,ofr:H$,ogon:G$,Ograve:W$,ograve:Y$,ogt:X$,ohbar:Q$,ohm:K$,oint:Z$,olarr:J$,olcir:tH,olcross:eH,oline:nH,olt:rH,Omacr:iH,omacr:oH,Omega:sH,omega:aH,Omicron:lH,omicron:cH,omid:uH,ominus:fH,Oopf:dH,oopf:hH,opar:pH,OpenCurlyDoubleQuote:mH,OpenCurlyQuote:gH,operp:vH,oplus:yH,orarr:_H,Or:wH,or:xH,ord:SH,order:TH,orderof:bH,ordf:CH,ordm:EH,origof:kH,oror:PH,orslope:DH,orv:AH,oS:LH,Oscr:OH,oscr:MH,Oslash:NH,oslash:RH,osol:IH,Otilde:VH,otilde:qH,otimesas:FH,Otimes:BH,otimes:zH,Ouml:jH,ouml:UH,ovbar:$H,OverBar:HH,OverBrace:GH,OverBracket:WH,OverParenthesis:YH,para:XH,parallel:QH,par:KH,parsim:ZH,parsl:JH,part:tG,PartialD:eG,Pcy:nG,pcy:rG,percnt:iG,period:oG,permil:sG,perp:aG,pertenk:lG,Pfr:cG,pfr:uG,Phi:fG,phi:dG,phiv:hG,phmmat:pG,phone:mG,Pi:gG,pi:vG,pitchfork:yG,piv:_G,planck:wG,planckh:xG,plankv:SG,plusacir:TG,plusb:bG,pluscir:CG,plus:EG,plusdo:kG,plusdu:PG,pluse:DG,PlusMinus:AG,plusmn:LG,plussim:OG,plustwo:MG,pm:NG,Poincareplane:RG,pointint:IG,popf:VG,Popf:qG,pound:FG,prap:BG,Pr:zG,pr:jG,prcue:UG,precapprox:$G,prec:HG,preccurlyeq:GG,Precedes:WG,PrecedesEqual:YG,PrecedesSlantEqual:XG,PrecedesTilde:QG,preceq:KG,precnapprox:ZG,precneqq:JG,precnsim:tW,pre:eW,prE:nW,precsim:rW,prime:iW,Prime:oW,primes:sW,prnap:aW,prnE:lW,prnsim:cW,prod:uW,Product:fW,profalar:dW,profline:hW,profsurf:pW,prop:mW,Proportional:gW,Proportion:vW,propto:yW,prsim:_W,prurel:wW,Pscr:xW,pscr:SW,Psi:TW,psi:bW,puncsp:CW,Qfr:EW,qfr:kW,qint:PW,qopf:DW,Qopf:AW,qprime:LW,Qscr:OW,qscr:MW,quaternions:NW,quatint:RW,quest:IW,questeq:VW,quot:qW,QUOT:FW,rAarr:BW,race:zW,Racute:jW,racute:UW,radic:$W,raemptyv:HW,rang:GW,Rang:WW,rangd:YW,range:XW,rangle:QW,raquo:KW,rarrap:ZW,rarrb:JW,rarrbfs:tY,rarrc:eY,rarr:nY,Rarr:rY,rArr:iY,rarrfs:oY,rarrhk:sY,rarrlp:aY,rarrpl:lY,rarrsim:cY,Rarrtl:uY,rarrtl:fY,rarrw:dY,ratail:hY,rAtail:pY,ratio:mY,rationals:gY,rbarr:vY,rBarr:yY,RBarr:_Y,rbbrk:wY,rbrace:xY,rbrack:SY,rbrke:TY,rbrksld:bY,rbrkslu:CY,Rcaron:EY,rcaron:kY,Rcedil:PY,rcedil:DY,rceil:AY,rcub:LY,Rcy:OY,rcy:MY,rdca:NY,rdldhar:RY,rdquo:IY,rdquor:VY,rdsh:qY,real:FY,realine:BY,realpart:zY,reals:jY,Re:UY,rect:$Y,reg:HY,REG:GY,ReverseElement:WY,ReverseEquilibrium:YY,ReverseUpEquilibrium:XY,rfisht:QY,rfloor:KY,rfr:ZY,Rfr:JY,rHar:tX,rhard:eX,rharu:nX,rharul:rX,Rho:iX,rho:oX,rhov:sX,RightAngleBracket:aX,RightArrowBar:lX,rightarrow:cX,RightArrow:uX,Rightarrow:fX,RightArrowLeftArrow:dX,rightarrowtail:hX,RightCeiling:pX,RightDoubleBracket:mX,RightDownTeeVector:gX,RightDownVectorBar:vX,RightDownVector:yX,RightFloor:_X,rightharpoondown:wX,rightharpoonup:xX,rightleftarrows:SX,rightleftharpoons:TX,rightrightarrows:bX,rightsquigarrow:CX,RightTeeArrow:EX,RightTee:kX,RightTeeVector:PX,rightthreetimes:DX,RightTriangleBar:AX,RightTriangle:LX,RightTriangleEqual:OX,RightUpDownVector:MX,RightUpTeeVector:NX,RightUpVectorBar:RX,RightUpVector:IX,RightVectorBar:VX,RightVector:qX,ring:FX,risingdotseq:BX,rlarr:zX,rlhar:jX,rlm:UX,rmoustache:$X,rmoust:HX,rnmid:GX,roang:WX,roarr:YX,robrk:XX,ropar:QX,ropf:KX,Ropf:ZX,roplus:JX,rotimes:tQ,RoundImplies:eQ,rpar:nQ,rpargt:rQ,rppolint:iQ,rrarr:oQ,Rrightarrow:sQ,rsaquo:aQ,rscr:lQ,Rscr:cQ,rsh:uQ,Rsh:fQ,rsqb:dQ,rsquo:hQ,rsquor:pQ,rthree:mQ,rtimes:gQ,rtri:vQ,rtrie:yQ,rtrif:_Q,rtriltri:wQ,RuleDelayed:xQ,ruluhar:SQ,rx:TQ,Sacute:bQ,sacute:CQ,sbquo:EQ,scap:kQ,Scaron:PQ,scaron:DQ,Sc:AQ,sc:LQ,sccue:OQ,sce:MQ,scE:NQ,Scedil:RQ,scedil:IQ,Scirc:VQ,scirc:qQ,scnap:FQ,scnE:BQ,scnsim:zQ,scpolint:jQ,scsim:UQ,Scy:$Q,scy:HQ,sdotb:GQ,sdot:WQ,sdote:YQ,searhk:XQ,searr:QQ,seArr:KQ,searrow:ZQ,sect:JQ,semi:tK,seswar:eK,setminus:nK,setmn:rK,sext:iK,Sfr:oK,sfr:sK,sfrown:aK,sharp:lK,SHCHcy:cK,shchcy:uK,SHcy:fK,shcy:dK,ShortDownArrow:hK,ShortLeftArrow:pK,shortmid:mK,shortparallel:gK,ShortRightArrow:vK,ShortUpArrow:yK,shy:_K,Sigma:wK,sigma:xK,sigmaf:SK,sigmav:TK,sim:bK,simdot:CK,sime:EK,simeq:kK,simg:PK,simgE:DK,siml:AK,simlE:LK,simne:OK,simplus:MK,simrarr:NK,slarr:RK,SmallCircle:IK,smallsetminus:VK,smashp:qK,smeparsl:FK,smid:BK,smile:zK,smt:jK,smte:UK,smtes:$K,SOFTcy:HK,softcy:GK,solbar:WK,solb:YK,sol:XK,Sopf:QK,sopf:KK,spades:ZK,spadesuit:JK,spar:tZ,sqcap:eZ,sqcaps:nZ,sqcup:rZ,sqcups:iZ,Sqrt:oZ,sqsub:sZ,sqsube:aZ,sqsubset:lZ,sqsubseteq:cZ,sqsup:uZ,sqsupe:fZ,sqsupset:dZ,sqsupseteq:hZ,square:pZ,Square:mZ,SquareIntersection:gZ,SquareSubset:vZ,SquareSubsetEqual:yZ,SquareSuperset:_Z,SquareSupersetEqual:wZ,SquareUnion:xZ,squarf:SZ,squ:TZ,squf:bZ,srarr:CZ,Sscr:EZ,sscr:kZ,ssetmn:PZ,ssmile:DZ,sstarf:AZ,Star:LZ,star:OZ,starf:MZ,straightepsilon:NZ,straightphi:RZ,strns:IZ,sub:VZ,Sub:qZ,subdot:FZ,subE:BZ,sube:zZ,subedot:jZ,submult:UZ,subnE:$Z,subne:HZ,subplus:GZ,subrarr:WZ,subset:YZ,Subset:XZ,subseteq:QZ,subseteqq:KZ,SubsetEqual:ZZ,subsetneq:JZ,subsetneqq:tJ,subsim:eJ,subsub:nJ,subsup:rJ,succapprox:iJ,succ:oJ,succcurlyeq:sJ,Succeeds:aJ,SucceedsEqual:lJ,SucceedsSlantEqual:cJ,SucceedsTilde:uJ,succeq:fJ,succnapprox:dJ,succneqq:hJ,succnsim:pJ,succsim:mJ,SuchThat:gJ,sum:vJ,Sum:yJ,sung:_J,sup1:wJ,sup2:xJ,sup3:SJ,sup:TJ,Sup:bJ,supdot:CJ,supdsub:EJ,supE:kJ,supe:PJ,supedot:DJ,Superset:AJ,SupersetEqual:LJ,suphsol:OJ,suphsub:MJ,suplarr:NJ,supmult:RJ,supnE:IJ,supne:VJ,supplus:qJ,supset:FJ,Supset:BJ,supseteq:zJ,supseteqq:jJ,supsetneq:UJ,supsetneqq:$J,supsim:HJ,supsub:GJ,supsup:WJ,swarhk:YJ,swarr:XJ,swArr:QJ,swarrow:KJ,swnwar:ZJ,szlig:JJ,Tab:ttt,target:ett,Tau:ntt,tau:rtt,tbrk:itt,Tcaron:ott,tcaron:stt,Tcedil:att,tcedil:ltt,Tcy:ctt,tcy:utt,tdot:ftt,telrec:dtt,Tfr:htt,tfr:ptt,there4:mtt,therefore:gtt,Therefore:vtt,Theta:ytt,theta:_tt,thetasym:wtt,thetav:xtt,thickapprox:Stt,thicksim:Ttt,ThickSpace:btt,ThinSpace:Ctt,thinsp:Ett,thkap:ktt,thksim:Ptt,THORN:Dtt,thorn:Att,tilde:Ltt,Tilde:Ott,TildeEqual:Mtt,TildeFullEqual:Ntt,TildeTilde:Rtt,timesbar:Itt,timesb:Vtt,times:qtt,timesd:Ftt,tint:Btt,toea:ztt,topbot:jtt,topcir:Utt,top:$tt,Topf:Htt,topf:Gtt,topfork:Wtt,tosa:Ytt,tprime:Xtt,trade:Qtt,TRADE:Ktt,triangle:Ztt,triangledown:Jtt,triangleleft:tet,trianglelefteq:eet,triangleq:net,triangleright:ret,trianglerighteq:iet,tridot:oet,trie:set,triminus:aet,TripleDot:cet,triplus:uet,trisb:fet,tritime:det,trpezium:het,Tscr:pet,tscr:met,TScy:get,tscy:vet,TSHcy:yet,tshcy:_et,Tstrok:wet,tstrok:xet,twixt:Tet,twoheadleftarrow:bet,twoheadrightarrow:Cet,Uacute:Eet,uacute:ket,uarr:Pet,Uarr:Det,uArr:Aet,Uarrocir:Let,Ubrcy:Oet,ubrcy:Met,Ubreve:Net,ubreve:Ret,Ucirc:Iet,ucirc:Vet,Ucy:qet,ucy:Fet,udarr:Bet,Udblac:zet,udblac:jet,udhar:Uet,ufisht:$et,Ufr:Het,ufr:Get,Ugrave:Wet,ugrave:Yet,uHar:Xet,uharl:Qet,uharr:Ket,uhblk:Zet,ulcorn:Jet,ulcorner:tnt,ulcrop:ent,ultri:nnt,Umacr:rnt,umacr:int,uml:ont,UnderBar:snt,UnderBrace:ant,UnderBracket:lnt,UnderParenthesis:cnt,Union:unt,UnionPlus:fnt,Uogon:dnt,uogon:hnt,Uopf:pnt,uopf:mnt,UpArrowBar:gnt,uparrow:vnt,UpArrow:ynt,Uparrow:_nt,UpArrowDownArrow:wnt,updownarrow:xnt,UpDownArrow:Snt,Updownarrow:Tnt,UpEquilibrium:bnt,upharpoonleft:Cnt,upharpoonright:Ent,uplus:knt,UpperLeftArrow:Pnt,UpperRightArrow:Dnt,upsi:Ant,Upsi:Lnt,upsih:Ont,Upsilon:Mnt,upsilon:Nnt,UpTeeArrow:Rnt,UpTee:Int,upuparrows:Vnt,urcorn:qnt,urcorner:Fnt,urcrop:Bnt,Uring:znt,uring:jnt,urtri:Unt,Uscr:$nt,uscr:Hnt,utdot:Gnt,Utilde:Wnt,utilde:Ynt,utri:Xnt,utrif:Qnt,uuarr:Knt,Uuml:Znt,uuml:Jnt,uwangle:trt,vangrt:ert,varepsilon:nrt,varkappa:rrt,varnothing:irt,varphi:ort,varpi:srt,varpropto:art,varr:lrt,vArr:crt,varrho:urt,varsigma:frt,varsubsetneq:drt,varsubsetneqq:hrt,varsupsetneq:prt,varsupsetneqq:mrt,vartheta:grt,vartriangleleft:vrt,vartriangleright:yrt,vBar:_rt,Vbar:wrt,vBarv:xrt,Vcy:Srt,vcy:Trt,vdash:brt,vDash:Crt,Vdash:Ert,VDash:krt,Vdashl:Prt,veebar:Drt,vee:Art,Vee:Lrt,veeeq:Ort,vellip:Mrt,verbar:Nrt,Verbar:Rrt,vert:Irt,Vert:Vrt,VerticalBar:qrt,VerticalLine:Frt,VerticalSeparator:Brt,VerticalTilde:zrt,VeryThinSpace:jrt,Vfr:Urt,vfr:$rt,vltri:Hrt,vnsub:Grt,vnsup:Wrt,Vopf:Yrt,vopf:Xrt,vprop:Qrt,vrtri:Krt,Vscr:Zrt,vscr:Jrt,vsubnE:t1t,vsubne:e1t,vsupnE:n1t,vsupne:r1t,Vvdash:i1t,vzigzag:o1t,Wcirc:s1t,wcirc:a1t,wedbar:l1t,wedge:c1t,Wedge:u1t,wedgeq:f1t,weierp:d1t,Wfr:h1t,wfr:p1t,Wopf:m1t,wopf:g1t,wp:v1t,wr:y1t,wreath:_1t,Wscr:w1t,wscr:x1t,xcap:S1t,xcirc:T1t,xcup:b1t,xdtri:C1t,Xfr:E1t,xfr:k1t,xharr:P1t,xhArr:D1t,Xi:A1t,xi:L1t,xlarr:O1t,xlArr:M1t,xmap:N1t,xnis:R1t,xodot:I1t,Xopf:V1t,xopf:q1t,xoplus:F1t,xotime:B1t,xrarr:z1t,xrArr:j1t,Xscr:U1t,xscr:$1t,xsqcup:H1t,xuplus:G1t,xutri:W1t,xvee:Y1t,xwedge:X1t,Yacute:Q1t,yacute:K1t,YAcy:Z1t,yacy:J1t,Ycirc:tit,ycirc:eit,Ycy:nit,ycy:rit,yen:iit,Yfr:oit,yfr:sit,YIcy:ait,yicy:lit,Yopf:cit,yopf:uit,Yscr:fit,yscr:dit,YUcy:hit,yucy:pit,yuml:mit,Yuml:git,Zacute:vit,zacute:yit,Zcaron:_it,zcaron:wit,Zcy:xit,zcy:Sit,Zdot:Tit,zdot:bit,zeetrf:Cit,ZeroWidthSpace:Eit,Zeta:kit,zeta:Pit,zfr:Dit,Zfr:Ait,ZHcy:Lit,zhcy:Oit,zigrarr:Mit,zopf:Nit,Zopf:Rit,Zscr:Iit,zscr:Vit,zwj:qit,zwnj:Fit},Bit="Á",zit="á",jit="Â",Uit="â",$it="´",Hit="Æ",Git="æ",Wit="À",Yit="à",Xit="&",Qit="&",Kit="Å",Zit="å",Jit="Ã",tot="ã",eot="Ä",not="ä",rot="¦",iot="Ç",oot="ç",sot="¸",aot="¢",lot="©",cot="©",uot="¤",fot="°",dot="÷",hot="É",pot="é",mot="Ê",got="ê",vot="È",yot="è",_ot="Ð",wot="ð",xot="Ë",Sot="ë",Tot="½",bot="¼",Cot="¾",Eot=">",kot=">",Pot="Í",Dot="í",Aot="Î",Lot="î",Oot="¡",Mot="Ì",Not="ì",Rot="¿",Iot="Ï",Vot="ï",qot="«",Fot="<",Bot="<",zot="¯",jot="µ",Uot="·",$ot=" ",Hot="¬",Got="Ñ",Wot="ñ",Yot="Ó",Xot="ó",Qot="Ô",Kot="ô",Zot="Ò",Jot="ò",tst="ª",est="º",nst="Ø",rst="ø",ist="Õ",ost="õ",sst="Ö",ast="ö",lst="¶",cst="±",ust="£",fst='"',dst='"',hst="»",pst="®",mst="®",gst="§",vst="­",yst="¹",_st="²",wst="³",xst="ß",Sst="Þ",Tst="þ",bst="×",Cst="Ú",Est="ú",kst="Û",Pst="û",Dst="Ù",Ast="ù",Lst="¨",Ost="Ü",Mst="ü",Nst="Ý",Rst="ý",Ist="¥",Vst="ÿ",qst={Aacute:Bit,aacute:zit,Acirc:jit,acirc:Uit,acute:$it,AElig:Hit,aelig:Git,Agrave:Wit,agrave:Yit,amp:Xit,AMP:Qit,Aring:Kit,aring:Zit,Atilde:Jit,atilde:tot,Auml:eot,auml:not,brvbar:rot,Ccedil:iot,ccedil:oot,cedil:sot,cent:aot,copy:lot,COPY:cot,curren:uot,deg:fot,divide:dot,Eacute:hot,eacute:pot,Ecirc:mot,ecirc:got,Egrave:vot,egrave:yot,ETH:_ot,eth:wot,Euml:xot,euml:Sot,frac12:Tot,frac14:bot,frac34:Cot,gt:Eot,GT:kot,Iacute:Pot,iacute:Dot,Icirc:Aot,icirc:Lot,iexcl:Oot,Igrave:Mot,igrave:Not,iquest:Rot,Iuml:Iot,iuml:Vot,laquo:qot,lt:Fot,LT:Bot,macr:zot,micro:jot,middot:Uot,nbsp:$ot,not:Hot,Ntilde:Got,ntilde:Wot,Oacute:Yot,oacute:Xot,Ocirc:Qot,ocirc:Kot,Ograve:Zot,ograve:Jot,ordf:tst,ordm:est,Oslash:nst,oslash:rst,Otilde:ist,otilde:ost,Ouml:sst,ouml:ast,para:lst,plusmn:cst,pound:ust,quot:fst,QUOT:dst,raquo:hst,reg:pst,REG:mst,sect:gst,shy:vst,sup1:yst,sup2:_st,sup3:wst,szlig:xst,THORN:Sst,thorn:Tst,times:bst,Uacute:Cst,uacute:Est,Ucirc:kst,ucirc:Pst,Ugrave:Dst,ugrave:Ast,uml:Lst,Uuml:Ost,uuml:Mst,Yacute:Nst,yacute:Rst,yen:Ist,yuml:Vst},Fst="&",Bst="'",zst=">",jst="<",Ust='"',ep={amp:Fst,apos:Bst,gt:zst,lt:jst,quot:Ust};var W0={};const $st={0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376};var Hst=V&&V.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(W0,"__esModule",{value:!0});var z8=Hst($st),Gst=String.fromCodePoint||function(e){var t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|e&1023),t+=String.fromCharCode(e),t};function Wst(e){return e>=55296&&e<=57343||e>1114111?"�":(e in z8.default&&(e=z8.default[e]),Gst(e))}W0.default=Wst;var Sa=V&&V.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(hn,"__esModule",{value:!0});hn.decodeHTML=hn.decodeHTMLStrict=hn.decodeXML=void 0;var Xc=Sa(tp),Yst=Sa(qst),Xst=Sa(ep),j8=Sa(W0),Qst=/&(?:[a-zA-Z0-9]+|#[xX][\da-fA-F]+|#\d+);/g;hn.decodeXML=np(Xst.default);hn.decodeHTMLStrict=np(Xc.default);function np(e){var t=rp(e);return function(n){return String(n).replace(Qst,t)}}var U8=function(e,t){return e<t?1:-1};hn.decodeHTML=function(){for(var e=Object.keys(Yst.default).sort(U8),t=Object.keys(Xc.default).sort(U8),n=0,r=0;n<t.length;n++)e[r]===t[n]?(t[n]+=";?",r++):t[n]+=";";var i=new RegExp("&(?:"+t.join("|")+"|#[xX][\\da-fA-F]+;?|#\\d+;?)","g"),o=rp(Xc.default);function s(a){return a.substr(-1)!==";"&&(a+=";"),o(a)}return function(a){return String(a).replace(i,s)}}();function rp(e){return function(n){if(n.charAt(1)==="#"){var r=n.charAt(2);return r==="X"||r==="x"?j8.default(parseInt(n.substr(3),16)):j8.default(parseInt(n.substr(2),10))}return e[n.slice(1,-1)]||n}}var ce={},ip=V&&V.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ce,"__esModule",{value:!0});ce.escapeUTF8=ce.escape=ce.encodeNonAsciiHTML=ce.encodeHTML=ce.encodeXML=void 0;var Kst=ip(ep),op=ap(Kst.default),sp=lp(op);ce.encodeXML=fp(op);var Zst=ip(tp),Y0=ap(Zst.default),Jst=lp(Y0);ce.encodeHTML=eat(Y0,Jst);ce.encodeNonAsciiHTML=fp(Y0);function ap(e){return Object.keys(e).sort().reduce(function(t,n){return t[e[n]]="&"+n+";",t},{})}function lp(e){for(var t=[],n=[],r=0,i=Object.keys(e);r<i.length;r++){var o=i[r];o.length===1?t.push("\\"+o):n.push(o)}t.sort();for(var s=0;s<t.length-1;s++){for(var a=s;a<t.length-1&&t[a].charCodeAt(1)+1===t[a+1].charCodeAt(1);)a+=1;var l=1+a-s;l<3||t.splice(s,l,t[s]+"-"+t[a])}return n.unshift("["+t.join("")+"]"),new RegExp(n.join("|"),"g")}var cp=/(?:[\x80-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/g,tat=String.prototype.codePointAt!=null?function(e){return e.codePointAt(0)}:function(e){return(e.charCodeAt(0)-55296)*1024+e.charCodeAt(1)-56320+65536};function Ta(e){return"&#x"+(e.length>1?tat(e):e.charCodeAt(0)).toString(16).toUpperCase()+";"}function eat(e,t){return function(n){return n.replace(t,function(r){return e[r]}).replace(cp,Ta)}}var up=new RegExp(sp.source+"|"+cp.source,"g");function nat(e){return e.replace(up,Ta)}ce.escape=nat;function rat(e){return e.replace(sp,Ta)}ce.escapeUTF8=rat;function fp(e){return function(t){return t.replace(up,function(n){return e[n]||Ta(n)})}}(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.decodeXMLStrict=e.decodeHTML5Strict=e.decodeHTML4Strict=e.decodeHTML5=e.decodeHTML4=e.decodeHTMLStrict=e.decodeHTML=e.decodeXML=e.encodeHTML5=e.encodeHTML4=e.escapeUTF8=e.escape=e.encodeNonAsciiHTML=e.encodeHTML=e.encodeXML=e.encode=e.decodeStrict=e.decode=void 0;var t=hn,n=ce;function r(l,c){return(!c||c<=0?t.decodeXML:t.decodeHTML)(l)}e.decode=r;function i(l,c){return(!c||c<=0?t.decodeXML:t.decodeHTMLStrict)(l)}e.decodeStrict=i;function o(l,c){return(!c||c<=0?n.encodeXML:n.encodeHTML)(l)}e.encode=o;var s=ce;Object.defineProperty(e,"encodeXML",{enumerable:!0,get:function(){return s.encodeXML}}),Object.defineProperty(e,"encodeHTML",{enumerable:!0,get:function(){return s.encodeHTML}}),Object.defineProperty(e,"encodeNonAsciiHTML",{enumerable:!0,get:function(){return s.encodeNonAsciiHTML}}),Object.defineProperty(e,"escape",{enumerable:!0,get:function(){return s.escape}}),Object.defineProperty(e,"escapeUTF8",{enumerable:!0,get:function(){return s.escapeUTF8}}),Object.defineProperty(e,"encodeHTML4",{enumerable:!0,get:function(){return s.encodeHTML}}),Object.defineProperty(e,"encodeHTML5",{enumerable:!0,get:function(){return s.encodeHTML}});var a=hn;Object.defineProperty(e,"decodeXML",{enumerable:!0,get:function(){return a.decodeXML}}),Object.defineProperty(e,"decodeHTML",{enumerable:!0,get:function(){return a.decodeHTML}}),Object.defineProperty(e,"decodeHTMLStrict",{enumerable:!0,get:function(){return a.decodeHTMLStrict}}),Object.defineProperty(e,"decodeHTML4",{enumerable:!0,get:function(){return a.decodeHTML}}),Object.defineProperty(e,"decodeHTML5",{enumerable:!0,get:function(){return a.decodeHTML}}),Object.defineProperty(e,"decodeHTML4Strict",{enumerable:!0,get:function(){return a.decodeHTMLStrict}}),Object.defineProperty(e,"decodeHTML5Strict",{enumerable:!0,get:function(){return a.decodeHTMLStrict}}),Object.defineProperty(e,"decodeXMLStrict",{enumerable:!0,get:function(){return a.decodeXML}})})(Jh);var O1={};Object.defineProperty(O1,"__esModule",{value:!0});O1.attributeNames=O1.elementNames=void 0;O1.elementNames=new Map([["altglyph","altGlyph"],["altglyphdef","altGlyphDef"],["altglyphitem","altGlyphItem"],["animatecolor","animateColor"],["animatemotion","animateMotion"],["animatetransform","animateTransform"],["clippath","clipPath"],["feblend","feBlend"],["fecolormatrix","feColorMatrix"],["fecomponenttransfer","feComponentTransfer"],["fecomposite","feComposite"],["feconvolvematrix","feConvolveMatrix"],["fediffuselighting","feDiffuseLighting"],["fedisplacementmap","feDisplacementMap"],["fedistantlight","feDistantLight"],["fedropshadow","feDropShadow"],["feflood","feFlood"],["fefunca","feFuncA"],["fefuncb","feFuncB"],["fefuncg","feFuncG"],["fefuncr","feFuncR"],["fegaussianblur","feGaussianBlur"],["feimage","feImage"],["femerge","feMerge"],["femergenode","feMergeNode"],["femorphology","feMorphology"],["feoffset","feOffset"],["fepointlight","fePointLight"],["fespecularlighting","feSpecularLighting"],["fespotlight","feSpotLight"],["fetile","feTile"],["feturbulence","feTurbulence"],["foreignobject","foreignObject"],["glyphref","glyphRef"],["lineargradient","linearGradient"],["radialgradient","radialGradient"],["textpath","textPath"]]);O1.attributeNames=new Map([["definitionurl","definitionURL"],["attributename","attributeName"],["attributetype","attributeType"],["basefrequency","baseFrequency"],["baseprofile","baseProfile"],["calcmode","calcMode"],["clippathunits","clipPathUnits"],["diffuseconstant","diffuseConstant"],["edgemode","edgeMode"],["filterunits","filterUnits"],["glyphref","glyphRef"],["gradienttransform","gradientTransform"],["gradientunits","gradientUnits"],["kernelmatrix","kernelMatrix"],["kernelunitlength","kernelUnitLength"],["keypoints","keyPoints"],["keysplines","keySplines"],["keytimes","keyTimes"],["lengthadjust","lengthAdjust"],["limitingconeangle","limitingConeAngle"],["markerheight","markerHeight"],["markerunits","markerUnits"],["markerwidth","markerWidth"],["maskcontentunits","maskContentUnits"],["maskunits","maskUnits"],["numoctaves","numOctaves"],["pathlength","pathLength"],["patterncontentunits","patternContentUnits"],["patterntransform","patternTransform"],["patternunits","patternUnits"],["pointsatx","pointsAtX"],["pointsaty","pointsAtY"],["pointsatz","pointsAtZ"],["preservealpha","preserveAlpha"],["preserveaspectratio","preserveAspectRatio"],["primitiveunits","primitiveUnits"],["refx","refX"],["refy","refY"],["repeatcount","repeatCount"],["repeatdur","repeatDur"],["requiredextensions","requiredExtensions"],["requiredfeatures","requiredFeatures"],["specularconstant","specularConstant"],["specularexponent","specularExponent"],["spreadmethod","spreadMethod"],["startoffset","startOffset"],["stddeviation","stdDeviation"],["stitchtiles","stitchTiles"],["surfacescale","surfaceScale"],["systemlanguage","systemLanguage"],["tablevalues","tableValues"],["targetx","targetX"],["targety","targetY"],["textlength","textLength"],["viewbox","viewBox"],["viewtarget","viewTarget"],["xchannelselector","xChannelSelector"],["ychannelselector","yChannelSelector"],["zoomandpan","zoomAndPan"]]);var s1=V&&V.__assign||function(){return s1=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++){t=arguments[n];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e},s1.apply(this,arguments)},iat=V&&V.__createBinding||(Object.create?function(e,t,n,r){r===void 0&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){r===void 0&&(r=n),e[r]=t[n]}),oat=V&&V.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),sat=V&&V.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)n!=="default"&&Object.prototype.hasOwnProperty.call(e,n)&&iat(t,e,n);return oat(t,e),t};Object.defineProperty(H0,"__esModule",{value:!0});var nn=sat(G0),dp=Jh,hp=O1,aat=new Set(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]);function lat(e,t){if(e)return Object.keys(e).map(function(n){var r,i,o=(r=e[n])!==null&&r!==void 0?r:"";return t.xmlMode==="foreign"&&(n=(i=hp.attributeNames.get(n))!==null&&i!==void 0?i:n),!t.emptyAttrs&&!t.xmlMode&&o===""?n:n+'="'+(t.decodeEntities!==!1?dp.encodeXML(o):o.replace(/"/g,"&quot;"))+'"'}).join(" ")}var $8=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);function X0(e,t){t===void 0&&(t={});for(var n=("length"in e)?e:[e],r="",i=0;i<n.length;i++)r+=cat(n[i],t);return r}H0.default=X0;function cat(e,t){switch(e.type){case nn.Root:return X0(e.children,t);case nn.Directive:case nn.Doctype:return hat(e);case nn.Comment:return gat(e);case nn.CDATA:return mat(e);case nn.Script:case nn.Style:case nn.Tag:return dat(e,t);case nn.Text:return pat(e,t)}}var uat=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]),fat=new Set(["svg","math"]);function dat(e,t){var n;t.xmlMode==="foreign"&&(e.name=(n=hp.elementNames.get(e.name))!==null&&n!==void 0?n:e.name,e.parent&&uat.has(e.parent.name)&&(t=s1(s1({},t),{xmlMode:!1}))),!t.xmlMode&&fat.has(e.name)&&(t=s1(s1({},t),{xmlMode:"foreign"}));var r="<"+e.name,i=lat(e.attribs,t);return i&&(r+=" "+i),e.children.length===0&&(t.xmlMode?t.selfClosingTags!==!1:t.selfClosingTags&&$8.has(e.name))?(t.xmlMode||(r+=" "),r+="/>"):(r+=">",e.children.length>0&&(r+=X0(e.children,t)),(t.xmlMode||!$8.has(e.name))&&(r+="</"+e.name+">")),r}function hat(e){return"<"+e.data+">"}function pat(e,t){var n=e.data||"";return t.decodeEntities!==!1&&!(!t.xmlMode&&e.parent&&aat.has(e.parent.name))&&(n=dp.encodeXML(n)),n}function mat(e){return"<![CDATA["+e.children[0].data+"]]>"}function gat(e){return"<!--"+e.data+"-->"}var vat=V&&V.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(le,"__esModule",{value:!0});le.innerText=le.textContent=le.getText=le.getInnerHTML=le.getOuterHTML=void 0;var Ke=xn,yat=vat(H0),_at=G0;function pp(e,t){return(0,yat.default)(e,t)}le.getOuterHTML=pp;function wat(e,t){return(0,Ke.hasChildren)(e)?e.children.map(function(n){return pp(n,t)}).join(""):""}le.getInnerHTML=wat;function ts(e){return Array.isArray(e)?e.map(ts).join(""):(0,Ke.isTag)(e)?e.name==="br"?`
`:ts(e.children):(0,Ke.isCDATA)(e)?ts(e.children):(0,Ke.isText)(e)?e.data:""}le.getText=ts;function Qc(e){return Array.isArray(e)?e.map(Qc).join(""):(0,Ke.hasChildren)(e)&&!(0,Ke.isComment)(e)?Qc(e.children):(0,Ke.isText)(e)?e.data:""}le.textContent=Qc;function Kc(e){return Array.isArray(e)?e.map(Kc).join(""):(0,Ke.hasChildren)(e)&&(e.type===_at.ElementType.Tag||(0,Ke.isCDATA)(e))?Kc(e.children):(0,Ke.isText)(e)?e.data:""}le.innerText=Kc;var St={};Object.defineProperty(St,"__esModule",{value:!0});St.prevElementSibling=St.nextElementSibling=St.getName=St.hasAttrib=St.getAttributeValue=St.getSiblings=St.getParent=St.getChildren=void 0;var mp=xn,xat=[];function gp(e){var t;return(t=e.children)!==null&&t!==void 0?t:xat}St.getChildren=gp;function vp(e){return e.parent||null}St.getParent=vp;function Sat(e){var t,n,r=vp(e);if(r!=null)return gp(r);for(var i=[e],o=e.prev,s=e.next;o!=null;)i.unshift(o),t=o,o=t.prev;for(;s!=null;)i.push(s),n=s,s=n.next;return i}St.getSiblings=Sat;function Tat(e,t){var n;return(n=e.attribs)===null||n===void 0?void 0:n[t]}St.getAttributeValue=Tat;function bat(e,t){return e.attribs!=null&&Object.prototype.hasOwnProperty.call(e.attribs,t)&&e.attribs[t]!=null}St.hasAttrib=bat;function Cat(e){return e.name}St.getName=Cat;function Eat(e){for(var t,n=e.next;n!==null&&!(0,mp.isTag)(n);)t=n,n=t.next;return n}St.nextElementSibling=Eat;function kat(e){for(var t,n=e.prev;n!==null&&!(0,mp.isTag)(n);)t=n,n=t.prev;return n}St.prevElementSibling=kat;var Xt={};Object.defineProperty(Xt,"__esModule",{value:!0});Xt.prepend=Xt.prependChild=Xt.append=Xt.appendChild=Xt.replaceElement=Xt.removeElement=void 0;function lo(e){if(e.prev&&(e.prev.next=e.next),e.next&&(e.next.prev=e.prev),e.parent){var t=e.parent.children;t.splice(t.lastIndexOf(e),1)}}Xt.removeElement=lo;function Pat(e,t){var n=t.prev=e.prev;n&&(n.next=t);var r=t.next=e.next;r&&(r.prev=t);var i=t.parent=e.parent;if(i){var o=i.children;o[o.lastIndexOf(e)]=t}}Xt.replaceElement=Pat;function Dat(e,t){if(lo(t),t.next=null,t.parent=e,e.children.push(t)>1){var n=e.children[e.children.length-2];n.next=t,t.prev=n}else t.prev=null}Xt.appendChild=Dat;function Aat(e,t){lo(t);var n=e.parent,r=e.next;if(t.next=r,t.prev=e,e.next=t,t.parent=n,r){if(r.prev=t,n){var i=n.children;i.splice(i.lastIndexOf(r),0,t)}}else n&&n.children.push(t)}Xt.append=Aat;function Lat(e,t){if(lo(t),t.parent=e,t.prev=null,e.children.unshift(t)!==1){var n=e.children[1];n.prev=t,t.next=n}else t.next=null}Xt.prependChild=Lat;function Oat(e,t){lo(t);var n=e.parent;if(n){var r=n.children;r.splice(r.indexOf(e),0,t)}e.prev&&(e.prev.next=t),t.parent=n,t.prev=e.prev,t.next=e,e.prev=t}Xt.prepend=Oat;var Bt={};Object.defineProperty(Bt,"__esModule",{value:!0});Bt.findAll=Bt.existsOne=Bt.findOne=Bt.findOneChild=Bt.find=Bt.filter=void 0;var Ki=xn;function Mat(e,t,n,r){return n===void 0&&(n=!0),r===void 0&&(r=1/0),Array.isArray(t)||(t=[t]),Q0(e,t,n,r)}Bt.filter=Mat;function Q0(e,t,n,r){for(var i=[],o=0,s=t;o<s.length;o++){var a=s[o];if(e(a)&&(i.push(a),--r<=0))break;if(n&&(0,Ki.hasChildren)(a)&&a.children.length>0){var l=Q0(e,a.children,n,r);if(i.push.apply(i,l),r-=l.length,r<=0)break}}return i}Bt.find=Q0;function Nat(e,t){return t.find(e)}Bt.findOneChild=Nat;function yp(e,t,n){n===void 0&&(n=!0);for(var r=null,i=0;i<t.length&&!r;i++){var o=t[i];if((0,Ki.isTag)(o))e(o)?r=o:n&&o.children.length>0&&(r=yp(e,o.children));else continue}return r}Bt.findOne=yp;function _p(e,t){return t.some(function(n){return(0,Ki.isTag)(n)&&(e(n)||n.children.length>0&&_p(e,n.children))})}Bt.existsOne=_p;function Rat(e,t){for(var n,r=[],i=t.filter(Ki.isTag),o;o=i.shift();){var s=(n=o.children)===null||n===void 0?void 0:n.filter(Ki.isTag);s&&s.length>0&&i.unshift.apply(i,s),e(o)&&r.push(o)}return r}Bt.findAll=Rat;var ue={};Object.defineProperty(ue,"__esModule",{value:!0});ue.getElementsByTagType=ue.getElementsByTagName=ue.getElementById=ue.getElements=ue.testElement=void 0;var hr=xn,ba=Bt,Us={tag_name:function(e){return typeof e=="function"?function(t){return(0,hr.isTag)(t)&&e(t.name)}:e==="*"?hr.isTag:function(t){return(0,hr.isTag)(t)&&t.name===e}},tag_type:function(e){return typeof e=="function"?function(t){return e(t.type)}:function(t){return t.type===e}},tag_contains:function(e){return typeof e=="function"?function(t){return(0,hr.isText)(t)&&e(t.data)}:function(t){return(0,hr.isText)(t)&&t.data===e}}};function wp(e,t){return typeof t=="function"?function(n){return(0,hr.isTag)(n)&&t(n.attribs[e])}:function(n){return(0,hr.isTag)(n)&&n.attribs[e]===t}}function Iat(e,t){return function(n){return e(n)||t(n)}}function xp(e){var t=Object.keys(e).map(function(n){var r=e[n];return Object.prototype.hasOwnProperty.call(Us,n)?Us[n](r):wp(n,r)});return t.length===0?null:t.reduce(Iat)}function Vat(e,t){var n=xp(e);return n?n(t):!0}ue.testElement=Vat;function qat(e,t,n,r){r===void 0&&(r=1/0);var i=xp(e);return i?(0,ba.filter)(i,t,n,r):[]}ue.getElements=qat;function Fat(e,t,n){return n===void 0&&(n=!0),Array.isArray(t)||(t=[t]),(0,ba.findOne)(wp("id",e),t,n)}ue.getElementById=Fat;function Bat(e,t,n,r){return n===void 0&&(n=!0),r===void 0&&(r=1/0),(0,ba.filter)(Us.tag_name(e),t,n,r)}ue.getElementsByTagName=Bat;function zat(e,t,n,r){return n===void 0&&(n=!0),r===void 0&&(r=1/0),(0,ba.filter)(Us.tag_type(e),t,n,r)}ue.getElementsByTagType=zat;var Hn={};Object.defineProperty(Hn,"__esModule",{value:!0});Hn.uniqueSort=Hn.compareDocumentPosition=Hn.removeSubsets=void 0;var H8=xn;function jat(e){for(var t=e.length;--t>=0;){var n=e[t];if(t>0&&e.lastIndexOf(n,t-1)>=0){e.splice(t,1);continue}for(var r=n.parent;r;r=r.parent)if(e.includes(r)){e.splice(t,1);break}}return e}Hn.removeSubsets=jat;function Sp(e,t){var n=[],r=[];if(e===t)return 0;for(var i=(0,H8.hasChildren)(e)?e:e.parent;i;)n.unshift(i),i=i.parent;for(i=(0,H8.hasChildren)(t)?t:t.parent;i;)r.unshift(i),i=i.parent;for(var o=Math.min(n.length,r.length),s=0;s<o&&n[s]===r[s];)s++;if(s===0)return 1;var a=n[s-1],l=a.children,c=n[s],u=r[s];return l.indexOf(c)>l.indexOf(u)?a===t?20:4:a===e?10:2}Hn.compareDocumentPosition=Sp;function Uat(e){return e=e.filter(function(t,n,r){return!r.includes(t,n+1)}),e.sort(function(t,n){var r=Sp(t,n);return r&2?-1:r&4?1:0}),e}Hn.uniqueSort=Uat;var Ca={};Object.defineProperty(Ca,"__esModule",{value:!0});Ca.getFeed=void 0;var $at=le,co=ue;function Hat(e){var t=$s(Qat,e);return t?t.name==="feed"?Gat(t):Wat(t):null}Ca.getFeed=Hat;function Gat(e){var t,n=e.children,r={type:"atom",items:(0,co.getElementsByTagName)("entry",n).map(function(s){var a,l=s.children,c={media:Tp(l)};Wt(c,"id","id",l),Wt(c,"title","title",l);var u=(a=$s("link",l))===null||a===void 0?void 0:a.attribs.href;u&&(c.link=u);var f=_r("summary",l)||_r("content",l);f&&(c.description=f);var d=_r("updated",l);return d&&(c.pubDate=new Date(d)),c})};Wt(r,"id","id",n),Wt(r,"title","title",n);var i=(t=$s("link",n))===null||t===void 0?void 0:t.attribs.href;i&&(r.link=i),Wt(r,"description","subtitle",n);var o=_r("updated",n);return o&&(r.updated=new Date(o)),Wt(r,"author","email",n,!0),r}function Wat(e){var t,n,r=(n=(t=$s("channel",e.children))===null||t===void 0?void 0:t.children)!==null&&n!==void 0?n:[],i={type:e.name.substr(0,3),id:"",items:(0,co.getElementsByTagName)("item",e.children).map(function(s){var a=s.children,l={media:Tp(a)};Wt(l,"id","guid",a),Wt(l,"title","title",a),Wt(l,"link","link",a),Wt(l,"description","description",a);var c=_r("pubDate",a);return c&&(l.pubDate=new Date(c)),l})};Wt(i,"title","title",r),Wt(i,"link","link",r),Wt(i,"description","description",r);var o=_r("lastBuildDate",r);return o&&(i.updated=new Date(o)),Wt(i,"author","managingEditor",r,!0),i}var Yat=["url","type","lang"],Xat=["fileSize","bitrate","framerate","samplingrate","channels","duration","height","width"];function Tp(e){return(0,co.getElementsByTagName)("media:content",e).map(function(t){for(var n=t.attribs,r={medium:n.medium,isDefault:!!n.isDefault},i=0,o=Yat;i<o.length;i++){var s=o[i];n[s]&&(r[s]=n[s])}for(var a=0,l=Xat;a<l.length;a++){var s=l[a];n[s]&&(r[s]=parseInt(n[s],10))}return n.expression&&(r.expression=n.expression),r})}function $s(e,t){return(0,co.getElementsByTagName)(e,t,!0,1)[0]}function _r(e,t,n){return n===void 0&&(n=!1),(0,$at.textContent)((0,co.getElementsByTagName)(e,t,n,1)).trim()}function Wt(e,t,n,r,i){i===void 0&&(i=!1);var o=_r(n,r,i);o&&(e[t]=o)}function Qat(e){return e==="rss"||e==="feed"||e==="rdf:RDF"}(function(e){var t=V&&V.__createBinding||(Object.create?function(i,o,s,a){a===void 0&&(a=s),Object.defineProperty(i,a,{enumerable:!0,get:function(){return o[s]}})}:function(i,o,s,a){a===void 0&&(a=s),i[a]=o[s]}),n=V&&V.__exportStar||function(i,o){for(var s in i)s!=="default"&&!Object.prototype.hasOwnProperty.call(o,s)&&t(o,i,s)};Object.defineProperty(e,"__esModule",{value:!0}),e.hasChildren=e.isDocument=e.isComment=e.isText=e.isCDATA=e.isTag=void 0,n(le,e),n(St,e),n(Xt,e),n(Bt,e),n(ue,e),n(Hn,e),n(Ca,e);var r=xn;Object.defineProperty(e,"isTag",{enumerable:!0,get:function(){return r.isTag}}),Object.defineProperty(e,"isCDATA",{enumerable:!0,get:function(){return r.isCDATA}}),Object.defineProperty(e,"isText",{enumerable:!0,get:function(){return r.isText}}),Object.defineProperty(e,"isComment",{enumerable:!0,get:function(){return r.isComment}}),Object.defineProperty(e,"isDocument",{enumerable:!0,get:function(){return r.isDocument}}),Object.defineProperty(e,"hasChildren",{enumerable:!0,get:function(){return r.hasChildren}})})($0);(function(e){var t=V&&V.__extends||function(){var l=function(c,u){return l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,d){f.__proto__=d}||function(f,d){for(var p in d)Object.prototype.hasOwnProperty.call(d,p)&&(f[p]=d[p])},l(c,u)};return function(c,u){if(typeof u!="function"&&u!==null)throw new TypeError("Class extends value "+String(u)+" is not a constructor or null");l(c,u);function f(){this.constructor=c}c.prototype=u===null?Object.create(u):(f.prototype=u.prototype,new f)}}(),n=V&&V.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(e,"__esModule",{value:!0}),e.parseFeed=e.FeedHandler=e.getFeed=void 0;var r=n(xn),i=$0;Object.defineProperty(e,"getFeed",{enumerable:!0,get:function(){return i.getFeed}});var o=ao,s=function(l){t(c,l);function c(u,f){var d=this;return typeof u=="object"&&(u=void 0,f=u),d=l.call(this,u,f)||this,d}return c.prototype.onend=function(){var u=(0,i.getFeed)(this.dom);u?(this.feed=u,this.handleCallback(null)):this.handleCallback(new Error("couldn't find root of feed"))},c}(r.default);e.FeedHandler=s;function a(l,c){c===void 0&&(c={xmlMode:!0});var u=new r.default(null,c);return new o.Parser(u,c).end(l),(0,i.getFeed)(u.dom)}e.parseFeed=a})(Yc);(function(e){var t=V&&V.__createBinding||(Object.create?function(v,h,_,m){m===void 0&&(m=_),Object.defineProperty(v,m,{enumerable:!0,get:function(){return h[_]}})}:function(v,h,_,m){m===void 0&&(m=_),v[m]=h[_]}),n=V&&V.__setModuleDefault||(Object.create?function(v,h){Object.defineProperty(v,"default",{enumerable:!0,value:h})}:function(v,h){v.default=h}),r=V&&V.__importStar||function(v){if(v&&v.__esModule)return v;var h={};if(v!=null)for(var _ in v)_!=="default"&&Object.prototype.hasOwnProperty.call(v,_)&&t(h,v,_);return n(h,v),h},i=V&&V.__exportStar||function(v,h){for(var _ in v)_!=="default"&&!Object.prototype.hasOwnProperty.call(h,_)&&t(h,v,_)},o=V&&V.__importDefault||function(v){return v&&v.__esModule?v:{default:v}};Object.defineProperty(e,"__esModule",{value:!0}),e.RssHandler=e.DefaultHandler=e.DomUtils=e.ElementType=e.Tokenizer=e.createDomStream=e.parseDOM=e.parseDocument=e.DomHandler=e.Parser=void 0;var s=ao;Object.defineProperty(e,"Parser",{enumerable:!0,get:function(){return s.Parser}});var a=xn;Object.defineProperty(e,"DomHandler",{enumerable:!0,get:function(){return a.DomHandler}}),Object.defineProperty(e,"DefaultHandler",{enumerable:!0,get:function(){return a.DomHandler}});function l(v,h){var _=new a.DomHandler(void 0,h);return new s.Parser(_,h).end(v),_.root}e.parseDocument=l;function c(v,h){return l(v,h).children}e.parseDOM=c;function u(v,h,_){var m=new a.DomHandler(v,h,_);return new s.Parser(m,h)}e.createDomStream=u;var f=ya;Object.defineProperty(e,"Tokenizer",{enumerable:!0,get:function(){return o(f).default}});var d=r(Zh);e.ElementType=d,i(Yc,e),e.DomUtils=r($0);var p=Yc;Object.defineProperty(e,"RssHandler",{enumerable:!0,get:function(){return p.FeedHandler}})})(Fh);var Kat=(e,t)=>{var[n,r]=K.useState(null);return K.useEffect(()=>{if(!e){r(null);return}var i=!1;function o(){return s.apply(this,arguments)}function s(){return s=P4(function*(){try{var a=yield fetch(e).then(l=>l.text()).then(l=>Fh.parseFeed(l));i||r(a)}catch(l){console.error("Unable to get data from ".concat(e),l)}}),s.apply(this,arguments)}return o(),()=>{i=!0}},[e]),n},Zat={exports:{}};(function(e){(function(){function t(h,_){document.addEventListener?h.addEventListener("scroll",_,!1):h.attachEvent("scroll",_)}function n(h){document.body?h():document.addEventListener?document.addEventListener("DOMContentLoaded",function _(){document.removeEventListener("DOMContentLoaded",_),h()}):document.attachEvent("onreadystatechange",function _(){(document.readyState=="interactive"||document.readyState=="complete")&&(document.detachEvent("onreadystatechange",_),h())})}function r(h){this.g=document.createElement("div"),this.g.setAttribute("aria-hidden","true"),this.g.appendChild(document.createTextNode(h)),this.h=document.createElement("span"),this.i=document.createElement("span"),this.m=document.createElement("span"),this.j=document.createElement("span"),this.l=-1,this.h.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.i.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.j.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.m.style.cssText="display:inline-block;width:200%;height:200%;font-size:16px;max-width:none;",this.h.appendChild(this.m),this.i.appendChild(this.j),this.g.appendChild(this.h),this.g.appendChild(this.i)}function i(h,_){h.g.style.cssText="max-width:none;min-width:20px;min-height:20px;display:inline-block;overflow:hidden;position:absolute;width:auto;margin:0;padding:0;top:-999px;white-space:nowrap;font-synthesis:none;font:"+_+";"}function o(h){var _=h.g.offsetWidth,m=_+100;return h.j.style.width=m+"px",h.i.scrollLeft=m,h.h.scrollLeft=h.h.scrollWidth+100,h.l!==_?(h.l=_,!0):!1}function s(h,_){function m(){var y=g;o(y)&&y.g.parentNode!==null&&_(y.l)}var g=h;t(h.h,m),t(h.i,m),o(h)}function a(h,_,m){_=_||{},m=m||window,this.family=h,this.style=_.style||"normal",this.weight=_.weight||"normal",this.stretch=_.stretch||"normal",this.context=m}var l=null,c=null,u=null,f=null;function d(h){return c===null&&(p(h)&&/Apple/.test(window.navigator.vendor)?(h=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))(?:\.([0-9]+))/.exec(window.navigator.userAgent),c=!!h&&603>parseInt(h[1],10)):c=!1),c}function p(h){return f===null&&(f=!!h.document.fonts),f}function v(h,_){var m=h.style,g=h.weight;if(u===null){var y=document.createElement("div");try{y.style.font="condensed 100px sans-serif"}catch{}u=y.style.font!==""}return[m,g,u?h.stretch:"","100px",_].join(" ")}a.prototype.load=function(h,_){var m=this,g=h||"BESbswy",y=0,w=_||3e3,x=new Date().getTime();return new Promise(function(S,T){if(p(m.context)&&!d(m.context)){var b=new Promise(function(E,L){function O(){new Date().getTime()-x>=w?L(Error(""+w+"ms timeout exceeded")):m.context.document.fonts.load(v(m,'"'+m.family+'"'),g).then(function(B){1<=B.length?E():setTimeout(O,25)},L)}O()}),C=new Promise(function(E,L){y=setTimeout(function(){L(Error(""+w+"ms timeout exceeded"))},w)});Promise.race([C,b]).then(function(){clearTimeout(y),S(m)},T)}else n(function(){function E(){var q;(q=I!=-1&&F!=-1||I!=-1&&z!=-1||F!=-1&&z!=-1)&&((q=I!=F&&I!=z&&F!=z)||(l===null&&(q=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))/.exec(window.navigator.userAgent),l=!!q&&(536>parseInt(q[1],10)||parseInt(q[1],10)===536&&11>=parseInt(q[2],10))),q=l&&(I==P&&F==P&&z==P||I==M&&F==M&&z==M||I==N&&F==N&&z==N)),q=!q),q&&(j.parentNode!==null&&j.parentNode.removeChild(j),clearTimeout(y),S(m))}function L(){if(new Date().getTime()-x>=w)j.parentNode!==null&&j.parentNode.removeChild(j),T(Error(""+w+"ms timeout exceeded"));else{var q=m.context.document.hidden;(q===!0||q===void 0)&&(I=O.g.offsetWidth,F=B.g.offsetWidth,z=G.g.offsetWidth,E()),y=setTimeout(L,50)}}var O=new r(g),B=new r(g),G=new r(g),I=-1,F=-1,z=-1,P=-1,M=-1,N=-1,j=document.createElement("div");j.dir="ltr",i(O,v(m,"sans-serif")),i(B,v(m,"serif")),i(G,v(m,"monospace")),j.appendChild(O.g),j.appendChild(B.g),j.appendChild(G.g),m.context.document.body.appendChild(j),P=O.g.offsetWidth,M=B.g.offsetWidth,N=G.g.offsetWidth,L(),s(O,function(q){I=q,E()}),i(O,v(m,'"'+m.family+'",sans-serif')),s(B,function(q){F=q,E()}),i(B,v(m,'"'+m.family+'",serif')),s(G,function(q){z=q,E()}),i(G,v(m,'"'+m.family+'",monospace'))})})},e.exports=a})()})(Zat);function G8(){return new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}function Jat(){var l;const{isPlaying:e}=w6(),{breaking:t,feed:n}=x6(),[r,i]=D.useState(G8()),o=D.useRef(null),s=D.useRef(null),a=Kat(n);return console.log("Feed URL:",n),console.log("RSS Feed Data:",a),D.useEffect(()=>{const c=setInterval(()=>{i(G8())},1e3);return()=>clearInterval(c)},[]),D.useEffect(()=>{const c=dr.timeline({defaults:{duration:.5}});t?c.to(o.current,{opacity:0},0).to(s.current,{opacity:1},0):c.to(s.current,{opacity:0},0).to(o.current,{opacity:1},0)},[t]),tn.jsxs(N4,{hide:!e,onPlay:c=>{t?(dr.set(o.current,{opacity:0}),dr.set(s.current,{opacity:1})):(dr.set(o.current,{opacity:1}),dr.set(s.current,{opacity:0})),c.from(".tickerClock, .tickerFeed",{y:100,duration:.5})},onStop:c=>{c.to(".tickerClock, .tickerFeed",{y:100,duration:.3})},children:[tn.jsxs("div",{className:"tickerClock",children:[tn.jsxs("div",{ref:o,className:"tickerClock-content",children:[r," "]}),tn.jsx("div",{ref:s,className:"tickerClock-breakingNews",children:"BREAKING NEWS "})]}),tn.jsx("div",{className:"tickerFeed",children:tn.jsx("div",{className:"tickerFeed-content",children:(l=a==null?void 0:a.items)!=null&&l.length?tn.jsx(rw,{play:e,items:a.items.slice(0,10),renderItem:({id:c,title:u})=>tn.jsx("div",{className:"tickerFeed-item",children:u},c)}):tn.jsx("div",{children:"No data availables"})})})]})}fm(Jat);

</script>
    <style>
.tickerFeed{position:fixed;bottom:0;width:100%;height:7.5%;background-color:#1e2832;overflow:hidden;padding-top:10px}.tickerFeed-content{display:flex;align-items:center;font-size:40px;font-family:Arial,Helvetica,sans-serif;color:#fff}.tickerFeed-item{margin-right:50px;white-space:nowrap}.tickerClock{position:fixed;bottom:0;width:25%;height:7.5%;z-index:9999;display:flex;justify-content:center;overflow:hidden;padding-top:10px;background:linear-gradient(to right,#1e2832,#1e2832 95%,#0000)}.tickerClock-content{font-size:40px;font-family:Arial,Helvetica,sans-serif;color:#fff;text-align:center}.tickerClock-breakingNews{position:absolute;top:0;left:0;right:0;bottom:0;background-color:#d62828;background:linear-gradient(to right,#d62828,#d62828 90%,#0000);display:flex;justify-content:center;padding-top:10px;font-weight:700;font-size:40px;font-family:Arial,Helvetica,sans-serif;color:#fff}

</style>
  </head>
  <body>
  </body>
</html>
