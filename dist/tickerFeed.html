<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <script type="module" crossorigin>
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();var V=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function H8(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var G8={exports:{}},$s={},W8={exports:{}},U={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ki=Symbol.for("react.element"),Tp=Symbol.for("react.portal"),bp=Symbol.for("react.fragment"),Cp=Symbol.for("react.strict_mode"),Ep=Symbol.for("react.profiler"),kp=Symbol.for("react.provider"),Pp=Symbol.for("react.context"),Dp=Symbol.for("react.forward_ref"),Ap=Symbol.for("react.suspense"),Lp=Symbol.for("react.memo"),Op=Symbol.for("react.lazy"),Q0=Symbol.iterator;function Mp(e){return e===null||typeof e!="object"?null:(e=Q0&&e[Q0]||e["@@iterator"],typeof e=="function"?e:null)}var Y8={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},X8=Object.assign,Q8={};function O1(e,t,n){this.props=e,this.context=t,this.refs=Q8,this.updater=n||Y8}O1.prototype.isReactComponent={};O1.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};O1.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function K8(){}K8.prototype=O1.prototype;function Ku(e,t,n){this.props=e,this.context=t,this.refs=Q8,this.updater=n||Y8}var Zu=Ku.prototype=new K8;Zu.constructor=Ku;X8(Zu,O1.prototype);Zu.isPureReactComponent=!0;var K0=Array.isArray,Z8=Object.prototype.hasOwnProperty,Ju={current:null},J8={key:!0,ref:!0,__self:!0,__source:!0};function t9(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)Z8.call(t,r)&&!J8.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:Ki,type:e,key:o,ref:s,props:i,_owner:Ju.current}}function Np(e,t){return{$$typeof:Ki,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function tc(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ki}function Rp(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Z0=/\/+/g;function Ca(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Rp(""+e.key):t.toString(36)}function Ro(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Ki:case Tp:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+Ca(s,0):r,K0(i)?(n="",e!=null&&(n=e.replace(Z0,"$&/")+"/"),Ro(i,t,n,"",function(u){return u})):i!=null&&(tc(i)&&(i=Np(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(Z0,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",K0(e))for(var a=0;a<e.length;a++){o=e[a];var l=r+Ca(o,a);s+=Ro(o,t,n,l,i)}else if(l=Mp(e),typeof l=="function")for(e=l.call(e),a=0;!(o=e.next()).done;)o=o.value,l=r+Ca(o,a++),s+=Ro(o,t,n,l,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function co(e,t,n){if(e==null)return e;var r=[],i=0;return Ro(e,r,"","",function(o){return t.call(n,o,i++)}),r}function Ip(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ut={current:null},Io={transition:null},Vp={ReactCurrentDispatcher:Ut,ReactCurrentBatchConfig:Io,ReactCurrentOwner:Ju};function e9(){throw Error("act(...) is not supported in production builds of React.")}U.Children={map:co,forEach:function(e,t,n){co(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return co(e,function(){t++}),t},toArray:function(e){return co(e,function(t){return t})||[]},only:function(e){if(!tc(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};U.Component=O1;U.Fragment=bp;U.Profiler=Ep;U.PureComponent=Ku;U.StrictMode=Cp;U.Suspense=Ap;U.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Vp;U.act=e9;U.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=X8({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=Ju.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Z8.call(t,l)&&!J8.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:Ki,type:e.type,key:i,ref:o,props:r,_owner:s}};U.createContext=function(e){return e={$$typeof:Pp,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:kp,_context:e},e.Consumer=e};U.createElement=t9;U.createFactory=function(e){var t=t9.bind(null,e);return t.type=e,t};U.createRef=function(){return{current:null}};U.forwardRef=function(e){return{$$typeof:Dp,render:e}};U.isValidElement=tc;U.lazy=function(e){return{$$typeof:Op,_payload:{_status:-1,_result:e},_init:Ip}};U.memo=function(e,t){return{$$typeof:Lp,type:e,compare:t===void 0?null:t}};U.startTransition=function(e){var t=Io.transition;Io.transition={};try{e()}finally{Io.transition=t}};U.unstable_act=e9;U.useCallback=function(e,t){return Ut.current.useCallback(e,t)};U.useContext=function(e){return Ut.current.useContext(e)};U.useDebugValue=function(){};U.useDeferredValue=function(e){return Ut.current.useDeferredValue(e)};U.useEffect=function(e,t){return Ut.current.useEffect(e,t)};U.useId=function(){return Ut.current.useId()};U.useImperativeHandle=function(e,t,n){return Ut.current.useImperativeHandle(e,t,n)};U.useInsertionEffect=function(e,t){return Ut.current.useInsertionEffect(e,t)};U.useLayoutEffect=function(e,t){return Ut.current.useLayoutEffect(e,t)};U.useMemo=function(e,t){return Ut.current.useMemo(e,t)};U.useReducer=function(e,t,n){return Ut.current.useReducer(e,t,n)};U.useRef=function(e){return Ut.current.useRef(e)};U.useState=function(e){return Ut.current.useState(e)};U.useSyncExternalStore=function(e,t,n){return Ut.current.useSyncExternalStore(e,t,n)};U.useTransition=function(){return Ut.current.useTransition()};U.version="18.3.1";W8.exports=U;var D=W8.exports;const K=H8(D);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qp=D,Fp=Symbol.for("react.element"),Bp=Symbol.for("react.fragment"),zp=Object.prototype.hasOwnProperty,jp=qp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Up={key:!0,ref:!0,__self:!0,__source:!0};function n9(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)zp.call(t,r)&&!Up.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Fp,type:e,key:o,ref:s,props:i,_owner:jp.current}}$s.Fragment=Bp;$s.jsx=n9;$s.jsxs=n9;G8.exports=$s;var qr=G8.exports,Gt={loading:0,loaded:1,playing:2,paused:3,stopped:4,removed:5},r9={exports:{}},_e={},i9={exports:{}},o9={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,M){var N=P.length;P.push(M);t:for(;0<N;){var j=N-1>>>1,q=P[j];if(0<i(q,M))P[j]=M,P[N]=q,N=j;else break t}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var M=P[0],N=P.pop();if(N!==M){P[0]=N;t:for(var j=0,q=P.length,er=q>>>1;j<er;){var Et=2*(j+1)-1,Le=P[Et],nr=Et+1,uo=P[nr];if(0>i(Le,N))nr<q&&0>i(uo,Le)?(P[j]=uo,P[nr]=N,j=nr):(P[j]=Le,P[Et]=N,j=Et);else if(nr<q&&0>i(uo,N))P[j]=uo,P[nr]=N,j=nr;else break t}}return M}function i(P,M){var N=P.sortIndex-M.sortIndex;return N!==0?N:P.id-M.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],u=[],c=1,f=null,d=3,p=!1,v=!1,h=!1,_=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,g=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(P){for(var M=n(u);M!==null;){if(M.callback===null)r(u);else if(M.startTime<=P)r(u),M.sortIndex=M.expirationTime,t(l,M);else break;M=n(u)}}function w(P){if(h=!1,y(P),!v)if(n(l)!==null)v=!0,F(x);else{var M=n(u);M!==null&&z(w,M.startTime-P)}}function x(P,M){v=!1,h&&(h=!1,m(b),b=-1),p=!0;var N=d;try{for(y(M),f=n(l);f!==null&&(!(f.expirationTime>M)||P&&!L());){var j=f.callback;if(typeof j=="function"){f.callback=null,d=f.priorityLevel;var q=j(f.expirationTime<=M);M=e.unstable_now(),typeof q=="function"?f.callback=q:f===n(l)&&r(l),y(M)}else r(l);f=n(l)}if(f!==null)var er=!0;else{var Et=n(u);Et!==null&&z(w,Et.startTime-M),er=!1}return er}finally{f=null,d=N,p=!1}}var S=!1,T=null,b=-1,C=5,E=-1;function L(){return!(e.unstable_now()-E<C)}function O(){if(T!==null){var P=e.unstable_now();E=P;var M=!0;try{M=T(!0,P)}finally{M?B():(S=!1,T=null)}}else S=!1}var B;if(typeof g=="function")B=function(){g(O)};else if(typeof MessageChannel<"u"){var G=new MessageChannel,I=G.port2;G.port1.onmessage=O,B=function(){I.postMessage(null)}}else B=function(){_(O,0)};function F(P){T=P,S||(S=!0,B())}function z(P,M){b=_(function(){P(e.unstable_now())},M)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){v||p||(v=!0,F(x))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(P){switch(d){case 1:case 2:case 3:var M=3;break;default:M=d}var N=d;d=M;try{return P()}finally{d=N}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,M){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var N=d;d=P;try{return M()}finally{d=N}},e.unstable_scheduleCallback=function(P,M,N){var j=e.unstable_now();switch(typeof N=="object"&&N!==null?(N=N.delay,N=typeof N=="number"&&0<N?j+N:j):N=j,P){case 1:var q=-1;break;case 2:q=250;break;case 5:q=**********;break;case 4:q=1e4;break;default:q=5e3}return q=N+q,P={id:c++,callback:M,priorityLevel:P,startTime:N,expirationTime:q,sortIndex:-1},N>j?(P.sortIndex=N,t(u,P),n(l)===null&&P===n(u)&&(h?(m(b),b=-1):h=!0,z(w,N-j))):(P.sortIndex=q,t(l,P),v||p||(v=!0,F(x))),P},e.unstable_shouldYield=L,e.unstable_wrapCallback=function(P){var M=d;return function(){var N=d;d=M;try{return P.apply(this,arguments)}finally{d=N}}}})(o9);i9.exports=o9;var $p=i9.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hp=D,ge=$p;function k(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var s9=new Set,xi={};function Lr(e,t){v1(e,t),v1(e+"Capture",t)}function v1(e,t){for(xi[e]=t,e=0;e<t.length;e++)s9.add(t[e])}var hn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),xl=Object.prototype.hasOwnProperty,Gp=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,J0={},tf={};function Wp(e){return xl.call(tf,e)?!0:xl.call(J0,e)?!1:Gp.test(e)?tf[e]=!0:(J0[e]=!0,!1)}function Yp(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Xp(e,t,n,r){if(t===null||typeof t>"u"||Yp(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function $t(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var At={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){At[e]=new $t(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];At[t]=new $t(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){At[e]=new $t(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){At[e]=new $t(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){At[e]=new $t(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){At[e]=new $t(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){At[e]=new $t(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){At[e]=new $t(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){At[e]=new $t(e,5,!1,e.toLowerCase(),null,!1,!1)});var ec=/[\-:]([a-z])/g;function nc(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ec,nc);At[t]=new $t(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ec,nc);At[t]=new $t(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ec,nc);At[t]=new $t(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){At[e]=new $t(e,1,!1,e.toLowerCase(),null,!1,!1)});At.xlinkHref=new $t("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){At[e]=new $t(e,1,!1,e.toLowerCase(),null,!0,!0)});function rc(e,t,n,r){var i=At.hasOwnProperty(t)?At[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Xp(t,n,i,r)&&(n=null),r||i===null?Wp(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var _n=Hp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,fo=Symbol.for("react.element"),jr=Symbol.for("react.portal"),Ur=Symbol.for("react.fragment"),ic=Symbol.for("react.strict_mode"),Sl=Symbol.for("react.profiler"),a9=Symbol.for("react.provider"),l9=Symbol.for("react.context"),oc=Symbol.for("react.forward_ref"),Tl=Symbol.for("react.suspense"),bl=Symbol.for("react.suspense_list"),sc=Symbol.for("react.memo"),Tn=Symbol.for("react.lazy"),u9=Symbol.for("react.offscreen"),ef=Symbol.iterator;function I1(e){return e===null||typeof e!="object"?null:(e=ef&&e[ef]||e["@@iterator"],typeof e=="function"?e:null)}var ut=Object.assign,Ea;function Y1(e){if(Ea===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ea=t&&t[1]||""}return`
`+Ea+e}var ka=!1;function Pa(e,t){if(!e||ka)return"";ka=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,a=o.length-1;1<=s&&0<=a&&i[s]!==o[a];)a--;for(;1<=s&&0<=a;s--,a--)if(i[s]!==o[a]){if(s!==1||a!==1)do if(s--,a--,0>a||i[s]!==o[a]){var l=`
`+i[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{ka=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Y1(e):""}function Qp(e){switch(e.tag){case 5:return Y1(e.type);case 16:return Y1("Lazy");case 13:return Y1("Suspense");case 19:return Y1("SuspenseList");case 0:case 2:case 15:return e=Pa(e.type,!1),e;case 11:return e=Pa(e.type.render,!1),e;case 1:return e=Pa(e.type,!0),e;default:return""}}function Cl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Ur:return"Fragment";case jr:return"Portal";case Sl:return"Profiler";case ic:return"StrictMode";case Tl:return"Suspense";case bl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case l9:return(e.displayName||"Context")+".Consumer";case a9:return(e._context.displayName||"Context")+".Provider";case oc:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case sc:return t=e.displayName||null,t!==null?t:Cl(e.type)||"Memo";case Tn:t=e._payload,e=e._init;try{return Cl(e(t))}catch{}}return null}function Kp(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Cl(t);case 8:return t===ic?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Hn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function c9(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Zp(e){var t=c9(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ho(e){e._valueTracker||(e._valueTracker=Zp(e))}function f9(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=c9(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ts(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function El(e,t){var n=t.checked;return ut({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n!=null?n:e._wrapperState.initialChecked})}function nf(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Hn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function d9(e,t){t=t.checked,t!=null&&rc(e,"checked",t,!1)}function kl(e,t){d9(e,t);var n=Hn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Pl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Pl(e,t.type,Hn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function rf(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Pl(e,t,n){(t!=="number"||ts(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var X1=Array.isArray;function s1(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Hn(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Dl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(k(91));return ut({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function of(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(k(92));if(X1(n)){if(1<n.length)throw Error(k(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Hn(n)}}function h9(e,t){var n=Hn(t.value),r=Hn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function sf(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function p9(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Al(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?p9(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var po,m9=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(po=po||document.createElement("div"),po.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=po.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Si(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var ni={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Jp=["Webkit","ms","Moz","O"];Object.keys(ni).forEach(function(e){Jp.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),ni[t]=ni[e]})});function g9(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||ni.hasOwnProperty(e)&&ni[e]?(""+t).trim():t+"px"}function v9(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=g9(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var t7=ut({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ll(e,t){if(t){if(t7[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(k(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(k(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(k(61))}if(t.style!=null&&typeof t.style!="object")throw Error(k(62))}}function Ol(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ml=null;function ac(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Nl=null,a1=null,l1=null;function af(e){if(e=to(e)){if(typeof Nl!="function")throw Error(k(280));var t=e.stateNode;t&&(t=Xs(t),Nl(e.stateNode,e.type,t))}}function y9(e){a1?l1?l1.push(e):l1=[e]:a1=e}function _9(){if(a1){var e=a1,t=l1;if(l1=a1=null,af(e),t)for(e=0;e<t.length;e++)af(t[e])}}function w9(e,t){return e(t)}function x9(){}var Da=!1;function S9(e,t,n){if(Da)return e(t,n);Da=!0;try{return w9(e,t,n)}finally{Da=!1,(a1!==null||l1!==null)&&(x9(),_9())}}function Ti(e,t){var n=e.stateNode;if(n===null)return null;var r=Xs(n);if(r===null)return null;n=r[t];t:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break t;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(k(231,t,typeof n));return n}var Rl=!1;if(hn)try{var V1={};Object.defineProperty(V1,"passive",{get:function(){Rl=!0}}),window.addEventListener("test",V1,V1),window.removeEventListener("test",V1,V1)}catch{Rl=!1}function e7(e,t,n,r,i,o,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var ri=!1,es=null,ns=!1,Il=null,n7={onError:function(e){ri=!0,es=e}};function r7(e,t,n,r,i,o,s,a,l){ri=!1,es=null,e7.apply(n7,arguments)}function i7(e,t,n,r,i,o,s,a,l){if(r7.apply(this,arguments),ri){if(ri){var u=es;ri=!1,es=null}else throw Error(k(198));ns||(ns=!0,Il=u)}}function Or(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function T9(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function lf(e){if(Or(e)!==e)throw Error(k(188))}function o7(e){var t=e.alternate;if(!t){if(t=Or(e),t===null)throw Error(k(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return lf(i),e;if(o===r)return lf(i),t;o=o.sibling}throw Error(k(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s){for(a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s)throw Error(k(189))}}if(n.alternate!==r)throw Error(k(190))}if(n.tag!==3)throw Error(k(188));return n.stateNode.current===n?e:t}function b9(e){return e=o7(e),e!==null?C9(e):null}function C9(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=C9(e);if(t!==null)return t;e=e.sibling}return null}var E9=ge.unstable_scheduleCallback,uf=ge.unstable_cancelCallback,s7=ge.unstable_shouldYield,a7=ge.unstable_requestPaint,mt=ge.unstable_now,l7=ge.unstable_getCurrentPriorityLevel,lc=ge.unstable_ImmediatePriority,k9=ge.unstable_UserBlockingPriority,rs=ge.unstable_NormalPriority,u7=ge.unstable_LowPriority,P9=ge.unstable_IdlePriority,Hs=null,We=null;function c7(e){if(We&&typeof We.onCommitFiberRoot=="function")try{We.onCommitFiberRoot(Hs,e,void 0,(e.current.flags&128)===128)}catch{}}var Ve=Math.clz32?Math.clz32:h7,f7=Math.log,d7=Math.LN2;function h7(e){return e>>>=0,e===0?32:31-(f7(e)/d7|0)|0}var mo=64,go=4194304;function Q1(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function is(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~i;a!==0?r=Q1(a):(o&=s,o!==0&&(r=Q1(o)))}else s=n&~i,s!==0?r=Q1(s):o!==0&&(r=Q1(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ve(t),i=1<<n,r|=e[n],t&=~i;return r}function p7(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function m7(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Ve(o),a=1<<s,l=i[s];l===-1?(!(a&n)||a&r)&&(i[s]=p7(a,t)):l<=t&&(e.expiredLanes|=a),o&=~a}}function Vl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function D9(){var e=mo;return mo<<=1,!(mo&4194240)&&(mo=64),e}function Aa(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Zi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ve(t),e[t]=n}function g7(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Ve(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function uc(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ve(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var Y=0;function A9(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var L9,cc,O9,M9,N9,ql=!1,vo=[],Nn=null,Rn=null,In=null,bi=new Map,Ci=new Map,En=[],v7="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function cf(e,t){switch(e){case"focusin":case"focusout":Nn=null;break;case"dragenter":case"dragleave":Rn=null;break;case"mouseover":case"mouseout":In=null;break;case"pointerover":case"pointerout":bi.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ci.delete(t.pointerId)}}function q1(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=to(t),t!==null&&cc(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function y7(e,t,n,r,i){switch(t){case"focusin":return Nn=q1(Nn,e,t,n,r,i),!0;case"dragenter":return Rn=q1(Rn,e,t,n,r,i),!0;case"mouseover":return In=q1(In,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return bi.set(o,q1(bi.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Ci.set(o,q1(Ci.get(o)||null,e,t,n,r,i)),!0}return!1}function R9(e){var t=dr(e.target);if(t!==null){var n=Or(t);if(n!==null){if(t=n.tag,t===13){if(t=T9(n),t!==null){e.blockedOn=t,N9(e.priority,function(){O9(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Vo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Fl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ml=r,n.target.dispatchEvent(r),Ml=null}else return t=to(n),t!==null&&cc(t),e.blockedOn=n,!1;t.shift()}return!0}function ff(e,t,n){Vo(e)&&n.delete(t)}function _7(){ql=!1,Nn!==null&&Vo(Nn)&&(Nn=null),Rn!==null&&Vo(Rn)&&(Rn=null),In!==null&&Vo(In)&&(In=null),bi.forEach(ff),Ci.forEach(ff)}function F1(e,t){e.blockedOn===t&&(e.blockedOn=null,ql||(ql=!0,ge.unstable_scheduleCallback(ge.unstable_NormalPriority,_7)))}function Ei(e){function t(i){return F1(i,e)}if(0<vo.length){F1(vo[0],e);for(var n=1;n<vo.length;n++){var r=vo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Nn!==null&&F1(Nn,e),Rn!==null&&F1(Rn,e),In!==null&&F1(In,e),bi.forEach(t),Ci.forEach(t),n=0;n<En.length;n++)r=En[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<En.length&&(n=En[0],n.blockedOn===null);)R9(n),n.blockedOn===null&&En.shift()}var u1=_n.ReactCurrentBatchConfig,os=!0;function w7(e,t,n,r){var i=Y,o=u1.transition;u1.transition=null;try{Y=1,fc(e,t,n,r)}finally{Y=i,u1.transition=o}}function x7(e,t,n,r){var i=Y,o=u1.transition;u1.transition=null;try{Y=4,fc(e,t,n,r)}finally{Y=i,u1.transition=o}}function fc(e,t,n,r){if(os){var i=Fl(e,t,n,r);if(i===null)Ba(e,t,r,ss,n),cf(e,r);else if(y7(i,e,t,n,r))r.stopPropagation();else if(cf(e,r),t&4&&-1<v7.indexOf(e)){for(;i!==null;){var o=to(i);if(o!==null&&L9(o),o=Fl(e,t,n,r),o===null&&Ba(e,t,r,ss,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else Ba(e,t,r,null,n)}}var ss=null;function Fl(e,t,n,r){if(ss=null,e=ac(r),e=dr(e),e!==null)if(t=Or(e),t===null)e=null;else if(n=t.tag,n===13){if(e=T9(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ss=e,null}function I9(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(l7()){case lc:return 1;case k9:return 4;case rs:case u7:return 16;case P9:return 536870912;default:return 16}default:return 16}}var Pn=null,dc=null,qo=null;function V9(){if(qo)return qo;var e,t=dc,n=t.length,r,i="value"in Pn?Pn.value:Pn.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return qo=i.slice(e,1<r?1-r:void 0)}function Fo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function yo(){return!0}function df(){return!1}function we(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?yo:df,this.isPropagationStopped=df,this}return ut(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=yo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=yo)},persist:function(){},isPersistent:yo}),t}var M1={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},hc=we(M1),Ji=ut({},M1,{view:0,detail:0}),S7=we(Ji),La,Oa,B1,Gs=ut({},Ji,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==B1&&(B1&&e.type==="mousemove"?(La=e.screenX-B1.screenX,Oa=e.screenY-B1.screenY):Oa=La=0,B1=e),La)},movementY:function(e){return"movementY"in e?e.movementY:Oa}}),hf=we(Gs),T7=ut({},Gs,{dataTransfer:0}),b7=we(T7),C7=ut({},Ji,{relatedTarget:0}),Ma=we(C7),E7=ut({},M1,{animationName:0,elapsedTime:0,pseudoElement:0}),k7=we(E7),P7=ut({},M1,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),D7=we(P7),A7=ut({},M1,{data:0}),pf=we(A7),L7={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},O7={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},M7={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function N7(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=M7[e])?!!t[e]:!1}function pc(){return N7}var R7=ut({},Ji,{key:function(e){if(e.key){var t=L7[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Fo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?O7[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pc,charCode:function(e){return e.type==="keypress"?Fo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Fo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),I7=we(R7),V7=ut({},Gs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),mf=we(V7),q7=ut({},Ji,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pc}),F7=we(q7),B7=ut({},M1,{propertyName:0,elapsedTime:0,pseudoElement:0}),z7=we(B7),j7=ut({},Gs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),U7=we(j7),$7=[9,13,27,32],mc=hn&&"CompositionEvent"in window,ii=null;hn&&"documentMode"in document&&(ii=document.documentMode);var H7=hn&&"TextEvent"in window&&!ii,q9=hn&&(!mc||ii&&8<ii&&11>=ii),gf=" ",vf=!1;function F9(e,t){switch(e){case"keyup":return $7.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function B9(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var $r=!1;function G7(e,t){switch(e){case"compositionend":return B9(t);case"keypress":return t.which!==32?null:(vf=!0,gf);case"textInput":return e=t.data,e===gf&&vf?null:e;default:return null}}function W7(e,t){if($r)return e==="compositionend"||!mc&&F9(e,t)?(e=V9(),qo=dc=Pn=null,$r=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return q9&&t.locale!=="ko"?null:t.data;default:return null}}var Y7={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Y7[e.type]:t==="textarea"}function z9(e,t,n,r){y9(r),t=as(t,"onChange"),0<t.length&&(n=new hc("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var oi=null,ki=null;function X7(e){Z9(e,0)}function Ws(e){var t=Wr(e);if(f9(t))return e}function Q7(e,t){if(e==="change")return t}var j9=!1;if(hn){var Na;if(hn){var Ra="oninput"in document;if(!Ra){var _f=document.createElement("div");_f.setAttribute("oninput","return;"),Ra=typeof _f.oninput=="function"}Na=Ra}else Na=!1;j9=Na&&(!document.documentMode||9<document.documentMode)}function wf(){oi&&(oi.detachEvent("onpropertychange",U9),ki=oi=null)}function U9(e){if(e.propertyName==="value"&&Ws(ki)){var t=[];z9(t,ki,e,ac(e)),S9(X7,t)}}function K7(e,t,n){e==="focusin"?(wf(),oi=t,ki=n,oi.attachEvent("onpropertychange",U9)):e==="focusout"&&wf()}function Z7(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ws(ki)}function J7(e,t){if(e==="click")return Ws(t)}function t3(e,t){if(e==="input"||e==="change")return Ws(t)}function e3(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Fe=typeof Object.is=="function"?Object.is:e3;function Pi(e,t){if(Fe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!xl.call(t,i)||!Fe(e[i],t[i]))return!1}return!0}function xf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Sf(e,t){var n=xf(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=xf(n)}}function $9(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?$9(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function H9(){for(var e=window,t=ts();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ts(e.document)}return t}function gc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function n3(e){var t=H9(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&$9(n.ownerDocument.documentElement,n)){if(r!==null&&gc(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=Sf(n,o);var s=Sf(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var r3=hn&&"documentMode"in document&&11>=document.documentMode,Hr=null,Bl=null,si=null,zl=!1;function Tf(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;zl||Hr==null||Hr!==ts(r)||(r=Hr,"selectionStart"in r&&gc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),si&&Pi(si,r)||(si=r,r=as(Bl,"onSelect"),0<r.length&&(t=new hc("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Hr)))}function _o(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Gr={animationend:_o("Animation","AnimationEnd"),animationiteration:_o("Animation","AnimationIteration"),animationstart:_o("Animation","AnimationStart"),transitionend:_o("Transition","TransitionEnd")},Ia={},G9={};hn&&(G9=document.createElement("div").style,"AnimationEvent"in window||(delete Gr.animationend.animation,delete Gr.animationiteration.animation,delete Gr.animationstart.animation),"TransitionEvent"in window||delete Gr.transitionend.transition);function Ys(e){if(Ia[e])return Ia[e];if(!Gr[e])return e;var t=Gr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in G9)return Ia[e]=t[n];return e}var W9=Ys("animationend"),Y9=Ys("animationiteration"),X9=Ys("animationstart"),Q9=Ys("transitionend"),K9=new Map,bf="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Kn(e,t){K9.set(e,t),Lr(t,[e])}for(var Va=0;Va<bf.length;Va++){var qa=bf[Va],i3=qa.toLowerCase(),o3=qa[0].toUpperCase()+qa.slice(1);Kn(i3,"on"+o3)}Kn(W9,"onAnimationEnd");Kn(Y9,"onAnimationIteration");Kn(X9,"onAnimationStart");Kn("dblclick","onDoubleClick");Kn("focusin","onFocus");Kn("focusout","onBlur");Kn(Q9,"onTransitionEnd");v1("onMouseEnter",["mouseout","mouseover"]);v1("onMouseLeave",["mouseout","mouseover"]);v1("onPointerEnter",["pointerout","pointerover"]);v1("onPointerLeave",["pointerout","pointerover"]);Lr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Lr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Lr("onBeforeInput",["compositionend","keypress","textInput","paste"]);Lr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Lr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Lr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var K1="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),s3=new Set("cancel close invalid load scroll toggle".split(" ").concat(K1));function Cf(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,i7(r,t,void 0,e),e.currentTarget=null}function Z9(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;t:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==o&&i.isPropagationStopped())break t;Cf(i,a,u),o=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==o&&i.isPropagationStopped())break t;Cf(i,a,u),o=l}}}if(ns)throw e=Il,ns=!1,Il=null,e}function et(e,t){var n=t[Gl];n===void 0&&(n=t[Gl]=new Set);var r=e+"__bubble";n.has(r)||(J9(t,e,2,!1),n.add(r))}function Fa(e,t,n){var r=0;t&&(r|=4),J9(n,e,r,t)}var wo="_reactListening"+Math.random().toString(36).slice(2);function Di(e){if(!e[wo]){e[wo]=!0,s9.forEach(function(n){n!=="selectionchange"&&(s3.has(n)||Fa(n,!1,e),Fa(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[wo]||(t[wo]=!0,Fa("selectionchange",!1,t))}}function J9(e,t,n,r){switch(I9(t)){case 1:var i=w7;break;case 4:i=x7;break;default:i=fc}n=i.bind(null,t,n,e),i=void 0,!Rl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Ba(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)t:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;s=s.return}for(;a!==null;){if(s=dr(a),s===null)return;if(l=s.tag,l===5||l===6){r=o=s;continue t}a=a.parentNode}}r=r.return}S9(function(){var u=o,c=ac(n),f=[];t:{var d=K9.get(e);if(d!==void 0){var p=hc,v=e;switch(e){case"keypress":if(Fo(n)===0)break t;case"keydown":case"keyup":p=I7;break;case"focusin":v="focus",p=Ma;break;case"focusout":v="blur",p=Ma;break;case"beforeblur":case"afterblur":p=Ma;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":p=hf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":p=b7;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":p=F7;break;case W9:case Y9:case X9:p=k7;break;case Q9:p=z7;break;case"scroll":p=S7;break;case"wheel":p=U7;break;case"copy":case"cut":case"paste":p=D7;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":p=mf}var h=(t&4)!==0,_=!h&&e==="scroll",m=h?d!==null?d+"Capture":null:d;h=[];for(var g=u,y;g!==null;){y=g;var w=y.stateNode;if(y.tag===5&&w!==null&&(y=w,m!==null&&(w=Ti(g,m),w!=null&&h.push(Ai(g,w,y)))),_)break;g=g.return}0<h.length&&(d=new p(d,v,null,n,c),f.push({event:d,listeners:h}))}}if(!(t&7)){t:{if(d=e==="mouseover"||e==="pointerover",p=e==="mouseout"||e==="pointerout",d&&n!==Ml&&(v=n.relatedTarget||n.fromElement)&&(dr(v)||v[pn]))break t;if((p||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,p?(v=n.relatedTarget||n.toElement,p=u,v=v?dr(v):null,v!==null&&(_=Or(v),v!==_||v.tag!==5&&v.tag!==6)&&(v=null)):(p=null,v=u),p!==v)){if(h=hf,w="onMouseLeave",m="onMouseEnter",g="mouse",(e==="pointerout"||e==="pointerover")&&(h=mf,w="onPointerLeave",m="onPointerEnter",g="pointer"),_=p==null?d:Wr(p),y=v==null?d:Wr(v),d=new h(w,g+"leave",p,n,c),d.target=_,d.relatedTarget=y,w=null,dr(c)===u&&(h=new h(m,g+"enter",v,n,c),h.target=y,h.relatedTarget=_,w=h),_=w,p&&v)e:{for(h=p,m=v,g=0,y=h;y;y=Fr(y))g++;for(y=0,w=m;w;w=Fr(w))y++;for(;0<g-y;)h=Fr(h),g--;for(;0<y-g;)m=Fr(m),y--;for(;g--;){if(h===m||m!==null&&h===m.alternate)break e;h=Fr(h),m=Fr(m)}h=null}else h=null;p!==null&&Ef(f,d,p,h,!1),v!==null&&_!==null&&Ef(f,_,v,h,!0)}}t:{if(d=u?Wr(u):window,p=d.nodeName&&d.nodeName.toLowerCase(),p==="select"||p==="input"&&d.type==="file")var x=Q7;else if(yf(d))if(j9)x=t3;else{x=Z7;var S=K7}else(p=d.nodeName)&&p.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(x=J7);if(x&&(x=x(e,u))){z9(f,x,n,c);break t}S&&S(e,d,u),e==="focusout"&&(S=d._wrapperState)&&S.controlled&&d.type==="number"&&Pl(d,"number",d.value)}switch(S=u?Wr(u):window,e){case"focusin":(yf(S)||S.contentEditable==="true")&&(Hr=S,Bl=u,si=null);break;case"focusout":si=Bl=Hr=null;break;case"mousedown":zl=!0;break;case"contextmenu":case"mouseup":case"dragend":zl=!1,Tf(f,n,c);break;case"selectionchange":if(r3)break;case"keydown":case"keyup":Tf(f,n,c)}var T;if(mc)t:{switch(e){case"compositionstart":var b="onCompositionStart";break t;case"compositionend":b="onCompositionEnd";break t;case"compositionupdate":b="onCompositionUpdate";break t}b=void 0}else $r?F9(e,n)&&(b="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(b="onCompositionStart");b&&(q9&&n.locale!=="ko"&&($r||b!=="onCompositionStart"?b==="onCompositionEnd"&&$r&&(T=V9()):(Pn=c,dc="value"in Pn?Pn.value:Pn.textContent,$r=!0)),S=as(u,b),0<S.length&&(b=new pf(b,e,null,n,c),f.push({event:b,listeners:S}),T?b.data=T:(T=B9(n),T!==null&&(b.data=T)))),(T=H7?G7(e,n):W7(e,n))&&(u=as(u,"onBeforeInput"),0<u.length&&(c=new pf("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=T))}Z9(f,t)})}function Ai(e,t,n){return{instance:e,listener:t,currentTarget:n}}function as(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=Ti(e,n),o!=null&&r.unshift(Ai(e,o,i)),o=Ti(e,t),o!=null&&r.push(Ai(e,o,i))),e=e.return}return r}function Fr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Ef(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,i?(l=Ti(n,o),l!=null&&s.unshift(Ai(n,l,a))):i||(l=Ti(n,o),l!=null&&s.push(Ai(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var a3=/\r\n?/g,l3=/\u0000|\uFFFD/g;function kf(e){return(typeof e=="string"?e:""+e).replace(a3,`
`).replace(l3,"")}function xo(e,t,n){if(t=kf(t),kf(e)!==t&&n)throw Error(k(425))}function ls(){}var jl=null,Ul=null;function $l(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Hl=typeof setTimeout=="function"?setTimeout:void 0,u3=typeof clearTimeout=="function"?clearTimeout:void 0,Pf=typeof Promise=="function"?Promise:void 0,c3=typeof queueMicrotask=="function"?queueMicrotask:typeof Pf<"u"?function(e){return Pf.resolve(null).then(e).catch(f3)}:Hl;function f3(e){setTimeout(function(){throw e})}function za(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Ei(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Ei(t)}function Vn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Df(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var N1=Math.random().toString(36).slice(2),$e="__reactFiber$"+N1,Li="__reactProps$"+N1,pn="__reactContainer$"+N1,Gl="__reactEvents$"+N1,d3="__reactListeners$"+N1,h3="__reactHandles$"+N1;function dr(e){var t=e[$e];if(t)return t;for(var n=e.parentNode;n;){if(t=n[pn]||n[$e]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Df(e);e!==null;){if(n=e[$e])return n;e=Df(e)}return t}e=n,n=e.parentNode}return null}function to(e){return e=e[$e]||e[pn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Wr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(k(33))}function Xs(e){return e[Li]||null}var Wl=[],Yr=-1;function Zn(e){return{current:e}}function nt(e){0>Yr||(e.current=Wl[Yr],Wl[Yr]=null,Yr--)}function tt(e,t){Yr++,Wl[Yr]=e.current,e.current=t}var Gn={},Vt=Zn(Gn),Kt=Zn(!1),br=Gn;function y1(e,t){var n=e.type.contextTypes;if(!n)return Gn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Zt(e){return e=e.childContextTypes,e!=null}function us(){nt(Kt),nt(Vt)}function Af(e,t,n){if(Vt.current!==Gn)throw Error(k(168));tt(Vt,t),tt(Kt,n)}function t2(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(k(108,Kp(e)||"Unknown",i));return ut({},n,r)}function cs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Gn,br=Vt.current,tt(Vt,e),tt(Kt,Kt.current),!0}function Lf(e,t,n){var r=e.stateNode;if(!r)throw Error(k(169));n?(e=t2(e,t,br),r.__reactInternalMemoizedMergedChildContext=e,nt(Kt),nt(Vt),tt(Vt,e)):nt(Kt),tt(Kt,n)}var sn=null,Qs=!1,ja=!1;function e2(e){sn===null?sn=[e]:sn.push(e)}function p3(e){Qs=!0,e2(e)}function Jn(){if(!ja&&sn!==null){ja=!0;var e=0,t=Y;try{var n=sn;for(Y=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}sn=null,Qs=!1}catch(i){throw sn!==null&&(sn=sn.slice(e+1)),E9(lc,Jn),i}finally{Y=t,ja=!1}}return null}var Xr=[],Qr=0,fs=null,ds=0,Se=[],Te=0,Cr=null,ln=1,un="";function ar(e,t){Xr[Qr++]=ds,Xr[Qr++]=fs,fs=e,ds=t}function n2(e,t,n){Se[Te++]=ln,Se[Te++]=un,Se[Te++]=Cr,Cr=e;var r=ln;e=un;var i=32-Ve(r)-1;r&=~(1<<i),n+=1;var o=32-Ve(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,ln=1<<32-Ve(t)+i|n<<i|r,un=o+e}else ln=1<<o|n<<i|r,un=e}function vc(e){e.return!==null&&(ar(e,1),n2(e,1,0))}function yc(e){for(;e===fs;)fs=Xr[--Qr],Xr[Qr]=null,ds=Xr[--Qr],Xr[Qr]=null;for(;e===Cr;)Cr=Se[--Te],Se[Te]=null,un=Se[--Te],Se[Te]=null,ln=Se[--Te],Se[Te]=null}var he=null,fe=null,rt=!1,Ie=null;function r2(e,t){var n=be(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Of(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,he=e,fe=Vn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,he=e,fe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Cr!==null?{id:ln,overflow:un}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=be(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,he=e,fe=null,!0):!1;default:return!1}}function Yl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Xl(e){if(rt){var t=fe;if(t){var n=t;if(!Of(e,t)){if(Yl(e))throw Error(k(418));t=Vn(n.nextSibling);var r=he;t&&Of(e,t)?r2(r,n):(e.flags=e.flags&-4097|2,rt=!1,he=e)}}else{if(Yl(e))throw Error(k(418));e.flags=e.flags&-4097|2,rt=!1,he=e}}}function Mf(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;he=e}function So(e){if(e!==he)return!1;if(!rt)return Mf(e),rt=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!$l(e.type,e.memoizedProps)),t&&(t=fe)){if(Yl(e))throw i2(),Error(k(418));for(;t;)r2(e,t),t=Vn(t.nextSibling)}if(Mf(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(k(317));t:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){fe=Vn(e.nextSibling);break t}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}fe=null}}else fe=he?Vn(e.stateNode.nextSibling):null;return!0}function i2(){for(var e=fe;e;)e=Vn(e.nextSibling)}function _1(){fe=he=null,rt=!1}function _c(e){Ie===null?Ie=[e]:Ie.push(e)}var m3=_n.ReactCurrentBatchConfig;function z1(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(k(309));var r=n.stateNode}if(!r)throw Error(k(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var a=i.refs;s===null?delete a[o]:a[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(k(284));if(!n._owner)throw Error(k(290,e))}return e}function To(e,t){throw e=Object.prototype.toString.call(t),Error(k(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Nf(e){var t=e._init;return t(e._payload)}function o2(e){function t(m,g){if(e){var y=m.deletions;y===null?(m.deletions=[g],m.flags|=16):y.push(g)}}function n(m,g){if(!e)return null;for(;g!==null;)t(m,g),g=g.sibling;return null}function r(m,g){for(m=new Map;g!==null;)g.key!==null?m.set(g.key,g):m.set(g.index,g),g=g.sibling;return m}function i(m,g){return m=zn(m,g),m.index=0,m.sibling=null,m}function o(m,g,y){return m.index=y,e?(y=m.alternate,y!==null?(y=y.index,y<g?(m.flags|=2,g):y):(m.flags|=2,g)):(m.flags|=1048576,g)}function s(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,g,y,w){return g===null||g.tag!==6?(g=Xa(y,m.mode,w),g.return=m,g):(g=i(g,y),g.return=m,g)}function l(m,g,y,w){var x=y.type;return x===Ur?c(m,g,y.props.children,w,y.key):g!==null&&(g.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===Tn&&Nf(x)===g.type)?(w=i(g,y.props),w.ref=z1(m,g,y),w.return=m,w):(w=Go(y.type,y.key,y.props,null,m.mode,w),w.ref=z1(m,g,y),w.return=m,w)}function u(m,g,y,w){return g===null||g.tag!==4||g.stateNode.containerInfo!==y.containerInfo||g.stateNode.implementation!==y.implementation?(g=Qa(y,m.mode,w),g.return=m,g):(g=i(g,y.children||[]),g.return=m,g)}function c(m,g,y,w,x){return g===null||g.tag!==7?(g=_r(y,m.mode,w,x),g.return=m,g):(g=i(g,y),g.return=m,g)}function f(m,g,y){if(typeof g=="string"&&g!==""||typeof g=="number")return g=Xa(""+g,m.mode,y),g.return=m,g;if(typeof g=="object"&&g!==null){switch(g.$$typeof){case fo:return y=Go(g.type,g.key,g.props,null,m.mode,y),y.ref=z1(m,null,g),y.return=m,y;case jr:return g=Qa(g,m.mode,y),g.return=m,g;case Tn:var w=g._init;return f(m,w(g._payload),y)}if(X1(g)||I1(g))return g=_r(g,m.mode,y,null),g.return=m,g;To(m,g)}return null}function d(m,g,y,w){var x=g!==null?g.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return x!==null?null:a(m,g,""+y,w);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case fo:return y.key===x?l(m,g,y,w):null;case jr:return y.key===x?u(m,g,y,w):null;case Tn:return x=y._init,d(m,g,x(y._payload),w)}if(X1(y)||I1(y))return x!==null?null:c(m,g,y,w,null);To(m,y)}return null}function p(m,g,y,w,x){if(typeof w=="string"&&w!==""||typeof w=="number")return m=m.get(y)||null,a(g,m,""+w,x);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case fo:return m=m.get(w.key===null?y:w.key)||null,l(g,m,w,x);case jr:return m=m.get(w.key===null?y:w.key)||null,u(g,m,w,x);case Tn:var S=w._init;return p(m,g,y,S(w._payload),x)}if(X1(w)||I1(w))return m=m.get(y)||null,c(g,m,w,x,null);To(g,w)}return null}function v(m,g,y,w){for(var x=null,S=null,T=g,b=g=0,C=null;T!==null&&b<y.length;b++){T.index>b?(C=T,T=null):C=T.sibling;var E=d(m,T,y[b],w);if(E===null){T===null&&(T=C);break}e&&T&&E.alternate===null&&t(m,T),g=o(E,g,b),S===null?x=E:S.sibling=E,S=E,T=C}if(b===y.length)return n(m,T),rt&&ar(m,b),x;if(T===null){for(;b<y.length;b++)T=f(m,y[b],w),T!==null&&(g=o(T,g,b),S===null?x=T:S.sibling=T,S=T);return rt&&ar(m,b),x}for(T=r(m,T);b<y.length;b++)C=p(T,m,b,y[b],w),C!==null&&(e&&C.alternate!==null&&T.delete(C.key===null?b:C.key),g=o(C,g,b),S===null?x=C:S.sibling=C,S=C);return e&&T.forEach(function(L){return t(m,L)}),rt&&ar(m,b),x}function h(m,g,y,w){var x=I1(y);if(typeof x!="function")throw Error(k(150));if(y=x.call(y),y==null)throw Error(k(151));for(var S=x=null,T=g,b=g=0,C=null,E=y.next();T!==null&&!E.done;b++,E=y.next()){T.index>b?(C=T,T=null):C=T.sibling;var L=d(m,T,E.value,w);if(L===null){T===null&&(T=C);break}e&&T&&L.alternate===null&&t(m,T),g=o(L,g,b),S===null?x=L:S.sibling=L,S=L,T=C}if(E.done)return n(m,T),rt&&ar(m,b),x;if(T===null){for(;!E.done;b++,E=y.next())E=f(m,E.value,w),E!==null&&(g=o(E,g,b),S===null?x=E:S.sibling=E,S=E);return rt&&ar(m,b),x}for(T=r(m,T);!E.done;b++,E=y.next())E=p(T,m,b,E.value,w),E!==null&&(e&&E.alternate!==null&&T.delete(E.key===null?b:E.key),g=o(E,g,b),S===null?x=E:S.sibling=E,S=E);return e&&T.forEach(function(O){return t(m,O)}),rt&&ar(m,b),x}function _(m,g,y,w){if(typeof y=="object"&&y!==null&&y.type===Ur&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case fo:t:{for(var x=y.key,S=g;S!==null;){if(S.key===x){if(x=y.type,x===Ur){if(S.tag===7){n(m,S.sibling),g=i(S,y.props.children),g.return=m,m=g;break t}}else if(S.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===Tn&&Nf(x)===S.type){n(m,S.sibling),g=i(S,y.props),g.ref=z1(m,S,y),g.return=m,m=g;break t}n(m,S);break}else t(m,S);S=S.sibling}y.type===Ur?(g=_r(y.props.children,m.mode,w,y.key),g.return=m,m=g):(w=Go(y.type,y.key,y.props,null,m.mode,w),w.ref=z1(m,g,y),w.return=m,m=w)}return s(m);case jr:t:{for(S=y.key;g!==null;){if(g.key===S)if(g.tag===4&&g.stateNode.containerInfo===y.containerInfo&&g.stateNode.implementation===y.implementation){n(m,g.sibling),g=i(g,y.children||[]),g.return=m,m=g;break t}else{n(m,g);break}else t(m,g);g=g.sibling}g=Qa(y,m.mode,w),g.return=m,m=g}return s(m);case Tn:return S=y._init,_(m,g,S(y._payload),w)}if(X1(y))return v(m,g,y,w);if(I1(y))return h(m,g,y,w);To(m,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,g!==null&&g.tag===6?(n(m,g.sibling),g=i(g,y),g.return=m,m=g):(n(m,g),g=Xa(y,m.mode,w),g.return=m,m=g),s(m)):n(m,g)}return _}var w1=o2(!0),s2=o2(!1),hs=Zn(null),ps=null,Kr=null,wc=null;function xc(){wc=Kr=ps=null}function Sc(e){var t=hs.current;nt(hs),e._currentValue=t}function Ql(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function c1(e,t){ps=e,wc=Kr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Qt=!0),e.firstContext=null)}function Pe(e){var t=e._currentValue;if(wc!==e)if(e={context:e,memoizedValue:t,next:null},Kr===null){if(ps===null)throw Error(k(308));Kr=e,ps.dependencies={lanes:0,firstContext:e}}else Kr=Kr.next=e;return t}var hr=null;function Tc(e){hr===null?hr=[e]:hr.push(e)}function a2(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Tc(t)):(n.next=i.next,i.next=n),t.interleaved=n,mn(e,r)}function mn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var bn=!1;function bc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function l2(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function cn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function qn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,W&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,mn(e,n)}return i=r.interleaved,i===null?(t.next=t,Tc(r)):(t.next=i.next,i.next=t),r.interleaved=t,mn(e,n)}function Bo(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,uc(e,n)}}function Rf(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ms(e,t,n,r){var i=e.updateQueue;bn=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?o=u:s.next=u,s=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==s&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(o!==null){var f=i.baseState;s=0,c=u=l=null,a=o;do{var d=a.lane,p=a.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:p,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});t:{var v=e,h=a;switch(d=t,p=n,h.tag){case 1:if(v=h.payload,typeof v=="function"){f=v.call(p,f,d);break t}f=v;break t;case 3:v.flags=v.flags&-65537|128;case 0:if(v=h.payload,d=typeof v=="function"?v.call(p,f,d):v,d==null)break t;f=ut({},f,d);break t;case 2:bn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,d=i.effects,d===null?i.effects=[a]:d.push(a))}else p={eventTime:p,lane:d,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=p,l=f):c=c.next=p,s|=d;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;d=a,a=d.next,d.next=null,i.lastBaseUpdate=d,i.shared.pending=null}}while(!0);if(c===null&&(l=f),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);kr|=s,e.lanes=s,e.memoizedState=f}}function If(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(k(191,i));i.call(r)}}}var eo={},Ye=Zn(eo),Oi=Zn(eo),Mi=Zn(eo);function pr(e){if(e===eo)throw Error(k(174));return e}function Cc(e,t){switch(tt(Mi,t),tt(Oi,e),tt(Ye,eo),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Al(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Al(t,e)}nt(Ye),tt(Ye,t)}function x1(){nt(Ye),nt(Oi),nt(Mi)}function u2(e){pr(Mi.current);var t=pr(Ye.current),n=Al(t,e.type);t!==n&&(tt(Oi,e),tt(Ye,n))}function Ec(e){Oi.current===e&&(nt(Ye),nt(Oi))}var ot=Zn(0);function gs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ua=[];function kc(){for(var e=0;e<Ua.length;e++)Ua[e]._workInProgressVersionPrimary=null;Ua.length=0}var zo=_n.ReactCurrentDispatcher,$a=_n.ReactCurrentBatchConfig,Er=0,lt=null,_t=null,xt=null,vs=!1,ai=!1,Ni=0,g3=0;function Lt(){throw Error(k(321))}function Pc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Fe(e[n],t[n]))return!1;return!0}function Dc(e,t,n,r,i,o){if(Er=o,lt=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,zo.current=e===null||e.memoizedState===null?w3:x3,e=n(r,i),ai){o=0;do{if(ai=!1,Ni=0,25<=o)throw Error(k(301));o+=1,xt=_t=null,t.updateQueue=null,zo.current=S3,e=n(r,i)}while(ai)}if(zo.current=ys,t=_t!==null&&_t.next!==null,Er=0,xt=_t=lt=null,vs=!1,t)throw Error(k(300));return e}function Ac(){var e=Ni!==0;return Ni=0,e}function ze(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return xt===null?lt.memoizedState=xt=e:xt=xt.next=e,xt}function De(){if(_t===null){var e=lt.alternate;e=e!==null?e.memoizedState:null}else e=_t.next;var t=xt===null?lt.memoizedState:xt.next;if(t!==null)xt=t,_t=e;else{if(e===null)throw Error(k(310));_t=e,e={memoizedState:_t.memoizedState,baseState:_t.baseState,baseQueue:_t.baseQueue,queue:_t.queue,next:null},xt===null?lt.memoizedState=xt=e:xt=xt.next=e}return xt}function Ri(e,t){return typeof t=="function"?t(e):t}function Ha(e){var t=De(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=_t,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var a=s=null,l=null,u=o;do{var c=u.lane;if((Er&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=f,s=r):l=l.next=f,lt.lanes|=c,kr|=c}u=u.next}while(u!==null&&u!==o);l===null?s=r:l.next=a,Fe(r,t.memoizedState)||(Qt=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,lt.lanes|=o,kr|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ga(e){var t=De(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);Fe(o,t.memoizedState)||(Qt=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function c2(){}function f2(e,t){var n=lt,r=De(),i=t(),o=!Fe(r.memoizedState,i);if(o&&(r.memoizedState=i,Qt=!0),r=r.queue,Lc(p2.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||xt!==null&&xt.memoizedState.tag&1){if(n.flags|=2048,Ii(9,h2.bind(null,n,r,i,t),void 0,null),bt===null)throw Error(k(349));Er&30||d2(n,t,i)}return i}function d2(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=lt.updateQueue,t===null?(t={lastEffect:null,stores:null},lt.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function h2(e,t,n,r){t.value=n,t.getSnapshot=r,m2(t)&&g2(e)}function p2(e,t,n){return n(function(){m2(t)&&g2(e)})}function m2(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Fe(e,n)}catch{return!0}}function g2(e){var t=mn(e,1);t!==null&&qe(t,e,1,-1)}function Vf(e){var t=ze();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ri,lastRenderedState:e},t.queue=e,e=e.dispatch=_3.bind(null,lt,e),[t.memoizedState,e]}function Ii(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=lt.updateQueue,t===null?(t={lastEffect:null,stores:null},lt.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function v2(){return De().memoizedState}function jo(e,t,n,r){var i=ze();lt.flags|=e,i.memoizedState=Ii(1|t,n,void 0,r===void 0?null:r)}function Ks(e,t,n,r){var i=De();r=r===void 0?null:r;var o=void 0;if(_t!==null){var s=_t.memoizedState;if(o=s.destroy,r!==null&&Pc(r,s.deps)){i.memoizedState=Ii(t,n,o,r);return}}lt.flags|=e,i.memoizedState=Ii(1|t,n,o,r)}function qf(e,t){return jo(8390656,8,e,t)}function Lc(e,t){return Ks(2048,8,e,t)}function y2(e,t){return Ks(4,2,e,t)}function _2(e,t){return Ks(4,4,e,t)}function w2(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function x2(e,t,n){return n=n!=null?n.concat([e]):null,Ks(4,4,w2.bind(null,t,e),n)}function Oc(){}function S2(e,t){var n=De();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Pc(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function T2(e,t){var n=De();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Pc(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function b2(e,t,n){return Er&21?(Fe(n,t)||(n=D9(),lt.lanes|=n,kr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Qt=!0),e.memoizedState=n)}function v3(e,t){var n=Y;Y=n!==0&&4>n?n:4,e(!0);var r=$a.transition;$a.transition={};try{e(!1),t()}finally{Y=n,$a.transition=r}}function C2(){return De().memoizedState}function y3(e,t,n){var r=Bn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},E2(e))k2(t,n);else if(n=a2(e,t,n,r),n!==null){var i=jt();qe(n,e,r,i),P2(n,t,r)}}function _3(e,t,n){var r=Bn(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(E2(e))k2(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,a=o(s,n);if(i.hasEagerState=!0,i.eagerState=a,Fe(a,s)){var l=t.interleaved;l===null?(i.next=i,Tc(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=a2(e,t,i,r),n!==null&&(i=jt(),qe(n,e,r,i),P2(n,t,r))}}function E2(e){var t=e.alternate;return e===lt||t!==null&&t===lt}function k2(e,t){ai=vs=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function P2(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,uc(e,n)}}var ys={readContext:Pe,useCallback:Lt,useContext:Lt,useEffect:Lt,useImperativeHandle:Lt,useInsertionEffect:Lt,useLayoutEffect:Lt,useMemo:Lt,useReducer:Lt,useRef:Lt,useState:Lt,useDebugValue:Lt,useDeferredValue:Lt,useTransition:Lt,useMutableSource:Lt,useSyncExternalStore:Lt,useId:Lt,unstable_isNewReconciler:!1},w3={readContext:Pe,useCallback:function(e,t){return ze().memoizedState=[e,t===void 0?null:t],e},useContext:Pe,useEffect:qf,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,jo(4194308,4,w2.bind(null,t,e),n)},useLayoutEffect:function(e,t){return jo(4194308,4,e,t)},useInsertionEffect:function(e,t){return jo(4,2,e,t)},useMemo:function(e,t){var n=ze();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ze();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=y3.bind(null,lt,e),[r.memoizedState,e]},useRef:function(e){var t=ze();return e={current:e},t.memoizedState=e},useState:Vf,useDebugValue:Oc,useDeferredValue:function(e){return ze().memoizedState=e},useTransition:function(){var e=Vf(!1),t=e[0];return e=v3.bind(null,e[1]),ze().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=lt,i=ze();if(rt){if(n===void 0)throw Error(k(407));n=n()}else{if(n=t(),bt===null)throw Error(k(349));Er&30||d2(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,qf(p2.bind(null,r,o,e),[e]),r.flags|=2048,Ii(9,h2.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=ze(),t=bt.identifierPrefix;if(rt){var n=un,r=ln;n=(r&~(1<<32-Ve(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Ni++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=g3++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},x3={readContext:Pe,useCallback:S2,useContext:Pe,useEffect:Lc,useImperativeHandle:x2,useInsertionEffect:y2,useLayoutEffect:_2,useMemo:T2,useReducer:Ha,useRef:v2,useState:function(){return Ha(Ri)},useDebugValue:Oc,useDeferredValue:function(e){var t=De();return b2(t,_t.memoizedState,e)},useTransition:function(){var e=Ha(Ri)[0],t=De().memoizedState;return[e,t]},useMutableSource:c2,useSyncExternalStore:f2,useId:C2,unstable_isNewReconciler:!1},S3={readContext:Pe,useCallback:S2,useContext:Pe,useEffect:Lc,useImperativeHandle:x2,useInsertionEffect:y2,useLayoutEffect:_2,useMemo:T2,useReducer:Ga,useRef:v2,useState:function(){return Ga(Ri)},useDebugValue:Oc,useDeferredValue:function(e){var t=De();return _t===null?t.memoizedState=e:b2(t,_t.memoizedState,e)},useTransition:function(){var e=Ga(Ri)[0],t=De().memoizedState;return[e,t]},useMutableSource:c2,useSyncExternalStore:f2,useId:C2,unstable_isNewReconciler:!1};function Ne(e,t){if(e&&e.defaultProps){t=ut({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Kl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ut({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Zs={isMounted:function(e){return(e=e._reactInternals)?Or(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=jt(),i=Bn(e),o=cn(r,i);o.payload=t,n!=null&&(o.callback=n),t=qn(e,o,i),t!==null&&(qe(t,e,i,r),Bo(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=jt(),i=Bn(e),o=cn(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=qn(e,o,i),t!==null&&(qe(t,e,i,r),Bo(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=jt(),r=Bn(e),i=cn(n,r);i.tag=2,t!=null&&(i.callback=t),t=qn(e,i,r),t!==null&&(qe(t,e,r,n),Bo(t,e,r))}};function Ff(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!Pi(n,r)||!Pi(i,o):!0}function D2(e,t,n){var r=!1,i=Gn,o=t.contextType;return typeof o=="object"&&o!==null?o=Pe(o):(i=Zt(t)?br:Vt.current,r=t.contextTypes,o=(r=r!=null)?y1(e,i):Gn),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Zs,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function Bf(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Zs.enqueueReplaceState(t,t.state,null)}function Zl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},bc(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=Pe(o):(o=Zt(t)?br:Vt.current,i.context=y1(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Kl(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Zs.enqueueReplaceState(i,i.state,null),ms(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function S1(e,t){try{var n="",r=t;do n+=Qp(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function Wa(e,t,n){return{value:e,source:null,stack:n!=null?n:null,digest:t!=null?t:null}}function Jl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var T3=typeof WeakMap=="function"?WeakMap:Map;function A2(e,t,n){n=cn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ws||(ws=!0,uu=r),Jl(e,t)},n}function L2(e,t,n){n=cn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Jl(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Jl(e,t),typeof r!="function"&&(Fn===null?Fn=new Set([this]):Fn.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function zf(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new T3;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=V3.bind(null,e,t,n),t.then(e,e))}function jf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Uf(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=cn(-1,1),t.tag=2,qn(n,t,1))),n.lanes|=1),e)}var b3=_n.ReactCurrentOwner,Qt=!1;function Ft(e,t,n,r){t.child=e===null?s2(t,null,n,r):w1(t,e.child,n,r)}function $f(e,t,n,r,i){n=n.render;var o=t.ref;return c1(t,i),r=Dc(e,t,n,r,o,i),n=Ac(),e!==null&&!Qt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gn(e,t,i)):(rt&&n&&vc(t),t.flags|=1,Ft(e,t,r,i),t.child)}function Hf(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!Bc(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,O2(e,t,o,r,i)):(e=Go(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:Pi,n(s,r)&&e.ref===t.ref)return gn(e,t,i)}return t.flags|=1,e=zn(o,r),e.ref=t.ref,e.return=t,t.child=e}function O2(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(Pi(o,r)&&e.ref===t.ref)if(Qt=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Qt=!0);else return t.lanes=e.lanes,gn(e,t,i)}return tu(e,t,n,r,i)}function M2(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},tt(Jr,oe),oe|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,tt(Jr,oe),oe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,tt(Jr,oe),oe|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,tt(Jr,oe),oe|=r;return Ft(e,t,i,n),t.child}function N2(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function tu(e,t,n,r,i){var o=Zt(n)?br:Vt.current;return o=y1(t,o),c1(t,i),n=Dc(e,t,n,r,o,i),r=Ac(),e!==null&&!Qt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gn(e,t,i)):(rt&&r&&vc(t),t.flags|=1,Ft(e,t,n,i),t.child)}function Gf(e,t,n,r,i){if(Zt(n)){var o=!0;cs(t)}else o=!1;if(c1(t,i),t.stateNode===null)Uo(e,t),D2(t,n,r),Zl(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=Pe(u):(u=Zt(n)?br:Vt.current,u=y1(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&Bf(t,s,r,u),bn=!1;var d=t.memoizedState;s.state=d,ms(t,r,s,i),l=t.memoizedState,a!==r||d!==l||Kt.current||bn?(typeof c=="function"&&(Kl(t,n,c,r),l=t.memoizedState),(a=bn||Ff(t,n,a,r,d,l,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,l2(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:Ne(t.type,a),s.props=u,f=t.pendingProps,d=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=Pe(l):(l=Zt(n)?br:Vt.current,l=y1(t,l));var p=n.getDerivedStateFromProps;(c=typeof p=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==f||d!==l)&&Bf(t,s,r,l),bn=!1,d=t.memoizedState,s.state=d,ms(t,r,s,i);var v=t.memoizedState;a!==f||d!==v||Kt.current||bn?(typeof p=="function"&&(Kl(t,n,p,r),v=t.memoizedState),(u=bn||Ff(t,n,u,r,d,v,l)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,v,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,v,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),s.props=r,s.state=v,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return eu(e,t,n,r,o,i)}function eu(e,t,n,r,i,o){N2(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&Lf(t,n,!1),gn(e,t,o);r=t.stateNode,b3.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=w1(t,e.child,null,o),t.child=w1(t,null,a,o)):Ft(e,t,a,o),t.memoizedState=r.state,i&&Lf(t,n,!0),t.child}function R2(e){var t=e.stateNode;t.pendingContext?Af(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Af(e,t.context,!1),Cc(e,t.containerInfo)}function Wf(e,t,n,r,i){return _1(),_c(i),t.flags|=256,Ft(e,t,n,r),t.child}var nu={dehydrated:null,treeContext:null,retryLane:0};function ru(e){return{baseLanes:e,cachePool:null,transitions:null}}function I2(e,t,n){var r=t.pendingProps,i=ot.current,o=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),tt(ot,i&1),e===null)return Xl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=ea(s,r,0,null),e=_r(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=ru(n),t.memoizedState=nu,e):Mc(t,s));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return C3(e,t,s,r,a,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=zn(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?o=zn(a,o):(o=_r(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?ru(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=nu,r}return o=e.child,e=o.sibling,r=zn(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Mc(e,t){return t=ea({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function bo(e,t,n,r){return r!==null&&_c(r),w1(t,e.child,null,n),e=Mc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function C3(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=Wa(Error(k(422))),bo(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=ea({mode:"visible",children:r.children},i,0,null),o=_r(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&w1(t,e.child,null,s),t.child.memoizedState=ru(s),t.memoizedState=nu,o);if(!(t.mode&1))return bo(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,o=Error(k(419)),r=Wa(o,r,void 0),bo(e,t,s,r)}if(a=(s&e.childLanes)!==0,Qt||a){if(r=bt,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,mn(e,i),qe(r,e,i,-1))}return Fc(),r=Wa(Error(k(421))),bo(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=q3.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,fe=Vn(i.nextSibling),he=t,rt=!0,Ie=null,e!==null&&(Se[Te++]=ln,Se[Te++]=un,Se[Te++]=Cr,ln=e.id,un=e.overflow,Cr=t),t=Mc(t,r.children),t.flags|=4096,t)}function Yf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ql(e.return,t,n)}function Ya(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function V2(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(Ft(e,t,r.children,n),r=ot.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)t:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Yf(e,n,t);else if(e.tag===19)Yf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(tt(ot,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&gs(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Ya(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&gs(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Ya(t,!0,n,null,o);break;case"together":Ya(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Uo(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function gn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),kr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(k(153));if(t.child!==null){for(e=t.child,n=zn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=zn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function E3(e,t,n){switch(t.tag){case 3:R2(t),_1();break;case 5:u2(t);break;case 1:Zt(t.type)&&cs(t);break;case 4:Cc(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;tt(hs,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(tt(ot,ot.current&1),t.flags|=128,null):n&t.child.childLanes?I2(e,t,n):(tt(ot,ot.current&1),e=gn(e,t,n),e!==null?e.sibling:null);tt(ot,ot.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return V2(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),tt(ot,ot.current),r)break;return null;case 22:case 23:return t.lanes=0,M2(e,t,n)}return gn(e,t,n)}var q2,iu,F2,B2;q2=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};iu=function(){};F2=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,pr(Ye.current);var o=null;switch(n){case"input":i=El(e,i),r=El(e,r),o=[];break;case"select":i=ut({},i,{value:void 0}),r=ut({},r,{value:void 0}),o=[];break;case"textarea":i=Dl(e,i),r=Dl(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ls)}Ll(n,r);var s;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var a=i[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(xi.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var l=r[u];if(a=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(o||(o=[]),o.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(o=o||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(o=o||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(xi.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&et("scroll",e),o||a===l||(o=[])):(o=o||[]).push(u,l))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};B2=function(e,t,n,r){n!==r&&(t.flags|=4)};function j1(e,t){if(!rt)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ot(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function k3(e,t,n){var r=t.pendingProps;switch(yc(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ot(t),null;case 1:return Zt(t.type)&&us(),Ot(t),null;case 3:return r=t.stateNode,x1(),nt(Kt),nt(Vt),kc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(So(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ie!==null&&(du(Ie),Ie=null))),iu(e,t),Ot(t),null;case 5:Ec(t);var i=pr(Mi.current);if(n=t.type,e!==null&&t.stateNode!=null)F2(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(k(166));return Ot(t),null}if(e=pr(Ye.current),So(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[$e]=t,r[Li]=o,e=(t.mode&1)!==0,n){case"dialog":et("cancel",r),et("close",r);break;case"iframe":case"object":case"embed":et("load",r);break;case"video":case"audio":for(i=0;i<K1.length;i++)et(K1[i],r);break;case"source":et("error",r);break;case"img":case"image":case"link":et("error",r),et("load",r);break;case"details":et("toggle",r);break;case"input":nf(r,o),et("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},et("invalid",r);break;case"textarea":of(r,o),et("invalid",r)}Ll(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var a=o[s];s==="children"?typeof a=="string"?r.textContent!==a&&(o.suppressHydrationWarning!==!0&&xo(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&xo(r.textContent,a,e),i=["children",""+a]):xi.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&et("scroll",r)}switch(n){case"input":ho(r),rf(r,o,!0);break;case"textarea":ho(r),sf(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=ls)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=p9(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[$e]=t,e[Li]=r,q2(e,t,!1,!1),t.stateNode=e;t:{switch(s=Ol(n,r),n){case"dialog":et("cancel",e),et("close",e),i=r;break;case"iframe":case"object":case"embed":et("load",e),i=r;break;case"video":case"audio":for(i=0;i<K1.length;i++)et(K1[i],e);i=r;break;case"source":et("error",e),i=r;break;case"img":case"image":case"link":et("error",e),et("load",e),i=r;break;case"details":et("toggle",e),i=r;break;case"input":nf(e,r),i=El(e,r),et("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=ut({},r,{value:void 0}),et("invalid",e);break;case"textarea":of(e,r),i=Dl(e,r),et("invalid",e);break;default:i=r}Ll(n,i),a=i;for(o in a)if(a.hasOwnProperty(o)){var l=a[o];o==="style"?v9(e,l):o==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&m9(e,l)):o==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Si(e,l):typeof l=="number"&&Si(e,""+l):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(xi.hasOwnProperty(o)?l!=null&&o==="onScroll"&&et("scroll",e):l!=null&&rc(e,o,l,s))}switch(n){case"input":ho(e),rf(e,r,!1);break;case"textarea":ho(e),sf(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Hn(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?s1(e,!!r.multiple,o,!1):r.defaultValue!=null&&s1(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=ls)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break t;case"img":r=!0;break t;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ot(t),null;case 6:if(e&&t.stateNode!=null)B2(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(k(166));if(n=pr(Mi.current),pr(Ye.current),So(t)){if(r=t.stateNode,n=t.memoizedProps,r[$e]=t,(o=r.nodeValue!==n)&&(e=he,e!==null))switch(e.tag){case 3:xo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&xo(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[$e]=t,t.stateNode=r}return Ot(t),null;case 13:if(nt(ot),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(rt&&fe!==null&&t.mode&1&&!(t.flags&128))i2(),_1(),t.flags|=98560,o=!1;else if(o=So(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(k(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(k(317));o[$e]=t}else _1(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ot(t),o=!1}else Ie!==null&&(du(Ie),Ie=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ot.current&1?wt===0&&(wt=3):Fc())),t.updateQueue!==null&&(t.flags|=4),Ot(t),null);case 4:return x1(),iu(e,t),e===null&&Di(t.stateNode.containerInfo),Ot(t),null;case 10:return Sc(t.type._context),Ot(t),null;case 17:return Zt(t.type)&&us(),Ot(t),null;case 19:if(nt(ot),o=t.memoizedState,o===null)return Ot(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)j1(o,!1);else{if(wt!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=gs(e),s!==null){for(t.flags|=128,j1(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return tt(ot,ot.current&1|2),t.child}e=e.sibling}o.tail!==null&&mt()>T1&&(t.flags|=128,r=!0,j1(o,!1),t.lanes=4194304)}else{if(!r)if(e=gs(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),j1(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!rt)return Ot(t),null}else 2*mt()-o.renderingStartTime>T1&&n!==1073741824&&(t.flags|=128,r=!0,j1(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=mt(),t.sibling=null,n=ot.current,tt(ot,r?n&1|2:n&1),t):(Ot(t),null);case 22:case 23:return qc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?oe&1073741824&&(Ot(t),t.subtreeFlags&6&&(t.flags|=8192)):Ot(t),null;case 24:return null;case 25:return null}throw Error(k(156,t.tag))}function P3(e,t){switch(yc(t),t.tag){case 1:return Zt(t.type)&&us(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return x1(),nt(Kt),nt(Vt),kc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ec(t),null;case 13:if(nt(ot),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(k(340));_1()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return nt(ot),null;case 4:return x1(),null;case 10:return Sc(t.type._context),null;case 22:case 23:return qc(),null;case 24:return null;default:return null}}var Co=!1,Mt=!1,D3=typeof WeakSet=="function"?WeakSet:Set,A=null;function Zr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ct(e,t,r)}else n.current=null}function ou(e,t,n){try{n()}catch(r){ct(e,t,r)}}var Xf=!1;function A3(e,t){if(jl=os,e=H9(),gc(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else t:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break t}var s=0,a=-1,l=-1,u=0,c=0,f=e,d=null;e:for(;;){for(var p;f!==n||i!==0&&f.nodeType!==3||(a=s+i),f!==o||r!==0&&f.nodeType!==3||(l=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(p=f.firstChild)!==null;)d=f,f=p;for(;;){if(f===e)break e;if(d===n&&++u===i&&(a=s),d===o&&++c===r&&(l=s),(p=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=p}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ul={focusedElem:e,selectionRange:n},os=!1,A=t;A!==null;)if(t=A,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,A=e;else for(;A!==null;){t=A;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var h=v.memoizedProps,_=v.memoizedState,m=t.stateNode,g=m.getSnapshotBeforeUpdate(t.elementType===t.type?h:Ne(t.type,h),_);m.__reactInternalSnapshotBeforeUpdate=g}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(k(163))}}catch(w){ct(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,A=e;break}A=t.return}return v=Xf,Xf=!1,v}function li(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&ou(t,n,o)}i=i.next}while(i!==r)}}function Js(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function su(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function z2(e){var t=e.alternate;t!==null&&(e.alternate=null,z2(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[$e],delete t[Li],delete t[Gl],delete t[d3],delete t[h3])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function j2(e){return e.tag===5||e.tag===3||e.tag===4}function Qf(e){t:for(;;){for(;e.sibling===null;){if(e.return===null||j2(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue t;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function au(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ls));else if(r!==4&&(e=e.child,e!==null))for(au(e,t,n),e=e.sibling;e!==null;)au(e,t,n),e=e.sibling}function lu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(lu(e,t,n),e=e.sibling;e!==null;)lu(e,t,n),e=e.sibling}var kt=null,Re=!1;function xn(e,t,n){for(n=n.child;n!==null;)U2(e,t,n),n=n.sibling}function U2(e,t,n){if(We&&typeof We.onCommitFiberUnmount=="function")try{We.onCommitFiberUnmount(Hs,n)}catch{}switch(n.tag){case 5:Mt||Zr(n,t);case 6:var r=kt,i=Re;kt=null,xn(e,t,n),kt=r,Re=i,kt!==null&&(Re?(e=kt,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):kt.removeChild(n.stateNode));break;case 18:kt!==null&&(Re?(e=kt,n=n.stateNode,e.nodeType===8?za(e.parentNode,n):e.nodeType===1&&za(e,n),Ei(e)):za(kt,n.stateNode));break;case 4:r=kt,i=Re,kt=n.stateNode.containerInfo,Re=!0,xn(e,t,n),kt=r,Re=i;break;case 0:case 11:case 14:case 15:if(!Mt&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&ou(n,t,s),i=i.next}while(i!==r)}xn(e,t,n);break;case 1:if(!Mt&&(Zr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ct(n,t,a)}xn(e,t,n);break;case 21:xn(e,t,n);break;case 22:n.mode&1?(Mt=(r=Mt)||n.memoizedState!==null,xn(e,t,n),Mt=r):xn(e,t,n);break;default:xn(e,t,n)}}function Kf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new D3),t.forEach(function(r){var i=F3.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Oe(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,a=s;t:for(;a!==null;){switch(a.tag){case 5:kt=a.stateNode,Re=!1;break t;case 3:kt=a.stateNode.containerInfo,Re=!0;break t;case 4:kt=a.stateNode.containerInfo,Re=!0;break t}a=a.return}if(kt===null)throw Error(k(160));U2(o,s,i),kt=null,Re=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){ct(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)$2(t,e),t=t.sibling}function $2(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Oe(t,e),Be(e),r&4){try{li(3,e,e.return),Js(3,e)}catch(h){ct(e,e.return,h)}try{li(5,e,e.return)}catch(h){ct(e,e.return,h)}}break;case 1:Oe(t,e),Be(e),r&512&&n!==null&&Zr(n,n.return);break;case 5:if(Oe(t,e),Be(e),r&512&&n!==null&&Zr(n,n.return),e.flags&32){var i=e.stateNode;try{Si(i,"")}catch(h){ct(e,e.return,h)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&d9(i,o),Ol(a,s);var u=Ol(a,o);for(s=0;s<l.length;s+=2){var c=l[s],f=l[s+1];c==="style"?v9(i,f):c==="dangerouslySetInnerHTML"?m9(i,f):c==="children"?Si(i,f):rc(i,c,f,u)}switch(a){case"input":kl(i,o);break;case"textarea":h9(i,o);break;case"select":var d=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var p=o.value;p!=null?s1(i,!!o.multiple,p,!1):d!==!!o.multiple&&(o.defaultValue!=null?s1(i,!!o.multiple,o.defaultValue,!0):s1(i,!!o.multiple,o.multiple?[]:"",!1))}i[Li]=o}catch(h){ct(e,e.return,h)}}break;case 6:if(Oe(t,e),Be(e),r&4){if(e.stateNode===null)throw Error(k(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(h){ct(e,e.return,h)}}break;case 3:if(Oe(t,e),Be(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Ei(t.containerInfo)}catch(h){ct(e,e.return,h)}break;case 4:Oe(t,e),Be(e);break;case 13:Oe(t,e),Be(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(Ic=mt())),r&4&&Kf(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(Mt=(u=Mt)||c,Oe(t,e),Mt=u):Oe(t,e),Be(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(A=e,c=e.child;c!==null;){for(f=A=c;A!==null;){switch(d=A,p=d.child,d.tag){case 0:case 11:case 14:case 15:li(4,d,d.return);break;case 1:Zr(d,d.return);var v=d.stateNode;if(typeof v.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(h){ct(r,n,h)}}break;case 5:Zr(d,d.return);break;case 22:if(d.memoizedState!==null){Jf(f);continue}}p!==null?(p.return=d,A=p):Jf(f)}c=c.sibling}t:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{i=f.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=f.stateNode,l=f.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=g9("display",s))}catch(h){ct(e,e.return,h)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(h){ct(e,e.return,h)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break t;for(;f.sibling===null;){if(f.return===null||f.return===e)break t;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Oe(t,e),Be(e),r&4&&Kf(e);break;case 21:break;default:Oe(t,e),Be(e)}}function Be(e){var t=e.flags;if(t&2){try{t:{for(var n=e.return;n!==null;){if(j2(n)){var r=n;break t}n=n.return}throw Error(k(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Si(i,""),r.flags&=-33);var o=Qf(e);lu(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,a=Qf(e);au(e,a,s);break;default:throw Error(k(161))}}catch(l){ct(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function L3(e,t,n){A=e,H2(e)}function H2(e,t,n){for(var r=(e.mode&1)!==0;A!==null;){var i=A,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||Co;if(!s){var a=i.alternate,l=a!==null&&a.memoizedState!==null||Mt;a=Co;var u=Mt;if(Co=s,(Mt=l)&&!u)for(A=i;A!==null;)s=A,l=s.child,s.tag===22&&s.memoizedState!==null?td(i):l!==null?(l.return=s,A=l):td(i);for(;o!==null;)A=o,H2(o),o=o.sibling;A=i,Co=a,Mt=u}Zf(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,A=o):Zf(e)}}function Zf(e){for(;A!==null;){var t=A;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Mt||Js(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Mt)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ne(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&If(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}If(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&Ei(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(k(163))}Mt||t.flags&512&&su(t)}catch(d){ct(t,t.return,d)}}if(t===e){A=null;break}if(n=t.sibling,n!==null){n.return=t.return,A=n;break}A=t.return}}function Jf(e){for(;A!==null;){var t=A;if(t===e){A=null;break}var n=t.sibling;if(n!==null){n.return=t.return,A=n;break}A=t.return}}function td(e){for(;A!==null;){var t=A;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Js(4,t)}catch(l){ct(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){ct(t,i,l)}}var o=t.return;try{su(t)}catch(l){ct(t,o,l)}break;case 5:var s=t.return;try{su(t)}catch(l){ct(t,s,l)}}}catch(l){ct(t,t.return,l)}if(t===e){A=null;break}var a=t.sibling;if(a!==null){a.return=t.return,A=a;break}A=t.return}}var O3=Math.ceil,_s=_n.ReactCurrentDispatcher,Nc=_n.ReactCurrentOwner,ke=_n.ReactCurrentBatchConfig,W=0,bt=null,vt=null,Dt=0,oe=0,Jr=Zn(0),wt=0,Vi=null,kr=0,ta=0,Rc=0,ui=null,Yt=null,Ic=0,T1=1/0,rn=null,ws=!1,uu=null,Fn=null,Eo=!1,Dn=null,xs=0,ci=0,cu=null,$o=-1,Ho=0;function jt(){return W&6?mt():$o!==-1?$o:$o=mt()}function Bn(e){return e.mode&1?W&2&&Dt!==0?Dt&-Dt:m3.transition!==null?(Ho===0&&(Ho=D9()),Ho):(e=Y,e!==0||(e=window.event,e=e===void 0?16:I9(e.type)),e):1}function qe(e,t,n,r){if(50<ci)throw ci=0,cu=null,Error(k(185));Zi(e,n,r),(!(W&2)||e!==bt)&&(e===bt&&(!(W&2)&&(ta|=n),wt===4&&kn(e,Dt)),Jt(e,r),n===1&&W===0&&!(t.mode&1)&&(T1=mt()+500,Qs&&Jn()))}function Jt(e,t){var n=e.callbackNode;m7(e,t);var r=is(e,e===bt?Dt:0);if(r===0)n!==null&&uf(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&uf(n),t===1)e.tag===0?p3(ed.bind(null,e)):e2(ed.bind(null,e)),c3(function(){!(W&6)&&Jn()}),n=null;else{switch(A9(r)){case 1:n=lc;break;case 4:n=k9;break;case 16:n=rs;break;case 536870912:n=P9;break;default:n=rs}n=J2(n,G2.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function G2(e,t){if($o=-1,Ho=0,W&6)throw Error(k(327));var n=e.callbackNode;if(f1()&&e.callbackNode!==n)return null;var r=is(e,e===bt?Dt:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ss(e,r);else{t=r;var i=W;W|=2;var o=Y2();(bt!==e||Dt!==t)&&(rn=null,T1=mt()+500,yr(e,t));do try{R3();break}catch(a){W2(e,a)}while(!0);xc(),_s.current=o,W=i,vt!==null?t=0:(bt=null,Dt=0,t=wt)}if(t!==0){if(t===2&&(i=Vl(e),i!==0&&(r=i,t=fu(e,i))),t===1)throw n=Vi,yr(e,0),kn(e,r),Jt(e,mt()),n;if(t===6)kn(e,r);else{if(i=e.current.alternate,!(r&30)&&!M3(i)&&(t=Ss(e,r),t===2&&(o=Vl(e),o!==0&&(r=o,t=fu(e,o))),t===1))throw n=Vi,yr(e,0),kn(e,r),Jt(e,mt()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(k(345));case 2:lr(e,Yt,rn);break;case 3:if(kn(e,r),(r&130023424)===r&&(t=Ic+500-mt(),10<t)){if(is(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){jt(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Hl(lr.bind(null,e,Yt,rn),t);break}lr(e,Yt,rn);break;case 4:if(kn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Ve(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=mt()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*O3(r/1960))-r,10<r){e.timeoutHandle=Hl(lr.bind(null,e,Yt,rn),r);break}lr(e,Yt,rn);break;case 5:lr(e,Yt,rn);break;default:throw Error(k(329))}}}return Jt(e,mt()),e.callbackNode===n?G2.bind(null,e):null}function fu(e,t){var n=ui;return e.current.memoizedState.isDehydrated&&(yr(e,t).flags|=256),e=Ss(e,t),e!==2&&(t=Yt,Yt=n,t!==null&&du(t)),e}function du(e){Yt===null?Yt=e:Yt.push.apply(Yt,e)}function M3(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!Fe(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function kn(e,t){for(t&=~Rc,t&=~ta,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ve(t),r=1<<n;e[n]=-1,t&=~r}}function ed(e){if(W&6)throw Error(k(327));f1();var t=is(e,0);if(!(t&1))return Jt(e,mt()),null;var n=Ss(e,t);if(e.tag!==0&&n===2){var r=Vl(e);r!==0&&(t=r,n=fu(e,r))}if(n===1)throw n=Vi,yr(e,0),kn(e,t),Jt(e,mt()),n;if(n===6)throw Error(k(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,lr(e,Yt,rn),Jt(e,mt()),null}function Vc(e,t){var n=W;W|=1;try{return e(t)}finally{W=n,W===0&&(T1=mt()+500,Qs&&Jn())}}function Pr(e){Dn!==null&&Dn.tag===0&&!(W&6)&&f1();var t=W;W|=1;var n=ke.transition,r=Y;try{if(ke.transition=null,Y=1,e)return e()}finally{Y=r,ke.transition=n,W=t,!(W&6)&&Jn()}}function qc(){oe=Jr.current,nt(Jr)}function yr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,u3(n)),vt!==null)for(n=vt.return;n!==null;){var r=n;switch(yc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&us();break;case 3:x1(),nt(Kt),nt(Vt),kc();break;case 5:Ec(r);break;case 4:x1();break;case 13:nt(ot);break;case 19:nt(ot);break;case 10:Sc(r.type._context);break;case 22:case 23:qc()}n=n.return}if(bt=e,vt=e=zn(e.current,null),Dt=oe=t,wt=0,Vi=null,Rc=ta=kr=0,Yt=ui=null,hr!==null){for(t=0;t<hr.length;t++)if(n=hr[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}hr=null}return e}function W2(e,t){do{var n=vt;try{if(xc(),zo.current=ys,vs){for(var r=lt.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}vs=!1}if(Er=0,xt=_t=lt=null,ai=!1,Ni=0,Nc.current=null,n===null||n.return===null){wt=1,Vi=t,vt=null;break}t:{var o=e,s=n.return,a=n,l=t;if(t=Dt,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var p=jf(s);if(p!==null){p.flags&=-257,Uf(p,s,a,o,t),p.mode&1&&zf(o,u,t),t=p,l=u;var v=t.updateQueue;if(v===null){var h=new Set;h.add(l),t.updateQueue=h}else v.add(l);break t}else{if(!(t&1)){zf(o,u,t),Fc();break t}l=Error(k(426))}}else if(rt&&a.mode&1){var _=jf(s);if(_!==null){!(_.flags&65536)&&(_.flags|=256),Uf(_,s,a,o,t),_c(S1(l,a));break t}}o=l=S1(l,a),wt!==4&&(wt=2),ui===null?ui=[o]:ui.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=A2(o,l,t);Rf(o,m);break t;case 1:a=l;var g=o.type,y=o.stateNode;if(!(o.flags&128)&&(typeof g.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(Fn===null||!Fn.has(y)))){o.flags|=65536,t&=-t,o.lanes|=t;var w=L2(o,a,t);Rf(o,w);break t}}o=o.return}while(o!==null)}Q2(n)}catch(x){t=x,vt===n&&n!==null&&(vt=n=n.return);continue}break}while(!0)}function Y2(){var e=_s.current;return _s.current=ys,e===null?ys:e}function Fc(){(wt===0||wt===3||wt===2)&&(wt=4),bt===null||!(kr&268435455)&&!(ta&268435455)||kn(bt,Dt)}function Ss(e,t){var n=W;W|=2;var r=Y2();(bt!==e||Dt!==t)&&(rn=null,yr(e,t));do try{N3();break}catch(i){W2(e,i)}while(!0);if(xc(),W=n,_s.current=r,vt!==null)throw Error(k(261));return bt=null,Dt=0,wt}function N3(){for(;vt!==null;)X2(vt)}function R3(){for(;vt!==null&&!s7();)X2(vt)}function X2(e){var t=Z2(e.alternate,e,oe);e.memoizedProps=e.pendingProps,t===null?Q2(e):vt=t,Nc.current=null}function Q2(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=P3(n,t),n!==null){n.flags&=32767,vt=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{wt=6,vt=null;return}}else if(n=k3(n,t,oe),n!==null){vt=n;return}if(t=t.sibling,t!==null){vt=t;return}vt=t=e}while(t!==null);wt===0&&(wt=5)}function lr(e,t,n){var r=Y,i=ke.transition;try{ke.transition=null,Y=1,I3(e,t,n,r)}finally{ke.transition=i,Y=r}return null}function I3(e,t,n,r){do f1();while(Dn!==null);if(W&6)throw Error(k(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(k(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(g7(e,o),e===bt&&(vt=bt=null,Dt=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Eo||(Eo=!0,J2(rs,function(){return f1(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=ke.transition,ke.transition=null;var s=Y;Y=1;var a=W;W|=4,Nc.current=null,A3(e,n),$2(n,e),n3(Ul),os=!!jl,Ul=jl=null,e.current=n,L3(n),a7(),W=a,Y=s,ke.transition=o}else e.current=n;if(Eo&&(Eo=!1,Dn=e,xs=i),o=e.pendingLanes,o===0&&(Fn=null),c7(n.stateNode),Jt(e,mt()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(ws)throw ws=!1,e=uu,uu=null,e;return xs&1&&e.tag!==0&&f1(),o=e.pendingLanes,o&1?e===cu?ci++:(ci=0,cu=e):ci=0,Jn(),null}function f1(){if(Dn!==null){var e=A9(xs),t=ke.transition,n=Y;try{if(ke.transition=null,Y=16>e?16:e,Dn===null)var r=!1;else{if(e=Dn,Dn=null,xs=0,W&6)throw Error(k(331));var i=W;for(W|=4,A=e.current;A!==null;){var o=A,s=o.child;if(A.flags&16){var a=o.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(A=u;A!==null;){var c=A;switch(c.tag){case 0:case 11:case 15:li(8,c,o)}var f=c.child;if(f!==null)f.return=c,A=f;else for(;A!==null;){c=A;var d=c.sibling,p=c.return;if(z2(c),c===u){A=null;break}if(d!==null){d.return=p,A=d;break}A=p}}}var v=o.alternate;if(v!==null){var h=v.child;if(h!==null){v.child=null;do{var _=h.sibling;h.sibling=null,h=_}while(h!==null)}}A=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,A=s;else t:for(;A!==null;){if(o=A,o.flags&2048)switch(o.tag){case 0:case 11:case 15:li(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,A=m;break t}A=o.return}}var g=e.current;for(A=g;A!==null;){s=A;var y=s.child;if(s.subtreeFlags&2064&&y!==null)y.return=s,A=y;else t:for(s=g;A!==null;){if(a=A,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Js(9,a)}}catch(x){ct(a,a.return,x)}if(a===s){A=null;break t}var w=a.sibling;if(w!==null){w.return=a.return,A=w;break t}A=a.return}}if(W=i,Jn(),We&&typeof We.onPostCommitFiberRoot=="function")try{We.onPostCommitFiberRoot(Hs,e)}catch{}r=!0}return r}finally{Y=n,ke.transition=t}}return!1}function nd(e,t,n){t=S1(n,t),t=A2(e,t,1),e=qn(e,t,1),t=jt(),e!==null&&(Zi(e,1,t),Jt(e,t))}function ct(e,t,n){if(e.tag===3)nd(e,e,n);else for(;t!==null;){if(t.tag===3){nd(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Fn===null||!Fn.has(r))){e=S1(n,e),e=L2(t,e,1),t=qn(t,e,1),e=jt(),t!==null&&(Zi(t,1,e),Jt(t,e));break}}t=t.return}}function V3(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=jt(),e.pingedLanes|=e.suspendedLanes&n,bt===e&&(Dt&n)===n&&(wt===4||wt===3&&(Dt&130023424)===Dt&&500>mt()-Ic?yr(e,0):Rc|=n),Jt(e,t)}function K2(e,t){t===0&&(e.mode&1?(t=go,go<<=1,!(go&130023424)&&(go=4194304)):t=1);var n=jt();e=mn(e,t),e!==null&&(Zi(e,t,n),Jt(e,n))}function q3(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),K2(e,n)}function F3(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(k(314))}r!==null&&r.delete(t),K2(e,n)}var Z2;Z2=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Kt.current)Qt=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Qt=!1,E3(e,t,n);Qt=!!(e.flags&131072)}else Qt=!1,rt&&t.flags&1048576&&n2(t,ds,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Uo(e,t),e=t.pendingProps;var i=y1(t,Vt.current);c1(t,n),i=Dc(null,t,r,e,i,n);var o=Ac();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Zt(r)?(o=!0,cs(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,bc(t),i.updater=Zs,t.stateNode=i,i._reactInternals=t,Zl(t,r,e,n),t=eu(null,t,r,!0,o,n)):(t.tag=0,rt&&o&&vc(t),Ft(null,t,i,n),t=t.child),t;case 16:r=t.elementType;t:{switch(Uo(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=z3(r),e=Ne(r,e),i){case 0:t=tu(null,t,r,e,n);break t;case 1:t=Gf(null,t,r,e,n);break t;case 11:t=$f(null,t,r,e,n);break t;case 14:t=Hf(null,t,r,Ne(r.type,e),n);break t}throw Error(k(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ne(r,i),tu(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ne(r,i),Gf(e,t,r,i,n);case 3:t:{if(R2(t),e===null)throw Error(k(387));r=t.pendingProps,o=t.memoizedState,i=o.element,l2(e,t),ms(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=S1(Error(k(423)),t),t=Wf(e,t,r,n,i);break t}else if(r!==i){i=S1(Error(k(424)),t),t=Wf(e,t,r,n,i);break t}else for(fe=Vn(t.stateNode.containerInfo.firstChild),he=t,rt=!0,Ie=null,n=s2(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(_1(),r===i){t=gn(e,t,n);break t}Ft(e,t,r,n)}t=t.child}return t;case 5:return u2(t),e===null&&Xl(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,$l(r,i)?s=null:o!==null&&$l(r,o)&&(t.flags|=32),N2(e,t),Ft(e,t,s,n),t.child;case 6:return e===null&&Xl(t),null;case 13:return I2(e,t,n);case 4:return Cc(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=w1(t,null,r,n):Ft(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ne(r,i),$f(e,t,r,i,n);case 7:return Ft(e,t,t.pendingProps,n),t.child;case 8:return Ft(e,t,t.pendingProps.children,n),t.child;case 12:return Ft(e,t,t.pendingProps.children,n),t.child;case 10:t:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,tt(hs,r._currentValue),r._currentValue=s,o!==null)if(Fe(o.value,s)){if(o.children===i.children&&!Kt.current){t=gn(e,t,n);break t}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){s=o.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(o.tag===1){l=cn(-1,n&-n),l.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),Ql(o.return,n,t),a.lanes|=n;break}l=l.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(k(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),Ql(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}Ft(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,c1(t,n),i=Pe(i),r=r(i),t.flags|=1,Ft(e,t,r,n),t.child;case 14:return r=t.type,i=Ne(r,t.pendingProps),i=Ne(r.type,i),Hf(e,t,r,i,n);case 15:return O2(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ne(r,i),Uo(e,t),t.tag=1,Zt(r)?(e=!0,cs(t)):e=!1,c1(t,n),D2(t,r,i),Zl(t,r,i,n),eu(null,t,r,!0,e,n);case 19:return V2(e,t,n);case 22:return M2(e,t,n)}throw Error(k(156,t.tag))};function J2(e,t){return E9(e,t)}function B3(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function be(e,t,n,r){return new B3(e,t,n,r)}function Bc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function z3(e){if(typeof e=="function")return Bc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===oc)return 11;if(e===sc)return 14}return 2}function zn(e,t){var n=e.alternate;return n===null?(n=be(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Go(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")Bc(e)&&(s=1);else if(typeof e=="string")s=5;else t:switch(e){case Ur:return _r(n.children,i,o,t);case ic:s=8,i|=8;break;case Sl:return e=be(12,n,t,i|2),e.elementType=Sl,e.lanes=o,e;case Tl:return e=be(13,n,t,i),e.elementType=Tl,e.lanes=o,e;case bl:return e=be(19,n,t,i),e.elementType=bl,e.lanes=o,e;case u9:return ea(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case a9:s=10;break t;case l9:s=9;break t;case oc:s=11;break t;case sc:s=14;break t;case Tn:s=16,r=null;break t}throw Error(k(130,e==null?e:typeof e,""))}return t=be(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function _r(e,t,n,r){return e=be(7,e,r,t),e.lanes=n,e}function ea(e,t,n,r){return e=be(22,e,r,t),e.elementType=u9,e.lanes=n,e.stateNode={isHidden:!1},e}function Xa(e,t,n){return e=be(6,e,null,t),e.lanes=n,e}function Qa(e,t,n){return t=be(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function j3(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Aa(0),this.expirationTimes=Aa(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Aa(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function zc(e,t,n,r,i,o,s,a,l){return e=new j3(e,t,n,a,l),t===1?(t=1,o===!0&&(t|=8)):t=0,o=be(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},bc(o),e}function U3(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:jr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function t5(e){if(!e)return Gn;e=e._reactInternals;t:{if(Or(e)!==e||e.tag!==1)throw Error(k(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break t;case 1:if(Zt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break t}}t=t.return}while(t!==null);throw Error(k(171))}if(e.tag===1){var n=e.type;if(Zt(n))return t2(e,n,t)}return t}function e5(e,t,n,r,i,o,s,a,l){return e=zc(n,r,!0,e,i,o,s,a,l),e.context=t5(null),n=e.current,r=jt(),i=Bn(n),o=cn(r,i),o.callback=t!=null?t:null,qn(n,o,i),e.current.lanes=i,Zi(e,i,r),Jt(e,r),e}function na(e,t,n,r){var i=t.current,o=jt(),s=Bn(i);return n=t5(n),t.context===null?t.context=n:t.pendingContext=n,t=cn(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=qn(i,t,s),e!==null&&(qe(e,i,s,o),Bo(e,i,s)),s}function Ts(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function rd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function jc(e,t){rd(e,t),(e=e.alternate)&&rd(e,t)}function $3(){return null}var n5=typeof reportError=="function"?reportError:function(e){console.error(e)};function Uc(e){this._internalRoot=e}ra.prototype.render=Uc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(k(409));na(e,t,null,null)};ra.prototype.unmount=Uc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Pr(function(){na(null,e,null,null)}),t[pn]=null}};function ra(e){this._internalRoot=e}ra.prototype.unstable_scheduleHydration=function(e){if(e){var t=M9();e={blockedOn:null,target:e,priority:t};for(var n=0;n<En.length&&t!==0&&t<En[n].priority;n++);En.splice(n,0,e),n===0&&R9(e)}};function $c(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ia(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function id(){}function H3(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=Ts(s);o.call(u)}}var s=e5(t,r,e,0,null,!1,!1,"",id);return e._reactRootContainer=s,e[pn]=s.current,Di(e.nodeType===8?e.parentNode:e),Pr(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var u=Ts(l);a.call(u)}}var l=zc(e,0,!1,null,null,!1,!1,"",id);return e._reactRootContainer=l,e[pn]=l.current,Di(e.nodeType===8?e.parentNode:e),Pr(function(){na(t,l,n,r)}),l}function oa(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var a=i;i=function(){var l=Ts(s);a.call(l)}}na(t,s,e,i)}else s=H3(n,t,e,i,r);return Ts(s)}L9=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Q1(t.pendingLanes);n!==0&&(uc(t,n|1),Jt(t,mt()),!(W&6)&&(T1=mt()+500,Jn()))}break;case 13:Pr(function(){var r=mn(e,1);if(r!==null){var i=jt();qe(r,e,1,i)}}),jc(e,1)}};cc=function(e){if(e.tag===13){var t=mn(e,134217728);if(t!==null){var n=jt();qe(t,e,134217728,n)}jc(e,134217728)}};O9=function(e){if(e.tag===13){var t=Bn(e),n=mn(e,t);if(n!==null){var r=jt();qe(n,e,t,r)}jc(e,t)}};M9=function(){return Y};N9=function(e,t){var n=Y;try{return Y=e,t()}finally{Y=n}};Nl=function(e,t,n){switch(t){case"input":if(kl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Xs(r);if(!i)throw Error(k(90));f9(r),kl(r,i)}}}break;case"textarea":h9(e,n);break;case"select":t=n.value,t!=null&&s1(e,!!n.multiple,t,!1)}};w9=Vc;x9=Pr;var G3={usingClientEntryPoint:!1,Events:[to,Wr,Xs,y9,_9,Vc]},U1={findFiberByHostInstance:dr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},W3={bundleType:U1.bundleType,version:U1.version,rendererPackageName:U1.rendererPackageName,rendererConfig:U1.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:_n.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=b9(e),e===null?null:e.stateNode},findFiberByHostInstance:U1.findFiberByHostInstance||$3,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ko=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ko.isDisabled&&ko.supportsFiber)try{Hs=ko.inject(W3),We=ko}catch{}}_e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=G3;_e.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!$c(t))throw Error(k(200));return U3(e,t,null,n)};_e.createRoot=function(e,t){if(!$c(e))throw Error(k(299));var n=!1,r="",i=n5;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=zc(e,1,!1,null,null,n,!1,r,i),e[pn]=t.current,Di(e.nodeType===8?e.parentNode:e),new Uc(t)};_e.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(k(188)):(e=Object.keys(e).join(","),Error(k(268,e)));return e=b9(t),e=e===null?null:e.stateNode,e};_e.flushSync=function(e){return Pr(e)};_e.hydrate=function(e,t,n){if(!ia(t))throw Error(k(200));return oa(null,e,t,!0,n)};_e.hydrateRoot=function(e,t,n){if(!$c(e))throw Error(k(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=n5;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=e5(t,null,e,1,n!=null?n:null,i,!1,o,s),e[pn]=t.current,Di(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new ra(t)};_e.render=function(e,t,n){if(!ia(t))throw Error(k(200));return oa(null,e,t,!1,n)};_e.unmountComponentAtNode=function(e){if(!ia(e))throw Error(k(40));return e._reactRootContainer?(Pr(function(){oa(null,null,e,!1,function(){e._reactRootContainer=null,e[pn]=null})}),!0):!1};_e.unstable_batchedUpdates=Vc;_e.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ia(n))throw Error(k(200));if(e==null||e._reactInternals===void 0)throw Error(k(38));return oa(e,t,n,!1,r)};_e.version="18.3.1-next-f1338f8080-20240426";function r5(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r5)}catch(e){console.error(e)}}r5(),r9.exports=_e;var i5=r9.exports;const Y3=H8(i5);var o5,od=Y3;o5=od.createRoot,od.hydrateRoot;const s5="3.7.7",X3=s5,R1=typeof Buffer=="function",sd=typeof TextDecoder=="function"?new TextDecoder:void 0,ad=typeof TextEncoder=="function"?new TextEncoder:void 0,Q3="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Z1=Array.prototype.slice.call(Q3),Po=(e=>{let t={};return e.forEach((n,r)=>t[n]=r),t})(Z1),K3=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,Pt=String.fromCharCode.bind(String),ld=typeof Uint8Array.from=="function"?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),a5=e=>e.replace(/=/g,"").replace(/[+\/]/g,t=>t=="+"?"-":"_"),l5=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),u5=e=>{let t,n,r,i,o="";const s=e.length%3;for(let a=0;a<e.length;){if((n=e.charCodeAt(a++))>255||(r=e.charCodeAt(a++))>255||(i=e.charCodeAt(a++))>255)throw new TypeError("invalid character found");t=n<<16|r<<8|i,o+=Z1[t>>18&63]+Z1[t>>12&63]+Z1[t>>6&63]+Z1[t&63]}return s?o.slice(0,s-3)+"===".substring(s):o},Hc=typeof btoa=="function"?e=>btoa(e):R1?e=>Buffer.from(e,"binary").toString("base64"):u5,hu=R1?e=>Buffer.from(e).toString("base64"):e=>{let n=[];for(let r=0,i=e.length;r<i;r+=4096)n.push(Pt.apply(null,e.subarray(r,r+4096)));return Hc(n.join(""))},Wo=(e,t=!1)=>t?a5(hu(e)):hu(e),Z3=e=>{if(e.length<2){var t=e.charCodeAt(0);return t<128?e:t<2048?Pt(192|t>>>6)+Pt(128|t&63):Pt(224|t>>>12&15)+Pt(128|t>>>6&63)+Pt(128|t&63)}else{var t=65536+(e.charCodeAt(0)-55296)*1024+(e.charCodeAt(1)-56320);return Pt(240|t>>>18&7)+Pt(128|t>>>12&63)+Pt(128|t>>>6&63)+Pt(128|t&63)}},J3=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,c5=e=>e.replace(J3,Z3),ud=R1?e=>Buffer.from(e,"utf8").toString("base64"):ad?e=>hu(ad.encode(e)):e=>Hc(c5(e)),d1=(e,t=!1)=>t?a5(ud(e)):ud(e),cd=e=>d1(e,!0),tm=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,em=e=>{switch(e.length){case 4:var t=(7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3),n=t-65536;return Pt((n>>>10)+55296)+Pt((n&1023)+56320);case 3:return Pt((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return Pt((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},f5=e=>e.replace(tm,em),d5=e=>{if(e=e.replace(/\s+/g,""),!K3.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(e.length&3));let t,n="",r,i;for(let o=0;o<e.length;)t=Po[e.charAt(o++)]<<18|Po[e.charAt(o++)]<<12|(r=Po[e.charAt(o++)])<<6|(i=Po[e.charAt(o++)]),n+=r===64?Pt(t>>16&255):i===64?Pt(t>>16&255,t>>8&255):Pt(t>>16&255,t>>8&255,t&255);return n},Gc=typeof atob=="function"?e=>atob(l5(e)):R1?e=>Buffer.from(e,"base64").toString("binary"):d5,h5=R1?e=>ld(Buffer.from(e,"base64")):e=>ld(Gc(e).split("").map(t=>t.charCodeAt(0))),p5=e=>h5(m5(e)),nm=R1?e=>Buffer.from(e,"base64").toString("utf8"):sd?e=>sd.decode(h5(e)):e=>f5(Gc(e)),m5=e=>l5(e.replace(/[-_]/g,t=>t=="-"?"+":"/")),pu=e=>nm(m5(e)),rm=e=>{if(typeof e!="string")return!1;const t=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},g5=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),v5=function(){const e=(t,n)=>Object.defineProperty(String.prototype,t,g5(n));e("fromBase64",function(){return pu(this)}),e("toBase64",function(t){return d1(this,t)}),e("toBase64URI",function(){return d1(this,!0)}),e("toBase64URL",function(){return d1(this,!0)}),e("toUint8Array",function(){return p5(this)})},y5=function(){const e=(t,n)=>Object.defineProperty(Uint8Array.prototype,t,g5(n));e("toBase64",function(t){return Wo(this,t)}),e("toBase64URI",function(){return Wo(this,!0)}),e("toBase64URL",function(){return Wo(this,!0)})},im=()=>{v5(),y5()},om={version:s5,VERSION:X3,atob:Gc,atobPolyfill:d5,btoa:Hc,btoaPolyfill:u5,fromBase64:pu,toBase64:d1,encode:d1,encodeURI:cd,encodeURL:cd,utob:c5,btou:f5,decode:pu,isValid:rm,fromUint8Array:Wo,toUint8Array:p5,extendString:v5,extendUint8Array:y5,extendBuiltins:im};function sm(e){if(typeof e=="object")return e;if(typeof e!="string")return null;try{return e.startsWith("{")?JSON.parse(e):am(e)}catch(t){return console.log("parse failed:"+t.message),null}}function am(e){var t=new window.DOMParser().parseFromString(e,"text/xml"),n=t.getElementsByTagName("componentData");if(!n.length){var r,i=(r=e.match(/<templateData>(.*)<\/templateData>/))===null||r===void 0?void 0:r[1];return JSON.parse(om.decode(i))}var o={};for(var s of n)for(var a of s.getElementsByTagName("data")||[])if(a.getAttribute("value")!=null){o[s.getAttribute("id")]=a.getAttribute("value");break}return o}var Wc=K.createContext(),lm=e=>{var{children:t,name:n}=e,[r,i]=D.useState(Gt.loading),[o,s]=D.useState({}),[a,l]=D.useState([]),[u,c]=D.useState(),[f,d]=D.useState(!1),p=_=>{console.log("".concat(n||"").concat(_))},v=D.useCallback(_=>(l(m=>[...m,_]),()=>{l(m=>m.filter(g=>g!==_))}),[]);D.useLayoutEffect(()=>{var _=!1;return window.load=()=>{i(Gt.loaded),p(".load()")},window.play=()=>{_=!0,d(!0),p(".play()")},window.pause=()=>{i(Gt.paused),p(".pause()")},window.stop=()=>{_?(i(Gt.stopped),p(".stop()")):(i(Gt.removed),p(".stop() without play"))},window.update=m=>{var g=sm(m);if(g&&(p(".update(".concat(g?JSON.stringify(g||{},null,2):"null",")")),s(g),!_)){var y=v("__initialData");c(()=>y)}},()=>{delete window.load,delete window.play,delete window.pause,delete window.stop,delete window.update}},[]),D.useEffect(()=>{u==null||u()},[u]),D.useEffect(()=>{r<Gt.playing&&f&&!a.length&&i(Gt.playing)},[r,o,f,a]),D.useEffect(()=>{if(r===Gt.removed){var _,m;p(".remove()"),(_=(m=window).remove)===null||_===void 0||_.call(m)}},[r]);var h=D.useCallback(()=>{i(Gt.removed)},[]);return K.createElement(Wc.Provider,{value:{data:o,state:r,name:n,safeToRemove:h,delayPlay:v}},r!==Gt.removed?K.createElement(um,null,t):null)},um=D.memo(e=>{var{children:t}=e;return t}),Ka=null,cm=(e,t)=>{var{container:n=document.getElementById("root"),cssReset:r=!0,name:i=e.name}={};if(n||(n=document.createElement("div"),n.id="root",document.body.appendChild(n)),r){var o=` 
      width: 100vw;
      height: 100vh;
      overflow: hidden;
      margin: 0;
    `;document.body.style.cssText=o,n.style.cssText=o}Ka||(Ka=o5(n)),i5.flushSync(()=>{Ka.render(D.createElement(lm,{name:i},D.createElement(e)))})};function on(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _5(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}/*!
 * GSAP 3.12.5
 * https://gsap.com
 *
 * @license Copyright 2008-2024, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license or for
 * Club GSAP members, the agreement issued with that membership.
 * @author: Jack Doyle, <EMAIL>
*/var pe={autoSleep:120,force3D:"auto",nullTargetWarn:1,units:{lineHeight:""}},b1={duration:.5,overwrite:!1,delay:0},Yc,Rt,it,Ce=1e8,Z=1/Ce,mu=Math.PI*2,fm=mu/4,dm=0,w5=Math.sqrt,hm=Math.cos,pm=Math.sin,Ct=function(t){return typeof t=="string"},ht=function(t){return typeof t=="function"},vn=function(t){return typeof t=="number"},Xc=function(t){return typeof t>"u"},Ze=function(t){return typeof t=="object"},te=function(t){return t!==!1},Qc=function(){return typeof window<"u"},Do=function(t){return ht(t)||Ct(t)},x5=typeof ArrayBuffer=="function"&&ArrayBuffer.isView||function(){},It=Array.isArray,gu=/(?:-?\.?\d|\.)+/gi,S5=/[-+=.]*\d+[.e\-+]*\d*[e\-+]*\d*/g,t1=/[-+=.]*\d+[.e-]*\d*[a-z%]*/g,Za=/[-+=.]*\d+\.?\d*(?:e-|e\+)?\d*/gi,T5=/[+-]=-?[.\d]+/,b5=/[^,'"\[\]\s]+/gi,mm=/^[+\-=e\s\d]*\d+[.\d]*([a-z]*|%)\s*$/i,st,Ue,vu,Kc,ve={},bs={},C5,E5=function(t){return(bs=Dr(t,ve))&&ie},Zc=function(t,n){return console.warn("Invalid property",t,"set to",n,"Missing plugin? gsap.registerPlugin()")},qi=function(t,n){return!n&&console.warn(t)},k5=function(t,n){return t&&(ve[t]=n)&&bs&&(bs[t]=n)||ve},Fi=function(){return 0},gm={suppressEvents:!0,isStart:!0,kill:!1},Yo={suppressEvents:!0,kill:!1},vm={suppressEvents:!0},Jc={},jn=[],yu={},P5,se={},Ja={},fd=30,Xo=[],t0="",e0=function(t){var n=t[0],r,i;if(Ze(n)||ht(n)||(t=[t]),!(r=(n._gsap||{}).harness)){for(i=Xo.length;i--&&!Xo[i].targetTest(n););r=Xo[i]}for(i=t.length;i--;)t[i]&&(t[i]._gsap||(t[i]._gsap=new Z5(t[i],r)))||t.splice(i,1);return t},wr=function(t){return t._gsap||e0(Ee(t))[0]._gsap},D5=function(t,n,r){return(r=t[n])&&ht(r)?t[n]():Xc(r)&&t.getAttribute&&t.getAttribute(n)||r},ee=function(t,n){return(t=t.split(",")).forEach(n)||t},pt=function(t){return Math.round(t*1e5)/1e5||0},Tt=function(t){return Math.round(t*1e7)/1e7||0},h1=function(t,n){var r=n.charAt(0),i=parseFloat(n.substr(2));return t=parseFloat(t),r==="+"?t+i:r==="-"?t-i:r==="*"?t*i:t/i},ym=function(t,n){for(var r=n.length,i=0;t.indexOf(n[i])<0&&++i<r;);return i<r},Cs=function(){var t=jn.length,n=jn.slice(0),r,i;for(yu={},jn.length=0,r=0;r<t;r++)i=n[r],i&&i._lazy&&(i.render(i._lazy[0],i._lazy[1],!0)._lazy=0)},A5=function(t,n,r,i){jn.length&&!Rt&&Cs(),t.render(n,r,Rt&&n<0&&(t._initted||t._startAt)),jn.length&&!Rt&&Cs()},L5=function(t){var n=parseFloat(t);return(n||n===0)&&(t+"").match(b5).length<2?n:Ct(t)?t.trim():t},O5=function(t){return t},Ae=function(t,n){for(var r in n)r in t||(t[r]=n[r]);return t},_m=function(t){return function(n,r){for(var i in r)i in n||i==="duration"&&t||i==="ease"||(n[i]=r[i])}},Dr=function(t,n){for(var r in n)t[r]=n[r];return t},dd=function e(t,n){for(var r in n)r!=="__proto__"&&r!=="constructor"&&r!=="prototype"&&(t[r]=Ze(n[r])?e(t[r]||(t[r]={}),n[r]):n[r]);return t},Es=function(t,n){var r={},i;for(i in t)i in n||(r[i]=t[i]);return r},fi=function(t){var n=t.parent||st,r=t.keyframes?_m(It(t.keyframes)):Ae;if(te(t.inherit))for(;n;)r(t,n.vars.defaults),n=n.parent||n._dp;return t},wm=function(t,n){for(var r=t.length,i=r===n.length;i&&r--&&t[r]===n[r];);return r<0},M5=function(t,n,r,i,o){var s=t[i],a;if(o)for(a=n[o];s&&s[o]>a;)s=s._prev;return s?(n._next=s._next,s._next=n):(n._next=t[r],t[r]=n),n._next?n._next._prev=n:t[i]=n,n._prev=s,n.parent=n._dp=t,n},sa=function(t,n,r,i){r===void 0&&(r="_first"),i===void 0&&(i="_last");var o=n._prev,s=n._next;o?o._next=s:t[r]===n&&(t[r]=s),s?s._prev=o:t[i]===n&&(t[i]=o),n._next=n._prev=n.parent=null},Wn=function(t,n){t.parent&&(!n||t.parent.autoRemoveChildren)&&t.parent.remove&&t.parent.remove(t),t._act=0},xr=function(t,n){if(t&&(!n||n._end>t._dur||n._start<0))for(var r=t;r;)r._dirty=1,r=r.parent;return t},xm=function(t){for(var n=t.parent;n&&n.parent;)n._dirty=1,n.totalDuration(),n=n.parent;return t},_u=function(t,n,r,i){return t._startAt&&(Rt?t._startAt.revert(Yo):t.vars.immediateRender&&!t.vars.autoRevert||t._startAt.render(n,!0,i))},Sm=function e(t){return!t||t._ts&&e(t.parent)},hd=function(t){return t._repeat?C1(t._tTime,t=t.duration()+t._rDelay)*t:0},C1=function(t,n){var r=Math.floor(t/=n);return t&&r===t?r-1:r},ks=function(t,n){return(t-n._start)*n._ts+(n._ts>=0?0:n._dirty?n.totalDuration():n._tDur)},aa=function(t){return t._end=Tt(t._start+(t._tDur/Math.abs(t._ts||t._rts||Z)||0))},la=function(t,n){var r=t._dp;return r&&r.smoothChildTiming&&t._ts&&(t._start=Tt(r._time-(t._ts>0?n/t._ts:((t._dirty?t.totalDuration():t._tDur)-n)/-t._ts)),aa(t),r._dirty||xr(r,t)),t},N5=function(t,n){var r;if((n._time||!n._dur&&n._initted||n._start<t._time&&(n._dur||!n.add))&&(r=ks(t.rawTime(),n),(!n._dur||no(0,n.totalDuration(),r)-n._tTime>Z)&&n.render(r,!0)),xr(t,n)._dp&&t._initted&&t._time>=t._dur&&t._ts){if(t._dur<t.duration())for(r=t;r._dp;)r.rawTime()>=0&&r.totalTime(r._tTime),r=r._dp;t._zTime=-Z}},He=function(t,n,r,i){return n.parent&&Wn(n),n._start=Tt((vn(r)?r:r||t!==st?xe(t,r,n):t._time)+n._delay),n._end=Tt(n._start+(n.totalDuration()/Math.abs(n.timeScale())||0)),M5(t,n,"_first","_last",t._sort?"_start":0),wu(n)||(t._recent=n),i||N5(t,n),t._ts<0&&la(t,t._tTime),t},R5=function(t,n){return(ve.ScrollTrigger||Zc("scrollTrigger",n))&&ve.ScrollTrigger.create(n,t)},I5=function(t,n,r,i,o){if(r0(t,n,o),!t._initted)return 1;if(!r&&t._pt&&!Rt&&(t._dur&&t.vars.lazy!==!1||!t._dur&&t.vars.lazy)&&P5!==ae.frame)return jn.push(t),t._lazy=[o,i],1},Tm=function e(t){var n=t.parent;return n&&n._ts&&n._initted&&!n._lock&&(n.rawTime()<0||e(n))},wu=function(t){var n=t.data;return n==="isFromStart"||n==="isStart"},bm=function(t,n,r,i){var o=t.ratio,s=n<0||!n&&(!t._start&&Tm(t)&&!(!t._initted&&wu(t))||(t._ts<0||t._dp._ts<0)&&!wu(t))?0:1,a=t._rDelay,l=0,u,c,f;if(a&&t._repeat&&(l=no(0,t._tDur,n),c=C1(l,a),t._yoyo&&c&1&&(s=1-s),c!==C1(t._tTime,a)&&(o=1-s,t.vars.repeatRefresh&&t._initted&&t.invalidate())),s!==o||Rt||i||t._zTime===Z||!n&&t._zTime){if(!t._initted&&I5(t,n,i,r,l))return;for(f=t._zTime,t._zTime=n||(r?Z:0),r||(r=n&&!f),t.ratio=s,t._from&&(s=1-s),t._time=0,t._tTime=l,u=t._pt;u;)u.r(s,u.d),u=u._next;n<0&&_u(t,n,r,!0),t._onUpdate&&!r&&de(t,"onUpdate"),l&&t._repeat&&!r&&t.parent&&de(t,"onRepeat"),(n>=t._tDur||n<0)&&t.ratio===s&&(s&&Wn(t,1),!r&&!Rt&&(de(t,s?"onComplete":"onReverseComplete",!0),t._prom&&t._prom()))}else t._zTime||(t._zTime=n)},Cm=function(t,n,r){var i;if(r>n)for(i=t._first;i&&i._start<=r;){if(i.data==="isPause"&&i._start>n)return i;i=i._next}else for(i=t._last;i&&i._start>=r;){if(i.data==="isPause"&&i._start<n)return i;i=i._prev}},E1=function(t,n,r,i){var o=t._repeat,s=Tt(n)||0,a=t._tTime/t._tDur;return a&&!i&&(t._time*=s/t._dur),t._dur=s,t._tDur=o?o<0?1e10:Tt(s*(o+1)+t._rDelay*o):s,a>0&&!i&&la(t,t._tTime=t._tDur*a),t.parent&&aa(t),r||xr(t.parent,t),t},pd=function(t){return t instanceof zt?xr(t):E1(t,t._dur)},Em={_start:0,endTime:Fi,totalDuration:Fi},xe=function e(t,n,r){var i=t.labels,o=t._recent||Em,s=t.duration()>=Ce?o.endTime(!1):t._dur,a,l,u;return Ct(n)&&(isNaN(n)||n in i)?(l=n.charAt(0),u=n.substr(-1)==="%",a=n.indexOf("="),l==="<"||l===">"?(a>=0&&(n=n.replace(/=/,"")),(l==="<"?o._start:o.endTime(o._repeat>=0))+(parseFloat(n.substr(1))||0)*(u?(a<0?o:r).totalDuration()/100:1)):a<0?(n in i||(i[n]=s),i[n]):(l=parseFloat(n.charAt(a-1)+n.substr(a+1)),u&&r&&(l=l/100*(It(r)?r[0]:r).totalDuration()),a>1?e(t,n.substr(0,a-1),r)+l:s+l)):n==null?s:+n},di=function(t,n,r){var i=vn(n[1]),o=(i?2:1)+(t<2?0:1),s=n[o],a,l;if(i&&(s.duration=n[1]),s.parent=r,t){for(a=s,l=r;l&&!("immediateRender"in a);)a=l.vars.defaults||{},l=te(l.vars.inherit)&&l.parent;s.immediateRender=te(a.immediateRender),t<2?s.runBackwards=1:s.startAt=n[o-1]}return new gt(n[0],s,n[o+1])},tr=function(t,n){return t||t===0?n(t):n},no=function(t,n,r){return r<t?t:r>n?n:r},Nt=function(t,n){return!Ct(t)||!(n=mm.exec(t))?"":n[1]},km=function(t,n,r){return tr(r,function(i){return no(t,n,i)})},xu=[].slice,V5=function(t,n){return t&&Ze(t)&&"length"in t&&(!n&&!t.length||t.length-1 in t&&Ze(t[0]))&&!t.nodeType&&t!==Ue},Pm=function(t,n,r){return r===void 0&&(r=[]),t.forEach(function(i){var o;return Ct(i)&&!n||V5(i,1)?(o=r).push.apply(o,Ee(i)):r.push(i)})||r},Ee=function(t,n,r){return it&&!n&&it.selector?it.selector(t):Ct(t)&&!r&&(vu||!k1())?xu.call((n||Kc).querySelectorAll(t),0):It(t)?Pm(t,r):V5(t)?xu.call(t,0):t?[t]:[]},Su=function(t){return t=Ee(t)[0]||qi("Invalid scope")||{},function(n){var r=t.current||t.nativeElement||t;return Ee(n,r.querySelectorAll?r:r===t?qi("Invalid scope")||Kc.createElement("div"):t)}},q5=function(t){return t.sort(function(){return .5-Math.random()})},F5=function(t){if(ht(t))return t;var n=Ze(t)?t:{each:t},r=Sr(n.ease),i=n.from||0,o=parseFloat(n.base)||0,s={},a=i>0&&i<1,l=isNaN(i)||a,u=n.axis,c=i,f=i;return Ct(i)?c=f={center:.5,edges:.5,end:1}[i]||0:!a&&l&&(c=i[0],f=i[1]),function(d,p,v){var h=(v||n).length,_=s[h],m,g,y,w,x,S,T,b,C;if(!_){if(C=n.grid==="auto"?0:(n.grid||[1,Ce])[1],!C){for(T=-Ce;T<(T=v[C++].getBoundingClientRect().left)&&C<h;);C<h&&C--}for(_=s[h]=[],m=l?Math.min(C,h)*c-.5:i%C,g=C===Ce?0:l?h*f/C-.5:i/C|0,T=0,b=Ce,S=0;S<h;S++)y=S%C-m,w=g-(S/C|0),_[S]=x=u?Math.abs(u==="y"?w:y):w5(y*y+w*w),x>T&&(T=x),x<b&&(b=x);i==="random"&&q5(_),_.max=T-b,_.min=b,_.v=h=(parseFloat(n.amount)||parseFloat(n.each)*(C>h?h-1:u?u==="y"?h/C:C:Math.max(C,h/C))||0)*(i==="edges"?-1:1),_.b=h<0?o-h:o,_.u=Nt(n.amount||n.each)||0,r=r&&h<0?X5(r):r}return h=(_[d]-_.min)/_.max||0,Tt(_.b+(r?r(h):h)*_.v)+_.u}},Tu=function(t){var n=Math.pow(10,((t+"").split(".")[1]||"").length);return function(r){var i=Tt(Math.round(parseFloat(r)/t)*t*n);return(i-i%1)/n+(vn(r)?0:Nt(r))}},B5=function(t,n){var r=It(t),i,o;return!r&&Ze(t)&&(i=r=t.radius||Ce,t.values?(t=Ee(t.values),(o=!vn(t[0]))&&(i*=i)):t=Tu(t.increment)),tr(n,r?ht(t)?function(s){return o=t(s),Math.abs(o-s)<=i?o:s}:function(s){for(var a=parseFloat(o?s.x:s),l=parseFloat(o?s.y:0),u=Ce,c=0,f=t.length,d,p;f--;)o?(d=t[f].x-a,p=t[f].y-l,d=d*d+p*p):d=Math.abs(t[f]-a),d<u&&(u=d,c=f);return c=!i||u<=i?t[c]:s,o||c===s||vn(s)?c:c+Nt(s)}:Tu(t))},z5=function(t,n,r,i){return tr(It(t)?!n:r===!0?!!(r=0):!i,function(){return It(t)?t[~~(Math.random()*t.length)]:(r=r||1e-5)&&(i=r<1?Math.pow(10,(r+"").length-2):1)&&Math.floor(Math.round((t-r/2+Math.random()*(n-t+r*.99))/r)*r*i)/i})},Dm=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return function(i){return n.reduce(function(o,s){return s(o)},i)}},Am=function(t,n){return function(r){return t(parseFloat(r))+(n||Nt(r))}},Lm=function(t,n,r){return U5(t,n,0,1,r)},j5=function(t,n,r){return tr(r,function(i){return t[~~n(i)]})},Om=function e(t,n,r){var i=n-t;return It(t)?j5(t,e(0,t.length),n):tr(r,function(o){return(i+(o-t)%i)%i+t})},Mm=function e(t,n,r){var i=n-t,o=i*2;return It(t)?j5(t,e(0,t.length-1),n):tr(r,function(s){return s=(o+(s-t)%o)%o||0,t+(s>i?o-s:s)})},Bi=function(t){for(var n=0,r="",i,o,s,a;~(i=t.indexOf("random(",n));)s=t.indexOf(")",i),a=t.charAt(i+7)==="[",o=t.substr(i+7,s-i-7).match(a?b5:gu),r+=t.substr(n,i-n)+z5(a?o:+o[0],a?0:+o[1],+o[2]||1e-5),n=s+1;return r+t.substr(n,t.length-n)},U5=function(t,n,r,i,o){var s=n-t,a=i-r;return tr(o,function(l){return r+((l-t)/s*a||0)})},Nm=function e(t,n,r,i){var o=isNaN(t+n)?0:function(p){return(1-p)*t+p*n};if(!o){var s=Ct(t),a={},l,u,c,f,d;if(r===!0&&(i=1)&&(r=null),s)t={p:t},n={p:n};else if(It(t)&&!It(n)){for(c=[],f=t.length,d=f-2,u=1;u<f;u++)c.push(e(t[u-1],t[u]));f--,o=function(v){v*=f;var h=Math.min(d,~~v);return c[h](v-h)},r=n}else i||(t=Dr(It(t)?[]:{},t));if(!c){for(l in n)n0.call(a,t,l,"get",n[l]);o=function(v){return s0(v,a)||(s?t.p:t)}}}return tr(r,o)},md=function(t,n,r){var i=t.labels,o=Ce,s,a,l;for(s in i)a=i[s]-n,a<0==!!r&&a&&o>(a=Math.abs(a))&&(l=s,o=a);return l},de=function(t,n,r){var i=t.vars,o=i[n],s=it,a=t._ctx,l,u,c;if(o)return l=i[n+"Params"],u=i.callbackScope||t,r&&jn.length&&Cs(),a&&(it=a),c=l?o.apply(u,l):o.call(u),it=s,c},J1=function(t){return Wn(t),t.scrollTrigger&&t.scrollTrigger.kill(!!Rt),t.progress()<1&&de(t,"onInterrupt"),t},e1,$5=[],H5=function(t){if(t)if(t=!t.name&&t.default||t,Qc()||t.headless){var n=t.name,r=ht(t),i=n&&!r&&t.init?function(){this._props=[]}:t,o={init:Fi,render:s0,add:n0,kill:Qm,modifier:Xm,rawVars:0},s={targetTest:0,get:0,getSetter:o0,aliases:{},register:0};if(k1(),t!==i){if(se[n])return;Ae(i,Ae(Es(t,o),s)),Dr(i.prototype,Dr(o,Es(t,s))),se[i.prop=n]=i,t.targetTest&&(Xo.push(i),Jc[n]=1),n=(n==="css"?"CSS":n.charAt(0).toUpperCase()+n.substr(1))+"Plugin"}k5(n,i),t.register&&t.register(ie,i,ne)}else $5.push(t)},Q=255,ti={aqua:[0,Q,Q],lime:[0,Q,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,Q],navy:[0,0,128],white:[Q,Q,Q],olive:[128,128,0],yellow:[Q,Q,0],orange:[Q,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[Q,0,0],pink:[Q,192,203],cyan:[0,Q,Q],transparent:[Q,Q,Q,0]},tl=function(t,n,r){return t+=t<0?1:t>1?-1:0,(t*6<1?n+(r-n)*t*6:t<.5?r:t*3<2?n+(r-n)*(2/3-t)*6:n)*Q+.5|0},G5=function(t,n,r){var i=t?vn(t)?[t>>16,t>>8&Q,t&Q]:0:ti.black,o,s,a,l,u,c,f,d,p,v;if(!i){if(t.substr(-1)===","&&(t=t.substr(0,t.length-1)),ti[t])i=ti[t];else if(t.charAt(0)==="#"){if(t.length<6&&(o=t.charAt(1),s=t.charAt(2),a=t.charAt(3),t="#"+o+o+s+s+a+a+(t.length===5?t.charAt(4)+t.charAt(4):"")),t.length===9)return i=parseInt(t.substr(1,6),16),[i>>16,i>>8&Q,i&Q,parseInt(t.substr(7),16)/255];t=parseInt(t.substr(1),16),i=[t>>16,t>>8&Q,t&Q]}else if(t.substr(0,3)==="hsl"){if(i=v=t.match(gu),!n)l=+i[0]%360/360,u=+i[1]/100,c=+i[2]/100,s=c<=.5?c*(u+1):c+u-c*u,o=c*2-s,i.length>3&&(i[3]*=1),i[0]=tl(l+1/3,o,s),i[1]=tl(l,o,s),i[2]=tl(l-1/3,o,s);else if(~t.indexOf("="))return i=t.match(S5),r&&i.length<4&&(i[3]=1),i}else i=t.match(gu)||ti.transparent;i=i.map(Number)}return n&&!v&&(o=i[0]/Q,s=i[1]/Q,a=i[2]/Q,f=Math.max(o,s,a),d=Math.min(o,s,a),c=(f+d)/2,f===d?l=u=0:(p=f-d,u=c>.5?p/(2-f-d):p/(f+d),l=f===o?(s-a)/p+(s<a?6:0):f===s?(a-o)/p+2:(o-s)/p+4,l*=60),i[0]=~~(l+.5),i[1]=~~(u*100+.5),i[2]=~~(c*100+.5)),r&&i.length<4&&(i[3]=1),i},W5=function(t){var n=[],r=[],i=-1;return t.split(Un).forEach(function(o){var s=o.match(t1)||[];n.push.apply(n,s),r.push(i+=s.length+1)}),n.c=r,n},gd=function(t,n,r){var i="",o=(t+i).match(Un),s=n?"hsla(":"rgba(",a=0,l,u,c,f;if(!o)return t;if(o=o.map(function(d){return(d=G5(d,n,1))&&s+(n?d[0]+","+d[1]+"%,"+d[2]+"%,"+d[3]:d.join(","))+")"}),r&&(c=W5(t),l=r.c,l.join(i)!==c.c.join(i)))for(u=t.replace(Un,"1").split(t1),f=u.length-1;a<f;a++)i+=u[a]+(~l.indexOf(a)?o.shift()||s+"0,0,0,0)":(c.length?c:o.length?o:r).shift());if(!u)for(u=t.split(Un),f=u.length-1;a<f;a++)i+=u[a]+o[a];return i+u[f]},Un=function(){var e="(?:\\b(?:(?:rgb|rgba|hsl|hsla)\\(.+?\\))|\\B#(?:[0-9a-f]{3,4}){1,2}\\b",t;for(t in ti)e+="|"+t+"\\b";return new RegExp(e+")","gi")}(),Rm=/hsl[a]?\(/,Y5=function(t){var n=t.join(" "),r;if(Un.lastIndex=0,Un.test(n))return r=Rm.test(n),t[1]=gd(t[1],r),t[0]=gd(t[0],r,W5(t[1])),!0},zi,ae=function(){var e=Date.now,t=500,n=33,r=e(),i=r,o=1e3/240,s=o,a=[],l,u,c,f,d,p,v=function h(_){var m=e()-i,g=_===!0,y,w,x,S;if((m>t||m<0)&&(r+=m-n),i+=m,x=i-r,y=x-s,(y>0||g)&&(S=++f.frame,d=x-f.time*1e3,f.time=x=x/1e3,s+=y+(y>=o?4:o-y),w=1),g||(l=u(h)),w)for(p=0;p<a.length;p++)a[p](x,d,S,_)};return f={time:0,frame:0,tick:function(){v(!0)},deltaRatio:function(_){return d/(1e3/(_||60))},wake:function(){C5&&(!vu&&Qc()&&(Ue=vu=window,Kc=Ue.document||{},ve.gsap=ie,(Ue.gsapVersions||(Ue.gsapVersions=[])).push(ie.version),E5(bs||Ue.GreenSockGlobals||!Ue.gsap&&Ue||{}),$5.forEach(H5)),c=typeof requestAnimationFrame<"u"&&requestAnimationFrame,l&&f.sleep(),u=c||function(_){return setTimeout(_,s-f.time*1e3+1|0)},zi=1,v(2))},sleep:function(){(c?cancelAnimationFrame:clearTimeout)(l),zi=0,u=Fi},lagSmoothing:function(_,m){t=_||1/0,n=Math.min(m||33,t)},fps:function(_){o=1e3/(_||240),s=f.time*1e3+o},add:function(_,m,g){var y=m?function(w,x,S,T){_(w,x,S,T),f.remove(y)}:_;return f.remove(_),a[g?"unshift":"push"](y),k1(),y},remove:function(_,m){~(m=a.indexOf(_))&&a.splice(m,1)&&p>=m&&p--},_listeners:a},f}(),k1=function(){return!zi&&ae.wake()},H={},Im=/^[\d.\-M][\d.\-,\s]/,Vm=/["']/g,qm=function(t){for(var n={},r=t.substr(1,t.length-3).split(":"),i=r[0],o=1,s=r.length,a,l,u;o<s;o++)l=r[o],a=o!==s-1?l.lastIndexOf(","):l.length,u=l.substr(0,a),n[i]=isNaN(u)?u.replace(Vm,"").trim():+u,i=l.substr(a+1).trim();return n},Fm=function(t){var n=t.indexOf("(")+1,r=t.indexOf(")"),i=t.indexOf("(",n);return t.substring(n,~i&&i<r?t.indexOf(")",r+1):r)},Bm=function(t){var n=(t+"").split("("),r=H[n[0]];return r&&n.length>1&&r.config?r.config.apply(null,~t.indexOf("{")?[qm(n[1])]:Fm(t).split(",").map(L5)):H._CE&&Im.test(t)?H._CE("",t):r},X5=function(t){return function(n){return 1-t(1-n)}},Q5=function e(t,n){for(var r=t._first,i;r;)r instanceof zt?e(r,n):r.vars.yoyoEase&&(!r._yoyo||!r._repeat)&&r._yoyo!==n&&(r.timeline?e(r.timeline,n):(i=r._ease,r._ease=r._yEase,r._yEase=i,r._yoyo=n)),r=r._next},Sr=function(t,n){return t&&(ht(t)?t:H[t]||Bm(t))||n},Mr=function(t,n,r,i){r===void 0&&(r=function(l){return 1-n(1-l)}),i===void 0&&(i=function(l){return l<.5?n(l*2)/2:1-n((1-l)*2)/2});var o={easeIn:n,easeOut:r,easeInOut:i},s;return ee(t,function(a){H[a]=ve[a]=o,H[s=a.toLowerCase()]=r;for(var l in o)H[s+(l==="easeIn"?".in":l==="easeOut"?".out":".inOut")]=H[a+"."+l]=o[l]}),o},K5=function(t){return function(n){return n<.5?(1-t(1-n*2))/2:.5+t((n-.5)*2)/2}},el=function e(t,n,r){var i=n>=1?n:1,o=(r||(t?.3:.45))/(n<1?n:1),s=o/mu*(Math.asin(1/i)||0),a=function(c){return c===1?1:i*Math.pow(2,-10*c)*pm((c-s)*o)+1},l=t==="out"?a:t==="in"?function(u){return 1-a(1-u)}:K5(a);return o=mu/o,l.config=function(u,c){return e(t,u,c)},l},nl=function e(t,n){n===void 0&&(n=1.70158);var r=function(s){return s?--s*s*((n+1)*s+n)+1:0},i=t==="out"?r:t==="in"?function(o){return 1-r(1-o)}:K5(r);return i.config=function(o){return e(t,o)},i};ee("Linear,Quad,Cubic,Quart,Quint,Strong",function(e,t){var n=t<5?t+1:t;Mr(e+",Power"+(n-1),t?function(r){return Math.pow(r,n)}:function(r){return r},function(r){return 1-Math.pow(1-r,n)},function(r){return r<.5?Math.pow(r*2,n)/2:1-Math.pow((1-r)*2,n)/2})});H.Linear.easeNone=H.none=H.Linear.easeIn;Mr("Elastic",el("in"),el("out"),el());(function(e,t){var n=1/t,r=2*n,i=2.5*n,o=function(a){return a<n?e*a*a:a<r?e*Math.pow(a-1.5/t,2)+.75:a<i?e*(a-=2.25/t)*a+.9375:e*Math.pow(a-2.625/t,2)+.984375};Mr("Bounce",function(s){return 1-o(1-s)},o)})(7.5625,2.75);Mr("Expo",function(e){return e?Math.pow(2,10*(e-1)):0});Mr("Circ",function(e){return-(w5(1-e*e)-1)});Mr("Sine",function(e){return e===1?1:-hm(e*fm)+1});Mr("Back",nl("in"),nl("out"),nl());H.SteppedEase=H.steps=ve.SteppedEase={config:function(t,n){t===void 0&&(t=1);var r=1/t,i=t+(n?0:1),o=n?1:0,s=1-Z;return function(a){return((i*no(0,s,a)|0)+o)*r}}};b1.ease=H["quad.out"];ee("onComplete,onUpdate,onStart,onRepeat,onReverseComplete,onInterrupt",function(e){return t0+=e+","+e+"Params,"});var Z5=function(t,n){this.id=dm++,t._gsap=this,this.target=t,this.harness=n,this.get=n?n.get:D5,this.set=n?n.getSetter:o0},ji=function(){function e(n){this.vars=n,this._delay=+n.delay||0,(this._repeat=n.repeat===1/0?-2:n.repeat||0)&&(this._rDelay=n.repeatDelay||0,this._yoyo=!!n.yoyo||!!n.yoyoEase),this._ts=1,E1(this,+n.duration,1,1),this.data=n.data,it&&(this._ctx=it,it.data.push(this)),zi||ae.wake()}var t=e.prototype;return t.delay=function(r){return r||r===0?(this.parent&&this.parent.smoothChildTiming&&this.startTime(this._start+r-this._delay),this._delay=r,this):this._delay},t.duration=function(r){return arguments.length?this.totalDuration(this._repeat>0?r+(r+this._rDelay)*this._repeat:r):this.totalDuration()&&this._dur},t.totalDuration=function(r){return arguments.length?(this._dirty=0,E1(this,this._repeat<0?r:(r-this._repeat*this._rDelay)/(this._repeat+1))):this._tDur},t.totalTime=function(r,i){if(k1(),!arguments.length)return this._tTime;var o=this._dp;if(o&&o.smoothChildTiming&&this._ts){for(la(this,r),!o._dp||o.parent||N5(o,this);o&&o.parent;)o.parent._time!==o._start+(o._ts>=0?o._tTime/o._ts:(o.totalDuration()-o._tTime)/-o._ts)&&o.totalTime(o._tTime,!0),o=o.parent;!this.parent&&this._dp.autoRemoveChildren&&(this._ts>0&&r<this._tDur||this._ts<0&&r>0||!this._tDur&&!r)&&He(this._dp,this,this._start-this._delay)}return(this._tTime!==r||!this._dur&&!i||this._initted&&Math.abs(this._zTime)===Z||!r&&!this._initted&&(this.add||this._ptLookup))&&(this._ts||(this._pTime=r),A5(this,r,i)),this},t.time=function(r,i){return arguments.length?this.totalTime(Math.min(this.totalDuration(),r+hd(this))%(this._dur+this._rDelay)||(r?this._dur:0),i):this._time},t.totalProgress=function(r,i){return arguments.length?this.totalTime(this.totalDuration()*r,i):this.totalDuration()?Math.min(1,this._tTime/this._tDur):this.rawTime()>0?1:0},t.progress=function(r,i){return arguments.length?this.totalTime(this.duration()*(this._yoyo&&!(this.iteration()&1)?1-r:r)+hd(this),i):this.duration()?Math.min(1,this._time/this._dur):this.rawTime()>0?1:0},t.iteration=function(r,i){var o=this.duration()+this._rDelay;return arguments.length?this.totalTime(this._time+(r-1)*o,i):this._repeat?C1(this._tTime,o)+1:1},t.timeScale=function(r,i){if(!arguments.length)return this._rts===-Z?0:this._rts;if(this._rts===r)return this;var o=this.parent&&this._ts?ks(this.parent._time,this):this._tTime;return this._rts=+r||0,this._ts=this._ps||r===-Z?0:this._rts,this.totalTime(no(-Math.abs(this._delay),this._tDur,o),i!==!1),aa(this),xm(this)},t.paused=function(r){return arguments.length?(this._ps!==r&&(this._ps=r,r?(this._pTime=this._tTime||Math.max(-this._delay,this.rawTime()),this._ts=this._act=0):(k1(),this._ts=this._rts,this.totalTime(this.parent&&!this.parent.smoothChildTiming?this.rawTime():this._tTime||this._pTime,this.progress()===1&&Math.abs(this._zTime)!==Z&&(this._tTime-=Z)))),this):this._ps},t.startTime=function(r){if(arguments.length){this._start=r;var i=this.parent||this._dp;return i&&(i._sort||!this.parent)&&He(i,this,r-this._delay),this}return this._start},t.endTime=function(r){return this._start+(te(r)?this.totalDuration():this.duration())/Math.abs(this._ts||1)},t.rawTime=function(r){var i=this.parent||this._dp;return i?r&&(!this._ts||this._repeat&&this._time&&this.totalProgress()<1)?this._tTime%(this._dur+this._rDelay):this._ts?ks(i.rawTime(r),this):this._tTime:this._tTime},t.revert=function(r){r===void 0&&(r=vm);var i=Rt;return Rt=r,(this._initted||this._startAt)&&(this.timeline&&this.timeline.revert(r),this.totalTime(-.01,r.suppressEvents)),this.data!=="nested"&&r.kill!==!1&&this.kill(),Rt=i,this},t.globalTime=function(r){for(var i=this,o=arguments.length?r:i.rawTime();i;)o=i._start+o/(Math.abs(i._ts)||1),i=i._dp;return!this.parent&&this._sat?this._sat.globalTime(r):o},t.repeat=function(r){return arguments.length?(this._repeat=r===1/0?-2:r,pd(this)):this._repeat===-2?1/0:this._repeat},t.repeatDelay=function(r){if(arguments.length){var i=this._time;return this._rDelay=r,pd(this),i?this.time(i):this}return this._rDelay},t.yoyo=function(r){return arguments.length?(this._yoyo=r,this):this._yoyo},t.seek=function(r,i){return this.totalTime(xe(this,r),te(i))},t.restart=function(r,i){return this.play().totalTime(r?-this._delay:0,te(i))},t.play=function(r,i){return r!=null&&this.seek(r,i),this.reversed(!1).paused(!1)},t.reverse=function(r,i){return r!=null&&this.seek(r||this.totalDuration(),i),this.reversed(!0).paused(!1)},t.pause=function(r,i){return r!=null&&this.seek(r,i),this.paused(!0)},t.resume=function(){return this.paused(!1)},t.reversed=function(r){return arguments.length?(!!r!==this.reversed()&&this.timeScale(-this._rts||(r?-Z:0)),this):this._rts<0},t.invalidate=function(){return this._initted=this._act=0,this._zTime=-Z,this},t.isActive=function(){var r=this.parent||this._dp,i=this._start,o;return!!(!r||this._ts&&this._initted&&r.isActive()&&(o=r.rawTime(!0))>=i&&o<this.endTime(!0)-Z)},t.eventCallback=function(r,i,o){var s=this.vars;return arguments.length>1?(i?(s[r]=i,o&&(s[r+"Params"]=o),r==="onUpdate"&&(this._onUpdate=i)):delete s[r],this):s[r]},t.then=function(r){var i=this;return new Promise(function(o){var s=ht(r)?r:O5,a=function(){var u=i.then;i.then=null,ht(s)&&(s=s(i))&&(s.then||s===i)&&(i.then=u),o(s),i.then=u};i._initted&&i.totalProgress()===1&&i._ts>=0||!i._tTime&&i._ts<0?a():i._prom=a})},t.kill=function(){J1(this)},e}();Ae(ji.prototype,{_time:0,_start:0,_end:0,_tTime:0,_tDur:0,_dirty:0,_repeat:0,_yoyo:!1,parent:null,_initted:!1,_rDelay:0,_ts:1,_dp:0,ratio:0,_zTime:-Z,_prom:0,_ps:!1,_rts:1});var zt=function(e){_5(t,e);function t(r,i){var o;return r===void 0&&(r={}),o=e.call(this,r)||this,o.labels={},o.smoothChildTiming=!!r.smoothChildTiming,o.autoRemoveChildren=!!r.autoRemoveChildren,o._sort=te(r.sortChildren),st&&He(r.parent||st,on(o),i),r.reversed&&o.reverse(),r.paused&&o.paused(!0),r.scrollTrigger&&R5(on(o),r.scrollTrigger),o}var n=t.prototype;return n.to=function(i,o,s){return di(0,arguments,this),this},n.from=function(i,o,s){return di(1,arguments,this),this},n.fromTo=function(i,o,s,a){return di(2,arguments,this),this},n.set=function(i,o,s){return o.duration=0,o.parent=this,fi(o).repeatDelay||(o.repeat=0),o.immediateRender=!!o.immediateRender,new gt(i,o,xe(this,s),1),this},n.call=function(i,o,s){return He(this,gt.delayedCall(0,i,o),s)},n.staggerTo=function(i,o,s,a,l,u,c){return s.duration=o,s.stagger=s.stagger||a,s.onComplete=u,s.onCompleteParams=c,s.parent=this,new gt(i,s,xe(this,l)),this},n.staggerFrom=function(i,o,s,a,l,u,c){return s.runBackwards=1,fi(s).immediateRender=te(s.immediateRender),this.staggerTo(i,o,s,a,l,u,c)},n.staggerFromTo=function(i,o,s,a,l,u,c,f){return a.startAt=s,fi(a).immediateRender=te(a.immediateRender),this.staggerTo(i,o,a,l,u,c,f)},n.render=function(i,o,s){var a=this._time,l=this._dirty?this.totalDuration():this._tDur,u=this._dur,c=i<=0?0:Tt(i),f=this._zTime<0!=i<0&&(this._initted||!u),d,p,v,h,_,m,g,y,w,x,S,T;if(this!==st&&c>l&&i>=0&&(c=l),c!==this._tTime||s||f){if(a!==this._time&&u&&(c+=this._time-a,i+=this._time-a),d=c,w=this._start,y=this._ts,m=!y,f&&(u||(a=this._zTime),(i||!o)&&(this._zTime=i)),this._repeat){if(S=this._yoyo,_=u+this._rDelay,this._repeat<-1&&i<0)return this.totalTime(_*100+i,o,s);if(d=Tt(c%_),c===l?(h=this._repeat,d=u):(h=~~(c/_),h&&h===c/_&&(d=u,h--),d>u&&(d=u)),x=C1(this._tTime,_),!a&&this._tTime&&x!==h&&this._tTime-x*_-this._dur<=0&&(x=h),S&&h&1&&(d=u-d,T=1),h!==x&&!this._lock){var b=S&&x&1,C=b===(S&&h&1);if(h<x&&(b=!b),a=b?0:c%u?u:c,this._lock=1,this.render(a||(T?0:Tt(h*_)),o,!u)._lock=0,this._tTime=c,!o&&this.parent&&de(this,"onRepeat"),this.vars.repeatRefresh&&!T&&(this.invalidate()._lock=1),a&&a!==this._time||m!==!this._ts||this.vars.onRepeat&&!this.parent&&!this._act)return this;if(u=this._dur,l=this._tDur,C&&(this._lock=2,a=b?u:-1e-4,this.render(a,!0),this.vars.repeatRefresh&&!T&&this.invalidate()),this._lock=0,!this._ts&&!m)return this;Q5(this,T)}}if(this._hasPause&&!this._forcing&&this._lock<2&&(g=Cm(this,Tt(a),Tt(d)),g&&(c-=d-(d=g._start))),this._tTime=c,this._time=d,this._act=!y,this._initted||(this._onUpdate=this.vars.onUpdate,this._initted=1,this._zTime=i,a=0),!a&&d&&!o&&!h&&(de(this,"onStart"),this._tTime!==c))return this;if(d>=a&&i>=0)for(p=this._first;p;){if(v=p._next,(p._act||d>=p._start)&&p._ts&&g!==p){if(p.parent!==this)return this.render(i,o,s);if(p.render(p._ts>0?(d-p._start)*p._ts:(p._dirty?p.totalDuration():p._tDur)+(d-p._start)*p._ts,o,s),d!==this._time||!this._ts&&!m){g=0,v&&(c+=this._zTime=-Z);break}}p=v}else{p=this._last;for(var E=i<0?i:d;p;){if(v=p._prev,(p._act||E<=p._end)&&p._ts&&g!==p){if(p.parent!==this)return this.render(i,o,s);if(p.render(p._ts>0?(E-p._start)*p._ts:(p._dirty?p.totalDuration():p._tDur)+(E-p._start)*p._ts,o,s||Rt&&(p._initted||p._startAt)),d!==this._time||!this._ts&&!m){g=0,v&&(c+=this._zTime=E?-Z:Z);break}}p=v}}if(g&&!o&&(this.pause(),g.render(d>=a?0:-Z)._zTime=d>=a?1:-1,this._ts))return this._start=w,aa(this),this.render(i,o,s);this._onUpdate&&!o&&de(this,"onUpdate",!0),(c===l&&this._tTime>=this.totalDuration()||!c&&a)&&(w===this._start||Math.abs(y)!==Math.abs(this._ts))&&(this._lock||((i||!u)&&(c===l&&this._ts>0||!c&&this._ts<0)&&Wn(this,1),!o&&!(i<0&&!a)&&(c||a||!l)&&(de(this,c===l&&i>=0?"onComplete":"onReverseComplete",!0),this._prom&&!(c<l&&this.timeScale()>0)&&this._prom())))}return this},n.add=function(i,o){var s=this;if(vn(o)||(o=xe(this,o,i)),!(i instanceof ji)){if(It(i))return i.forEach(function(a){return s.add(a,o)}),this;if(Ct(i))return this.addLabel(i,o);if(ht(i))i=gt.delayedCall(0,i);else return this}return this!==i?He(this,i,o):this},n.getChildren=function(i,o,s,a){i===void 0&&(i=!0),o===void 0&&(o=!0),s===void 0&&(s=!0),a===void 0&&(a=-Ce);for(var l=[],u=this._first;u;)u._start>=a&&(u instanceof gt?o&&l.push(u):(s&&l.push(u),i&&l.push.apply(l,u.getChildren(!0,o,s)))),u=u._next;return l},n.getById=function(i){for(var o=this.getChildren(1,1,1),s=o.length;s--;)if(o[s].vars.id===i)return o[s]},n.remove=function(i){return Ct(i)?this.removeLabel(i):ht(i)?this.killTweensOf(i):(sa(this,i),i===this._recent&&(this._recent=this._last),xr(this))},n.totalTime=function(i,o){return arguments.length?(this._forcing=1,!this._dp&&this._ts&&(this._start=Tt(ae.time-(this._ts>0?i/this._ts:(this.totalDuration()-i)/-this._ts))),e.prototype.totalTime.call(this,i,o),this._forcing=0,this):this._tTime},n.addLabel=function(i,o){return this.labels[i]=xe(this,o),this},n.removeLabel=function(i){return delete this.labels[i],this},n.addPause=function(i,o,s){var a=gt.delayedCall(0,o||Fi,s);return a.data="isPause",this._hasPause=1,He(this,a,xe(this,i))},n.removePause=function(i){var o=this._first;for(i=xe(this,i);o;)o._start===i&&o.data==="isPause"&&Wn(o),o=o._next},n.killTweensOf=function(i,o,s){for(var a=this.getTweensOf(i,s),l=a.length;l--;)An!==a[l]&&a[l].kill(i,o);return this},n.getTweensOf=function(i,o){for(var s=[],a=Ee(i),l=this._first,u=vn(o),c;l;)l instanceof gt?ym(l._targets,a)&&(u?(!An||l._initted&&l._ts)&&l.globalTime(0)<=o&&l.globalTime(l.totalDuration())>o:!o||l.isActive())&&s.push(l):(c=l.getTweensOf(a,o)).length&&s.push.apply(s,c),l=l._next;return s},n.tweenTo=function(i,o){o=o||{};var s=this,a=xe(s,i),l=o,u=l.startAt,c=l.onStart,f=l.onStartParams,d=l.immediateRender,p,v=gt.to(s,Ae({ease:o.ease||"none",lazy:!1,immediateRender:!1,time:a,overwrite:"auto",duration:o.duration||Math.abs((a-(u&&"time"in u?u.time:s._time))/s.timeScale())||Z,onStart:function(){if(s.pause(),!p){var _=o.duration||Math.abs((a-(u&&"time"in u?u.time:s._time))/s.timeScale());v._dur!==_&&E1(v,_,0,1).render(v._time,!0,!0),p=1}c&&c.apply(v,f||[])}},o));return d?v.render(0):v},n.tweenFromTo=function(i,o,s){return this.tweenTo(o,Ae({startAt:{time:xe(this,i)}},s))},n.recent=function(){return this._recent},n.nextLabel=function(i){return i===void 0&&(i=this._time),md(this,xe(this,i))},n.previousLabel=function(i){return i===void 0&&(i=this._time),md(this,xe(this,i),1)},n.currentLabel=function(i){return arguments.length?this.seek(i,!0):this.previousLabel(this._time+Z)},n.shiftChildren=function(i,o,s){s===void 0&&(s=0);for(var a=this._first,l=this.labels,u;a;)a._start>=s&&(a._start+=i,a._end+=i),a=a._next;if(o)for(u in l)l[u]>=s&&(l[u]+=i);return xr(this)},n.invalidate=function(i){var o=this._first;for(this._lock=0;o;)o.invalidate(i),o=o._next;return e.prototype.invalidate.call(this,i)},n.clear=function(i){i===void 0&&(i=!0);for(var o=this._first,s;o;)s=o._next,this.remove(o),o=s;return this._dp&&(this._time=this._tTime=this._pTime=0),i&&(this.labels={}),xr(this)},n.totalDuration=function(i){var o=0,s=this,a=s._last,l=Ce,u,c,f;if(arguments.length)return s.timeScale((s._repeat<0?s.duration():s.totalDuration())/(s.reversed()?-i:i));if(s._dirty){for(f=s.parent;a;)u=a._prev,a._dirty&&a.totalDuration(),c=a._start,c>l&&s._sort&&a._ts&&!s._lock?(s._lock=1,He(s,a,c-a._delay,1)._lock=0):l=c,c<0&&a._ts&&(o-=c,(!f&&!s._dp||f&&f.smoothChildTiming)&&(s._start+=c/s._ts,s._time-=c,s._tTime-=c),s.shiftChildren(-c,!1,-1/0),l=0),a._end>o&&a._ts&&(o=a._end),a=u;E1(s,s===st&&s._time>o?s._time:o,1,1),s._dirty=0}return s._tDur},t.updateRoot=function(i){if(st._ts&&(A5(st,ks(i,st)),P5=ae.frame),ae.frame>=fd){fd+=pe.autoSleep||120;var o=st._first;if((!o||!o._ts)&&pe.autoSleep&&ae._listeners.length<2){for(;o&&!o._ts;)o=o._next;o||ae.sleep()}}},t}(ji);Ae(zt.prototype,{_lock:0,_hasPause:0,_forcing:0});var zm=function(t,n,r,i,o,s,a){var l=new ne(this._pt,t,n,0,1,i6,null,o),u=0,c=0,f,d,p,v,h,_,m,g;for(l.b=r,l.e=i,r+="",i+="",(m=~i.indexOf("random("))&&(i=Bi(i)),s&&(g=[r,i],s(g,t,n),r=g[0],i=g[1]),d=r.match(Za)||[];f=Za.exec(i);)v=f[0],h=i.substring(u,f.index),p?p=(p+1)%5:h.substr(-5)==="rgba("&&(p=1),v!==d[c++]&&(_=parseFloat(d[c-1])||0,l._pt={_next:l._pt,p:h||c===1?h:",",s:_,c:v.charAt(1)==="="?h1(_,v)-_:parseFloat(v)-_,m:p&&p<4?Math.round:0},u=Za.lastIndex);return l.c=u<i.length?i.substring(u,i.length):"",l.fp=a,(T5.test(i)||m)&&(l.e=0),this._pt=l,l},n0=function(t,n,r,i,o,s,a,l,u,c){ht(i)&&(i=i(o||0,t,s));var f=t[n],d=r!=="get"?r:ht(f)?u?t[n.indexOf("set")||!ht(t["get"+n.substr(3)])?n:"get"+n.substr(3)](u):t[n]():f,p=ht(f)?u?Gm:n6:i0,v;if(Ct(i)&&(~i.indexOf("random(")&&(i=Bi(i)),i.charAt(1)==="="&&(v=h1(d,i)+(Nt(d)||0),(v||v===0)&&(i=v))),!c||d!==i||bu)return!isNaN(d*i)&&i!==""?(v=new ne(this._pt,t,n,+d||0,i-(d||0),typeof f=="boolean"?Ym:r6,0,p),u&&(v.fp=u),a&&v.modifier(a,this,t),this._pt=v):(!f&&!(n in t)&&Zc(n,i),zm.call(this,t,n,d,i,p,l||pe.stringFilter,u))},jm=function(t,n,r,i,o){if(ht(t)&&(t=hi(t,o,n,r,i)),!Ze(t)||t.style&&t.nodeType||It(t)||x5(t))return Ct(t)?hi(t,o,n,r,i):t;var s={},a;for(a in t)s[a]=hi(t[a],o,n,r,i);return s},J5=function(t,n,r,i,o,s){var a,l,u,c;if(se[t]&&(a=new se[t]).init(o,a.rawVars?n[t]:jm(n[t],i,o,s,r),r,i,s)!==!1&&(r._pt=l=new ne(r._pt,o,t,0,1,a.render,a,0,a.priority),r!==e1))for(u=r._ptLookup[r._targets.indexOf(o)],c=a._props.length;c--;)u[a._props[c]]=l;return a},An,bu,r0=function e(t,n,r){var i=t.vars,o=i.ease,s=i.startAt,a=i.immediateRender,l=i.lazy,u=i.onUpdate,c=i.runBackwards,f=i.yoyoEase,d=i.keyframes,p=i.autoRevert,v=t._dur,h=t._startAt,_=t._targets,m=t.parent,g=m&&m.data==="nested"?m.vars.targets:_,y=t._overwrite==="auto"&&!Yc,w=t.timeline,x,S,T,b,C,E,L,O,B,G,I,F,z;if(w&&(!d||!o)&&(o="none"),t._ease=Sr(o,b1.ease),t._yEase=f?X5(Sr(f===!0?o:f,b1.ease)):0,f&&t._yoyo&&!t._repeat&&(f=t._yEase,t._yEase=t._ease,t._ease=f),t._from=!w&&!!i.runBackwards,!w||d&&!i.stagger){if(O=_[0]?wr(_[0]).harness:0,F=O&&i[O.prop],x=Es(i,Jc),h&&(h._zTime<0&&h.progress(1),n<0&&c&&a&&!p?h.render(-1,!0):h.revert(c&&v?Yo:gm),h._lazy=0),s){if(Wn(t._startAt=gt.set(_,Ae({data:"isStart",overwrite:!1,parent:m,immediateRender:!0,lazy:!h&&te(l),startAt:null,delay:0,onUpdate:u&&function(){return de(t,"onUpdate")},stagger:0},s))),t._startAt._dp=0,t._startAt._sat=t,n<0&&(Rt||!a&&!p)&&t._startAt.revert(Yo),a&&v&&n<=0&&r<=0){n&&(t._zTime=n);return}}else if(c&&v&&!h){if(n&&(a=!1),T=Ae({overwrite:!1,data:"isFromStart",lazy:a&&!h&&te(l),immediateRender:a,stagger:0,parent:m},x),F&&(T[O.prop]=F),Wn(t._startAt=gt.set(_,T)),t._startAt._dp=0,t._startAt._sat=t,n<0&&(Rt?t._startAt.revert(Yo):t._startAt.render(-1,!0)),t._zTime=n,!a)e(t._startAt,Z,Z);else if(!n)return}for(t._pt=t._ptCache=0,l=v&&te(l)||l&&!v,S=0;S<_.length;S++){if(C=_[S],L=C._gsap||e0(_)[S]._gsap,t._ptLookup[S]=G={},yu[L.id]&&jn.length&&Cs(),I=g===_?S:g.indexOf(C),O&&(B=new O).init(C,F||x,t,I,g)!==!1&&(t._pt=b=new ne(t._pt,C,B.name,0,1,B.render,B,0,B.priority),B._props.forEach(function(P){G[P]=b}),B.priority&&(E=1)),!O||F)for(T in x)se[T]&&(B=J5(T,x,t,I,C,g))?B.priority&&(E=1):G[T]=b=n0.call(t,C,T,"get",x[T],I,g,0,i.stringFilter);t._op&&t._op[S]&&t.kill(C,t._op[S]),y&&t._pt&&(An=t,st.killTweensOf(C,G,t.globalTime(n)),z=!t.parent,An=0),t._pt&&l&&(yu[L.id]=1)}E&&o6(t),t._onInit&&t._onInit(t)}t._onUpdate=u,t._initted=(!t._op||t._pt)&&!z,d&&n<=0&&w.render(Ce,!0,!0)},Um=function(t,n,r,i,o,s,a,l){var u=(t._pt&&t._ptCache||(t._ptCache={}))[n],c,f,d,p;if(!u)for(u=t._ptCache[n]=[],d=t._ptLookup,p=t._targets.length;p--;){if(c=d[p][n],c&&c.d&&c.d._pt)for(c=c.d._pt;c&&c.p!==n&&c.fp!==n;)c=c._next;if(!c)return bu=1,t.vars[n]="+=0",r0(t,a),bu=0,l?qi(n+" not eligible for reset"):1;u.push(c)}for(p=u.length;p--;)f=u[p],c=f._pt||f,c.s=(i||i===0)&&!o?i:c.s+(i||0)+s*c.c,c.c=r-c.s,f.e&&(f.e=pt(r)+Nt(f.e)),f.b&&(f.b=c.s+Nt(f.b))},$m=function(t,n){var r=t[0]?wr(t[0]).harness:0,i=r&&r.aliases,o,s,a,l;if(!i)return n;o=Dr({},n);for(s in i)if(s in o)for(l=i[s].split(","),a=l.length;a--;)o[l[a]]=o[s];return o},Hm=function(t,n,r,i){var o=n.ease||i||"power1.inOut",s,a;if(It(n))a=r[t]||(r[t]=[]),n.forEach(function(l,u){return a.push({t:u/(n.length-1)*100,v:l,e:o})});else for(s in n)a=r[s]||(r[s]=[]),s==="ease"||a.push({t:parseFloat(t),v:n[s],e:o})},hi=function(t,n,r,i,o){return ht(t)?t.call(n,r,i,o):Ct(t)&&~t.indexOf("random(")?Bi(t):t},t6=t0+"repeat,repeatDelay,yoyo,repeatRefresh,yoyoEase,autoRevert",e6={};ee(t6+",id,stagger,delay,duration,paused,scrollTrigger",function(e){return e6[e]=1});var gt=function(e){_5(t,e);function t(r,i,o,s){var a;typeof i=="number"&&(o.duration=i,i=o,o=null),a=e.call(this,s?i:fi(i))||this;var l=a.vars,u=l.duration,c=l.delay,f=l.immediateRender,d=l.stagger,p=l.overwrite,v=l.keyframes,h=l.defaults,_=l.scrollTrigger,m=l.yoyoEase,g=i.parent||st,y=(It(r)||x5(r)?vn(r[0]):"length"in i)?[r]:Ee(r),w,x,S,T,b,C,E,L;if(a._targets=y.length?e0(y):qi("GSAP target "+r+" not found. https://gsap.com",!pe.nullTargetWarn)||[],a._ptLookup=[],a._overwrite=p,v||d||Do(u)||Do(c)){if(i=a.vars,w=a.timeline=new zt({data:"nested",defaults:h||{},targets:g&&g.data==="nested"?g.vars.targets:y}),w.kill(),w.parent=w._dp=on(a),w._start=0,d||Do(u)||Do(c)){if(T=y.length,E=d&&F5(d),Ze(d))for(b in d)~t6.indexOf(b)&&(L||(L={}),L[b]=d[b]);for(x=0;x<T;x++)S=Es(i,e6),S.stagger=0,m&&(S.yoyoEase=m),L&&Dr(S,L),C=y[x],S.duration=+hi(u,on(a),x,C,y),S.delay=(+hi(c,on(a),x,C,y)||0)-a._delay,!d&&T===1&&S.delay&&(a._delay=c=S.delay,a._start+=c,S.delay=0),w.to(C,S,E?E(x,C,y):0),w._ease=H.none;w.duration()?u=c=0:a.timeline=0}else if(v){fi(Ae(w.vars.defaults,{ease:"none"})),w._ease=Sr(v.ease||i.ease||"none");var O=0,B,G,I;if(It(v))v.forEach(function(F){return w.to(y,F,">")}),w.duration();else{S={};for(b in v)b==="ease"||b==="easeEach"||Hm(b,v[b],S,v.easeEach);for(b in S)for(B=S[b].sort(function(F,z){return F.t-z.t}),O=0,x=0;x<B.length;x++)G=B[x],I={ease:G.e,duration:(G.t-(x?B[x-1].t:0))/100*u},I[b]=G.v,w.to(y,I,O),O+=I.duration;w.duration()<u&&w.to({},{duration:u-w.duration()})}}u||a.duration(u=w.duration())}else a.timeline=0;return p===!0&&!Yc&&(An=on(a),st.killTweensOf(y),An=0),He(g,on(a),o),i.reversed&&a.reverse(),i.paused&&a.paused(!0),(f||!u&&!v&&a._start===Tt(g._time)&&te(f)&&Sm(on(a))&&g.data!=="nested")&&(a._tTime=-Z,a.render(Math.max(0,-c)||0)),_&&R5(on(a),_),a}var n=t.prototype;return n.render=function(i,o,s){var a=this._time,l=this._tDur,u=this._dur,c=i<0,f=i>l-Z&&!c?l:i<Z?0:i,d,p,v,h,_,m,g,y,w;if(!u)bm(this,i,o,s);else if(f!==this._tTime||!i||s||!this._initted&&this._tTime||this._startAt&&this._zTime<0!==c){if(d=f,y=this.timeline,this._repeat){if(h=u+this._rDelay,this._repeat<-1&&c)return this.totalTime(h*100+i,o,s);if(d=Tt(f%h),f===l?(v=this._repeat,d=u):(v=~~(f/h),v&&v===Tt(f/h)&&(d=u,v--),d>u&&(d=u)),m=this._yoyo&&v&1,m&&(w=this._yEase,d=u-d),_=C1(this._tTime,h),d===a&&!s&&this._initted&&v===_)return this._tTime=f,this;v!==_&&(y&&this._yEase&&Q5(y,m),this.vars.repeatRefresh&&!m&&!this._lock&&this._time!==h&&this._initted&&(this._lock=s=1,this.render(Tt(h*v),!0).invalidate()._lock=0))}if(!this._initted){if(I5(this,c?i:d,s,o,f))return this._tTime=0,this;if(a!==this._time&&!(s&&this.vars.repeatRefresh&&v!==_))return this;if(u!==this._dur)return this.render(i,o,s)}if(this._tTime=f,this._time=d,!this._act&&this._ts&&(this._act=1,this._lazy=0),this.ratio=g=(w||this._ease)(d/u),this._from&&(this.ratio=g=1-g),d&&!a&&!o&&!v&&(de(this,"onStart"),this._tTime!==f))return this;for(p=this._pt;p;)p.r(g,p.d),p=p._next;y&&y.render(i<0?i:y._dur*y._ease(d/this._dur),o,s)||this._startAt&&(this._zTime=i),this._onUpdate&&!o&&(c&&_u(this,i,o,s),de(this,"onUpdate")),this._repeat&&v!==_&&this.vars.onRepeat&&!o&&this.parent&&de(this,"onRepeat"),(f===this._tDur||!f)&&this._tTime===f&&(c&&!this._onUpdate&&_u(this,i,!0,!0),(i||!u)&&(f===this._tDur&&this._ts>0||!f&&this._ts<0)&&Wn(this,1),!o&&!(c&&!a)&&(f||a||m)&&(de(this,f===l?"onComplete":"onReverseComplete",!0),this._prom&&!(f<l&&this.timeScale()>0)&&this._prom()))}return this},n.targets=function(){return this._targets},n.invalidate=function(i){return(!i||!this.vars.runBackwards)&&(this._startAt=0),this._pt=this._op=this._onUpdate=this._lazy=this.ratio=0,this._ptLookup=[],this.timeline&&this.timeline.invalidate(i),e.prototype.invalidate.call(this,i)},n.resetTo=function(i,o,s,a,l){zi||ae.wake(),this._ts||this.play();var u=Math.min(this._dur,(this._dp._time-this._start)*this._ts),c;return this._initted||r0(this,u),c=this._ease(u/this._dur),Um(this,i,o,s,a,c,u,l)?this.resetTo(i,o,s,a,1):(la(this,0),this.parent||M5(this._dp,this,"_first","_last",this._dp._sort?"_start":0),this.render(0))},n.kill=function(i,o){if(o===void 0&&(o="all"),!i&&(!o||o==="all"))return this._lazy=this._pt=0,this.parent?J1(this):this;if(this.timeline){var s=this.timeline.totalDuration();return this.timeline.killTweensOf(i,o,An&&An.vars.overwrite!==!0)._first||J1(this),this.parent&&s!==this.timeline.totalDuration()&&E1(this,this._dur*this.timeline._tDur/s,0,1),this}var a=this._targets,l=i?Ee(i):a,u=this._ptLookup,c=this._pt,f,d,p,v,h,_,m;if((!o||o==="all")&&wm(a,l))return o==="all"&&(this._pt=0),J1(this);for(f=this._op=this._op||[],o!=="all"&&(Ct(o)&&(h={},ee(o,function(g){return h[g]=1}),o=h),o=$m(a,o)),m=a.length;m--;)if(~l.indexOf(a[m])){d=u[m],o==="all"?(f[m]=o,v=d,p={}):(p=f[m]=f[m]||{},v=o);for(h in v)_=d&&d[h],_&&((!("kill"in _.d)||_.d.kill(h)===!0)&&sa(this,_,"_pt"),delete d[h]),p!=="all"&&(p[h]=1)}return this._initted&&!this._pt&&c&&J1(this),this},t.to=function(i,o){return new t(i,o,arguments[2])},t.from=function(i,o){return di(1,arguments)},t.delayedCall=function(i,o,s,a){return new t(o,0,{immediateRender:!1,lazy:!1,overwrite:!1,delay:i,onComplete:o,onReverseComplete:o,onCompleteParams:s,onReverseCompleteParams:s,callbackScope:a})},t.fromTo=function(i,o,s){return di(2,arguments)},t.set=function(i,o){return o.duration=0,o.repeatDelay||(o.repeat=0),new t(i,o)},t.killTweensOf=function(i,o,s){return st.killTweensOf(i,o,s)},t}(ji);Ae(gt.prototype,{_targets:[],_lazy:0,_startAt:0,_op:0,_onInit:0});ee("staggerTo,staggerFrom,staggerFromTo",function(e){gt[e]=function(){var t=new zt,n=xu.call(arguments,0);return n.splice(e==="staggerFromTo"?5:4,0,0),t[e].apply(t,n)}});var i0=function(t,n,r){return t[n]=r},n6=function(t,n,r){return t[n](r)},Gm=function(t,n,r,i){return t[n](i.fp,r)},Wm=function(t,n,r){return t.setAttribute(n,r)},o0=function(t,n){return ht(t[n])?n6:Xc(t[n])&&t.setAttribute?Wm:i0},r6=function(t,n){return n.set(n.t,n.p,Math.round((n.s+n.c*t)*1e6)/1e6,n)},Ym=function(t,n){return n.set(n.t,n.p,!!(n.s+n.c*t),n)},i6=function(t,n){var r=n._pt,i="";if(!t&&n.b)i=n.b;else if(t===1&&n.e)i=n.e;else{for(;r;)i=r.p+(r.m?r.m(r.s+r.c*t):Math.round((r.s+r.c*t)*1e4)/1e4)+i,r=r._next;i+=n.c}n.set(n.t,n.p,i,n)},s0=function(t,n){for(var r=n._pt;r;)r.r(t,r.d),r=r._next},Xm=function(t,n,r,i){for(var o=this._pt,s;o;)s=o._next,o.p===i&&o.modifier(t,n,r),o=s},Qm=function(t){for(var n=this._pt,r,i;n;)i=n._next,n.p===t&&!n.op||n.op===t?sa(this,n,"_pt"):n.dep||(r=1),n=i;return!r},Km=function(t,n,r,i){i.mSet(t,n,i.m.call(i.tween,r,i.mt),i)},o6=function(t){for(var n=t._pt,r,i,o,s;n;){for(r=n._next,i=o;i&&i.pr>n.pr;)i=i._next;(n._prev=i?i._prev:s)?n._prev._next=n:o=n,(n._next=i)?i._prev=n:s=n,n=r}t._pt=o},ne=function(){function e(n,r,i,o,s,a,l,u,c){this.t=r,this.s=o,this.c=s,this.p=i,this.r=a||r6,this.d=l||this,this.set=u||i0,this.pr=c||0,this._next=n,n&&(n._prev=this)}var t=e.prototype;return t.modifier=function(r,i,o){this.mSet=this.mSet||this.set,this.set=Km,this.m=r,this.mt=o,this.tween=i},e}();ee(t0+"parent,duration,ease,delay,overwrite,runBackwards,startAt,yoyo,immediateRender,repeat,repeatDelay,data,paused,reversed,lazy,callbackScope,stringFilter,id,yoyoEase,stagger,inherit,repeatRefresh,keyframes,autoRevert,scrollTrigger",function(e){return Jc[e]=1});ve.TweenMax=ve.TweenLite=gt;ve.TimelineLite=ve.TimelineMax=zt;st=new zt({sortChildren:!1,defaults:b1,autoRemoveChildren:!0,id:"root",smoothChildTiming:!0});pe.stringFilter=Y5;var Tr=[],Qo={},Zm=[],vd=0,Jm=0,rl=function(t){return(Qo[t]||Zm).map(function(n){return n()})},Cu=function(){var t=Date.now(),n=[];t-vd>2&&(rl("matchMediaInit"),Tr.forEach(function(r){var i=r.queries,o=r.conditions,s,a,l,u;for(a in i)s=Ue.matchMedia(i[a]).matches,s&&(l=1),s!==o[a]&&(o[a]=s,u=1);u&&(r.revert(),l&&n.push(r))}),rl("matchMediaRevert"),n.forEach(function(r){return r.onMatch(r,function(i){return r.add(null,i)})}),vd=t,rl("matchMedia"))},s6=function(){function e(n,r){this.selector=r&&Su(r),this.data=[],this._r=[],this.isReverted=!1,this.id=Jm++,n&&this.add(n)}var t=e.prototype;return t.add=function(r,i,o){ht(r)&&(o=i,i=r,r=ht);var s=this,a=function(){var u=it,c=s.selector,f;return u&&u!==s&&u.data.push(s),o&&(s.selector=Su(o)),it=s,f=i.apply(s,arguments),ht(f)&&s._r.push(f),it=u,s.selector=c,s.isReverted=!1,f};return s.last=a,r===ht?a(s,function(l){return s.add(null,l)}):r?s[r]=a:a},t.ignore=function(r){var i=it;it=null,r(this),it=i},t.getTweens=function(){var r=[];return this.data.forEach(function(i){return i instanceof e?r.push.apply(r,i.getTweens()):i instanceof gt&&!(i.parent&&i.parent.data==="nested")&&r.push(i)}),r},t.clear=function(){this._r.length=this.data.length=0},t.kill=function(r,i){var o=this;if(r?function(){for(var a=o.getTweens(),l=o.data.length,u;l--;)u=o.data[l],u.data==="isFlip"&&(u.revert(),u.getChildren(!0,!0,!1).forEach(function(c){return a.splice(a.indexOf(c),1)}));for(a.map(function(c){return{g:c._dur||c._delay||c._sat&&!c._sat.vars.immediateRender?c.globalTime(0):-1/0,t:c}}).sort(function(c,f){return f.g-c.g||-1/0}).forEach(function(c){return c.t.revert(r)}),l=o.data.length;l--;)u=o.data[l],u instanceof zt?u.data!=="nested"&&(u.scrollTrigger&&u.scrollTrigger.revert(),u.kill()):!(u instanceof gt)&&u.revert&&u.revert(r);o._r.forEach(function(c){return c(r,o)}),o.isReverted=!0}():this.data.forEach(function(a){return a.kill&&a.kill()}),this.clear(),i)for(var s=Tr.length;s--;)Tr[s].id===this.id&&Tr.splice(s,1)},t.revert=function(r){this.kill(r||{})},e}(),t4=function(){function e(n){this.contexts=[],this.scope=n,it&&it.data.push(this)}var t=e.prototype;return t.add=function(r,i,o){Ze(r)||(r={matches:r});var s=new s6(0,o||this.scope),a=s.conditions={},l,u,c;it&&!s.selector&&(s.selector=it.selector),this.contexts.push(s),i=s.add("onMatch",i),s.queries=r;for(u in r)u==="all"?c=1:(l=Ue.matchMedia(r[u]),l&&(Tr.indexOf(s)<0&&Tr.push(s),(a[u]=l.matches)&&(c=1),l.addListener?l.addListener(Cu):l.addEventListener("change",Cu)));return c&&i(s,function(f){return s.add(null,f)}),this},t.revert=function(r){this.kill(r||{})},t.kill=function(r){this.contexts.forEach(function(i){return i.kill(r,!0)})},e}(),Ps={registerPlugin:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];n.forEach(function(i){return H5(i)})},timeline:function(t){return new zt(t)},getTweensOf:function(t,n){return st.getTweensOf(t,n)},getProperty:function(t,n,r,i){Ct(t)&&(t=Ee(t)[0]);var o=wr(t||{}).get,s=r?O5:L5;return r==="native"&&(r=""),t&&(n?s((se[n]&&se[n].get||o)(t,n,r,i)):function(a,l,u){return s((se[a]&&se[a].get||o)(t,a,l,u))})},quickSetter:function(t,n,r){if(t=Ee(t),t.length>1){var i=t.map(function(c){return ie.quickSetter(c,n,r)}),o=i.length;return function(c){for(var f=o;f--;)i[f](c)}}t=t[0]||{};var s=se[n],a=wr(t),l=a.harness&&(a.harness.aliases||{})[n]||n,u=s?function(c){var f=new s;e1._pt=0,f.init(t,r?c+r:c,e1,0,[t]),f.render(1,f),e1._pt&&s0(1,e1)}:a.set(t,l);return s?u:function(c){return u(t,l,r?c+r:c,a,1)}},quickTo:function(t,n,r){var i,o=ie.to(t,Dr((i={},i[n]="+=0.1",i.paused=!0,i),r||{})),s=function(l,u,c){return o.resetTo(n,l,u,c)};return s.tween=o,s},isTweening:function(t){return st.getTweensOf(t,!0).length>0},defaults:function(t){return t&&t.ease&&(t.ease=Sr(t.ease,b1.ease)),dd(b1,t||{})},config:function(t){return dd(pe,t||{})},registerEffect:function(t){var n=t.name,r=t.effect,i=t.plugins,o=t.defaults,s=t.extendTimeline;(i||"").split(",").forEach(function(a){return a&&!se[a]&&!ve[a]&&qi(n+" effect requires "+a+" plugin.")}),Ja[n]=function(a,l,u){return r(Ee(a),Ae(l||{},o),u)},s&&(zt.prototype[n]=function(a,l,u){return this.add(Ja[n](a,Ze(l)?l:(u=l)&&{},this),u)})},registerEase:function(t,n){H[t]=Sr(n)},parseEase:function(t,n){return arguments.length?Sr(t,n):H},getById:function(t){return st.getById(t)},exportRoot:function(t,n){t===void 0&&(t={});var r=new zt(t),i,o;for(r.smoothChildTiming=te(t.smoothChildTiming),st.remove(r),r._dp=0,r._time=r._tTime=st._time,i=st._first;i;)o=i._next,(n||!(!i._dur&&i instanceof gt&&i.vars.onComplete===i._targets[0]))&&He(r,i,i._start-i._delay),i=o;return He(st,r,0),r},context:function(t,n){return t?new s6(t,n):it},matchMedia:function(t){return new t4(t)},matchMediaRefresh:function(){return Tr.forEach(function(t){var n=t.conditions,r,i;for(i in n)n[i]&&(n[i]=!1,r=1);r&&t.revert()})||Cu()},addEventListener:function(t,n){var r=Qo[t]||(Qo[t]=[]);~r.indexOf(n)||r.push(n)},removeEventListener:function(t,n){var r=Qo[t],i=r&&r.indexOf(n);i>=0&&r.splice(i,1)},utils:{wrap:Om,wrapYoyo:Mm,distribute:F5,random:z5,snap:B5,normalize:Lm,getUnit:Nt,clamp:km,splitColor:G5,toArray:Ee,selector:Su,mapRange:U5,pipe:Dm,unitize:Am,interpolate:Nm,shuffle:q5},install:E5,effects:Ja,ticker:ae,updateRoot:zt.updateRoot,plugins:se,globalTimeline:st,core:{PropTween:ne,globals:k5,Tween:gt,Timeline:zt,Animation:ji,getCache:wr,_removeLinkedListItem:sa,reverting:function(){return Rt},context:function(t){return t&&it&&(it.data.push(t),t._ctx=it),it},suppressOverwrites:function(t){return Yc=t}}};ee("to,from,fromTo,delayedCall,set,killTweensOf",function(e){return Ps[e]=gt[e]});ae.add(zt.updateRoot);e1=Ps.to({},{duration:0});var e4=function(t,n){for(var r=t._pt;r&&r.p!==n&&r.op!==n&&r.fp!==n;)r=r._next;return r},n4=function(t,n){var r=t._targets,i,o,s;for(i in n)for(o=r.length;o--;)s=t._ptLookup[o][i],s&&(s=s.d)&&(s._pt&&(s=e4(s,i)),s&&s.modifier&&s.modifier(n[i],t,r[o],i))},il=function(t,n){return{name:t,rawVars:1,init:function(i,o,s){s._onInit=function(a){var l,u;if(Ct(o)&&(l={},ee(o,function(c){return l[c]=1}),o=l),n){l={};for(u in o)l[u]=n(o[u]);o=l}n4(a,o)}}}},ie=Ps.registerPlugin({name:"attr",init:function(t,n,r,i,o){var s,a,l;this.tween=r;for(s in n)l=t.getAttribute(s)||"",a=this.add(t,"setAttribute",(l||0)+"",n[s],i,o,0,0,s),a.op=s,a.b=l,this._props.push(s)},render:function(t,n){for(var r=n._pt;r;)Rt?r.set(r.t,r.p,r.b,r):r.r(t,r.d),r=r._next}},{name:"endArray",init:function(t,n){for(var r=n.length;r--;)this.add(t,r,t[r]||0,n[r],0,0,0,0,0,1)}},il("roundProps",Tu),il("modifiers"),il("snap",B5))||Ps;gt.version=zt.version=ie.version="3.12.5";C5=1;Qc()&&k1();H.Power0;H.Power1;H.Power2;H.Power3;H.Power4;H.Linear;H.Quad;H.Cubic;H.Quart;H.Quint;H.Strong;H.Elastic;H.Back;H.SteppedEase;H.Bounce;H.Sine;H.Expo;H.Circ;/*!
 * CSSPlugin 3.12.5
 * https://gsap.com
 *
 * Copyright 2008-2024, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license or for
 * Club GSAP members, the agreement issued with that membership.
 * @author: Jack Doyle, <EMAIL>
*/var yd,Ln,p1,a0,mr,_d,l0,r4=function(){return typeof window<"u"},yn={},ur=180/Math.PI,m1=Math.PI/180,Br=Math.atan2,wd=1e8,u0=/([A-Z])/g,i4=/(left|right|width|margin|padding|x)/i,o4=/[\s,\(]\S/,Ge={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},Eu=function(t,n){return n.set(n.t,n.p,Math.round((n.s+n.c*t)*1e4)/1e4+n.u,n)},s4=function(t,n){return n.set(n.t,n.p,t===1?n.e:Math.round((n.s+n.c*t)*1e4)/1e4+n.u,n)},a4=function(t,n){return n.set(n.t,n.p,t?Math.round((n.s+n.c*t)*1e4)/1e4+n.u:n.b,n)},l4=function(t,n){var r=n.s+n.c*t;n.set(n.t,n.p,~~(r+(r<0?-.5:.5))+n.u,n)},a6=function(t,n){return n.set(n.t,n.p,t?n.e:n.b,n)},l6=function(t,n){return n.set(n.t,n.p,t!==1?n.b:n.e,n)},u4=function(t,n,r){return t.style[n]=r},c4=function(t,n,r){return t.style.setProperty(n,r)},f4=function(t,n,r){return t._gsap[n]=r},d4=function(t,n,r){return t._gsap.scaleX=t._gsap.scaleY=r},h4=function(t,n,r,i,o){var s=t._gsap;s.scaleX=s.scaleY=r,s.renderTransform(o,s)},p4=function(t,n,r,i,o){var s=t._gsap;s[n]=r,s.renderTransform(o,s)},at="transform",re=at+"Origin",m4=function e(t,n){var r=this,i=this.target,o=i.style,s=i._gsap;if(t in yn&&o){if(this.tfm=this.tfm||{},t!=="transform")t=Ge[t]||t,~t.indexOf(",")?t.split(",").forEach(function(a){return r.tfm[a]=an(i,a)}):this.tfm[t]=s.x?s[t]:an(i,t),t===re&&(this.tfm.zOrigin=s.zOrigin);else return Ge.transform.split(",").forEach(function(a){return e.call(r,a,n)});if(this.props.indexOf(at)>=0)return;s.svg&&(this.svgo=i.getAttribute("data-svg-origin"),this.props.push(re,n,"")),t=at}(o||n)&&this.props.push(t,n,o[t])},u6=function(t){t.translate&&(t.removeProperty("translate"),t.removeProperty("scale"),t.removeProperty("rotate"))},g4=function(){var t=this.props,n=this.target,r=n.style,i=n._gsap,o,s;for(o=0;o<t.length;o+=3)t[o+1]?n[t[o]]=t[o+2]:t[o+2]?r[t[o]]=t[o+2]:r.removeProperty(t[o].substr(0,2)==="--"?t[o]:t[o].replace(u0,"-$1").toLowerCase());if(this.tfm){for(s in this.tfm)i[s]=this.tfm[s];i.svg&&(i.renderTransform(),n.setAttribute("data-svg-origin",this.svgo||"")),o=l0(),(!o||!o.isStart)&&!r[at]&&(u6(r),i.zOrigin&&r[re]&&(r[re]+=" "+i.zOrigin+"px",i.zOrigin=0,i.renderTransform()),i.uncache=1)}},c6=function(t,n){var r={target:t,props:[],revert:g4,save:m4};return t._gsap||ie.core.getCache(t),n&&n.split(",").forEach(function(i){return r.save(i)}),r},f6,ku=function(t,n){var r=Ln.createElementNS?Ln.createElementNS((n||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),t):Ln.createElement(t);return r&&r.style?r:Ln.createElement(t)},Xe=function e(t,n,r){var i=getComputedStyle(t);return i[n]||i.getPropertyValue(n.replace(u0,"-$1").toLowerCase())||i.getPropertyValue(n)||!r&&e(t,P1(n)||n,1)||""},xd="O,Moz,ms,Ms,Webkit".split(","),P1=function(t,n,r){var i=n||mr,o=i.style,s=5;if(t in o&&!r)return t;for(t=t.charAt(0).toUpperCase()+t.substr(1);s--&&!(xd[s]+t in o););return s<0?null:(s===3?"ms":s>=0?xd[s]:"")+t},Pu=function(){r4()&&window.document&&(yd=window,Ln=yd.document,p1=Ln.documentElement,mr=ku("div")||{style:{}},ku("div"),at=P1(at),re=at+"Origin",mr.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",f6=!!P1("perspective"),l0=ie.core.reverting,a0=1)},ol=function e(t){var n=ku("svg",this.ownerSVGElement&&this.ownerSVGElement.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),r=this.parentNode,i=this.nextSibling,o=this.style.cssText,s;if(p1.appendChild(n),n.appendChild(this),this.style.display="block",t)try{s=this.getBBox(),this._gsapBBox=this.getBBox,this.getBBox=e}catch{}else this._gsapBBox&&(s=this._gsapBBox());return r&&(i?r.insertBefore(this,i):r.appendChild(this)),p1.removeChild(n),this.style.cssText=o,s},Sd=function(t,n){for(var r=n.length;r--;)if(t.hasAttribute(n[r]))return t.getAttribute(n[r])},d6=function(t){var n;try{n=t.getBBox()}catch{n=ol.call(t,!0)}return n&&(n.width||n.height)||t.getBBox===ol||(n=ol.call(t,!0)),n&&!n.width&&!n.x&&!n.y?{x:+Sd(t,["x","cx","x1"])||0,y:+Sd(t,["y","cy","y1"])||0,width:0,height:0}:n},h6=function(t){return!!(t.getCTM&&(!t.parentNode||t.ownerSVGElement)&&d6(t))},Ar=function(t,n){if(n){var r=t.style,i;n in yn&&n!==re&&(n=at),r.removeProperty?(i=n.substr(0,2),(i==="ms"||n.substr(0,6)==="webkit")&&(n="-"+n),r.removeProperty(i==="--"?n:n.replace(u0,"-$1").toLowerCase())):r.removeAttribute(n)}},On=function(t,n,r,i,o,s){var a=new ne(t._pt,n,r,0,1,s?l6:a6);return t._pt=a,a.b=i,a.e=o,t._props.push(r),a},Td={deg:1,rad:1,turn:1},v4={grid:1,flex:1},Yn=function e(t,n,r,i){var o=parseFloat(r)||0,s=(r+"").trim().substr((o+"").length)||"px",a=mr.style,l=i4.test(n),u=t.tagName.toLowerCase()==="svg",c=(u?"client":"offset")+(l?"Width":"Height"),f=100,d=i==="px",p=i==="%",v,h,_,m;if(i===s||!o||Td[i]||Td[s])return o;if(s!=="px"&&!d&&(o=e(t,n,r,"px")),m=t.getCTM&&h6(t),(p||s==="%")&&(yn[n]||~n.indexOf("adius")))return v=m?t.getBBox()[l?"width":"height"]:t[c],pt(p?o/v*f:o/100*v);if(a[l?"width":"height"]=f+(d?s:i),h=~n.indexOf("adius")||i==="em"&&t.appendChild&&!u?t:t.parentNode,m&&(h=(t.ownerSVGElement||{}).parentNode),(!h||h===Ln||!h.appendChild)&&(h=Ln.body),_=h._gsap,_&&p&&_.width&&l&&_.time===ae.time&&!_.uncache)return pt(o/_.width*f);if(p&&(n==="height"||n==="width")){var g=t.style[n];t.style[n]=f+i,v=t[c],g?t.style[n]=g:Ar(t,n)}else(p||s==="%")&&!v4[Xe(h,"display")]&&(a.position=Xe(t,"position")),h===t&&(a.position="static"),h.appendChild(mr),v=mr[c],h.removeChild(mr),a.position="absolute";return l&&p&&(_=wr(h),_.time=ae.time,_.width=h[c]),pt(d?v*o/f:v&&o?f/v*o:0)},an=function(t,n,r,i){var o;return a0||Pu(),n in Ge&&n!=="transform"&&(n=Ge[n],~n.indexOf(",")&&(n=n.split(",")[0])),yn[n]&&n!=="transform"?(o=$i(t,i),o=n!=="transformOrigin"?o[n]:o.svg?o.origin:As(Xe(t,re))+" "+o.zOrigin+"px"):(o=t.style[n],(!o||o==="auto"||i||~(o+"").indexOf("calc("))&&(o=Ds[n]&&Ds[n](t,n,r)||Xe(t,n)||D5(t,n)||(n==="opacity"?1:0))),r&&!~(o+"").trim().indexOf(" ")?Yn(t,n,o,r)+r:o},y4=function(t,n,r,i){if(!r||r==="none"){var o=P1(n,t,1),s=o&&Xe(t,o,1);s&&s!==r?(n=o,r=s):n==="borderColor"&&(r=Xe(t,"borderTopColor"))}var a=new ne(this._pt,t.style,n,0,1,i6),l=0,u=0,c,f,d,p,v,h,_,m,g,y,w,x;if(a.b=r,a.e=i,r+="",i+="",i==="auto"&&(h=t.style[n],t.style[n]=i,i=Xe(t,n)||i,h?t.style[n]=h:Ar(t,n)),c=[r,i],Y5(c),r=c[0],i=c[1],d=r.match(t1)||[],x=i.match(t1)||[],x.length){for(;f=t1.exec(i);)_=f[0],g=i.substring(l,f.index),v?v=(v+1)%5:(g.substr(-5)==="rgba("||g.substr(-5)==="hsla(")&&(v=1),_!==(h=d[u++]||"")&&(p=parseFloat(h)||0,w=h.substr((p+"").length),_.charAt(1)==="="&&(_=h1(p,_)+w),m=parseFloat(_),y=_.substr((m+"").length),l=t1.lastIndex-y.length,y||(y=y||pe.units[n]||w,l===i.length&&(i+=y,a.e+=y)),w!==y&&(p=Yn(t,n,h,y)||0),a._pt={_next:a._pt,p:g||u===1?g:",",s:p,c:m-p,m:v&&v<4||n==="zIndex"?Math.round:0});a.c=l<i.length?i.substring(l,i.length):""}else a.r=n==="display"&&i==="none"?l6:a6;return T5.test(i)&&(a.e=0),this._pt=a,a},bd={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},_4=function(t){var n=t.split(" "),r=n[0],i=n[1]||"50%";return(r==="top"||r==="bottom"||i==="left"||i==="right")&&(t=r,r=i,i=t),n[0]=bd[r]||r,n[1]=bd[i]||i,n.join(" ")},w4=function(t,n){if(n.tween&&n.tween._time===n.tween._dur){var r=n.t,i=r.style,o=n.u,s=r._gsap,a,l,u;if(o==="all"||o===!0)i.cssText="",l=1;else for(o=o.split(","),u=o.length;--u>-1;)a=o[u],yn[a]&&(l=1,a=a==="transformOrigin"?re:at),Ar(r,a);l&&(Ar(r,at),s&&(s.svg&&r.removeAttribute("transform"),$i(r,1),s.uncache=1,u6(i)))}},Ds={clearProps:function(t,n,r,i,o){if(o.data!=="isFromStart"){var s=t._pt=new ne(t._pt,n,r,0,0,w4);return s.u=i,s.pr=-10,s.tween=o,t._props.push(r),1}}},Ui=[1,0,0,1,0,0],p6={},m6=function(t){return t==="matrix(1, 0, 0, 1, 0, 0)"||t==="none"||!t},Cd=function(t){var n=Xe(t,at);return m6(n)?Ui:n.substr(7).match(S5).map(pt)},c0=function(t,n){var r=t._gsap||wr(t),i=t.style,o=Cd(t),s,a,l,u;return r.svg&&t.getAttribute("transform")?(l=t.transform.baseVal.consolidate().matrix,o=[l.a,l.b,l.c,l.d,l.e,l.f],o.join(",")==="1,0,0,1,0,0"?Ui:o):(o===Ui&&!t.offsetParent&&t!==p1&&!r.svg&&(l=i.display,i.display="block",s=t.parentNode,(!s||!t.offsetParent)&&(u=1,a=t.nextElementSibling,p1.appendChild(t)),o=Cd(t),l?i.display=l:Ar(t,"display"),u&&(a?s.insertBefore(t,a):s?s.appendChild(t):p1.removeChild(t))),n&&o.length>6?[o[0],o[1],o[4],o[5],o[12],o[13]]:o)},Du=function(t,n,r,i,o,s){var a=t._gsap,l=o||c0(t,!0),u=a.xOrigin||0,c=a.yOrigin||0,f=a.xOffset||0,d=a.yOffset||0,p=l[0],v=l[1],h=l[2],_=l[3],m=l[4],g=l[5],y=n.split(" "),w=parseFloat(y[0])||0,x=parseFloat(y[1])||0,S,T,b,C;r?l!==Ui&&(T=p*_-v*h)&&(b=w*(_/T)+x*(-h/T)+(h*g-_*m)/T,C=w*(-v/T)+x*(p/T)-(p*g-v*m)/T,w=b,x=C):(S=d6(t),w=S.x+(~y[0].indexOf("%")?w/100*S.width:w),x=S.y+(~(y[1]||y[0]).indexOf("%")?x/100*S.height:x)),i||i!==!1&&a.smooth?(m=w-u,g=x-c,a.xOffset=f+(m*p+g*h)-m,a.yOffset=d+(m*v+g*_)-g):a.xOffset=a.yOffset=0,a.xOrigin=w,a.yOrigin=x,a.smooth=!!i,a.origin=n,a.originIsAbsolute=!!r,t.style[re]="0px 0px",s&&(On(s,a,"xOrigin",u,w),On(s,a,"yOrigin",c,x),On(s,a,"xOffset",f,a.xOffset),On(s,a,"yOffset",d,a.yOffset)),t.setAttribute("data-svg-origin",w+" "+x)},$i=function(t,n){var r=t._gsap||new Z5(t);if("x"in r&&!n&&!r.uncache)return r;var i=t.style,o=r.scaleX<0,s="px",a="deg",l=getComputedStyle(t),u=Xe(t,re)||"0",c,f,d,p,v,h,_,m,g,y,w,x,S,T,b,C,E,L,O,B,G,I,F,z,P,M,N,j,q,er,Et,Le;return c=f=d=h=_=m=g=y=w=0,p=v=1,r.svg=!!(t.getCTM&&h6(t)),l.translate&&((l.translate!=="none"||l.scale!=="none"||l.rotate!=="none")&&(i[at]=(l.translate!=="none"?"translate3d("+(l.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+(l.rotate!=="none"?"rotate("+l.rotate+") ":"")+(l.scale!=="none"?"scale("+l.scale.split(" ").join(",")+") ":"")+(l[at]!=="none"?l[at]:"")),i.scale=i.rotate=i.translate="none"),T=c0(t,r.svg),r.svg&&(r.uncache?(P=t.getBBox(),u=r.xOrigin-P.x+"px "+(r.yOrigin-P.y)+"px",z=""):z=!n&&t.getAttribute("data-svg-origin"),Du(t,z||u,!!z||r.originIsAbsolute,r.smooth!==!1,T)),x=r.xOrigin||0,S=r.yOrigin||0,T!==Ui&&(L=T[0],O=T[1],B=T[2],G=T[3],c=I=T[4],f=F=T[5],T.length===6?(p=Math.sqrt(L*L+O*O),v=Math.sqrt(G*G+B*B),h=L||O?Br(O,L)*ur:0,g=B||G?Br(B,G)*ur+h:0,g&&(v*=Math.abs(Math.cos(g*m1))),r.svg&&(c-=x-(x*L+S*B),f-=S-(x*O+S*G))):(Le=T[6],er=T[7],N=T[8],j=T[9],q=T[10],Et=T[11],c=T[12],f=T[13],d=T[14],b=Br(Le,q),_=b*ur,b&&(C=Math.cos(-b),E=Math.sin(-b),z=I*C+N*E,P=F*C+j*E,M=Le*C+q*E,N=I*-E+N*C,j=F*-E+j*C,q=Le*-E+q*C,Et=er*-E+Et*C,I=z,F=P,Le=M),b=Br(-B,q),m=b*ur,b&&(C=Math.cos(-b),E=Math.sin(-b),z=L*C-N*E,P=O*C-j*E,M=B*C-q*E,Et=G*E+Et*C,L=z,O=P,B=M),b=Br(O,L),h=b*ur,b&&(C=Math.cos(b),E=Math.sin(b),z=L*C+O*E,P=I*C+F*E,O=O*C-L*E,F=F*C-I*E,L=z,I=P),_&&Math.abs(_)+Math.abs(h)>359.9&&(_=h=0,m=180-m),p=pt(Math.sqrt(L*L+O*O+B*B)),v=pt(Math.sqrt(F*F+Le*Le)),b=Br(I,F),g=Math.abs(b)>2e-4?b*ur:0,w=Et?1/(Et<0?-Et:Et):0),r.svg&&(z=t.getAttribute("transform"),r.forceCSS=t.setAttribute("transform","")||!m6(Xe(t,at)),z&&t.setAttribute("transform",z))),Math.abs(g)>90&&Math.abs(g)<270&&(o?(p*=-1,g+=h<=0?180:-180,h+=h<=0?180:-180):(v*=-1,g+=g<=0?180:-180)),n=n||r.uncache,r.x=c-((r.xPercent=c&&(!n&&r.xPercent||(Math.round(t.offsetWidth/2)===Math.round(-c)?-50:0)))?t.offsetWidth*r.xPercent/100:0)+s,r.y=f-((r.yPercent=f&&(!n&&r.yPercent||(Math.round(t.offsetHeight/2)===Math.round(-f)?-50:0)))?t.offsetHeight*r.yPercent/100:0)+s,r.z=d+s,r.scaleX=pt(p),r.scaleY=pt(v),r.rotation=pt(h)+a,r.rotationX=pt(_)+a,r.rotationY=pt(m)+a,r.skewX=g+a,r.skewY=y+a,r.transformPerspective=w+s,(r.zOrigin=parseFloat(u.split(" ")[2])||!n&&r.zOrigin||0)&&(i[re]=As(u)),r.xOffset=r.yOffset=0,r.force3D=pe.force3D,r.renderTransform=r.svg?S4:f6?g6:x4,r.uncache=0,r},As=function(t){return(t=t.split(" "))[0]+" "+t[1]},sl=function(t,n,r){var i=Nt(n);return pt(parseFloat(n)+parseFloat(Yn(t,"x",r+"px",i)))+i},x4=function(t,n){n.z="0px",n.rotationY=n.rotationX="0deg",n.force3D=0,g6(t,n)},rr="0deg",$1="0px",ir=") ",g6=function(t,n){var r=n||this,i=r.xPercent,o=r.yPercent,s=r.x,a=r.y,l=r.z,u=r.rotation,c=r.rotationY,f=r.rotationX,d=r.skewX,p=r.skewY,v=r.scaleX,h=r.scaleY,_=r.transformPerspective,m=r.force3D,g=r.target,y=r.zOrigin,w="",x=m==="auto"&&t&&t!==1||m===!0;if(y&&(f!==rr||c!==rr)){var S=parseFloat(c)*m1,T=Math.sin(S),b=Math.cos(S),C;S=parseFloat(f)*m1,C=Math.cos(S),s=sl(g,s,T*C*-y),a=sl(g,a,-Math.sin(S)*-y),l=sl(g,l,b*C*-y+y)}_!==$1&&(w+="perspective("+_+ir),(i||o)&&(w+="translate("+i+"%, "+o+"%) "),(x||s!==$1||a!==$1||l!==$1)&&(w+=l!==$1||x?"translate3d("+s+", "+a+", "+l+") ":"translate("+s+", "+a+ir),u!==rr&&(w+="rotate("+u+ir),c!==rr&&(w+="rotateY("+c+ir),f!==rr&&(w+="rotateX("+f+ir),(d!==rr||p!==rr)&&(w+="skew("+d+", "+p+ir),(v!==1||h!==1)&&(w+="scale("+v+", "+h+ir),g.style[at]=w||"translate(0, 0)"},S4=function(t,n){var r=n||this,i=r.xPercent,o=r.yPercent,s=r.x,a=r.y,l=r.rotation,u=r.skewX,c=r.skewY,f=r.scaleX,d=r.scaleY,p=r.target,v=r.xOrigin,h=r.yOrigin,_=r.xOffset,m=r.yOffset,g=r.forceCSS,y=parseFloat(s),w=parseFloat(a),x,S,T,b,C;l=parseFloat(l),u=parseFloat(u),c=parseFloat(c),c&&(c=parseFloat(c),u+=c,l+=c),l||u?(l*=m1,u*=m1,x=Math.cos(l)*f,S=Math.sin(l)*f,T=Math.sin(l-u)*-d,b=Math.cos(l-u)*d,u&&(c*=m1,C=Math.tan(u-c),C=Math.sqrt(1+C*C),T*=C,b*=C,c&&(C=Math.tan(c),C=Math.sqrt(1+C*C),x*=C,S*=C)),x=pt(x),S=pt(S),T=pt(T),b=pt(b)):(x=f,b=d,S=T=0),(y&&!~(s+"").indexOf("px")||w&&!~(a+"").indexOf("px"))&&(y=Yn(p,"x",s,"px"),w=Yn(p,"y",a,"px")),(v||h||_||m)&&(y=pt(y+v-(v*x+h*T)+_),w=pt(w+h-(v*S+h*b)+m)),(i||o)&&(C=p.getBBox(),y=pt(y+i/100*C.width),w=pt(w+o/100*C.height)),C="matrix("+x+","+S+","+T+","+b+","+y+","+w+")",p.setAttribute("transform",C),g&&(p.style[at]=C)},T4=function(t,n,r,i,o){var s=360,a=Ct(o),l=parseFloat(o)*(a&&~o.indexOf("rad")?ur:1),u=l-i,c=i+u+"deg",f,d;return a&&(f=o.split("_")[1],f==="short"&&(u%=s,u!==u%(s/2)&&(u+=u<0?s:-s)),f==="cw"&&u<0?u=(u+s*wd)%s-~~(u/s)*s:f==="ccw"&&u>0&&(u=(u-s*wd)%s-~~(u/s)*s)),t._pt=d=new ne(t._pt,n,r,i,u,s4),d.e=c,d.u="deg",t._props.push(r),d},Ed=function(t,n){for(var r in n)t[r]=n[r];return t},b4=function(t,n,r){var i=Ed({},r._gsap),o="perspective,force3D,transformOrigin,svgOrigin",s=r.style,a,l,u,c,f,d,p,v;i.svg?(u=r.getAttribute("transform"),r.setAttribute("transform",""),s[at]=n,a=$i(r,1),Ar(r,at),r.setAttribute("transform",u)):(u=getComputedStyle(r)[at],s[at]=n,a=$i(r,1),s[at]=u);for(l in yn)u=i[l],c=a[l],u!==c&&o.indexOf(l)<0&&(p=Nt(u),v=Nt(c),f=p!==v?Yn(r,l,u,v):parseFloat(u),d=parseFloat(c),t._pt=new ne(t._pt,a,l,f,d-f,Eu),t._pt.u=v||0,t._props.push(l));Ed(a,i)};ee("padding,margin,Width,Radius",function(e,t){var n="Top",r="Right",i="Bottom",o="Left",s=(t<3?[n,r,i,o]:[n+o,n+r,i+r,i+o]).map(function(a){return t<2?e+a:"border"+a+e});Ds[t>1?"border"+e:e]=function(a,l,u,c,f){var d,p;if(arguments.length<4)return d=s.map(function(v){return an(a,v,u)}),p=d.join(" "),p.split(d[0]).length===5?d[0]:p;d=(c+"").split(" "),p={},s.forEach(function(v,h){return p[v]=d[h]=d[h]||d[(h-1)/2|0]}),a.init(l,p,f)}});var v6={name:"css",register:Pu,targetTest:function(t){return t.style&&t.nodeType},init:function(t,n,r,i,o){var s=this._props,a=t.style,l=r.vars.startAt,u,c,f,d,p,v,h,_,m,g,y,w,x,S,T,b;a0||Pu(),this.styles=this.styles||c6(t),b=this.styles.props,this.tween=r;for(h in n)if(h!=="autoRound"&&(c=n[h],!(se[h]&&J5(h,n,r,i,t,o)))){if(p=typeof c,v=Ds[h],p==="function"&&(c=c.call(r,i,t,o),p=typeof c),p==="string"&&~c.indexOf("random(")&&(c=Bi(c)),v)v(this,t,h,c,r)&&(T=1);else if(h.substr(0,2)==="--")u=(getComputedStyle(t).getPropertyValue(h)+"").trim(),c+="",Un.lastIndex=0,Un.test(u)||(_=Nt(u),m=Nt(c)),m?_!==m&&(u=Yn(t,h,u,m)+m):_&&(c+=_),this.add(a,"setProperty",u,c,i,o,0,0,h),s.push(h),b.push(h,0,a[h]);else if(p!=="undefined"){if(l&&h in l?(u=typeof l[h]=="function"?l[h].call(r,i,t,o):l[h],Ct(u)&&~u.indexOf("random(")&&(u=Bi(u)),Nt(u+"")||u==="auto"||(u+=pe.units[h]||Nt(an(t,h))||""),(u+"").charAt(1)==="="&&(u=an(t,h))):u=an(t,h),d=parseFloat(u),g=p==="string"&&c.charAt(1)==="="&&c.substr(0,2),g&&(c=c.substr(2)),f=parseFloat(c),h in Ge&&(h==="autoAlpha"&&(d===1&&an(t,"visibility")==="hidden"&&f&&(d=0),b.push("visibility",0,a.visibility),On(this,a,"visibility",d?"inherit":"hidden",f?"inherit":"hidden",!f)),h!=="scale"&&h!=="transform"&&(h=Ge[h],~h.indexOf(",")&&(h=h.split(",")[0]))),y=h in yn,y){if(this.styles.save(h),w||(x=t._gsap,x.renderTransform&&!n.parseTransform||$i(t,n.parseTransform),S=n.smoothOrigin!==!1&&x.smooth,w=this._pt=new ne(this._pt,a,at,0,1,x.renderTransform,x,0,-1),w.dep=1),h==="scale")this._pt=new ne(this._pt,x,"scaleY",x.scaleY,(g?h1(x.scaleY,g+f):f)-x.scaleY||0,Eu),this._pt.u=0,s.push("scaleY",h),h+="X";else if(h==="transformOrigin"){b.push(re,0,a[re]),c=_4(c),x.svg?Du(t,c,0,S,0,this):(m=parseFloat(c.split(" ")[2])||0,m!==x.zOrigin&&On(this,x,"zOrigin",x.zOrigin,m),On(this,a,h,As(u),As(c)));continue}else if(h==="svgOrigin"){Du(t,c,1,S,0,this);continue}else if(h in p6){T4(this,x,h,d,g?h1(d,g+c):c);continue}else if(h==="smoothOrigin"){On(this,x,"smooth",x.smooth,c);continue}else if(h==="force3D"){x[h]=c;continue}else if(h==="transform"){b4(this,c,t);continue}}else h in a||(h=P1(h)||h);if(y||(f||f===0)&&(d||d===0)&&!o4.test(c)&&h in a)_=(u+"").substr((d+"").length),f||(f=0),m=Nt(c)||(h in pe.units?pe.units[h]:_),_!==m&&(d=Yn(t,h,u,m)),this._pt=new ne(this._pt,y?x:a,h,d,(g?h1(d,g+f):f)-d,!y&&(m==="px"||h==="zIndex")&&n.autoRound!==!1?l4:Eu),this._pt.u=m||0,_!==m&&m!=="%"&&(this._pt.b=u,this._pt.r=a4);else if(h in a)y4.call(this,t,h,u,g?g+c:c);else if(h in t)this.add(t,h,u||t[h],g?g+c:c,i,o);else if(h!=="parseTransform"){Zc(h,c);continue}y||(h in a?b.push(h,0,a[h]):b.push(h,1,u||t[h])),s.push(h)}}T&&o6(this)},render:function(t,n){if(n.tween._time||!l0())for(var r=n._pt;r;)r.r(t,r.d),r=r._next;else n.styles.revert()},get:an,aliases:Ge,getSetter:function(t,n,r){var i=Ge[n];return i&&i.indexOf(",")<0&&(n=i),n in yn&&n!==re&&(t._gsap.x||an(t,"x"))?r&&_d===r?n==="scale"?d4:f4:(_d=r||{})&&(n==="scale"?h4:p4):t.style&&!Xc(t.style[n])?u4:~n.indexOf("-")?c4:o0(t,n)},core:{_removeProperty:Ar,_getMatrix:c0}};ie.utils.checkPrefix=P1;ie.core.getStyleSaver=c6;(function(e,t,n,r){var i=ee(e+","+t+","+n,function(o){yn[o]=1});ee(t,function(o){pe.units[o]="deg",p6[o]=1}),Ge[i[13]]=e+","+t,ee(r,function(o){var s=o.split(":");Ge[s[1]]=i[s[0]]})})("x,y,z,scale,scaleX,scaleY,xPercent,yPercent","rotation,rotationX,rotationY,skewX,skewY","transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective","0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY");ee("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",function(e){pe.units[e]="px"});ie.registerPlugin(v6);var y6=ie.registerPlugin(v6)||ie;y6.core.Tween;function kd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function Pd(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?kd(Object(n),!0).forEach(function(r){P4(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):kd(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function C4(e,t){if(typeof e!="object"||!e)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function E4(e){var t=C4(e,"string");return typeof t=="symbol"?t:String(t)}function Dd(e,t,n,r,i,o,s){try{var a=e[o](s),l=a.value}catch(u){n(u);return}a.done?t(l):Promise.resolve(l).then(r,i)}function k4(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var o=e.apply(t,n);function s(l){Dd(o,r,i,s,a,"next",l)}function a(l){Dd(o,r,i,s,a,"throw",l)}s(void 0)})}}function P4(e,t,n){return t=E4(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function D4(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,o;for(o=0;o<r.length;o++)i=r[o],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function A4(e,t){if(e==null)return{};var n=D4(e,t),r,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)r=o[i],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}var L4=(e,t)=>{var n=K.useRef(null),r=K.useRef(e);return K.useEffect(()=>{r.current=e},[e]),K.useEffect(()=>{var i=()=>r.current();if(typeof t=="number")return n.current=window.setTimeout(i,t),()=>window.clearTimeout(n.current)},[t]),n},O4=["state","safeToRemove"],_6=e=>{var t=K.useContext(Wc),{state:n,safeToRemove:r}=t,i=A4(t,O4),o=w6();return L4(r,n===Gt.stopped&&Number.isFinite(void 0)?e.removeDelay*1e3:null),Pd(Pd({},i),{},{data:o,state:n,safeToRemove:r,isPlaying:n===Gt.playing,isStopped:n===Gt.stopped})},w6=e=>{var{data:t}=K.useContext(Wc),{trim:n=!0}={};return D.useMemo(()=>{if(!n)return t;var r={};for(var[i,o]of Object.entries(t))r[i]=typeof o=="string"?o.trim():o;return r},[t,n])},M4=D.forwardRef(function(t,n){var{children:r,hide:i,wait:o=i,onPlay:s,onStop:a}=t,[l]=D.useState(y6.timeline({paused:!0})),{state:u,isStopped:c,safeToRemove:f}=_6(),[d,p]=D.useState(!1),v=u>=Gt.playing&&!o;return D.useImperativeHandle(n,()=>l),D.useLayoutEffect(()=>{v&&(s&&(s(l),l.play()),p(!0))},[v]),D.useEffect(()=>{if(c)if(!a)f();else{a(l);var h=l.reversed()?"onReverseComplete":"onComplete";l.eventCallback(h,f)}},[c,f]),v||d?r:null});const f0=D.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),ua=D.createContext({});function N4(){return D.useContext(ua).visualElement}const ca=D.createContext(null),Nr=typeof document<"u",al=Nr?D.useLayoutEffect:D.useEffect,x6=D.createContext({strict:!1});function R4(e,t,n,r){const i=N4(),o=D.useContext(x6),s=D.useContext(ca),a=D.useContext(f0).reducedMotion,l=D.useRef();r=r||o.renderer,!l.current&&r&&(l.current=r(e,{visualState:t,parent:i,props:n,presenceId:s?s.id:void 0,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:a}));const u=l.current;return al(()=>{u&&u.render()}),al(()=>{u&&u.animationState&&u.animationState.animateChanges()}),al(()=>()=>u&&u.notify("Unmount"),[]),u}function n1(e){return typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function I4(e,t,n){return D.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):n1(n)&&(n.current=r))},[t])}function Hi(e){return typeof e=="string"||Array.isArray(e)}function fa(e){return typeof e=="object"&&typeof e.start=="function"}const V4=["initial","animate","exit","whileHover","whileDrag","whileTap","whileFocus","whileInView"];function da(e){return fa(e.animate)||V4.some(t=>Hi(e[t]))}function S6(e){return!!(da(e)||e.variants)}function q4(e,t){if(da(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Hi(n)?n:void 0,animate:Hi(r)?r:void 0}}return e.inherit!==!1?t:{}}function F4(e){const{initial:t,animate:n}=q4(e,D.useContext(ua));return D.useMemo(()=>({initial:t,animate:n}),[Ad(t),Ad(n)])}function Ad(e){return Array.isArray(e)?e.join(" "):e}const tn=e=>({isEnabled:t=>e.some(n=>!!t[n])}),Gi={measureLayout:tn(["layout","layoutId","drag"]),animation:tn(["animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView"]),exit:tn(["exit"]),drag:tn(["drag","dragControls"]),focus:tn(["whileFocus"]),hover:tn(["whileHover","onHoverStart","onHoverEnd"]),tap:tn(["whileTap","onTap","onTapStart","onTapCancel"]),pan:tn(["onPan","onPanStart","onPanSessionStart","onPanEnd"]),inView:tn(["whileInView","onViewportEnter","onViewportLeave"])};function B4(e){for(const t in e)t==="projectionNodeConstructor"?Gi.projectionNodeConstructor=e[t]:Gi[t].Component=e[t]}function d0(e){const t=D.useRef(null);return t.current===null&&(t.current=e()),t.current}const pi={hasAnimatedSinceResize:!0,hasEverUpdated:!1};let z4=1;function j4(){return d0(()=>{if(pi.hasEverUpdated)return z4++})}const T6=D.createContext({});class U4 extends K.Component{getSnapshotBeforeUpdate(){const{visualElement:t,props:n}=this.props;return t&&t.setProps(n),null}componentDidUpdate(){}render(){return this.props.children}}const b6=D.createContext({}),$4=Symbol.for("motionComponentSymbol");function H4({preloadedFeatures:e,createVisualElement:t,projectionNodeConstructor:n,useRender:r,useVisualState:i,Component:o}){e&&B4(e);function s(l,u){const c={...D.useContext(f0),...l,layoutId:G4(l)},{isStatic:f}=c;let d=null;const p=F4(l),v=f?void 0:j4(),h=i(l,f);if(!f&&Nr){p.visualElement=R4(o,h,c,t);const _=D.useContext(x6).strict,m=D.useContext(b6);p.visualElement&&(d=p.visualElement.loadFeatures(c,_,e,v,n||Gi.projectionNodeConstructor,m))}return D.createElement(U4,{visualElement:p.visualElement,props:c},d,D.createElement(ua.Provider,{value:p},r(o,l,v,I4(h,p.visualElement,u),h,f,p.visualElement)))}const a=D.forwardRef(s);return a[$4]=o,a}function G4({layoutId:e}){const t=D.useContext(T6).id;return t&&e!==void 0?t+"-"+e:e}function W4(e){function t(r,i={}){return H4(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const Y4=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function h0(e){return typeof e!="string"||e.includes("-")?!1:!!(Y4.indexOf(e)>-1||/[A-Z]/.test(e))}const Ls={};function X4(e){Object.assign(Ls,e)}const Os=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Rr=new Set(Os);function C6(e,{layout:t,layoutId:n}){return Rr.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Ls[e]||e==="opacity")}const Je=e=>!!(e!=null&&e.getVelocity),Q4={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},K4=(e,t)=>Os.indexOf(e)-Os.indexOf(t);function Z4({transform:e,transformKeys:t},{enableHardwareAcceleration:n=!0,allowTransformNone:r=!0},i,o){let s="";t.sort(K4);for(const a of t)s+=`${Q4[a]||a}(${e[a]}) `;return n&&!e.z&&(s+="translateZ(0)"),s=s.trim(),o?s=o(e,i?"":s):r&&i&&(s="none"),s}function E6(e){return e.startsWith("--")}const J4=(e,t)=>t&&typeof e=="number"?t.transform(e):e,D1=(e,t,n)=>Math.min(Math.max(n,e),t),Ir={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},mi={...Ir,transform:e=>D1(0,1,e)},Ao={...Ir,default:1},gi=e=>Math.round(e*1e5)/1e5,Wi=/(-)?([\d]*\.?[\d])+/g,Au=/(#[0-9a-f]{6}|#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,tg=/^(#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function ro(e){return typeof e=="string"}const io=e=>({test:t=>ro(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Sn=io("deg"),Qe=io("%"),R=io("px"),eg=io("vh"),ng=io("vw"),Ld={...Qe,parse:e=>Qe.parse(e)/100,transform:e=>Qe.transform(e*100)},Od={...Ir,transform:Math.round},k6={borderWidth:R,borderTopWidth:R,borderRightWidth:R,borderBottomWidth:R,borderLeftWidth:R,borderRadius:R,radius:R,borderTopLeftRadius:R,borderTopRightRadius:R,borderBottomRightRadius:R,borderBottomLeftRadius:R,width:R,maxWidth:R,height:R,maxHeight:R,size:R,top:R,right:R,bottom:R,left:R,padding:R,paddingTop:R,paddingRight:R,paddingBottom:R,paddingLeft:R,margin:R,marginTop:R,marginRight:R,marginBottom:R,marginLeft:R,rotate:Sn,rotateX:Sn,rotateY:Sn,rotateZ:Sn,scale:Ao,scaleX:Ao,scaleY:Ao,scaleZ:Ao,skew:Sn,skewX:Sn,skewY:Sn,distance:R,translateX:R,translateY:R,translateZ:R,x:R,y:R,z:R,perspective:R,transformPerspective:R,opacity:mi,originX:Ld,originY:Ld,originZ:R,zIndex:Od,fillOpacity:mi,strokeOpacity:mi,numOctaves:Od};function p0(e,t,n,r){const{style:i,vars:o,transform:s,transformKeys:a,transformOrigin:l}=e;a.length=0;let u=!1,c=!1,f=!0;for(const d in t){const p=t[d];if(E6(d)){o[d]=p;continue}const v=k6[d],h=J4(p,v);if(Rr.has(d)){if(u=!0,s[d]=h,a.push(d),!f)continue;p!==(v.default||0)&&(f=!1)}else d.startsWith("origin")?(c=!0,l[d]=h):i[d]=h}if(t.transform||(u||r?i.transform=Z4(e,n,f,r):i.transform&&(i.transform="none")),c){const{originX:d="50%",originY:p="50%",originZ:v=0}=l;i.transformOrigin=`${d} ${p} ${v}`}}const m0=()=>({style:{},transform:{},transformKeys:[],transformOrigin:{},vars:{}});function P6(e,t,n){for(const r in t)!Je(t[r])&&!C6(r,n)&&(e[r]=t[r])}function rg({transformTemplate:e},t,n){return D.useMemo(()=>{const r=m0();return p0(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function ig(e,t,n){const r=e.style||{},i={};return P6(i,r,e),Object.assign(i,rg(e,t,n)),e.transformValues?e.transformValues(i):i}function og(e,t,n){const r={},i=ig(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),r.style=i,r}const sg=["animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView"],ag=["whileTap","onTap","onTapStart","onTapCancel"],lg=["onPan","onPanStart","onPanSessionStart","onPanEnd"],ug=["whileInView","onViewportEnter","onViewportLeave","viewport"],cg=new Set(["initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","layout","layoutId","layoutDependency","onLayoutAnimationStart","onLayoutAnimationComplete","onLayoutMeasure","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","drag","dragControls","dragListener","dragConstraints","dragDirectionLock","dragSnapToOrigin","_dragX","_dragY","dragElastic","dragMomentum","dragPropagation","dragTransition","onHoverStart","onHoverEnd","layoutScroll",...ug,...ag,...sg,...lg]);function Ms(e){return cg.has(e)}let D6=e=>!Ms(e);function fg(e){e&&(D6=t=>t.startsWith("on")?!Ms(t):e(t))}try{fg(require("@emotion/is-prop-valid").default)}catch{}function dg(e,t,n){const r={};for(const i in e)(D6(i)||n===!0&&Ms(i)||!t&&!Ms(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function Md(e,t,n){return typeof e=="string"?e:R.transform(t+n*e)}function hg(e,t,n){const r=Md(t,e.x,e.width),i=Md(n,e.y,e.height);return`${r} ${i}`}const pg={offset:"stroke-dashoffset",array:"stroke-dasharray"},mg={offset:"strokeDashoffset",array:"strokeDasharray"};function gg(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?pg:mg;e[o.offset]=R.transform(-r);const s=R.transform(t),a=R.transform(n);e[o.array]=`${s} ${a}`}function g0(e,{attrX:t,attrY:n,originX:r,originY:i,pathLength:o,pathSpacing:s=1,pathOffset:a=0,...l},u,c,f){if(p0(e,l,u,f),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:d,style:p,dimensions:v}=e;d.transform&&(v&&(p.transform=d.transform),delete d.transform),v&&(r!==void 0||i!==void 0||p.transform)&&(p.transformOrigin=hg(v,r!==void 0?r:.5,i!==void 0?i:.5)),t!==void 0&&(d.x=t),n!==void 0&&(d.y=n),o!==void 0&&gg(d,o,s,a,!1)}const A6=()=>({...m0(),attrs:{}}),v0=e=>typeof e=="string"&&e.toLowerCase()==="svg";function vg(e,t,n,r){const i=D.useMemo(()=>{const o=A6();return g0(o,t,{enableHardwareAcceleration:!1},v0(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};P6(o,e.style,e),i.style={...o,...i.style}}return i}function yg(e=!1){return(n,r,i,o,{latestValues:s},a)=>{const u=(h0(n)?vg:og)(r,s,a,n),f={...dg(r,typeof n=="string",e),...u,ref:o};return i&&(f["data-projection-id"]=i),D.createElement(n,f)}}const y0=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();function L6(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const O6=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function M6(e,t,n,r){L6(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(O6.has(i)?i:y0(i),t.attrs[i])}function _0(e){const{style:t}=e,n={};for(const r in t)(Je(t[r])||C6(r,e))&&(n[r]=t[r]);return n}function N6(e){const t=_0(e);for(const n in e)if(Je(e[n])){const r=n==="x"||n==="y"?"attr"+n.toUpperCase():n;t[r]=e[n]}return t}function w0(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}const Ns=e=>Array.isArray(e),_g=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),wg=e=>Ns(e)?e[e.length-1]||0:e;function Ko(e){const t=Je(e)?e.get():e;return _g(t)?t.toValue():t}function xg({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,o){const s={latestValues:Sg(r,i,o,e),renderState:t()};return n&&(s.mount=a=>n(r,a,s)),s}const R6=e=>(t,n)=>{const r=D.useContext(ua),i=D.useContext(ca),o=()=>xg(e,t,r,i);return n?o():d0(o)};function Sg(e,t,n,r){const i={},o=r(e);for(const d in o)i[d]=Ko(o[d]);let{initial:s,animate:a}=e;const l=da(e),u=S6(e);t&&u&&!l&&e.inherit!==!1&&(s===void 0&&(s=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||s===!1;const f=c?a:s;return f&&typeof f!="boolean"&&!fa(f)&&(Array.isArray(f)?f:[f]).forEach(p=>{const v=w0(e,p);if(!v)return;const{transitionEnd:h,transition:_,...m}=v;for(const g in m){let y=m[g];if(Array.isArray(y)){const w=c?y.length-1:0;y=y[w]}y!==null&&(i[g]=y)}for(const g in h)i[g]=h[g]}),i}const Tg={useVisualState:R6({scrapeMotionValuesFromProps:N6,createRenderState:A6,onMount:(e,t,{renderState:n,latestValues:r})=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}g0(n,r,{enableHardwareAcceleration:!1},v0(t.tagName),e.transformTemplate),M6(t,n)}})},bg={useVisualState:R6({scrapeMotionValuesFromProps:_0,createRenderState:m0})};function Cg(e,{forwardMotionProps:t=!1},n,r,i){return{...h0(e)?Tg:bg,preloadedFeatures:n,useRender:yg(t),createVisualElement:r,projectionNodeConstructor:i,Component:e}}var J;(function(e){e.Animate="animate",e.Hover="whileHover",e.Tap="whileTap",e.Drag="whileDrag",e.Focus="whileFocus",e.InView="whileInView",e.Exit="exit"})(J||(J={}));function ha(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function Lu(e,t,n,r){D.useEffect(()=>{const i=e.current;if(n&&i)return ha(i,t,n,r)},[e,t,n,r])}function Eg({whileFocus:e,visualElement:t}){const{animationState:n}=t,r=()=>{n&&n.setActive(J.Focus,!0)},i=()=>{n&&n.setActive(J.Focus,!1)};Lu(t,"focus",e?r:void 0),Lu(t,"blur",e?i:void 0)}function I6(e){return typeof PointerEvent<"u"&&e instanceof PointerEvent?e.pointerType==="mouse":e instanceof MouseEvent}function V6(e){return!!e.touches}function kg(e){return t=>{const n=t instanceof MouseEvent;(!n||n&&t.button===0)&&e(t)}}const Pg={pageX:0,pageY:0};function Dg(e,t="page"){const r=e.touches[0]||e.changedTouches[0]||Pg;return{x:r[t+"X"],y:r[t+"Y"]}}function Ag(e,t="page"){return{x:e[t+"X"],y:e[t+"Y"]}}function x0(e,t="page"){return{point:V6(e)?Dg(e,t):Ag(e,t)}}const q6=(e,t=!1)=>{const n=r=>e(r,x0(r));return t?kg(n):n},Lg=()=>Nr&&window.onpointerdown===null,Og=()=>Nr&&window.ontouchstart===null,Mg=()=>Nr&&window.onmousedown===null,Ng={pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointercancel:"mousecancel",pointerover:"mouseover",pointerout:"mouseout",pointerenter:"mouseenter",pointerleave:"mouseleave"},Rg={pointerdown:"touchstart",pointermove:"touchmove",pointerup:"touchend",pointercancel:"touchcancel"};function F6(e){return Lg()?e:Og()?Rg[e]:Mg()?Ng[e]:e}function g1(e,t,n,r){return ha(e,F6(t),q6(n,t==="pointerdown"),r)}function Rs(e,t,n,r){return Lu(e,F6(t),n&&q6(n,t==="pointerdown"),r)}function B6(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const Nd=B6("dragHorizontal"),Rd=B6("dragVertical");function z6(e){let t=!1;if(e==="y")t=Rd();else if(e==="x")t=Nd();else{const n=Nd(),r=Rd();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function j6(){const e=z6(!0);return e?(e(),!1):!0}function Id(e,t,n){return(r,i)=>{!I6(r)||j6()||(e.animationState&&e.animationState.setActive(J.Hover,t),n&&n(r,i))}}function Ig({onHoverStart:e,onHoverEnd:t,whileHover:n,visualElement:r}){Rs(r,"pointerenter",e||n?Id(r,!0,e):void 0,{passive:!e}),Rs(r,"pointerleave",t||n?Id(r,!1,t):void 0,{passive:!t})}const U6=(e,t)=>t?e===t?!0:U6(e,t.parentElement):!1;function $6(e){return D.useEffect(()=>()=>e(),[])}const Vg=(e,t)=>n=>t(e(n)),pa=(...e)=>e.reduce(Vg);function qg({onTap:e,onTapStart:t,onTapCancel:n,whileTap:r,visualElement:i}){const o=e||t||n||r,s=D.useRef(!1),a=D.useRef(null),l={passive:!(t||e||n||p)};function u(){a.current&&a.current(),a.current=null}function c(){return u(),s.current=!1,i.animationState&&i.animationState.setActive(J.Tap,!1),!j6()}function f(v,h){c()&&(U6(i.current,v.target)?e&&e(v,h):n&&n(v,h))}function d(v,h){c()&&n&&n(v,h)}function p(v,h){u(),!s.current&&(s.current=!0,a.current=pa(g1(window,"pointerup",f,l),g1(window,"pointercancel",d,l)),i.animationState&&i.animationState.setActive(J.Tap,!0),t&&t(v,h))}Rs(i,"pointerdown",o?p:void 0,l),$6(u)}var Fg={};const Bg="production",zg=typeof process>"u"||Fg===void 0?Bg:"production",Vd=new Set;function jg(e,t,n){Vd.has(t)||(console.warn(t),Vd.add(t))}const Ou=new WeakMap,ll=new WeakMap,Ug=e=>{const t=Ou.get(e.target);t&&t(e)},$g=e=>{e.forEach(Ug)};function Hg({root:e,...t}){const n=e||document;ll.has(n)||ll.set(n,{});const r=ll.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver($g,{root:e,...t})),r[i]}function Gg(e,t,n){const r=Hg(t);return Ou.set(e,n),r.observe(e),()=>{Ou.delete(e),r.unobserve(e)}}function Wg({visualElement:e,whileInView:t,onViewportEnter:n,onViewportLeave:r,viewport:i={}}){const o=D.useRef({hasEnteredView:!1,isInView:!1});let s=!!(t||n||r);i.once&&o.current.hasEnteredView&&(s=!1),(typeof IntersectionObserver>"u"?Qg:Xg)(s,o.current,e,i)}const Yg={some:0,all:1};function Xg(e,t,n,{root:r,margin:i,amount:o="some",once:s}){D.useEffect(()=>{if(!e||!n.current)return;const a={root:r==null?void 0:r.current,rootMargin:i,threshold:typeof o=="number"?o:Yg[o]},l=u=>{const{isIntersecting:c}=u;if(t.isInView===c||(t.isInView=c,s&&!c&&t.hasEnteredView))return;c&&(t.hasEnteredView=!0),n.animationState&&n.animationState.setActive(J.InView,c);const f=n.getProps(),d=c?f.onViewportEnter:f.onViewportLeave;d&&d(u)};return Gg(n.current,a,l)},[e,r,i,o])}function Qg(e,t,n,{fallback:r=!0}){D.useEffect(()=>{!e||!r||(zg!=="production"&&jg(!1,"IntersectionObserver not available on this device. whileInView animations will trigger on mount."),requestAnimationFrame(()=>{t.hasEnteredView=!0;const{onViewportEnter:i}=n.getProps();i&&i(null),n.animationState&&n.animationState.setActive(J.InView,!0)}))},[e])}const Mn=e=>t=>(e(t),null),Kg={inView:Mn(Wg),tap:Mn(qg),focus:Mn(Eg),hover:Mn(Ig)};function H6(){const e=D.useContext(ca);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=D.useId();return D.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}function G6(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}const Zg=e=>/^\-?\d*\.?\d+$/.test(e),Jg=e=>/^0[^.\s]+$/.test(e),fn={delta:0,timestamp:0},W6=1/60*1e3,tv=typeof performance<"u"?()=>performance.now():()=>Date.now(),Y6=typeof window<"u"?e=>window.requestAnimationFrame(e):e=>setTimeout(()=>e(tv()),W6);function ev(e){let t=[],n=[],r=0,i=!1,o=!1;const s=new WeakSet,a={schedule:(l,u=!1,c=!1)=>{const f=c&&i,d=f?t:n;return u&&s.add(l),d.indexOf(l)===-1&&(d.push(l),f&&i&&(r=t.length)),l},cancel:l=>{const u=n.indexOf(l);u!==-1&&n.splice(u,1),s.delete(l)},process:l=>{if(i){o=!0;return}if(i=!0,[t,n]=[n,t],n.length=0,r=t.length,r)for(let u=0;u<r;u++){const c=t[u];c(l),s.has(c)&&(a.schedule(c),e())}i=!1,o&&(o=!1,a.process(l))}};return a}const nv=40;let Mu=!0,Yi=!1,Nu=!1;const oo=["read","update","preRender","render","postRender"],ma=oo.reduce((e,t)=>(e[t]=ev(()=>Yi=!0),e),{}),me=oo.reduce((e,t)=>{const n=ma[t];return e[t]=(r,i=!1,o=!1)=>(Yi||iv(),n.schedule(r,i,o)),e},{}),Xn=oo.reduce((e,t)=>(e[t]=ma[t].cancel,e),{}),ul=oo.reduce((e,t)=>(e[t]=()=>ma[t].process(fn),e),{}),rv=e=>ma[e].process(fn),X6=e=>{Yi=!1,fn.delta=Mu?W6:Math.max(Math.min(e-fn.timestamp,nv),1),fn.timestamp=e,Nu=!0,oo.forEach(rv),Nu=!1,Yi&&(Mu=!1,Y6(X6))},iv=()=>{Yi=!0,Mu=!0,Nu||Y6(X6)};function S0(e,t){e.indexOf(t)===-1&&e.push(t)}function T0(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class b0{constructor(){this.subscriptions=[]}add(t){return S0(this.subscriptions,t),()=>T0(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function C0(e,t){return t?e*(1e3/t):0}const ov=e=>!isNaN(parseFloat(e));class sv{constructor(t,n={}){this.version="7.10.3",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:o,timestamp:s}=fn;this.lastUpdated!==s&&(this.timeDelta=o,this.lastUpdated=s,me.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>me.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=ov(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){return this.events[t]||(this.events[t]=new b0),this.events[t].add(n)}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t){this.passiveEffect=t}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?C0(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.stopAnimation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.stopAnimation&&(this.stopAnimation(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.stopAnimation}clearAnimation(){this.stopAnimation=null}destroy(){this.clearListeners(),this.stop()}}function A1(e,t){return new sv(e,t)}const E0=(e,t)=>n=>!!(ro(n)&&tg.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),Q6=(e,t,n)=>r=>{if(!ro(r))return r;const[i,o,s,a]=r.match(Wi);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:a!==void 0?parseFloat(a):1}},av=e=>D1(0,255,e),cl={...Ir,transform:e=>Math.round(av(e))},gr={test:E0("rgb","red"),parse:Q6("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+cl.transform(e)+", "+cl.transform(t)+", "+cl.transform(n)+", "+gi(mi.transform(r))+")"};function lv(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Ru={test:E0("#"),parse:lv,transform:gr.transform},r1={test:E0("hsl","hue"),parse:Q6("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+Qe.transform(gi(t))+", "+Qe.transform(gi(n))+", "+gi(mi.transform(r))+")"},qt={test:e=>gr.test(e)||Ru.test(e)||r1.test(e),parse:e=>gr.test(e)?gr.parse(e):r1.test(e)?r1.parse(e):Ru.parse(e),transform:e=>ro(e)?e:e.hasOwnProperty("red")?gr.transform(e):r1.transform(e)},K6="${c}",Z6="${n}";function uv(e){var t,n;return isNaN(e)&&ro(e)&&(((t=e.match(Wi))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Au))===null||n===void 0?void 0:n.length)||0)>0}function Is(e){typeof e=="number"&&(e=`${e}`);const t=[];let n=0,r=0;const i=e.match(Au);i&&(n=i.length,e=e.replace(Au,K6),t.push(...i.map(qt.parse)));const o=e.match(Wi);return o&&(r=o.length,e=e.replace(Wi,Z6),t.push(...o.map(Ir.parse))),{values:t,numColors:n,numNumbers:r,tokenised:e}}function J6(e){return Is(e).values}function th(e){const{values:t,numColors:n,tokenised:r}=Is(e),i=t.length;return o=>{let s=r;for(let a=0;a<i;a++)s=s.replace(a<n?K6:Z6,a<n?qt.transform(o[a]):gi(o[a]));return s}}const cv=e=>typeof e=="number"?0:e;function fv(e){const t=J6(e);return th(e)(t.map(cv))}const Qn={test:uv,parse:J6,createTransformer:th,getAnimatableNone:fv},dv=new Set(["brightness","contrast","saturate","opacity"]);function hv(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Wi)||[];if(!r)return e;const i=n.replace(r,"");let o=dv.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const pv=/([a-z-]*)\(.*?\)/g,Iu={...Qn,getAnimatableNone:e=>{const t=e.match(pv);return t?t.map(hv).join(" "):e}},mv={...k6,color:qt,backgroundColor:qt,outlineColor:qt,fill:qt,stroke:qt,borderColor:qt,borderTopColor:qt,borderRightColor:qt,borderBottomColor:qt,borderLeftColor:qt,filter:Iu,WebkitFilter:Iu},k0=e=>mv[e];function P0(e,t){var n;let r=k0(e);return r!==Iu&&(r=Qn),(n=r.getAnimatableNone)===null||n===void 0?void 0:n.call(r,t)}const eh=e=>t=>t.test(e),gv={test:e=>e==="auto",parse:e=>e},nh=[Ir,R,Qe,Sn,ng,eg,gv],H1=e=>nh.find(eh(e)),vv=[...nh,qt,Qn],yv=e=>vv.find(eh(e));function _v(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function wv(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function ga(e,t,n){const r=e.getProps();return w0(r,t,n!==void 0?n:r.custom,_v(e),wv(e))}function xv(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,A1(n))}function Sv(e,t){const n=ga(e,t);let{transitionEnd:r={},transition:i={},...o}=n?e.makeTargetAnimatable(n,!1):{};o={...o,...r};for(const s in o){const a=wg(o[s]);xv(e,s,a)}}function Tv(e,t,n){var r,i;const o=Object.keys(t).filter(a=>!e.hasValue(a)),s=o.length;if(s)for(let a=0;a<s;a++){const l=o[a],u=t[l];let c=null;Array.isArray(u)&&(c=u[0]),c===null&&(c=(i=(r=n[l])!==null&&r!==void 0?r:e.readValue(l))!==null&&i!==void 0?i:t[l]),c!=null&&(typeof c=="string"&&(Zg(c)||Jg(c))?c=parseFloat(c):!yv(c)&&Qn.test(u)&&(c=P0(l,u)),e.addValue(l,A1(c,{owner:e})),n[l]===void 0&&(n[l]=c),c!==null&&e.setBaseTarget(l,c))}}function bv(e,t){return t?(t[e]||t.default||t).from:void 0}function Cv(e,t,n){var r;const i={};for(const o in e){const s=bv(o,t);i[o]=s!==void 0?s:(r=n.getValue(o))===null||r===void 0?void 0:r.get()}return i}function Vs(e){return!!(Je(e)&&e.add)}const Ev=(e,t)=>`${e}: ${t}`;function kv(e,t){const{MotionAppearAnimations:n}=window,r=Ev(e,Rr.has(t)?"transform":t),i=n&&n.get(r);return i?(me.render(()=>{try{i.cancel(),n.delete(r)}catch{}}),i.currentTime||0):0}const Pv="framerAppearId",Dv="data-"+y0(Pv);var Xi=function(){};const Zo=e=>e*1e3,Av={current:!1},D0=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,A0=e=>t=>1-e(1-t),L0=e=>e*e,Lv=A0(L0),O0=D0(L0),dt=(e,t,n)=>-n*e+n*t+e;function fl(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Ov({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;i=fl(l,a,e+1/3),o=fl(l,a,e),s=fl(l,a,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}const dl=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},Mv=[Ru,gr,r1],Nv=e=>Mv.find(t=>t.test(e));function qd(e){const t=Nv(e);let n=t.parse(e);return t===r1&&(n=Ov(n)),n}const rh=(e,t)=>{const n=qd(e),r=qd(t),i={...n};return o=>(i.red=dl(n.red,r.red,o),i.green=dl(n.green,r.green,o),i.blue=dl(n.blue,r.blue,o),i.alpha=dt(n.alpha,r.alpha,o),gr.transform(i))};function ih(e,t){return typeof e=="number"?n=>dt(e,t,n):qt.test(e)?rh(e,t):sh(e,t)}const oh=(e,t)=>{const n=[...e],r=n.length,i=e.map((o,s)=>ih(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}},Rv=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=ih(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}},sh=(e,t)=>{const n=Qn.createTransformer(t),r=Is(e),i=Is(t);return r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?pa(oh(r.values,i.values),n):s=>`${s>0?t:e}`},qs=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Fd=(e,t)=>n=>dt(e,t,n);function Iv(e){return typeof e=="number"?Fd:typeof e=="string"?qt.test(e)?rh:sh:Array.isArray(e)?oh:typeof e=="object"?Rv:Fd}function Vv(e,t,n){const r=[],i=n||Iv(e[0]),o=e.length-1;for(let s=0;s<o;s++){let a=i(e[s],e[s+1]);if(t){const l=Array.isArray(t)?t[s]:t;a=pa(l,a)}r.push(a)}return r}function ah(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;Xi(o===t.length),Xi(!r||!Array.isArray(r)||r.length===o-1),e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=Vv(t,r,i),a=s.length,l=u=>{let c=0;if(a>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const f=qs(e[c],e[c+1],u);return s[c](f)};return n?u=>l(D1(e[0],e[o-1],u)):l}const M0=e=>e,lh=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,qv=1e-7,Fv=12;function Bv(e,t,n,r,i){let o,s,a=0;do s=t+(n-t)/2,o=lh(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>qv&&++a<Fv);return s}function uh(e,t,n,r){if(e===t&&n===r)return M0;const i=o=>Bv(o,0,1,e,n);return o=>o===0||o===1?o:lh(i(o),t,r)}const ch=e=>1-Math.sin(Math.acos(e)),N0=A0(ch),zv=D0(N0),fh=uh(.33,1.53,.69,.99),R0=A0(fh),jv=D0(R0),Uv=e=>(e*=2)<1?.5*R0(e):.5*(2-Math.pow(2,-10*(e-1))),Bd={linear:M0,easeIn:L0,easeInOut:O0,easeOut:Lv,circIn:ch,circInOut:zv,circOut:N0,backIn:R0,backInOut:jv,backOut:fh,anticipate:Uv},zd=e=>{if(Array.isArray(e)){Xi(e.length===4);const[t,n,r,i]=e;return uh(t,n,r,i)}else if(typeof e=="string")return Xi(Bd[e]!==void 0),Bd[e];return e},$v=e=>Array.isArray(e)&&typeof e[0]!="number";function Hv(e,t){return e.map(()=>t||O0).splice(0,e.length-1)}function Gv(e){const t=e.length;return e.map((n,r)=>r!==0?r/(t-1):0)}function Wv(e,t){return e.map(n=>n*t)}function Fs({keyframes:e,ease:t=O0,times:n,duration:r=300}){e=[...e];const i=Fs[0],o=$v(t)?t.map(zd):zd(t),s={done:!1,value:i},a=Wv(n&&n.length===Fs.length?n:Gv(e),r);function l(){return ah(a,e,{ease:Array.isArray(o)?o:Hv(e,o)})}let u=l();return{next:c=>(s.value=u(c),s.done=c>=r,s),flipTarget:()=>{e.reverse(),u=l()}}}const hl=.001,Yv=.01,Xv=10,Qv=.05,Kv=1;function Zv({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,o,s=1-t;s=D1(Qv,Kv,s),e=D1(Yv,Xv,e/1e3),s<1?(i=u=>{const c=u*s,f=c*e,d=c-n,p=Vu(u,s),v=Math.exp(-f);return hl-d/p*v},o=u=>{const f=u*s*e,d=f*n+n,p=Math.pow(s,2)*Math.pow(u,2)*e,v=Math.exp(-f),h=Vu(Math.pow(u,2),s);return(-i(u)+hl>0?-1:1)*((d-p)*v)/h}):(i=u=>{const c=Math.exp(-u*e),f=(u-n)*e+1;return-hl+c*f},o=u=>{const c=Math.exp(-u*e),f=(n-u)*(e*e);return c*f});const a=5/e,l=ty(i,o,a);if(e=e*1e3,isNaN(l))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const Jv=12;function ty(e,t,n){let r=n;for(let i=1;i<Jv;i++)r=r-e(r)/t(r);return r}function Vu(e,t){return e*Math.sqrt(1-t*t)}const ey=["duration","bounce"],ny=["stiffness","damping","mass"];function jd(e,t){return t.some(n=>e[n]!==void 0)}function ry(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!jd(e,ny)&&jd(e,ey)){const n=Zv(e);t={...t,...n,velocity:0,mass:1},t.isResolvedFromDuration=!0}return t}const iy=5;function dh({keyframes:e,restSpeed:t=2,restDelta:n=.01,...r}){let i=e[0],o=e[e.length-1];const s={done:!1,value:i},{stiffness:a,damping:l,mass:u,velocity:c,duration:f,isResolvedFromDuration:d}=ry(r);let p=oy,v=c?-(c/1e3):0;const h=l/(2*Math.sqrt(a*u));function _(){const m=o-i,g=Math.sqrt(a/u)/1e3;if(n===void 0&&(n=Math.min(Math.abs(o-i)/100,.4)),h<1){const y=Vu(g,h);p=w=>{const x=Math.exp(-h*g*w);return o-x*((v+h*g*m)/y*Math.sin(y*w)+m*Math.cos(y*w))}}else if(h===1)p=y=>o-Math.exp(-g*y)*(m+(v+g*m)*y);else{const y=g*Math.sqrt(h*h-1);p=w=>{const x=Math.exp(-h*g*w),S=Math.min(y*w,300);return o-x*((v+h*g*m)*Math.sinh(S)+y*m*Math.cosh(S))/y}}}return _(),{next:m=>{const g=p(m);if(d)s.done=m>=f;else{let y=v;if(m!==0)if(h<1){const S=Math.max(0,m-iy);y=C0(g-p(S),m-S)}else y=0;const w=Math.abs(y)<=t,x=Math.abs(o-g)<=n;s.done=w&&x}return s.value=s.done?o:g,s},flipTarget:()=>{v=-v,[i,o]=[o,i],_()}}}dh.needsInterpolation=(e,t)=>typeof e=="string"||typeof t=="string";const oy=e=>0;function sy({keyframes:e=[0],velocity:t=0,power:n=.8,timeConstant:r=350,restDelta:i=.5,modifyTarget:o}){const s=e[0],a={done:!1,value:s};let l=n*t;const u=s+l,c=o===void 0?u:o(u);return c!==u&&(l=c-s),{next:f=>{const d=-l*Math.exp(-f/r);return a.done=!(d>i||d<-i),a.value=a.done?c:c+d,a},flipTarget:()=>{}}}const ay={decay:sy,keyframes:Fs,tween:Fs,spring:dh};function hh(e,t,n=0){return e-t-n}function ly(e,t=0,n=0,r=!0){return r?hh(t+-e,t,n):t-(e-t)+n}function uy(e,t,n,r){return r?e>=t+n:e<=-n}const cy=e=>{const t=({delta:n})=>e(n);return{start:()=>me.update(t,!0),stop:()=>Xn.update(t)}};function Bs({duration:e,driver:t=cy,elapsed:n=0,repeat:r=0,repeatType:i="loop",repeatDelay:o=0,keyframes:s,autoplay:a=!0,onPlay:l,onStop:u,onComplete:c,onRepeat:f,onUpdate:d,type:p="keyframes",...v}){var h,_;let m,g=0,y=e,w,x=!1,S=!0,T;const b=ay[s.length>2?"keyframes":p],C=s[0],E=s[s.length-1];!((_=(h=b).needsInterpolation)===null||_===void 0)&&_.call(h,C,E)&&(T=ah([0,100],[C,E],{clamp:!1}),s=[0,100]);const L=b({...v,duration:e,keyframes:s});function O(){g++,i==="reverse"?(S=g%2===0,n=ly(n,y,o,S)):(n=hh(n,y,o),i==="mirror"&&L.flipTarget()),x=!1,f&&f()}function B(){m.stop(),c&&c()}function G(F){if(S||(F=-F),n+=F,!x){const z=L.next(Math.max(0,n));w=z.value,T&&(w=T(w)),x=S?z.done:n<=0}d&&d(w),x&&(g===0&&(y=y!==void 0?y:n),g<r?uy(n,y,o,S)&&O():B())}function I(){l&&l(),m=t(G),m.start()}return a&&I(),{stop:()=>{u&&u(),m.stop()},sample:F=>L.next(Math.max(0,F))}}function fy(e){return!e||Array.isArray(e)||typeof e=="string"&&ph[e]}const ei=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,ph={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ei([0,.65,.55,1]),circOut:ei([.55,0,1,.45]),backIn:ei([.31,.01,.66,-.59]),backOut:ei([.33,1.53,.69,.99])};function dy(e){if(e)return Array.isArray(e)?ei(e):ph[e]}function hy(e,t,n,{delay:r=0,duration:i,repeat:o=0,repeatType:s="loop",ease:a,times:l}={}){return e.animate({[t]:n,offset:l},{delay:r,duration:i,easing:dy(a),fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}const Lo=10;function py(e,t,{onUpdate:n,onComplete:r,...i}){let{keyframes:o,duration:s=.3,elapsed:a=0,ease:l}=i;if(i.type==="spring"||!fy(i.ease)){const c=Bs(i);let f={done:!1,value:o[0]};const d=[];let p=0;for(;!f.done;)f=c.sample(p),d.push(f.value),p+=Lo;o=d,s=p-Lo,l="linear"}const u=hy(e.owner.current,t,o,{...i,delay:-a,duration:s,ease:l});return u.onfinish=()=>{e.set(o[o.length-1]),r&&r()},()=>{const{currentTime:c}=u;if(c){const f=Bs(i);e.setWithVelocity(f.sample(c-Lo).value,f.sample(c).value,Lo)}me.update(()=>u.cancel())}}function mh(e,t){const n=performance.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(Xn.read(r),e(o-t))};return me.read(r,!0),()=>Xn.read(r)}function my({keyframes:e,elapsed:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),()=>{});return t?mh(i,-t):i()}function gy({keyframes:e,velocity:t=0,min:n,max:r,power:i=.8,timeConstant:o=750,bounceStiffness:s=500,bounceDamping:a=10,restDelta:l=1,modifyTarget:u,driver:c,onUpdate:f,onComplete:d,onStop:p}){const v=e[0];let h;function _(w){return n!==void 0&&w<n||r!==void 0&&w>r}function m(w){return n===void 0?r:r===void 0||Math.abs(n-w)<Math.abs(r-w)?n:r}function g(w){h==null||h.stop(),h=Bs({keyframes:[0,1],velocity:0,...w,driver:c,onUpdate:x=>{var S;f==null||f(x),(S=w.onUpdate)===null||S===void 0||S.call(w,x)},onComplete:d,onStop:p})}function y(w){g({type:"spring",stiffness:s,damping:a,restDelta:l,...w})}if(_(v))y({velocity:t,keyframes:[v,m(v)]});else{let w=i*t+v;typeof u<"u"&&(w=u(w));const x=m(w),S=x===n?-1:1;let T,b;const C=E=>{T=b,b=E,t=C0(E-T,fn.delta),(S===1&&E>x||S===-1&&E<x)&&y({keyframes:[E,x],velocity:t})};g({type:"decay",keyframes:[v,0],velocity:t,timeConstant:o,power:i,restDelta:l,modifyTarget:u,onUpdate:_(w)?C:void 0})}return{stop:()=>h==null?void 0:h.stop()}}const or=()=>({type:"spring",stiffness:500,damping:25,restSpeed:10}),Oo=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),pl=()=>({type:"keyframes",ease:"linear",duration:.3}),vy={type:"keyframes",duration:.8},Ud={x:or,y:or,z:or,rotate:or,rotateX:or,rotateY:or,rotateZ:or,scaleX:Oo,scaleY:Oo,scale:Oo,opacity:pl,backgroundColor:pl,color:pl,default:Oo},yy=(e,{keyframes:t})=>t.length>2?vy:(Ud[e]||Ud.default)(t[1]),qu=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&Qn.test(t)&&!t.startsWith("url("));function _y({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,...u}){return!!Object.keys(u).length}function $d(e){return e===0||typeof e=="string"&&parseFloat(e)===0&&e.indexOf(" ")===-1}function Hd(e){return typeof e=="number"?0:P0("",e)}function gh(e,t){return e[t]||e.default||e}function wy(e,t,n,r){const i=qu(t,n);let o=r.from!==void 0?r.from:e.get();return o==="none"&&i&&typeof n=="string"?o=P0(t,n):$d(o)&&typeof n=="string"?o=Hd(n):!Array.isArray(n)&&$d(n)&&typeof o=="string"&&(n=Hd(o)),Array.isArray(n)?(n[0]===null&&(n[0]=o),n):[o,n]}const Gd={waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate")},ml={},vh={};for(const e in Gd)vh[e]=()=>(ml[e]===void 0&&(ml[e]=Gd[e]()),ml[e]);const xy=new Set(["opacity"]),I0=(e,t,n,r={})=>i=>{const o=gh(r,e)||{},s=o.delay||r.delay||0;let{elapsed:a=0}=r;a=a-Zo(s);const l=wy(t,e,n,o),u=l[0],c=l[l.length-1],f=qu(e,u),d=qu(e,c);let p={keyframes:l,velocity:t.getVelocity(),...o,elapsed:a,onUpdate:m=>{t.set(m),o.onUpdate&&o.onUpdate(m)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(!f||!d||Av.current||o.type===!1)return my(p);if(o.type==="inertia"){const m=gy(p);return()=>m.stop()}_y(o)||(p={...p,...yy(e,p)}),p.duration&&(p.duration=Zo(p.duration)),p.repeatDelay&&(p.repeatDelay=Zo(p.repeatDelay));const v=t.owner,h=v&&v.current;if(vh.waapi()&&xy.has(e)&&!p.repeatDelay&&p.repeatType!=="mirror"&&p.damping!==0&&v&&h instanceof HTMLElement&&!v.getProps().onUpdate)return py(t,e,p);{const m=Bs(p);return()=>m.stop()}};function Sy(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>Fu(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=Fu(e,t,n);else{const i=typeof t=="function"?ga(e,t,n.custom):t;r=yh(e,i,n)}return r.then(()=>e.notify("AnimationComplete",t))}function Fu(e,t,n={}){var r;const i=ga(e,t,n.custom);let{transition:o=e.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(o=n.transitionOverride);const s=i?()=>yh(e,i,n):()=>Promise.resolve(),a=!((r=e.variantChildren)===null||r===void 0)&&r.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:f,staggerDirection:d}=o;return Ty(e,t,c+u,f,d,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[u,c]=l==="beforeChildren"?[s,a]:[a,s];return u().then(c)}else return Promise.all([s(),a(n.delay)])}function yh(e,t,{delay:n=0,transitionOverride:r,type:i}={}){var o;let{transition:s=e.getDefaultTransition(),transitionEnd:a,...l}=e.makeTargetAnimatable(t);const u=e.getValue("willChange");r&&(s=r);const c=[],f=i&&((o=e.animationState)===null||o===void 0?void 0:o.getState()[i]);for(const d in l){const p=e.getValue(d),v=l[d];if(!p||v===void 0||f&&Cy(f,d))continue;let h={delay:n,elapsed:0,...s};if(e.shouldReduceMotion&&Rr.has(d)&&(h={...h,type:!1,delay:0}),!p.hasAnimated){const m=e.getProps()[Dv];m&&(h.elapsed=kv(m,d))}let _=p.start(I0(d,p,v,h));Vs(u)&&(u.add(d),_=_.then(()=>u.remove(d))),c.push(_)}return Promise.all(c).then(()=>{a&&Sv(e,a)})}function Ty(e,t,n=0,r=0,i=1,o){const s=[],a=(e.variantChildren.size-1)*r,l=i===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(by).forEach((u,c)=>{s.push(Fu(u,t,{...o,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function by(e,t){return e.sortNodePosition(t)}function Cy({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}const V0=[J.Animate,J.InView,J.Focus,J.Hover,J.Tap,J.Drag,J.Exit],Ey=[...V0].reverse(),ky=V0.length;function Py(e){return t=>Promise.all(t.map(({animation:n,options:r})=>Sy(e,n,r)))}function Dy(e){let t=Py(e);const n=Ly();let r=!0;const i=(l,u)=>{const c=ga(e,u);if(c){const{transition:f,transitionEnd:d,...p}=c;l={...l,...p,...d}}return l};function o(l){t=l(e)}function s(l,u){const c=e.getProps(),f=e.getVariantContext(!0)||{},d=[],p=new Set;let v={},h=1/0;for(let m=0;m<ky;m++){const g=Ey[m],y=n[g],w=c[g]!==void 0?c[g]:f[g],x=Hi(w),S=g===u?y.isActive:null;S===!1&&(h=m);let T=w===f[g]&&w!==c[g]&&x;if(T&&r&&e.manuallyAnimateOnMount&&(T=!1),y.protectedKeys={...v},!y.isActive&&S===null||!w&&!y.prevProp||fa(w)||typeof w=="boolean")continue;const b=Ay(y.prevProp,w);let C=b||g===u&&y.isActive&&!T&&x||m>h&&x;const E=Array.isArray(w)?w:[w];let L=E.reduce(i,{});S===!1&&(L={});const{prevResolvedValues:O={}}=y,B={...O,...L},G=I=>{C=!0,p.delete(I),y.needsAnimating[I]=!0};for(const I in B){const F=L[I],z=O[I];v.hasOwnProperty(I)||(F!==z?Ns(F)&&Ns(z)?!G6(F,z)||b?G(I):y.protectedKeys[I]=!0:F!==void 0?G(I):p.add(I):F!==void 0&&p.has(I)?G(I):y.protectedKeys[I]=!0)}y.prevProp=w,y.prevResolvedValues=L,y.isActive&&(v={...v,...L}),r&&e.blockInitialAnimation&&(C=!1),C&&!T&&d.push(...E.map(I=>({animation:I,options:{type:g,...l}})))}if(p.size){const m={};p.forEach(g=>{const y=e.getBaseTarget(g);y!==void 0&&(m[g]=y)}),d.push({animation:m})}let _=!!d.length;return r&&c.initial===!1&&!e.manuallyAnimateOnMount&&(_=!1),r=!1,_?t(d):Promise.resolve()}function a(l,u,c){var f;if(n[l].isActive===u)return Promise.resolve();(f=e.variantChildren)===null||f===void 0||f.forEach(p=>{var v;return(v=p.animationState)===null||v===void 0?void 0:v.setActive(l,u)}),n[l].isActive=u;const d=s(c,l);for(const p in n)n[p].protectedKeys={};return d}return{animateChanges:s,setActive:a,setAnimateFunction:o,getState:()=>n}}function Ay(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!G6(t,e):!1}function sr(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Ly(){return{[J.Animate]:sr(!0),[J.InView]:sr(),[J.Hover]:sr(),[J.Tap]:sr(),[J.Drag]:sr(),[J.Focus]:sr(),[J.Exit]:sr()}}const Oy={animation:Mn(({visualElement:e,animate:t})=>{e.animationState||(e.animationState=Dy(e)),fa(t)&&D.useEffect(()=>t.subscribe(e),[t])}),exit:Mn(e=>{const{custom:t,visualElement:n}=e,[r,i]=H6(),o=D.useContext(ca);D.useEffect(()=>{n.isPresent=r;const s=n.animationState&&n.animationState.setActive(J.Exit,!r,{custom:o&&o.custom||t});s&&!r&&s.then(i)},[r])})},Wd=(e,t)=>Math.abs(e-t);function My(e,t){const n=Wd(e.x,t.x),r=Wd(e.y,t.y);return Math.sqrt(n**2+r**2)}class _h{constructor(t,n,{transformPagePoint:r}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const u=vl(this.lastMoveEventInfo,this.history),c=this.startEvent!==null,f=My(u.offset,{x:0,y:0})>=3;if(!c&&!f)return;const{point:d}=u,{timestamp:p}=fn;this.history.push({...d,timestamp:p});const{onStart:v,onMove:h}=this.handlers;c||(v&&v(this.lastMoveEvent,u),this.startEvent=this.lastMoveEvent),h&&h(this.lastMoveEvent,u)},this.handlePointerMove=(u,c)=>{if(this.lastMoveEvent=u,this.lastMoveEventInfo=gl(c,this.transformPagePoint),I6(u)&&u.buttons===0){this.handlePointerUp(u,c);return}me.update(this.updatePoint,!0)},this.handlePointerUp=(u,c)=>{this.end();const{onEnd:f,onSessionEnd:d}=this.handlers,p=vl(gl(c,this.transformPagePoint),this.history);this.startEvent&&f&&f(u,p),d&&d(u,p)},V6(t)&&t.touches.length>1)return;this.handlers=n,this.transformPagePoint=r;const i=x0(t),o=gl(i,this.transformPagePoint),{point:s}=o,{timestamp:a}=fn;this.history=[{...s,timestamp:a}];const{onSessionStart:l}=n;l&&l(t,vl(o,this.history)),this.removeListeners=pa(g1(window,"pointermove",this.handlePointerMove),g1(window,"pointerup",this.handlePointerUp),g1(window,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Xn.update(this.updatePoint)}}function gl(e,t){return t?{point:t(e.point)}:e}function Yd(e,t){return{x:e.x-t.x,y:e.y-t.y}}function vl({point:e},t){return{point:e,delta:Yd(e,wh(t)),offset:Yd(e,Ny(t)),velocity:Ry(t,.1)}}function Ny(e){return e[0]}function wh(e){return e[e.length-1]}function Ry(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=wh(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>Zo(t)));)n--;if(!r)return{x:0,y:0};const o=(i.timestamp-r.timestamp)/1e3;if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function ye(e){return e.max-e.min}function Bu(e,t=0,n=.01){return Math.abs(e-t)<=n}function Xd(e,t,n,r=.5){e.origin=r,e.originPoint=dt(t.min,t.max,e.origin),e.scale=ye(n)/ye(t),(Bu(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=dt(n.min,n.max,e.origin)-e.originPoint,(Bu(e.translate)||isNaN(e.translate))&&(e.translate=0)}function vi(e,t,n,r){Xd(e.x,t.x,n.x,r==null?void 0:r.originX),Xd(e.y,t.y,n.y,r==null?void 0:r.originY)}function Qd(e,t,n){e.min=n.min+t.min,e.max=e.min+ye(t)}function Iy(e,t,n){Qd(e.x,t.x,n.x),Qd(e.y,t.y,n.y)}function Kd(e,t,n){e.min=t.min-n.min,e.max=e.min+ye(t)}function yi(e,t,n){Kd(e.x,t.x,n.x),Kd(e.y,t.y,n.y)}function Vy(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?dt(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?dt(n,e,r.max):Math.min(e,n)),e}function Zd(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function qy(e,{top:t,left:n,bottom:r,right:i}){return{x:Zd(e.x,n,i),y:Zd(e.y,t,r)}}function Jd(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function Fy(e,t){return{x:Jd(e.x,t.x),y:Jd(e.y,t.y)}}function By(e,t){let n=.5;const r=ye(e),i=ye(t);return i>r?n=qs(t.min,t.max-r,e.min):r>i&&(n=qs(e.min,e.max-i,t.min)),D1(0,1,n)}function zy(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const zu=.35;function jy(e=zu){return e===!1?e=0:e===!0&&(e=zu),{x:t8(e,"left","right"),y:t8(e,"top","bottom")}}function t8(e,t,n){return{min:e8(e,t),max:e8(e,n)}}function e8(e,t){return typeof e=="number"?e:e[t]||0}const n8=()=>({translate:0,scale:1,origin:0,originPoint:0}),_i=()=>({x:n8(),y:n8()}),r8=()=>({min:0,max:0}),yt=()=>({x:r8(),y:r8()});function je(e){return[e("x"),e("y")]}function xh({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function Uy({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function $y(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function yl(e){return e===void 0||e===1}function ju({scale:e,scaleX:t,scaleY:n}){return!yl(e)||!yl(t)||!yl(n)}function cr(e){return ju(e)||Sh(e)||e.z||e.rotate||e.rotateX||e.rotateY}function Sh(e){return i8(e.x)||i8(e.y)}function i8(e){return e&&e!=="0%"}function zs(e,t,n){const r=e-n,i=t*r;return n+i}function o8(e,t,n,r,i){return i!==void 0&&(e=zs(e,i,r)),zs(e,n,r)+t}function Uu(e,t=0,n=1,r,i){e.min=o8(e.min,t,n,r,i),e.max=o8(e.max,t,n,r,i)}function Th(e,{x:t,y:n}){Uu(e.x,t.translate,t.scale,t.originPoint),Uu(e.y,n.translate,n.scale,n.originPoint)}function Hy(e,t,n,r=!1){var i,o;const s=n.length;if(!s)return;t.x=t.y=1;let a,l;for(let u=0;u<s;u++)a=n[u],l=a.projectionDelta,((o=(i=a.instance)===null||i===void 0?void 0:i.style)===null||o===void 0?void 0:o.display)!=="contents"&&(r&&a.options.layoutScroll&&a.scroll&&a!==a.root&&i1(e,{x:-a.scroll.offset.x,y:-a.scroll.offset.y}),l&&(t.x*=l.x.scale,t.y*=l.y.scale,Th(e,l)),r&&cr(a.latestValues)&&i1(e,a.latestValues));t.x=s8(t.x),t.y=s8(t.y)}function s8(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Cn(e,t){e.min=e.min+t,e.max=e.max+t}function a8(e,t,[n,r,i]){const o=t[i]!==void 0?t[i]:.5,s=dt(e.min,e.max,o);Uu(e,t[n],t[r],s,t.scale)}const Gy=["x","scaleX","originX"],Wy=["y","scaleY","originY"];function i1(e,t){a8(e.x,t,Gy),a8(e.y,t,Wy)}function bh(e,t){return xh($y(e.getBoundingClientRect(),t))}function Yy(e,t,n){const r=bh(e,n),{scroll:i}=t;return i&&(Cn(r.x,i.offset.x),Cn(r.y,i.offset.y)),r}const Xy=new WeakMap;class Qy{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=yt(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){if(this.visualElement.isPresent===!1)return;const r=a=>{this.stopAnimation(),n&&this.snapToCursor(x0(a,"page").point)},i=(a,l)=>{var u;const{drag:c,dragPropagation:f,onDragStart:d}=this.getProps();c&&!f&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=z6(c),!this.openGlobalLock)||(this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),je(p=>{var v,h;let _=this.getAxisMotionValue(p).get()||0;if(Qe.test(_)){const m=(h=(v=this.visualElement.projection)===null||v===void 0?void 0:v.layout)===null||h===void 0?void 0:h.layoutBox[p];m&&(_=ye(m)*(parseFloat(_)/100))}this.originPoint[p]=_}),d==null||d(a,l),(u=this.visualElement.animationState)===null||u===void 0||u.setActive(J.Drag,!0))},o=(a,l)=>{const{dragPropagation:u,dragDirectionLock:c,onDirectionLock:f,onDrag:d}=this.getProps();if(!u&&!this.openGlobalLock)return;const{offset:p}=l;if(c&&this.currentDirection===null){this.currentDirection=Ky(p),this.currentDirection!==null&&(f==null||f(this.currentDirection));return}this.updateAxis("x",l.point,p),this.updateAxis("y",l.point,p),this.visualElement.render(),d==null||d(a,l)},s=(a,l)=>this.stop(a,l);this.panSession=new _h(t,{onSessionStart:r,onStart:i,onMove:o,onSessionEnd:s},{transformPagePoint:this.visualElement.getTransformPagePoint()})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o==null||o(t,n)}cancel(){var t,n;this.isDragging=!1,this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!1),(t=this.panSession)===null||t===void 0||t.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),(n=this.visualElement.animationState)===null||n===void 0||n.setActive(J.Drag,!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Mo(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=Vy(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){const{dragConstraints:t,dragElastic:n}=this.getProps(),{layout:r}=this.visualElement.projection||{},i=this.constraints;t&&n1(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=qy(r.layoutBox,t):this.constraints=!1,this.elastic=jy(n),i!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&je(o=>{this.getAxisMotionValue(o)&&(this.constraints[o]=zy(r.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!n1(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=Yy(r,i.root,this.visualElement.getTransformPagePoint());let s=Fy(i.layout.layoutBox,o);if(n){const a=n(Uy(s));this.hasMutatedConstraints=!!a,a&&(s=xh(a))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=je(c=>{if(!Mo(c,n,this.currentDirection))return;let f=(l==null?void 0:l[c])||{};s&&(f={min:0,max:0});const d=i?200:1e6,p=i?40:1e7,v={type:"inertia",velocity:r?t[c]:0,bounceStiffness:d,bounceDamping:p,timeConstant:750,restDelta:1,restSpeed:10,...o,...f};return this.startAxisValueAnimation(c,v)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(I0(t,r,0,n))}stopAnimation(){je(t=>this.getAxisMotionValue(t).stop())}getAxisMotionValue(t){var n;const r="_drag"+t.toUpperCase(),i=this.visualElement.getProps()[r];return i||this.visualElement.getValue(t,((n=this.visualElement.getProps().initial)===null||n===void 0?void 0:n[t])||0)}snapToCursor(t){je(n=>{const{drag:r}=this.getProps();if(!Mo(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:a}=i.layout.layoutBox[n];o.set(t[n]-dt(s,a,.5))}})}scalePositionWithinConstraints(){var t;if(!this.visualElement.current)return;const{drag:n,dragConstraints:r}=this.getProps(),{projection:i}=this.visualElement;if(!n1(r)||!i||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};je(a=>{const l=this.getAxisMotionValue(a);if(l){const u=l.get();o[a]=By({min:u,max:u},this.constraints[a])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",(t=i.root)===null||t===void 0||t.updateScroll(),i.updateLayout(),this.resolveConstraints(),je(a=>{if(!Mo(a,n,null))return;const l=this.getAxisMotionValue(a),{min:u,max:c}=this.constraints[a];l.set(dt(u,c,o[a]))})}addListeners(){var t;if(!this.visualElement.current)return;Xy.set(this.visualElement,this);const n=this.visualElement.current,r=g1(n,"pointerdown",u=>{const{drag:c,dragListener:f=!0}=this.getProps();c&&f&&this.start(u)}),i=()=>{const{dragConstraints:u}=this.getProps();n1(u)&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,s=o.addEventListener("measure",i);o&&!o.layout&&((t=o.root)===null||t===void 0||t.updateScroll(),o.updateLayout()),i();const a=ha(window,"resize",()=>this.scalePositionWithinConstraints()),l=o.addEventListener("didUpdate",({delta:u,hasLayoutChanged:c})=>{this.isDragging&&c&&(je(f=>{const d=this.getAxisMotionValue(f);d&&(this.originPoint[f]+=u[f].translate,d.set(d.get()+u[f].translate))}),this.visualElement.render())});return()=>{a(),r(),s(),l==null||l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=zu,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:a}}}function Mo(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function Ky(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}function Zy(e){const{dragControls:t,visualElement:n}=e,r=d0(()=>new Qy(n));D.useEffect(()=>t&&t.subscribe(r),[r,t]),D.useEffect(()=>r.addListeners(),[r])}function Jy({onPan:e,onPanStart:t,onPanEnd:n,onPanSessionStart:r,visualElement:i}){const o=e||t||n||r,s=D.useRef(null),{transformPagePoint:a}=D.useContext(f0),l={onSessionStart:r,onStart:t,onMove:e,onEnd:(c,f)=>{s.current=null,n&&n(c,f)}};D.useEffect(()=>{s.current!==null&&s.current.updateHandlers(l)});function u(c){s.current=new _h(c,l,{transformPagePoint:a})}Rs(i,"pointerdown",o&&u),$6(()=>s.current&&s.current.end())}const t_={pan:Mn(Jy),drag:Mn(Zy)};function $u(e){return typeof e=="string"&&e.startsWith("var(--")}const Ch=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function e_(e){const t=Ch.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function Hu(e,t,n=1){const[r,i]=e_(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);return o?o.trim():$u(i)?Hu(i,t,n+1):i}function n_(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const o=i.get();if(!$u(o))return;const s=Hu(o,r);s&&i.set(s)});for(const i in t){const o=t[i];if(!$u(o))continue;const s=Hu(o,r);s&&(t[i]=s,n&&n[i]===void 0&&(n[i]=o))}return{target:t,transitionEnd:n}}const r_=new Set(["width","height","top","left","right","bottom","x","y"]),Eh=e=>r_.has(e),i_=e=>Object.keys(e).some(Eh),kh=(e,t)=>{e.set(t,!1),e.set(t)},l8=e=>e===Ir||e===R;var u8;(function(e){e.width="width",e.height="height",e.left="left",e.right="right",e.top="top",e.bottom="bottom"})(u8||(u8={}));const c8=(e,t)=>parseFloat(e.split(", ")[t]),f8=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return c8(i[1],t);{const o=r.match(/^matrix\((.+)\)$/);return o?c8(o[1],e):0}},o_=new Set(["x","y","z"]),s_=Os.filter(e=>!o_.has(e));function a_(e){const t=[];return s_.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const d8={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:f8(4,13),y:f8(5,14)},l_=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:s}=o,a={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{a[u]=d8[u](r,o)}),t.render();const l=t.measureViewportBox();return n.forEach(u=>{const c=t.getValue(u);kh(c,a[u]),e[u]=d8[u](l,o)}),e},u_=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(Eh);let o=[],s=!1;const a=[];if(i.forEach(l=>{const u=e.getValue(l);if(!e.hasValue(l))return;let c=n[l],f=H1(c);const d=t[l];let p;if(Ns(d)){const v=d.length,h=d[0]===null?1:0;c=d[h],f=H1(c);for(let _=h;_<v;_++)p?Xi(H1(d[_])===p):p=H1(d[_])}else p=H1(d);if(f!==p)if(l8(f)&&l8(p)){const v=u.get();typeof v=="string"&&u.set(parseFloat(v)),typeof d=="string"?t[l]=parseFloat(d):Array.isArray(d)&&p===R&&(t[l]=d.map(parseFloat))}else f!=null&&f.transform&&(p!=null&&p.transform)&&(c===0||d===0)?c===0?u.set(p.transform(c)):t[l]=f.transform(d):(s||(o=a_(e),s=!0),a.push(l),r[l]=r[l]!==void 0?r[l]:t[l],kh(u,d))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,u=l_(t,e,a);return o.length&&o.forEach(([c,f])=>{e.getValue(c).set(f)}),e.render(),Nr&&l!==null&&window.scrollTo({top:l}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function c_(e,t,n,r){return i_(t)?u_(e,t,n,r):{target:t,transitionEnd:r}}const f_=(e,t,n,r)=>{const i=n_(e,t,r);return t=i.target,r=i.transitionEnd,c_(e,t,n,r)},Gu={current:null},Ph={current:!1};function d_(){if(Ph.current=!0,!!Nr)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Gu.current=e.matches;e.addListener(t),t()}else Gu.current=!1}function h_(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],s=n[i];if(Je(o))e.addValue(i,o),Vs(r)&&r.add(i);else if(Je(s))e.addValue(i,A1(o,{owner:e})),Vs(r)&&r.remove(i);else if(s!==o)if(e.hasValue(i)){const a=e.getValue(i);!a.hasAnimated&&a.set(o)}else{const a=e.getStaticValue(i);e.addValue(i,A1(a!==void 0?a:o))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const Dh=Object.keys(Gi),p_=Dh.length,h8=["AnimationStart","AnimationComplete","Update","Unmount","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class m_{constructor({parent:t,props:n,reducedMotionConfig:r,visualState:i},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.isPresent=!0,this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>me.render(this.render,!1,!0);const{latestValues:s,renderState:a}=i;this.latestValues=s,this.baseTarget={...s},this.initialValues=n.initial?{...s}:{},this.renderState=a,this.parent=t,this.props=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.isControllingVariants=da(n),this.isVariantNode=S6(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:l,...u}=this.scrapeMotionValuesFromProps(n);for(const c in u){const f=u[c];s[c]!==void 0&&Je(f)&&(f.set(s[c],!1),Vs(l)&&l.add(c))}}scrapeMotionValuesFromProps(t){return{}}mount(t){var n;this.current=t,this.projection&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=(n=this.parent)===null||n===void 0?void 0:n.addVariantChild(this)),this.values.forEach((r,i)=>this.bindToMotionValue(i,r)),Ph.current||d_(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Gu.current,this.parent&&this.parent.children.add(this),this.setProps(this.props)}unmount(){var t,n,r;(t=this.projection)===null||t===void 0||t.unmount(),Xn.update(this.notifyUpdate),Xn.render(this.render),this.valueSubscriptions.forEach(i=>i()),(n=this.removeFromVariantTree)===null||n===void 0||n.call(this),(r=this.parent)===null||r===void 0||r.children.delete(this);for(const i in this.events)this.events[i].clear();this.current=null}bindToMotionValue(t,n){const r=Rr.has(t),i=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&me.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),o()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures(t,n,r,i,o,s){const a=[];for(let l=0;l<p_;l++){const u=Dh[l],{isEnabled:c,Component:f}=Gi[u];c(t)&&f&&a.push(D.createElement(f,{key:u,...t,visualElement:this}))}if(!this.projection&&o){this.projection=new o(i,this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:u,drag:c,dragConstraints:f,layoutScroll:d}=t;this.projection.setOptions({layoutId:l,layout:u,alwaysMeasureLayout:!!c||f&&n1(f),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:s,layoutScroll:d})}return a}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):yt()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}setProps(t){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.props=t;for(let n=0;n<h8.length;n++){const r=h8[n];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);const i=t["on"+r];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=h_(this,this.scrapeMotionValuesFromProps(t),this.prevMotionValues)}getProps(){return this.props}getVariant(t){var n;return(n=this.props.variants)===null||n===void 0?void 0:n[t]}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){var t;return this.isVariantNode?this:(t=this.parent)===null||t===void 0?void 0:t.getClosestVariantNode()}getVariantContext(t=!1){var n,r;if(t)return(n=this.parent)===null||n===void 0?void 0:n.getVariantContext();if(!this.isControllingVariants){const o=((r=this.parent)===null||r===void 0?void 0:r.getVariantContext())||{};return this.props.initial!==void 0&&(o.initial=this.props.initial),o}const i={};for(let o=0;o<g_;o++){const s=Ah[o],a=this.props[s];(Hi(a)||a===!1)&&(i[s]=a)}return i}addVariantChild(t){var n;const r=this.getClosestVariantNode();if(r)return(n=r.variantChildren)===null||n===void 0||n.add(t),()=>r.variantChildren.delete(t)}addValue(t,n){this.hasValue(t)&&this.removeValue(t),this.values.set(t,n),this.latestValues[t]=n.get(),this.bindToMotionValue(t,n)}removeValue(t){var n;this.values.delete(t),(n=this.valueSubscriptions.get(t))===null||n===void 0||n(),this.valueSubscriptions.delete(t),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=A1(n,{owner:this}),this.addValue(t,r)),r}readValue(t){return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=w0(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!Je(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new b0),this.events[t].add(n)}notify(t,...n){var r;(r=this.events[t])===null||r===void 0||r.notify(...n)}}const Ah=["initial",...V0],g_=Ah.length;class Lh extends m_{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){var r;return(r=t.style)===null||r===void 0?void 0:r[n]}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},o){let s=Cv(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),s&&(s=i(s))),o){Tv(this,r,s);const a=f_(this,r,s,n);n=a.transitionEnd,r=a.target}return{transition:t,transitionEnd:n,...r}}}function v_(e){return window.getComputedStyle(e)}class y_ extends Lh{readValueFromInstance(t,n){if(Rr.has(n)){const r=k0(n);return r&&r.default||0}else{const r=v_(t),i=(E6(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return bh(t,n)}build(t,n,r,i){p0(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t){return _0(t)}renderInstance(t,n,r,i){L6(t,n,r,i)}}class __ extends Lh{constructor(){super(...arguments),this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){var r;return Rr.has(n)?((r=k0(n))===null||r===void 0?void 0:r.default)||0:(n=O6.has(n)?n:y0(n),t.getAttribute(n))}measureInstanceViewportBox(){return yt()}scrapeMotionValuesFromProps(t){return N6(t)}build(t,n,r,i){g0(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){M6(t,n,r,i)}mount(t){this.isSVGTag=v0(t.tagName),super.mount(t)}}const w_=(e,t)=>h0(e)?new __(t,{enableHardwareAcceleration:!1}):new y_(t,{enableHardwareAcceleration:!0});function p8(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const G1={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(R.test(e))e=parseFloat(e);else return e;const n=p8(e,t.target.x),r=p8(e,t.target.y);return`${n}% ${r}%`}},m8="_$css",x_={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=e.includes("var("),o=[];i&&(e=e.replace(Ch,p=>(o.push(p),m8)));const s=Qn.parse(e);if(s.length>5)return r;const a=Qn.createTransformer(e),l=typeof s[0]!="number"?1:0,u=n.x.scale*t.x,c=n.y.scale*t.y;s[0+l]/=u,s[1+l]/=c;const f=dt(u,c,.5);typeof s[2+l]=="number"&&(s[2+l]/=f),typeof s[3+l]=="number"&&(s[3+l]/=f);let d=a(s);if(i){let p=0;d=d.replace(m8,()=>{const v=o[p];return p++,v})}return d}};class S_ extends K.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;X4(b_),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),pi.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||me.postRender(()=>{var a;!((a=s.getStack())===null||a===void 0)&&a.members.length||this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),!t.currentAnimation&&t.isLead()&&this.safeToRemove())}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n!=null&&n.group&&n.group.remove(i),r!=null&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t==null||t()}render(){return null}}function T_(e){const[t,n]=H6(),r=D.useContext(T6);return K.createElement(S_,{...e,layoutGroup:r,switchLayoutGroup:D.useContext(b6),isPresent:t,safeToRemove:n})}const b_={borderRadius:{...G1,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:G1,borderTopRightRadius:G1,borderBottomLeftRadius:G1,borderBottomRightRadius:G1,boxShadow:x_},C_={measureLayout:T_};function E_(e,t,n={}){const r=Je(e)?e:A1(e);return r.start(I0("",r,t,n)),{stop:()=>r.stop(),isAnimating:()=>r.isAnimating()}}const Oh=["TopLeft","TopRight","BottomLeft","BottomRight"],k_=Oh.length,g8=e=>typeof e=="string"?parseFloat(e):e,v8=e=>typeof e=="number"||R.test(e);function P_(e,t,n,r,i,o){i?(e.opacity=dt(0,n.opacity!==void 0?n.opacity:1,D_(r)),e.opacityExit=dt(t.opacity!==void 0?t.opacity:1,0,A_(r))):o&&(e.opacity=dt(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<k_;s++){const a=`border${Oh[s]}Radius`;let l=y8(t,a),u=y8(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||v8(l)===v8(u)?(e[a]=Math.max(dt(g8(l),g8(u),r),0),(Qe.test(u)||Qe.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=dt(t.rotate||0,n.rotate||0,r))}function y8(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const D_=Mh(0,.5,N0),A_=Mh(.5,.95,M0);function Mh(e,t,n){return r=>r<e?0:r>t?1:n(qs(e,t,r))}function _8(e,t){e.min=t.min,e.max=t.max}function Me(e,t){_8(e.x,t.x),_8(e.y,t.y)}function w8(e,t,n,r,i){return e-=t,e=zs(e,1/n,r),i!==void 0&&(e=zs(e,1/i,r)),e}function L_(e,t=0,n=1,r=.5,i,o=e,s=e){if(Qe.test(t)&&(t=parseFloat(t),t=dt(s.min,s.max,t/100)-s.min),typeof t!="number")return;let a=dt(o.min,o.max,r);e===o&&(a-=t),e.min=w8(e.min,t,n,a,i),e.max=w8(e.max,t,n,a,i)}function x8(e,t,[n,r,i],o,s){L_(e,t[n],t[r],t[i],t.scale,o,s)}const O_=["x","scaleX","originX"],M_=["y","scaleY","originY"];function S8(e,t,n,r){x8(e.x,t,O_,n==null?void 0:n.x,r==null?void 0:r.x),x8(e.y,t,M_,n==null?void 0:n.y,r==null?void 0:r.y)}function T8(e){return e.translate===0&&e.scale===1}function Nh(e){return T8(e.x)&&T8(e.y)}function Rh(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function b8(e){return ye(e.x)/ye(e.y)}class N_{constructor(){this.members=[]}add(t){S0(this.members,t),t.scheduleRender()}remove(t){if(T0(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){var r;const i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,n&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),!((r=t.root)===null||r===void 0)&&r.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:o}=t.options;o===!1&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{var n,r,i,o,s;(r=(n=t.options).onExitComplete)===null||r===void 0||r.call(n),(s=(i=t.resumingFrom)===null||i===void 0?void 0:(o=i.options).onExitComplete)===null||s===void 0||s.call(o)})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function C8(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r=`translate3d(${i}px, ${o}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:l,rotateX:u,rotateY:c}=n;l&&(r+=`rotate(${l}deg) `),u&&(r+=`rotateX(${u}deg) `),c&&(r+=`rotateY(${c}deg) `)}const s=e.x.scale*t.x,a=e.y.scale*t.y;return(s!==1||a!==1)&&(r+=`scale(${s}, ${a})`),r||"none"}const R_=(e,t)=>e.depth-t.depth;class I_{constructor(){this.children=[],this.isDirty=!1}add(t){S0(this.children,t),this.isDirty=!0}remove(t){T0(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(R_),this.isDirty=!1,this.children.forEach(t)}}const E8=["","X","Y","Z"],k8=1e3;let V_=0;function Ih({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s,a={},l=t==null?void 0:t()){this.id=V_++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isTransformDirty=!1,this.isProjectionDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.potentialNodes=new Map,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.nodes.forEach(B_),this.nodes.forEach(U_),this.nodes.forEach($_)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.elementId=s,this.latestValues=a,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0,s&&this.root.registerPotentialNode(s,this);for(let u=0;u<this.path.length;u++)this.path[u].shouldResetTransform=!0;this.root===this&&(this.nodes=new I_)}addEventListener(s,a){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new b0),this.eventHandlers.get(s).add(a)}notifyListeners(s,...a){const l=this.eventHandlers.get(s);l==null||l.notify(...a)}hasListeners(s){return this.eventHandlers.has(s)}registerPotentialNode(s,a){this.potentialNodes.set(s,a)}mount(s,a=!1){var l;if(this.instance)return;this.isSVG=s instanceof SVGElement&&s.tagName!=="svg",this.instance=s;const{layoutId:u,layout:c,visualElement:f}=this.options;if(f&&!f.current&&f.mount(s),this.root.nodes.add(this),(l=this.parent)===null||l===void 0||l.children.add(this),this.elementId&&this.root.potentialNodes.delete(this.elementId),a&&(c||u)&&(this.isLayoutDirty=!0),e){let d;const p=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=mh(p,250),pi.hasAnimatedSinceResize&&(pi.hasAnimatedSinceResize=!1,this.nodes.forEach(D8))})}u&&this.root.registerSharedNode(u,this),this.options.animate!==!1&&f&&(u||c)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:p,hasRelativeTargetChanged:v,layout:h})=>{var _,m,g,y,w;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=(m=(_=this.options.transition)!==null&&_!==void 0?_:f.getDefaultTransition())!==null&&m!==void 0?m:X_,{onLayoutAnimationStart:S,onLayoutAnimationComplete:T}=f.getProps(),b=!this.targetLayout||!Rh(this.targetLayout,h)||v,C=!p&&v;if(!((g=this.resumeFrom)===null||g===void 0)&&g.instance||C||p&&(b||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,C);const E={...gh(x,"layout"),onPlay:S,onComplete:T};f.shouldReduceMotion&&(E.delay=0,E.type=!1),this.startAnimation(E)}else!p&&this.animationProgress===0&&D8(this),this.isLead()&&((w=(y=this.options).onExitComplete)===null||w===void 0||w.call(y));this.targetLayout=h})}unmount(){var s,a;this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this),(s=this.getStack())===null||s===void 0||s.remove(this),(a=this.parent)===null||a===void 0||a.children.delete(this),this.instance=void 0,Xn.preRender(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){var s;return this.isAnimationBlocked||((s=this.parent)===null||s===void 0?void 0:s.isTreeAnimationBlocked())||!1}startUpdate(){var s;this.isUpdateBlocked()||(this.isUpdating=!0,(s=this.nodes)===null||s===void 0||s.forEach(H_),this.animationId++)}willUpdate(s=!0){var a,l,u;if(this.root.isUpdateBlocked()){(l=(a=this.options).onExitComplete)===null||l===void 0||l.call(a);return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let p=0;p<this.path.length;p++){const v=this.path[p];v.shouldResetTransform=!0,v.updateScroll("snapshot")}const{layoutId:c,layout:f}=this.options;if(c===void 0&&!f)return;const d=(u=this.options.visualElement)===null||u===void 0?void 0:u.getProps().transformTemplate;this.prevTransformTemplateValue=d==null?void 0:d(this.latestValues,""),this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}didUpdate(){if(this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(P8);return}this.isUpdating&&(this.isUpdating=!1,this.potentialNodes.size&&(this.potentialNodes.forEach(Q_),this.potentialNodes.clear()),this.nodes.forEach(j_),this.nodes.forEach(q_),this.nodes.forEach(F_),this.clearAllSnapshots(),ul.update(),ul.preRender(),ul.render())}clearAllSnapshots(){this.nodes.forEach(z_),this.sharedNodes.forEach(G_)}scheduleUpdateProjection(){me.preRender(this.updateProjection,!1,!0)}scheduleCheckAfterUnmount(){me.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){var s;if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=yt(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox),(s=this.options.visualElement)===null||s===void 0||s.notify("LayoutMeasure",this.layout.layoutBox,a==null?void 0:a.layoutBox)}updateScroll(s="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){var s;if(!i)return;const a=this.isLayoutDirty||this.shouldResetTransform,l=this.projectionDelta&&!Nh(this.projectionDelta),u=(s=this.options.visualElement)===null||s===void 0?void 0:s.getProps().transformTemplate,c=u==null?void 0:u(this.latestValues,""),f=c!==this.prevTransformTemplateValue;a&&(l||cr(this.latestValues)||f)&&(i(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return s&&(l=this.removeTransform(l)),K_(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return yt();const a=s.measureViewportBox(),{scroll:l}=this.root;return l&&(Cn(a.x,l.offset.x),Cn(a.y,l.offset.y)),a}removeElementScroll(s){const a=yt();Me(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l],{scroll:c,options:f}=u;if(u!==this.root&&c&&f.layoutScroll){if(c.isRoot){Me(a,s);const{scroll:d}=this.root;d&&(Cn(a.x,-d.offset.x),Cn(a.y,-d.offset.y))}Cn(a.x,c.offset.x),Cn(a.y,c.offset.y)}}return a}applyTransform(s,a=!1){const l=yt();Me(l,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&i1(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),cr(c.latestValues)&&i1(l,c.latestValues)}return cr(this.latestValues)&&i1(l,this.latestValues),l}removeTransform(s){var a;const l=yt();Me(l,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];if(!c.instance||!cr(c.latestValues))continue;ju(c.latestValues)&&c.updateSnapshot();const f=yt(),d=c.measurePageBox();Me(f,d),S8(l,c.latestValues,(a=c.snapshot)===null||a===void 0?void 0:a.layoutBox,f)}return cr(this.latestValues)&&S8(l,this.latestValues),l}setTargetDelta(s){this.targetDelta=s,this.isProjectionDirty=!0,this.root.scheduleUpdateProjection()}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}resolveTargetDelta(){var s;const a=this.getLead();if(this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),!this.isProjectionDirty&&!this.attemptToResolveRelativeTarget)return;const{layout:l,layoutId:u}=this.options;if(!(!this.layout||!(l||u))){if(!this.targetDelta&&!this.relativeTarget){const c=this.getClosestProjectingParent();c&&c.layout?(this.relativeParent=c,this.relativeTarget=yt(),this.relativeTargetOrigin=yt(),yi(this.relativeTargetOrigin,this.layout.layoutBox,c.layout.layoutBox),Me(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=yt(),this.targetWithTransforms=yt()),this.relativeTarget&&this.relativeTargetOrigin&&(!((s=this.relativeParent)===null||s===void 0)&&s.target)?Iy(this.target,this.relativeTarget,this.relativeParent.target):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Me(this.target,this.layout.layoutBox),Th(this.target,this.targetDelta)):Me(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const c=this.getClosestProjectingParent();c&&!!c.resumingFrom==!!this.resumingFrom&&!c.options.layoutScroll&&c.target?(this.relativeParent=c,this.relativeTarget=yt(),this.relativeTargetOrigin=yt(),yi(this.relativeTargetOrigin,this.target,c.target),Me(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||ju(this.parent.latestValues)||Sh(this.parent.latestValues)))return(this.parent.relativeTarget||this.parent.targetDelta)&&this.parent.layout?this.parent:this.parent.getClosestProjectingParent()}calcProjection(){var s;const{isProjectionDirty:a,isTransformDirty:l}=this;this.isProjectionDirty=this.isTransformDirty=!1;const u=this.getLead(),c=!!this.resumingFrom||this!==u;let f=!0;if(a&&(f=!1),c&&l&&(f=!1),f)return;const{layout:d,layoutId:p}=this.options;if(this.isTreeAnimating=!!(!((s=this.parent)===null||s===void 0)&&s.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||p))return;Me(this.layoutCorrected,this.layout.layoutBox),Hy(this.layoutCorrected,this.treeScale,this.path,c);const{target:v}=u;if(!v)return;this.projectionDelta||(this.projectionDelta=_i(),this.projectionDeltaWithTransform=_i());const h=this.treeScale.x,_=this.treeScale.y,m=this.projectionTransform;vi(this.projectionDelta,this.layoutCorrected,v,this.latestValues),this.projectionTransform=C8(this.projectionDelta,this.treeScale),(this.projectionTransform!==m||this.treeScale.x!==h||this.treeScale.y!==_)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){var a,l,u;(l=(a=this.options).scheduleRender)===null||l===void 0||l.call(a),s&&((u=this.getStack())===null||u===void 0||u.scheduleRender()),this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,a=!1){var l,u;const c=this.snapshot,f=(c==null?void 0:c.latestValues)||{},d={...this.latestValues},p=_i();this.relativeTarget=this.relativeTargetOrigin=void 0,this.attemptToResolveRelativeTarget=!a;const v=yt(),h=(c==null?void 0:c.source)!==((l=this.layout)===null||l===void 0?void 0:l.source),_=(((u=this.getStack())===null||u===void 0?void 0:u.members.length)||0)<=1,m=!!(h&&!_&&this.options.crossfade===!0&&!this.path.some(Y_));this.animationProgress=0,this.mixTargetDelta=g=>{var y;const w=g/1e3;A8(p.x,s.x,w),A8(p.y,s.y,w),this.setTargetDelta(p),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&(!((y=this.relativeParent)===null||y===void 0)&&y.layout)&&(yi(v,this.layout.layoutBox,this.relativeParent.layout.layoutBox),W_(this.relativeTarget,this.relativeTargetOrigin,v,w)),h&&(this.animationValues=d,P_(d,f,this.latestValues,w,m,_)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=w},this.mixTargetDelta(0)}startAnimation(s){var a,l;this.notifyListeners("animationStart"),(a=this.currentAnimation)===null||a===void 0||a.stop(),this.resumingFrom&&((l=this.resumingFrom.currentAnimation)===null||l===void 0||l.stop()),this.pendingAnimation&&(Xn.update(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=me.update(()=>{pi.hasAnimatedSinceResize=!0,this.currentAnimation=E_(0,k8,{...s,onUpdate:u=>{var c;this.mixTargetDelta(u),(c=s.onUpdate)===null||c===void 0||c.call(s,u)},onComplete:()=>{var u;(u=s.onComplete)===null||u===void 0||u.call(s),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){var s;this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0),(s=this.getStack())===null||s===void 0||s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){var s;this.currentAnimation&&((s=this.mixTargetDelta)===null||s===void 0||s.call(this,k8),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=s;if(!(!a||!l||!u)){if(this!==s&&this.layout&&u&&Vh(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||yt();const f=ye(this.layout.layoutBox.x);l.x.min=s.target.x.min,l.x.max=l.x.min+f;const d=ye(this.layout.layoutBox.y);l.y.min=s.target.y.min,l.y.max=l.y.min+d}Me(a,l),i1(a,c),vi(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(s,a){var l,u,c;this.sharedNodes.has(s)||this.sharedNodes.set(s,new N_),this.sharedNodes.get(s).add(a),a.promote({transition:(l=a.options.initialPromotionConfig)===null||l===void 0?void 0:l.transition,preserveFollowOpacity:(c=(u=a.options.initialPromotionConfig)===null||u===void 0?void 0:u.shouldPreserveFollowOpacity)===null||c===void 0?void 0:c.call(u,a)})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:a}=this.options;return a?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:a}=this.options;return a?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),s&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let a=!1;const{latestValues:l}=s;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const u={};for(let c=0;c<E8.length;c++){const f="rotate"+E8[c];l[f]&&(u[f]=l[f],s.setStaticValue(f,0))}s==null||s.render();for(const c in u)s.setStaticValue(c,u[c]);s.scheduleRender()}getProjectionStyles(s={}){var a,l,u;const c={};if(!this.instance||this.isSVG)return c;if(this.isVisible)c.visibility="";else return{visibility:"hidden"};const f=(a=this.options.visualElement)===null||a===void 0?void 0:a.getProps().transformTemplate;if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=Ko(s.pointerEvents)||"",c.transform=f?f(this.latestValues,""):"none",c;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const _={};return this.options.layoutId&&(_.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,_.pointerEvents=Ko(s.pointerEvents)||""),this.hasProjected&&!cr(this.latestValues)&&(_.transform=f?f({},""):"none",this.hasProjected=!1),_}const p=d.animationValues||d.latestValues;this.applyTransformsToTarget(),c.transform=C8(this.projectionDeltaWithTransform,this.treeScale,p),f&&(c.transform=f(p,c.transform));const{x:v,y:h}=this.projectionDelta;c.transformOrigin=`${v.origin*100}% ${h.origin*100}% 0`,d.animationValues?c.opacity=d===this?(u=(l=p.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&u!==void 0?u:1:this.preserveOpacity?this.latestValues.opacity:p.opacityExit:c.opacity=d===this?p.opacity!==void 0?p.opacity:"":p.opacityExit!==void 0?p.opacityExit:0;for(const _ in Ls){if(p[_]===void 0)continue;const{correct:m,applyTo:g}=Ls[_],y=m(p[_],d);if(g){const w=g.length;for(let x=0;x<w;x++)c[g[x]]=y}else c[_]=y}return this.options.layoutId&&(c.pointerEvents=d===this?Ko(s.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var a;return(a=s.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(P8),this.root.sharedNodes.clear()}}}function q_(e){e.updateLayout()}function F_(e){var t,n,r;const i=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&i&&e.hasListeners("didUpdate")){const{layoutBox:o,measuredBox:s}=e.layout,{animationType:a}=e.options,l=i.source!==e.layout.source;a==="size"?je(p=>{const v=l?i.measuredBox[p]:i.layoutBox[p],h=ye(v);v.min=o[p].min,v.max=v.min+h}):Vh(a,i.layoutBox,o)&&je(p=>{const v=l?i.measuredBox[p]:i.layoutBox[p],h=ye(o[p]);v.max=v.min+h});const u=_i();vi(u,o,i.layoutBox);const c=_i();l?vi(c,e.applyTransform(s,!0),i.measuredBox):vi(c,o,i.layoutBox);const f=!Nh(u);let d=!1;if(!e.resumeFrom){const p=e.getClosestProjectingParent();if(p&&!p.resumeFrom){const{snapshot:v,layout:h}=p;if(v&&h){const _=yt();yi(_,i.layoutBox,v.layoutBox);const m=yt();yi(m,o,h.layoutBox),Rh(_,m)||(d=!0)}}}e.notifyListeners("didUpdate",{layout:o,snapshot:i,delta:c,layoutDelta:u,hasLayoutChanged:f,hasRelativeTargetChanged:d})}else e.isLead()&&((r=(n=e.options).onExitComplete)===null||r===void 0||r.call(n));e.options.transition=void 0}function B_(e){e.isProjectionDirty||(e.isProjectionDirty=!!(e.parent&&e.parent.isProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=!!(e.parent&&e.parent.isTransformDirty))}function z_(e){e.clearSnapshot()}function P8(e){e.clearMeasurements()}function j_(e){const{visualElement:t}=e.options;t!=null&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function D8(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0}function U_(e){e.resolveTargetDelta()}function $_(e){e.calcProjection()}function H_(e){e.resetRotation()}function G_(e){e.removeLeadSnapshot()}function A8(e,t,n){e.translate=dt(t.translate,0,n),e.scale=dt(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function L8(e,t,n,r){e.min=dt(t.min,n.min,r),e.max=dt(t.max,n.max,r)}function W_(e,t,n,r){L8(e.x,t.x,n.x,r),L8(e.y,t.y,n.y,r)}function Y_(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const X_={duration:.45,ease:[.4,0,.1,1]};function Q_(e,t){let n=e.root;for(let o=e.path.length-1;o>=0;o--)if(e.path[o].instance){n=e.path[o];break}const i=(n&&n!==e.root?n.instance:document).querySelector(`[data-projection-id="${t}"]`);i&&e.mount(i,!0)}function O8(e){e.min=Math.round(e.min),e.max=Math.round(e.max)}function K_(e){O8(e.x),O8(e.y)}function Vh(e,t,n){return e==="position"||e==="preserve-aspect"&&!Bu(b8(t),b8(n),.2)}const Z_=Ih({attachResizeListener:(e,t)=>ha(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),_l={current:void 0},J_=Ih({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!_l.current){const e=new Z_(0,{});e.mount(window),e.setOptions({layoutScroll:!0}),_l.current=e}return _l.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),tw={...Oy,...Kg,...t_,...C_},ew=W4((e,t)=>Cg(e,t,tw,w_,J_));var nw=K.memo(e=>{var{play:t,items:n}=e,[r,i]=K.useState(t);return!r&&t&&i(!0),!(n!=null&&n.length)||!r?null:K.createElement(rw,e)}),M8=0,rw=e=>{var{items:t,renderItem:n,pixelsPerFrame:r=5,frameRate:i=25,play:o,loop:s=!0,onExit:a}=e,l=K.createRef(),[u,c]=K.useState(),[f,d]=K.useState([[M8,t[0]]]);K.useLayoutEffect(()=>{c(l.current.getBoundingClientRect())},[]);var p=h=>{var _=t.findIndex(y=>{var{id:w}=y;return w===h.id}),m=(_+1)%t.length,g=t[m];d(y=>[...y,[++M8,g]])},v=()=>{f.length===1&&a&&a(),d(h=>h.slice(1))};return K.createElement("div",{ref:l,style:{position:"relative",width:"100%",height:"100%"}},u!=null&&o&&f.map(h=>{var[_,m]=h,g=t.findIndex(y=>{var{id:w}=y;return w===m.id});return K.createElement(iw,{key:_,item:m,offset:Math.round(u.width),pixelsPerSecond:r*i,onEntered:p,onExited:v},n(m,{showSeparator:s||g<t.length-1}))}))},iw=e=>{var{item:t,children:n,offset:r,pixelsPerSecond:i,onEntered:o,onExited:s}=e,a=K.useRef(),[l,u]=K.useState();K.useEffect(()=>{u(a.current.getBoundingClientRect().width)},[]);var c=r+(l!=null?l:0);return K.createElement(ew.div,{ref:a,style:{position:"absolute",left:r},animate:l?{x:-c,transition:{duration:c/i,ease:"linear"}}:!1,onAnimationStart:()=>{window.setTimeout(()=>{o(t)},l/i*1e3)},onAnimationComplete:()=>{s(t)}},n)},qh={},so={},va={},ya={};Object.defineProperty(ya,"__esModule",{value:!0});var ow=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),sw=String.fromCodePoint||function(e){var t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|e&1023),t+=String.fromCharCode(e),t};function aw(e){var t;return e>=55296&&e<=57343||e>1114111?"�":sw((t=ow.get(e))!==null&&t!==void 0?t:e)}ya.default=aw;var Fh={},q0={};Object.defineProperty(q0,"__esModule",{value:!0});q0.default=new Uint16Array([14866,60,237,340,721,1312,1562,1654,1838,1957,2183,2239,2301,2958,3037,3893,4123,4298,4330,4801,5191,5395,5752,5903,5943,5972,6050,0,0,0,0,0,0,6135,6565,7422,8183,8738,9242,9503,9938,10189,10573,10637,10715,11950,12246,13539,13950,14445,14533,15364,16514,16980,17390,17763,17849,18036,18125,4096,69,77,97,98,99,102,103,108,109,110,111,112,114,115,116,117,92,100,106,115,122,137,142,151,157,163,167,182,196,204,220,229,108,105,103,33024,198,59,32768,198,80,33024,38,59,32768,38,99,117,116,101,33024,193,59,32768,193,114,101,118,101,59,32768,258,512,105,121,127,134,114,99,33024,194,59,32768,194,59,32768,1040,114,59,32896,55349,56580,114,97,118,101,33024,192,59,32768,192,112,104,97,59,32768,913,97,99,114,59,32768,256,100,59,32768,10835,512,103,112,172,177,111,110,59,32768,260,102,59,32896,55349,56632,112,108,121,70,117,110,99,116,105,111,110,59,32768,8289,105,110,103,33024,197,59,32768,197,512,99,115,209,214,114,59,32896,55349,56476,105,103,110,59,32768,8788,105,108,100,101,33024,195,59,32768,195,109,108,33024,196,59,32768,196,2048,97,99,101,102,111,114,115,117,253,278,282,310,315,321,327,332,512,99,114,258,267,107,115,108,97,115,104,59,32768,8726,583,271,274,59,32768,10983,101,100,59,32768,8966,121,59,32768,1041,768,99,114,116,289,296,306,97,117,115,101,59,32768,8757,110,111,117,108,108,105,115,59,32768,8492,97,59,32768,914,114,59,32896,55349,56581,112,102,59,32896,55349,56633,101,118,101,59,32768,728,99,114,59,32768,8492,109,112,101,113,59,32768,8782,3584,72,79,97,99,100,101,102,104,105,108,111,114,115,117,368,373,380,426,461,466,487,491,495,533,593,695,701,707,99,121,59,32768,1063,80,89,33024,169,59,32768,169,768,99,112,121,387,393,419,117,116,101,59,32768,262,512,59,105,398,400,32768,8914,116,97,108,68,105,102,102,101,114,101,110,116,105,97,108,68,59,32768,8517,108,101,121,115,59,32768,8493,1024,97,101,105,111,435,441,449,454,114,111,110,59,32768,268,100,105,108,33024,199,59,32768,199,114,99,59,32768,264,110,105,110,116,59,32768,8752,111,116,59,32768,266,512,100,110,471,478,105,108,108,97,59,32768,184,116,101,114,68,111,116,59,32768,183,114,59,32768,8493,105,59,32768,935,114,99,108,101,1024,68,77,80,84,508,513,520,526,111,116,59,32768,8857,105,110,117,115,59,32768,8854,108,117,115,59,32768,8853,105,109,101,115,59,32768,8855,111,512,99,115,539,562,107,119,105,115,101,67,111,110,116,111,117,114,73,110,116,101,103,114,97,108,59,32768,8754,101,67,117,114,108,121,512,68,81,573,586,111,117,98,108,101,81,117,111,116,101,59,32768,8221,117,111,116,101,59,32768,8217,1024,108,110,112,117,602,614,648,664,111,110,512,59,101,609,611,32768,8759,59,32768,10868,768,103,105,116,621,629,634,114,117,101,110,116,59,32768,8801,110,116,59,32768,8751,111,117,114,73,110,116,101,103,114,97,108,59,32768,8750,512,102,114,653,656,59,32768,8450,111,100,117,99,116,59,32768,8720,110,116,101,114,67,108,111,99,107,119,105,115,101,67,111,110,116,111,117,114,73,110,116,101,103,114,97,108,59,32768,8755,111,115,115,59,32768,10799,99,114,59,32896,55349,56478,112,512,59,67,713,715,32768,8915,97,112,59,32768,8781,2816,68,74,83,90,97,99,101,102,105,111,115,743,758,763,768,773,795,809,821,826,910,1295,512,59,111,748,750,32768,8517,116,114,97,104,100,59,32768,10513,99,121,59,32768,1026,99,121,59,32768,1029,99,121,59,32768,1039,768,103,114,115,780,786,790,103,101,114,59,32768,8225,114,59,32768,8609,104,118,59,32768,10980,512,97,121,800,806,114,111,110,59,32768,270,59,32768,1044,108,512,59,116,815,817,32768,8711,97,59,32768,916,114,59,32896,55349,56583,512,97,102,831,897,512,99,109,836,891,114,105,116,105,99,97,108,1024,65,68,71,84,852,859,877,884,99,117,116,101,59,32768,180,111,581,864,867,59,32768,729,98,108,101,65,99,117,116,101,59,32768,733,114,97,118,101,59,32768,96,105,108,100,101,59,32768,732,111,110,100,59,32768,8900,102,101,114,101,110,116,105,97,108,68,59,32768,8518,2113,920,0,0,0,925,946,0,1139,102,59,32896,55349,56635,768,59,68,69,931,933,938,32768,168,111,116,59,32768,8412,113,117,97,108,59,32768,8784,98,108,101,1536,67,68,76,82,85,86,961,978,996,1080,1101,1125,111,110,116,111,117,114,73,110,116,101,103,114,97,108,59,32768,8751,111,1093,985,0,0,988,59,32768,168,110,65,114,114,111,119,59,32768,8659,512,101,111,1001,1034,102,116,768,65,82,84,1010,1017,1029,114,114,111,119,59,32768,8656,105,103,104,116,65,114,114,111,119,59,32768,8660,101,101,59,32768,10980,110,103,512,76,82,1041,1068,101,102,116,512,65,82,1049,1056,114,114,111,119,59,32768,10232,105,103,104,116,65,114,114,111,119,59,32768,10234,105,103,104,116,65,114,114,111,119,59,32768,10233,105,103,104,116,512,65,84,1089,1096,114,114,111,119,59,32768,8658,101,101,59,32768,8872,112,1042,1108,0,0,1115,114,114,111,119,59,32768,8657,111,119,110,65,114,114,111,119,59,32768,8661,101,114,116,105,99,97,108,66,97,114,59,32768,8741,110,1536,65,66,76,82,84,97,1152,1179,1186,1236,1272,1288,114,114,111,119,768,59,66,85,1163,1165,1170,32768,8595,97,114,59,32768,10515,112,65,114,114,111,119,59,32768,8693,114,101,118,101,59,32768,785,101,102,116,1315,1196,0,1209,0,1220,105,103,104,116,86,101,99,116,111,114,59,32768,10576,101,101,86,101,99,116,111,114,59,32768,10590,101,99,116,111,114,512,59,66,1229,1231,32768,8637,97,114,59,32768,10582,105,103,104,116,805,1245,0,1256,101,101,86,101,99,116,111,114,59,32768,10591,101,99,116,111,114,512,59,66,1265,1267,32768,8641,97,114,59,32768,10583,101,101,512,59,65,1279,1281,32768,8868,114,114,111,119,59,32768,8615,114,114,111,119,59,32768,8659,512,99,116,1300,1305,114,59,32896,55349,56479,114,111,107,59,32768,272,4096,78,84,97,99,100,102,103,108,109,111,112,113,115,116,117,120,1344,1348,1354,1363,1386,1391,1396,1405,1413,1460,1475,1483,1514,1527,1531,1538,71,59,32768,330,72,33024,208,59,32768,208,99,117,116,101,33024,201,59,32768,201,768,97,105,121,1370,1376,1383,114,111,110,59,32768,282,114,99,33024,202,59,32768,202,59,32768,1069,111,116,59,32768,278,114,59,32896,55349,56584,114,97,118,101,33024,200,59,32768,200,101,109,101,110,116,59,32768,8712,512,97,112,1418,1423,99,114,59,32768,274,116,121,1060,1431,0,0,1444,109,97,108,108,83,113,117,97,114,101,59,32768,9723,101,114,121,83,109,97,108,108,83,113,117,97,114,101,59,32768,9643,512,103,112,1465,1470,111,110,59,32768,280,102,59,32896,55349,56636,115,105,108,111,110,59,32768,917,117,512,97,105,1489,1504,108,512,59,84,1495,1497,32768,10869,105,108,100,101,59,32768,8770,108,105,98,114,105,117,109,59,32768,8652,512,99,105,1519,1523,114,59,32768,8496,109,59,32768,10867,97,59,32768,919,109,108,33024,203,59,32768,203,512,105,112,1543,1549,115,116,115,59,32768,8707,111,110,101,110,116,105,97,108,69,59,32768,8519,1280,99,102,105,111,115,1572,1576,1581,1620,1648,121,59,32768,1060,114,59,32896,55349,56585,108,108,101,100,1060,1591,0,0,1604,109,97,108,108,83,113,117,97,114,101,59,32768,9724,101,114,121,83,109,97,108,108,83,113,117,97,114,101,59,32768,9642,1601,1628,0,1633,0,0,1639,102,59,32896,55349,56637,65,108,108,59,32768,8704,114,105,101,114,116,114,102,59,32768,8497,99,114,59,32768,8497,3072,74,84,97,98,99,100,102,103,111,114,115,116,1678,1683,1688,1701,1708,1729,1734,1739,1742,1748,1828,1834,99,121,59,32768,1027,33024,62,59,32768,62,109,109,97,512,59,100,1696,1698,32768,915,59,32768,988,114,101,118,101,59,32768,286,768,101,105,121,1715,1721,1726,100,105,108,59,32768,290,114,99,59,32768,284,59,32768,1043,111,116,59,32768,288,114,59,32896,55349,56586,59,32768,8921,112,102,59,32896,55349,56638,101,97,116,101,114,1536,69,70,71,76,83,84,1766,1783,1794,1803,1809,1821,113,117,97,108,512,59,76,1775,1777,32768,8805,101,115,115,59,32768,8923,117,108,108,69,113,117,97,108,59,32768,8807,114,101,97,116,101,114,59,32768,10914,101,115,115,59,32768,8823,108,97,110,116,69,113,117,97,108,59,32768,10878,105,108,100,101,59,32768,8819,99,114,59,32896,55349,56482,59,32768,8811,2048,65,97,99,102,105,111,115,117,1854,1861,1874,1880,1884,1897,1919,1934,82,68,99,121,59,32768,1066,512,99,116,1866,1871,101,107,59,32768,711,59,32768,94,105,114,99,59,32768,292,114,59,32768,8460,108,98,101,114,116,83,112,97,99,101,59,32768,8459,833,1902,0,1906,102,59,32768,8461,105,122,111,110,116,97,108,76,105,110,101,59,32768,9472,512,99,116,1924,1928,114,59,32768,8459,114,111,107,59,32768,294,109,112,533,1940,1950,111,119,110,72,117,109,112,59,32768,8782,113,117,97,108,59,32768,8783,3584,69,74,79,97,99,100,102,103,109,110,111,115,116,117,1985,1990,1996,2001,2010,2025,2030,2034,2043,2077,2134,2155,2160,2167,99,121,59,32768,1045,108,105,103,59,32768,306,99,121,59,32768,1025,99,117,116,101,33024,205,59,32768,205,512,105,121,2015,2022,114,99,33024,206,59,32768,206,59,32768,1048,111,116,59,32768,304,114,59,32768,8465,114,97,118,101,33024,204,59,32768,204,768,59,97,112,2050,2052,2070,32768,8465,512,99,103,2057,2061,114,59,32768,298,105,110,97,114,121,73,59,32768,8520,108,105,101,115,59,32768,8658,837,2082,0,2110,512,59,101,2086,2088,32768,8748,512,103,114,2093,2099,114,97,108,59,32768,8747,115,101,99,116,105,111,110,59,32768,8898,105,115,105,98,108,101,512,67,84,2120,2127,111,109,109,97,59,32768,8291,105,109,101,115,59,32768,8290,768,103,112,116,2141,2146,2151,111,110,59,32768,302,102,59,32896,55349,56640,97,59,32768,921,99,114,59,32768,8464,105,108,100,101,59,32768,296,828,2172,0,2177,99,121,59,32768,1030,108,33024,207,59,32768,207,1280,99,102,111,115,117,2193,2206,2211,2217,2232,512,105,121,2198,2203,114,99,59,32768,308,59,32768,1049,114,59,32896,55349,56589,112,102,59,32896,55349,56641,820,2222,0,2227,114,59,32896,55349,56485,114,99,121,59,32768,1032,107,99,121,59,32768,1028,1792,72,74,97,99,102,111,115,2253,2258,2263,2269,2283,2288,2294,99,121,59,32768,1061,99,121,59,32768,1036,112,112,97,59,32768,922,512,101,121,2274,2280,100,105,108,59,32768,310,59,32768,1050,114,59,32896,55349,56590,112,102,59,32896,55349,56642,99,114,59,32896,55349,56486,2816,74,84,97,99,101,102,108,109,111,115,116,2323,2328,2333,2374,2396,2775,2780,2797,2804,2934,2954,99,121,59,32768,1033,33024,60,59,32768,60,1280,99,109,110,112,114,2344,2350,2356,2360,2370,117,116,101,59,32768,313,98,100,97,59,32768,923,103,59,32768,10218,108,97,99,101,116,114,102,59,32768,8466,114,59,32768,8606,768,97,101,121,2381,2387,2393,114,111,110,59,32768,317,100,105,108,59,32768,315,59,32768,1051,512,102,115,2401,2702,116,2560,65,67,68,70,82,84,85,86,97,114,2423,2470,2479,2530,2537,2561,2618,2666,2683,2690,512,110,114,2428,2441,103,108,101,66,114,97,99,107,101,116,59,32768,10216,114,111,119,768,59,66,82,2451,2453,2458,32768,8592,97,114,59,32768,8676,105,103,104,116,65,114,114,111,119,59,32768,8646,101,105,108,105,110,103,59,32768,8968,111,838,2485,0,2498,98,108,101,66,114,97,99,107,101,116,59,32768,10214,110,805,2503,0,2514,101,101,86,101,99,116,111,114,59,32768,10593,101,99,116,111,114,512,59,66,2523,2525,32768,8643,97,114,59,32768,10585,108,111,111,114,59,32768,8970,105,103,104,116,512,65,86,2546,2553,114,114,111,119,59,32768,8596,101,99,116,111,114,59,32768,10574,512,101,114,2566,2591,101,768,59,65,86,2574,2576,2583,32768,8867,114,114,111,119,59,32768,8612,101,99,116,111,114,59,32768,10586,105,97,110,103,108,101,768,59,66,69,2604,2606,2611,32768,8882,97,114,59,32768,10703,113,117,97,108,59,32768,8884,112,768,68,84,86,2626,2638,2649,111,119,110,86,101,99,116,111,114,59,32768,10577,101,101,86,101,99,116,111,114,59,32768,10592,101,99,116,111,114,512,59,66,2659,2661,32768,8639,97,114,59,32768,10584,101,99,116,111,114,512,59,66,2676,2678,32768,8636,97,114,59,32768,10578,114,114,111,119,59,32768,8656,105,103,104,116,97,114,114,111,119,59,32768,8660,115,1536,69,70,71,76,83,84,2716,2730,2741,2750,2756,2768,113,117,97,108,71,114,101,97,116,101,114,59,32768,8922,117,108,108,69,113,117,97,108,59,32768,8806,114,101,97,116,101,114,59,32768,8822,101,115,115,59,32768,10913,108,97,110,116,69,113,117,97,108,59,32768,10877,105,108,100,101,59,32768,8818,114,59,32896,55349,56591,512,59,101,2785,2787,32768,8920,102,116,97,114,114,111,119,59,32768,8666,105,100,111,116,59,32768,319,768,110,112,119,2811,2899,2904,103,1024,76,82,108,114,2821,2848,2860,2887,101,102,116,512,65,82,2829,2836,114,114,111,119,59,32768,10229,105,103,104,116,65,114,114,111,119,59,32768,10231,105,103,104,116,65,114,114,111,119,59,32768,10230,101,102,116,512,97,114,2868,2875,114,114,111,119,59,32768,10232,105,103,104,116,97,114,114,111,119,59,32768,10234,105,103,104,116,97,114,114,111,119,59,32768,10233,102,59,32896,55349,56643,101,114,512,76,82,2911,2922,101,102,116,65,114,114,111,119,59,32768,8601,105,103,104,116,65,114,114,111,119,59,32768,8600,768,99,104,116,2941,2945,2948,114,59,32768,8466,59,32768,8624,114,111,107,59,32768,321,59,32768,8810,2048,97,99,101,102,105,111,115,117,2974,2978,2982,3007,3012,3022,3028,3033,112,59,32768,10501,121,59,32768,1052,512,100,108,2987,2998,105,117,109,83,112,97,99,101,59,32768,8287,108,105,110,116,114,102,59,32768,8499,114,59,32896,55349,56592,110,117,115,80,108,117,115,59,32768,8723,112,102,59,32896,55349,56644,99,114,59,32768,8499,59,32768,924,2304,74,97,99,101,102,111,115,116,117,3055,3060,3067,3089,3201,3206,3874,3880,3889,99,121,59,32768,1034,99,117,116,101,59,32768,323,768,97,101,121,3074,3080,3086,114,111,110,59,32768,327,100,105,108,59,32768,325,59,32768,1053,768,103,115,119,3096,3160,3194,97,116,105,118,101,768,77,84,86,3108,3121,3145,101,100,105,117,109,83,112,97,99,101,59,32768,8203,104,105,512,99,110,3128,3137,107,83,112,97,99,101,59,32768,8203,83,112,97,99,101,59,32768,8203,101,114,121,84,104,105,110,83,112,97,99,101,59,32768,8203,116,101,100,512,71,76,3168,3184,114,101,97,116,101,114,71,114,101,97,116,101,114,59,32768,8811,101,115,115,76,101,115,115,59,32768,8810,76,105,110,101,59,32768,10,114,59,32896,55349,56593,1024,66,110,112,116,3215,3222,3238,3242,114,101,97,107,59,32768,8288,66,114,101,97,107,105,110,103,83,112,97,99,101,59,32768,160,102,59,32768,8469,3328,59,67,68,69,71,72,76,78,80,82,83,84,86,3269,3271,3293,3312,3352,3430,3455,3551,3589,3625,3678,3821,3861,32768,10988,512,111,117,3276,3286,110,103,114,117,101,110,116,59,32768,8802,112,67,97,112,59,32768,8813,111,117,98,108,101,86,101,114,116,105,99,97,108,66,97,114,59,32768,8742,768,108,113,120,3319,3327,3345,101,109,101,110,116,59,32768,8713,117,97,108,512,59,84,3335,3337,32768,8800,105,108,100,101,59,32896,8770,824,105,115,116,115,59,32768,8708,114,101,97,116,101,114,1792,59,69,70,71,76,83,84,3373,3375,3382,3394,3404,3410,3423,32768,8815,113,117,97,108,59,32768,8817,117,108,108,69,113,117,97,108,59,32896,8807,824,114,101,97,116,101,114,59,32896,8811,824,101,115,115,59,32768,8825,108,97,110,116,69,113,117,97,108,59,32896,10878,824,105,108,100,101,59,32768,8821,117,109,112,533,3437,3448,111,119,110,72,117,109,112,59,32896,8782,824,113,117,97,108,59,32896,8783,824,101,512,102,115,3461,3492,116,84,114,105,97,110,103,108,101,768,59,66,69,3477,3479,3485,32768,8938,97,114,59,32896,10703,824,113,117,97,108,59,32768,8940,115,1536,59,69,71,76,83,84,3506,3508,3515,3524,3531,3544,32768,8814,113,117,97,108,59,32768,8816,114,101,97,116,101,114,59,32768,8824,101,115,115,59,32896,8810,824,108,97,110,116,69,113,117,97,108,59,32896,10877,824,105,108,100,101,59,32768,8820,101,115,116,101,100,512,71,76,3561,3578,114,101,97,116,101,114,71,114,101,97,116,101,114,59,32896,10914,824,101,115,115,76,101,115,115,59,32896,10913,824,114,101,99,101,100,101,115,768,59,69,83,3603,3605,3613,32768,8832,113,117,97,108,59,32896,10927,824,108,97,110,116,69,113,117,97,108,59,32768,8928,512,101,105,3630,3645,118,101,114,115,101,69,108,101,109,101,110,116,59,32768,8716,103,104,116,84,114,105,97,110,103,108,101,768,59,66,69,3663,3665,3671,32768,8939,97,114,59,32896,10704,824,113,117,97,108,59,32768,8941,512,113,117,3683,3732,117,97,114,101,83,117,512,98,112,3694,3712,115,101,116,512,59,69,3702,3705,32896,8847,824,113,117,97,108,59,32768,8930,101,114,115,101,116,512,59,69,3722,3725,32896,8848,824,113,117,97,108,59,32768,8931,768,98,99,112,3739,3757,3801,115,101,116,512,59,69,3747,3750,32896,8834,8402,113,117,97,108,59,32768,8840,99,101,101,100,115,1024,59,69,83,84,3771,3773,3781,3793,32768,8833,113,117,97,108,59,32896,10928,824,108,97,110,116,69,113,117,97,108,59,32768,8929,105,108,100,101,59,32896,8831,824,101,114,115,101,116,512,59,69,3811,3814,32896,8835,8402,113,117,97,108,59,32768,8841,105,108,100,101,1024,59,69,70,84,3834,3836,3843,3854,32768,8769,113,117,97,108,59,32768,8772,117,108,108,69,113,117,97,108,59,32768,8775,105,108,100,101,59,32768,8777,101,114,116,105,99,97,108,66,97,114,59,32768,8740,99,114,59,32896,55349,56489,105,108,100,101,33024,209,59,32768,209,59,32768,925,3584,69,97,99,100,102,103,109,111,112,114,115,116,117,118,3921,3927,3936,3951,3958,3963,3972,3996,4002,4034,4037,4055,4071,4078,108,105,103,59,32768,338,99,117,116,101,33024,211,59,32768,211,512,105,121,3941,3948,114,99,33024,212,59,32768,212,59,32768,1054,98,108,97,99,59,32768,336,114,59,32896,55349,56594,114,97,118,101,33024,210,59,32768,210,768,97,101,105,3979,3984,3989,99,114,59,32768,332,103,97,59,32768,937,99,114,111,110,59,32768,927,112,102,59,32896,55349,56646,101,110,67,117,114,108,121,512,68,81,4014,4027,111,117,98,108,101,81,117,111,116,101,59,32768,8220,117,111,116,101,59,32768,8216,59,32768,10836,512,99,108,4042,4047,114,59,32896,55349,56490,97,115,104,33024,216,59,32768,216,105,573,4060,4067,100,101,33024,213,59,32768,213,101,115,59,32768,10807,109,108,33024,214,59,32768,214,101,114,512,66,80,4085,4109,512,97,114,4090,4094,114,59,32768,8254,97,99,512,101,107,4101,4104,59,32768,9182,101,116,59,32768,9140,97,114,101,110,116,104,101,115,105,115,59,32768,9180,2304,97,99,102,104,105,108,111,114,115,4141,4150,4154,4159,4163,4166,4176,4198,4284,114,116,105,97,108,68,59,32768,8706,121,59,32768,1055,114,59,32896,55349,56595,105,59,32768,934,59,32768,928,117,115,77,105,110,117,115,59,32768,177,512,105,112,4181,4194,110,99,97,114,101,112,108,97,110,101,59,32768,8460,102,59,32768,8473,1024,59,101,105,111,4207,4209,4251,4256,32768,10939,99,101,100,101,115,1024,59,69,83,84,4223,4225,4232,4244,32768,8826,113,117,97,108,59,32768,10927,108,97,110,116,69,113,117,97,108,59,32768,8828,105,108,100,101,59,32768,8830,109,101,59,32768,8243,512,100,112,4261,4267,117,99,116,59,32768,8719,111,114,116,105,111,110,512,59,97,4278,4280,32768,8759,108,59,32768,8733,512,99,105,4289,4294,114,59,32896,55349,56491,59,32768,936,1024,85,102,111,115,4306,4313,4318,4323,79,84,33024,34,59,32768,34,114,59,32896,55349,56596,112,102,59,32768,8474,99,114,59,32896,55349,56492,3072,66,69,97,99,101,102,104,105,111,114,115,117,4354,4360,4366,4395,4417,4473,4477,4481,4743,4764,4776,4788,97,114,114,59,32768,10512,71,33024,174,59,32768,174,768,99,110,114,4373,4379,4383,117,116,101,59,32768,340,103,59,32768,10219,114,512,59,116,4389,4391,32768,8608,108,59,32768,10518,768,97,101,121,4402,4408,4414,114,111,110,59,32768,344,100,105,108,59,32768,342,59,32768,1056,512,59,118,4422,4424,32768,8476,101,114,115,101,512,69,85,4433,4458,512,108,113,4438,4446,101,109,101,110,116,59,32768,8715,117,105,108,105,98,114,105,117,109,59,32768,8651,112,69,113,117,105,108,105,98,114,105,117,109,59,32768,10607,114,59,32768,8476,111,59,32768,929,103,104,116,2048,65,67,68,70,84,85,86,97,4501,4547,4556,4607,4614,4671,4719,4736,512,110,114,4506,4519,103,108,101,66,114,97,99,107,101,116,59,32768,10217,114,111,119,768,59,66,76,4529,4531,4536,32768,8594,97,114,59,32768,8677,101,102,116,65,114,114,111,119,59,32768,8644,101,105,108,105,110,103,59,32768,8969,111,838,4562,0,4575,98,108,101,66,114,97,99,107,101,116,59,32768,10215,110,805,4580,0,4591,101,101,86,101,99,116,111,114,59,32768,10589,101,99,116,111,114,512,59,66,4600,4602,32768,8642,97,114,59,32768,10581,108,111,111,114,59,32768,8971,512,101,114,4619,4644,101,768,59,65,86,4627,4629,4636,32768,8866,114,114,111,119,59,32768,8614,101,99,116,111,114,59,32768,10587,105,97,110,103,108,101,768,59,66,69,4657,4659,4664,32768,8883,97,114,59,32768,10704,113,117,97,108,59,32768,8885,112,768,68,84,86,4679,4691,4702,111,119,110,86,101,99,116,111,114,59,32768,10575,101,101,86,101,99,116,111,114,59,32768,10588,101,99,116,111,114,512,59,66,4712,4714,32768,8638,97,114,59,32768,10580,101,99,116,111,114,512,59,66,4729,4731,32768,8640,97,114,59,32768,10579,114,114,111,119,59,32768,8658,512,112,117,4748,4752,102,59,32768,8477,110,100,73,109,112,108,105,101,115,59,32768,10608,105,103,104,116,97,114,114,111,119,59,32768,8667,512,99,104,4781,4785,114,59,32768,8475,59,32768,8625,108,101,68,101,108,97,121,101,100,59,32768,10740,3328,72,79,97,99,102,104,105,109,111,113,115,116,117,4827,4842,4849,4856,4889,4894,4949,4955,4967,4973,5059,5065,5070,512,67,99,4832,4838,72,99,121,59,32768,1065,121,59,32768,1064,70,84,99,121,59,32768,1068,99,117,116,101,59,32768,346,1280,59,97,101,105,121,4867,4869,4875,4881,4886,32768,10940,114,111,110,59,32768,352,100,105,108,59,32768,350,114,99,59,32768,348,59,32768,1057,114,59,32896,55349,56598,111,114,116,1024,68,76,82,85,4906,4917,4928,4940,111,119,110,65,114,114,111,119,59,32768,8595,101,102,116,65,114,114,111,119,59,32768,8592,105,103,104,116,65,114,114,111,119,59,32768,8594,112,65,114,114,111,119,59,32768,8593,103,109,97,59,32768,931,97,108,108,67,105,114,99,108,101,59,32768,8728,112,102,59,32896,55349,56650,1091,4979,0,0,4983,116,59,32768,8730,97,114,101,1024,59,73,83,85,4994,4996,5010,5052,32768,9633,110,116,101,114,115,101,99,116,105,111,110,59,32768,8851,117,512,98,112,5016,5033,115,101,116,512,59,69,5024,5026,32768,8847,113,117,97,108,59,32768,8849,101,114,115,101,116,512,59,69,5043,5045,32768,8848,113,117,97,108,59,32768,8850,110,105,111,110,59,32768,8852,99,114,59,32896,55349,56494,97,114,59,32768,8902,1024,98,99,109,112,5079,5102,5155,5158,512,59,115,5084,5086,32768,8912,101,116,512,59,69,5093,5095,32768,8912,113,117,97,108,59,32768,8838,512,99,104,5107,5148,101,101,100,115,1024,59,69,83,84,5120,5122,5129,5141,32768,8827,113,117,97,108,59,32768,10928,108,97,110,116,69,113,117,97,108,59,32768,8829,105,108,100,101,59,32768,8831,84,104,97,116,59,32768,8715,59,32768,8721,768,59,101,115,5165,5167,5185,32768,8913,114,115,101,116,512,59,69,5176,5178,32768,8835,113,117,97,108,59,32768,8839,101,116,59,32768,8913,2816,72,82,83,97,99,102,104,105,111,114,115,5213,5221,5227,5241,5252,5274,5279,5323,5362,5368,5378,79,82,78,33024,222,59,32768,222,65,68,69,59,32768,8482,512,72,99,5232,5237,99,121,59,32768,1035,121,59,32768,1062,512,98,117,5246,5249,59,32768,9,59,32768,932,768,97,101,121,5259,5265,5271,114,111,110,59,32768,356,100,105,108,59,32768,354,59,32768,1058,114,59,32896,55349,56599,512,101,105,5284,5300,835,5289,0,5297,101,102,111,114,101,59,32768,8756,97,59,32768,920,512,99,110,5305,5315,107,83,112,97,99,101,59,32896,8287,8202,83,112,97,99,101,59,32768,8201,108,100,101,1024,59,69,70,84,5335,5337,5344,5355,32768,8764,113,117,97,108,59,32768,8771,117,108,108,69,113,117,97,108,59,32768,8773,105,108,100,101,59,32768,8776,112,102,59,32896,55349,56651,105,112,108,101,68,111,116,59,32768,8411,512,99,116,5383,5388,114,59,32896,55349,56495,114,111,107,59,32768,358,5426,5417,5444,5458,5473,0,5480,5485,0,0,0,0,0,5494,5500,5564,5579,0,5726,5732,5738,5745,512,99,114,5421,5429,117,116,101,33024,218,59,32768,218,114,512,59,111,5435,5437,32768,8607,99,105,114,59,32768,10569,114,820,5449,0,5453,121,59,32768,1038,118,101,59,32768,364,512,105,121,5462,5469,114,99,33024,219,59,32768,219,59,32768,1059,98,108,97,99,59,32768,368,114,59,32896,55349,56600,114,97,118,101,33024,217,59,32768,217,97,99,114,59,32768,362,512,100,105,5504,5548,101,114,512,66,80,5511,5535,512,97,114,5516,5520,114,59,32768,95,97,99,512,101,107,5527,5530,59,32768,9183,101,116,59,32768,9141,97,114,101,110,116,104,101,115,105,115,59,32768,9181,111,110,512,59,80,5555,5557,32768,8899,108,117,115,59,32768,8846,512,103,112,5568,5573,111,110,59,32768,370,102,59,32896,55349,56652,2048,65,68,69,84,97,100,112,115,5595,5624,5635,5648,5664,5671,5682,5712,114,114,111,119,768,59,66,68,5606,5608,5613,32768,8593,97,114,59,32768,10514,111,119,110,65,114,114,111,119,59,32768,8645,111,119,110,65,114,114,111,119,59,32768,8597,113,117,105,108,105,98,114,105,117,109,59,32768,10606,101,101,512,59,65,5655,5657,32768,8869,114,114,111,119,59,32768,8613,114,114,111,119,59,32768,8657,111,119,110,97,114,114,111,119,59,32768,8661,101,114,512,76,82,5689,5700,101,102,116,65,114,114,111,119,59,32768,8598,105,103,104,116,65,114,114,111,119,59,32768,8599,105,512,59,108,5718,5720,32768,978,111,110,59,32768,933,105,110,103,59,32768,366,99,114,59,32896,55349,56496,105,108,100,101,59,32768,360,109,108,33024,220,59,32768,220,2304,68,98,99,100,101,102,111,115,118,5770,5776,5781,5785,5798,5878,5883,5889,5895,97,115,104,59,32768,8875,97,114,59,32768,10987,121,59,32768,1042,97,115,104,512,59,108,5793,5795,32768,8873,59,32768,10982,512,101,114,5803,5806,59,32768,8897,768,98,116,121,5813,5818,5866,97,114,59,32768,8214,512,59,105,5823,5825,32768,8214,99,97,108,1024,66,76,83,84,5837,5842,5848,5859,97,114,59,32768,8739,105,110,101,59,32768,124,101,112,97,114,97,116,111,114,59,32768,10072,105,108,100,101,59,32768,8768,84,104,105,110,83,112,97,99,101,59,32768,8202,114,59,32896,55349,56601,112,102,59,32896,55349,56653,99,114,59,32896,55349,56497,100,97,115,104,59,32768,8874,1280,99,101,102,111,115,5913,5919,5925,5930,5936,105,114,99,59,32768,372,100,103,101,59,32768,8896,114,59,32896,55349,56602,112,102,59,32896,55349,56654,99,114,59,32896,55349,56498,1024,102,105,111,115,5951,5956,5959,5965,114,59,32896,55349,56603,59,32768,926,112,102,59,32896,55349,56655,99,114,59,32896,55349,56499,2304,65,73,85,97,99,102,111,115,117,5990,5995,6e3,6005,6014,6027,6032,6038,6044,99,121,59,32768,1071,99,121,59,32768,1031,99,121,59,32768,1070,99,117,116,101,33024,221,59,32768,221,512,105,121,6019,6024,114,99,59,32768,374,59,32768,1067,114,59,32896,55349,56604,112,102,59,32896,55349,56656,99,114,59,32896,55349,56500,109,108,59,32768,376,2048,72,97,99,100,101,102,111,115,6066,6071,6078,6092,6097,6119,6123,6128,99,121,59,32768,1046,99,117,116,101,59,32768,377,512,97,121,6083,6089,114,111,110,59,32768,381,59,32768,1047,111,116,59,32768,379,835,6102,0,6116,111,87,105,100,116,104,83,112,97,99,101,59,32768,8203,97,59,32768,918,114,59,32768,8488,112,102,59,32768,8484,99,114,59,32896,55349,56501,5938,6159,6168,6175,0,6214,6222,6233,0,0,0,0,6242,6267,6290,6429,6444,0,6495,6503,6531,6540,0,6547,99,117,116,101,33024,225,59,32768,225,114,101,118,101,59,32768,259,1536,59,69,100,105,117,121,6187,6189,6193,6196,6203,6210,32768,8766,59,32896,8766,819,59,32768,8767,114,99,33024,226,59,32768,226,116,101,33024,180,59,32768,180,59,32768,1072,108,105,103,33024,230,59,32768,230,512,59,114,6226,6228,32768,8289,59,32896,55349,56606,114,97,118,101,33024,224,59,32768,224,512,101,112,6246,6261,512,102,112,6251,6257,115,121,109,59,32768,8501,104,59,32768,8501,104,97,59,32768,945,512,97,112,6271,6284,512,99,108,6276,6280,114,59,32768,257,103,59,32768,10815,33024,38,59,32768,38,1077,6295,0,0,6326,1280,59,97,100,115,118,6305,6307,6312,6315,6322,32768,8743,110,100,59,32768,10837,59,32768,10844,108,111,112,101,59,32768,10840,59,32768,10842,1792,59,101,108,109,114,115,122,6340,6342,6345,6349,6391,6410,6422,32768,8736,59,32768,10660,101,59,32768,8736,115,100,512,59,97,6356,6358,32768,8737,2098,6368,6371,6374,6377,6380,6383,6386,6389,59,32768,10664,59,32768,10665,59,32768,10666,59,32768,10667,59,32768,10668,59,32768,10669,59,32768,10670,59,32768,10671,116,512,59,118,6397,6399,32768,8735,98,512,59,100,6405,6407,32768,8894,59,32768,10653,512,112,116,6415,6419,104,59,32768,8738,59,32768,197,97,114,114,59,32768,9084,512,103,112,6433,6438,111,110,59,32768,261,102,59,32896,55349,56658,1792,59,69,97,101,105,111,112,6458,6460,6463,6469,6472,6476,6480,32768,8776,59,32768,10864,99,105,114,59,32768,10863,59,32768,8778,100,59,32768,8779,115,59,32768,39,114,111,120,512,59,101,6488,6490,32768,8776,113,59,32768,8778,105,110,103,33024,229,59,32768,229,768,99,116,121,6509,6514,6517,114,59,32896,55349,56502,59,32768,42,109,112,512,59,101,6524,6526,32768,8776,113,59,32768,8781,105,108,100,101,33024,227,59,32768,227,109,108,33024,228,59,32768,228,512,99,105,6551,6559,111,110,105,110,116,59,32768,8755,110,116,59,32768,10769,4096,78,97,98,99,100,101,102,105,107,108,110,111,112,114,115,117,6597,6602,6673,6688,6701,6707,6768,6773,6891,6898,6999,7023,7309,7316,7334,7383,111,116,59,32768,10989,512,99,114,6607,6652,107,1024,99,101,112,115,6617,6623,6632,6639,111,110,103,59,32768,8780,112,115,105,108,111,110,59,32768,1014,114,105,109,101,59,32768,8245,105,109,512,59,101,6646,6648,32768,8765,113,59,32768,8909,583,6656,6661,101,101,59,32768,8893,101,100,512,59,103,6667,6669,32768,8965,101,59,32768,8965,114,107,512,59,116,6680,6682,32768,9141,98,114,107,59,32768,9142,512,111,121,6693,6698,110,103,59,32768,8780,59,32768,1073,113,117,111,59,32768,8222,1280,99,109,112,114,116,6718,6731,6738,6743,6749,97,117,115,512,59,101,6726,6728,32768,8757,59,32768,8757,112,116,121,118,59,32768,10672,115,105,59,32768,1014,110,111,117,59,32768,8492,768,97,104,119,6756,6759,6762,59,32768,946,59,32768,8502,101,101,110,59,32768,8812,114,59,32896,55349,56607,103,1792,99,111,115,116,117,118,119,6789,6809,6834,6850,6872,6879,6884,768,97,105,117,6796,6800,6805,112,59,32768,8898,114,99,59,32768,9711,112,59,32768,8899,768,100,112,116,6816,6821,6827,111,116,59,32768,10752,108,117,115,59,32768,10753,105,109,101,115,59,32768,10754,1090,6840,0,0,6846,99,117,112,59,32768,10758,97,114,59,32768,9733,114,105,97,110,103,108,101,512,100,117,6862,6868,111,119,110,59,32768,9661,112,59,32768,9651,112,108,117,115,59,32768,10756,101,101,59,32768,8897,101,100,103,101,59,32768,8896,97,114,111,119,59,32768,10509,768,97,107,111,6905,6976,6994,512,99,110,6910,6972,107,768,108,115,116,6918,6927,6935,111,122,101,110,103,101,59,32768,10731,113,117,97,114,101,59,32768,9642,114,105,97,110,103,108,101,1024,59,100,108,114,6951,6953,6959,6965,32768,9652,111,119,110,59,32768,9662,101,102,116,59,32768,9666,105,103,104,116,59,32768,9656,107,59,32768,9251,770,6981,0,6991,771,6985,0,6988,59,32768,9618,59,32768,9617,52,59,32768,9619,99,107,59,32768,9608,512,101,111,7004,7019,512,59,113,7009,7012,32896,61,8421,117,105,118,59,32896,8801,8421,116,59,32768,8976,1024,112,116,119,120,7032,7037,7049,7055,102,59,32896,55349,56659,512,59,116,7042,7044,32768,8869,111,109,59,32768,8869,116,105,101,59,32768,8904,3072,68,72,85,86,98,100,104,109,112,116,117,118,7080,7101,7126,7147,7182,7187,7208,7233,7240,7246,7253,7274,1024,76,82,108,114,7089,7092,7095,7098,59,32768,9559,59,32768,9556,59,32768,9558,59,32768,9555,1280,59,68,85,100,117,7112,7114,7117,7120,7123,32768,9552,59,32768,9574,59,32768,9577,59,32768,9572,59,32768,9575,1024,76,82,108,114,7135,7138,7141,7144,59,32768,9565,59,32768,9562,59,32768,9564,59,32768,9561,1792,59,72,76,82,104,108,114,7162,7164,7167,7170,7173,7176,7179,32768,9553,59,32768,9580,59,32768,9571,59,32768,9568,59,32768,9579,59,32768,9570,59,32768,9567,111,120,59,32768,10697,1024,76,82,108,114,7196,7199,7202,7205,59,32768,9557,59,32768,9554,59,32768,9488,59,32768,9484,1280,59,68,85,100,117,7219,7221,7224,7227,7230,32768,9472,59,32768,9573,59,32768,9576,59,32768,9516,59,32768,9524,105,110,117,115,59,32768,8863,108,117,115,59,32768,8862,105,109,101,115,59,32768,8864,1024,76,82,108,114,7262,7265,7268,7271,59,32768,9563,59,32768,9560,59,32768,9496,59,32768,9492,1792,59,72,76,82,104,108,114,7289,7291,7294,7297,7300,7303,7306,32768,9474,59,32768,9578,59,32768,9569,59,32768,9566,59,32768,9532,59,32768,9508,59,32768,9500,114,105,109,101,59,32768,8245,512,101,118,7321,7326,118,101,59,32768,728,98,97,114,33024,166,59,32768,166,1024,99,101,105,111,7343,7348,7353,7364,114,59,32896,55349,56503,109,105,59,32768,8271,109,512,59,101,7359,7361,32768,8765,59,32768,8909,108,768,59,98,104,7372,7374,7377,32768,92,59,32768,10693,115,117,98,59,32768,10184,573,7387,7399,108,512,59,101,7392,7394,32768,8226,116,59,32768,8226,112,768,59,69,101,7406,7408,7411,32768,8782,59,32768,10926,512,59,113,7416,7418,32768,8783,59,32768,8783,6450,7448,0,7523,7571,7576,7613,0,7618,7647,0,0,7764,0,0,7779,0,0,7899,7914,7949,7955,0,8158,0,8176,768,99,112,114,7454,7460,7509,117,116,101,59,32768,263,1536,59,97,98,99,100,115,7473,7475,7480,7487,7500,7505,32768,8745,110,100,59,32768,10820,114,99,117,112,59,32768,10825,512,97,117,7492,7496,112,59,32768,10827,112,59,32768,10823,111,116,59,32768,10816,59,32896,8745,65024,512,101,111,7514,7518,116,59,32768,8257,110,59,32768,711,1024,97,101,105,117,7531,7544,7552,7557,833,7536,0,7540,115,59,32768,10829,111,110,59,32768,269,100,105,108,33024,231,59,32768,231,114,99,59,32768,265,112,115,512,59,115,7564,7566,32768,10828,109,59,32768,10832,111,116,59,32768,267,768,100,109,110,7582,7589,7596,105,108,33024,184,59,32768,184,112,116,121,118,59,32768,10674,116,33280,162,59,101,7603,7605,32768,162,114,100,111,116,59,32768,183,114,59,32896,55349,56608,768,99,101,105,7624,7628,7643,121,59,32768,1095,99,107,512,59,109,7635,7637,32768,10003,97,114,107,59,32768,10003,59,32768,967,114,1792,59,69,99,101,102,109,115,7662,7664,7667,7742,7745,7752,7757,32768,9675,59,32768,10691,768,59,101,108,7674,7676,7680,32768,710,113,59,32768,8791,101,1074,7687,0,0,7709,114,114,111,119,512,108,114,7695,7701,101,102,116,59,32768,8634,105,103,104,116,59,32768,8635,1280,82,83,97,99,100,7719,7722,7725,7730,7736,59,32768,174,59,32768,9416,115,116,59,32768,8859,105,114,99,59,32768,8858,97,115,104,59,32768,8861,59,32768,8791,110,105,110,116,59,32768,10768,105,100,59,32768,10991,99,105,114,59,32768,10690,117,98,115,512,59,117,7771,7773,32768,9827,105,116,59,32768,9827,1341,7785,7804,7850,0,7871,111,110,512,59,101,7791,7793,32768,58,512,59,113,7798,7800,32768,8788,59,32768,8788,1086,7809,0,0,7820,97,512,59,116,7814,7816,32768,44,59,32768,64,768,59,102,108,7826,7828,7832,32768,8705,110,59,32768,8728,101,512,109,120,7838,7844,101,110,116,59,32768,8705,101,115,59,32768,8450,824,7854,0,7866,512,59,100,7858,7860,32768,8773,111,116,59,32768,10861,110,116,59,32768,8750,768,102,114,121,7877,7881,7886,59,32896,55349,56660,111,100,59,32768,8720,33280,169,59,115,7892,7894,32768,169,114,59,32768,8471,512,97,111,7903,7908,114,114,59,32768,8629,115,115,59,32768,10007,512,99,117,7918,7923,114,59,32896,55349,56504,512,98,112,7928,7938,512,59,101,7933,7935,32768,10959,59,32768,10961,512,59,101,7943,7945,32768,10960,59,32768,10962,100,111,116,59,32768,8943,1792,100,101,108,112,114,118,119,7969,7983,7996,8009,8057,8147,8152,97,114,114,512,108,114,7977,7980,59,32768,10552,59,32768,10549,1089,7989,0,0,7993,114,59,32768,8926,99,59,32768,8927,97,114,114,512,59,112,8004,8006,32768,8630,59,32768,10557,1536,59,98,99,100,111,115,8022,8024,8031,8044,8049,8053,32768,8746,114,99,97,112,59,32768,10824,512,97,117,8036,8040,112,59,32768,10822,112,59,32768,10826,111,116,59,32768,8845,114,59,32768,10821,59,32896,8746,65024,1024,97,108,114,118,8066,8078,8116,8123,114,114,512,59,109,8073,8075,32768,8631,59,32768,10556,121,768,101,118,119,8086,8104,8109,113,1089,8093,0,0,8099,114,101,99,59,32768,8926,117,99,99,59,32768,8927,101,101,59,32768,8910,101,100,103,101,59,32768,8911,101,110,33024,164,59,32768,164,101,97,114,114,111,119,512,108,114,8134,8140,101,102,116,59,32768,8630,105,103,104,116,59,32768,8631,101,101,59,32768,8910,101,100,59,32768,8911,512,99,105,8162,8170,111,110,105,110,116,59,32768,8754,110,116,59,32768,8753,108,99,116,121,59,32768,9005,4864,65,72,97,98,99,100,101,102,104,105,106,108,111,114,115,116,117,119,122,8221,8226,8231,8267,8282,8296,8327,8351,8366,8379,8466,8471,8487,8621,8647,8676,8697,8712,8720,114,114,59,32768,8659,97,114,59,32768,10597,1024,103,108,114,115,8240,8246,8252,8256,103,101,114,59,32768,8224,101,116,104,59,32768,8504,114,59,32768,8595,104,512,59,118,8262,8264,32768,8208,59,32768,8867,572,8271,8278,97,114,111,119,59,32768,10511,97,99,59,32768,733,512,97,121,8287,8293,114,111,110,59,32768,271,59,32768,1076,768,59,97,111,8303,8305,8320,32768,8518,512,103,114,8310,8316,103,101,114,59,32768,8225,114,59,32768,8650,116,115,101,113,59,32768,10871,768,103,108,109,8334,8339,8344,33024,176,59,32768,176,116,97,59,32768,948,112,116,121,118,59,32768,10673,512,105,114,8356,8362,115,104,116,59,32768,10623,59,32896,55349,56609,97,114,512,108,114,8373,8376,59,32768,8643,59,32768,8642,1280,97,101,103,115,118,8390,8418,8421,8428,8433,109,768,59,111,115,8398,8400,8415,32768,8900,110,100,512,59,115,8407,8409,32768,8900,117,105,116,59,32768,9830,59,32768,9830,59,32768,168,97,109,109,97,59,32768,989,105,110,59,32768,8946,768,59,105,111,8440,8442,8461,32768,247,100,101,33280,247,59,111,8450,8452,32768,247,110,116,105,109,101,115,59,32768,8903,110,120,59,32768,8903,99,121,59,32768,1106,99,1088,8478,0,0,8483,114,110,59,32768,8990,111,112,59,32768,8973,1280,108,112,116,117,119,8498,8504,8509,8556,8570,108,97,114,59,32768,36,102,59,32896,55349,56661,1280,59,101,109,112,115,8520,8522,8535,8542,8548,32768,729,113,512,59,100,8528,8530,32768,8784,111,116,59,32768,8785,105,110,117,115,59,32768,8760,108,117,115,59,32768,8724,113,117,97,114,101,59,32768,8865,98,108,101,98,97,114,119,101,100,103,101,59,32768,8966,110,768,97,100,104,8578,8585,8597,114,114,111,119,59,32768,8595,111,119,110,97,114,114,111,119,115,59,32768,8650,97,114,112,111,111,110,512,108,114,8608,8614,101,102,116,59,32768,8643,105,103,104,116,59,32768,8642,563,8625,8633,107,97,114,111,119,59,32768,10512,1088,8638,0,0,8643,114,110,59,32768,8991,111,112,59,32768,8972,768,99,111,116,8654,8666,8670,512,114,121,8659,8663,59,32896,55349,56505,59,32768,1109,108,59,32768,10742,114,111,107,59,32768,273,512,100,114,8681,8686,111,116,59,32768,8945,105,512,59,102,8692,8694,32768,9663,59,32768,9662,512,97,104,8702,8707,114,114,59,32768,8693,97,114,59,32768,10607,97,110,103,108,101,59,32768,10662,512,99,105,8725,8729,121,59,32768,1119,103,114,97,114,114,59,32768,10239,4608,68,97,99,100,101,102,103,108,109,110,111,112,113,114,115,116,117,120,8774,8788,8807,8844,8849,8852,8866,8895,8929,8977,8989,9004,9046,9136,9151,9171,9184,9199,512,68,111,8779,8784,111,116,59,32768,10871,116,59,32768,8785,512,99,115,8793,8801,117,116,101,33024,233,59,32768,233,116,101,114,59,32768,10862,1024,97,105,111,121,8816,8822,8835,8841,114,111,110,59,32768,283,114,512,59,99,8828,8830,32768,8790,33024,234,59,32768,234,108,111,110,59,32768,8789,59,32768,1101,111,116,59,32768,279,59,32768,8519,512,68,114,8857,8862,111,116,59,32768,8786,59,32896,55349,56610,768,59,114,115,8873,8875,8883,32768,10906,97,118,101,33024,232,59,32768,232,512,59,100,8888,8890,32768,10902,111,116,59,32768,10904,1024,59,105,108,115,8904,8906,8914,8917,32768,10905,110,116,101,114,115,59,32768,9191,59,32768,8467,512,59,100,8922,8924,32768,10901,111,116,59,32768,10903,768,97,112,115,8936,8941,8960,99,114,59,32768,275,116,121,768,59,115,118,8950,8952,8957,32768,8709,101,116,59,32768,8709,59,32768,8709,112,512,49,59,8966,8975,516,8970,8973,59,32768,8196,59,32768,8197,32768,8195,512,103,115,8982,8985,59,32768,331,112,59,32768,8194,512,103,112,8994,8999,111,110,59,32768,281,102,59,32896,55349,56662,768,97,108,115,9011,9023,9028,114,512,59,115,9017,9019,32768,8917,108,59,32768,10723,117,115,59,32768,10865,105,768,59,108,118,9036,9038,9043,32768,949,111,110,59,32768,949,59,32768,1013,1024,99,115,117,118,9055,9071,9099,9128,512,105,111,9060,9065,114,99,59,32768,8790,108,111,110,59,32768,8789,1082,9077,0,0,9081,109,59,32768,8770,97,110,116,512,103,108,9088,9093,116,114,59,32768,10902,101,115,115,59,32768,10901,768,97,101,105,9106,9111,9116,108,115,59,32768,61,115,116,59,32768,8799,118,512,59,68,9122,9124,32768,8801,68,59,32768,10872,112,97,114,115,108,59,32768,10725,512,68,97,9141,9146,111,116,59,32768,8787,114,114,59,32768,10609,768,99,100,105,9158,9162,9167,114,59,32768,8495,111,116,59,32768,8784,109,59,32768,8770,512,97,104,9176,9179,59,32768,951,33024,240,59,32768,240,512,109,114,9189,9195,108,33024,235,59,32768,235,111,59,32768,8364,768,99,105,112,9206,9210,9215,108,59,32768,33,115,116,59,32768,8707,512,101,111,9220,9230,99,116,97,116,105,111,110,59,32768,8496,110,101,110,116,105,97,108,101,59,32768,8519,4914,9262,0,9276,0,9280,9287,0,0,9318,9324,0,9331,0,9352,9357,9386,0,9395,9497,108,108,105,110,103,100,111,116,115,101,113,59,32768,8786,121,59,32768,1092,109,97,108,101,59,32768,9792,768,105,108,114,9293,9299,9313,108,105,103,59,32768,64259,1082,9305,0,0,9309,103,59,32768,64256,105,103,59,32768,64260,59,32896,55349,56611,108,105,103,59,32768,64257,108,105,103,59,32896,102,106,768,97,108,116,9337,9341,9346,116,59,32768,9837,105,103,59,32768,64258,110,115,59,32768,9649,111,102,59,32768,402,833,9361,0,9366,102,59,32896,55349,56663,512,97,107,9370,9375,108,108,59,32768,8704,512,59,118,9380,9382,32768,8916,59,32768,10969,97,114,116,105,110,116,59,32768,10765,512,97,111,9399,9491,512,99,115,9404,9487,1794,9413,9443,9453,9470,9474,0,9484,1795,9421,9426,9429,9434,9437,0,9440,33024,189,59,32768,189,59,32768,8531,33024,188,59,32768,188,59,32768,8533,59,32768,8537,59,32768,8539,772,9447,0,9450,59,32768,8532,59,32768,8534,1285,9459,9464,0,0,9467,33024,190,59,32768,190,59,32768,8535,59,32768,8540,53,59,32768,8536,775,9478,0,9481,59,32768,8538,59,32768,8541,56,59,32768,8542,108,59,32768,8260,119,110,59,32768,8994,99,114,59,32896,55349,56507,4352,69,97,98,99,100,101,102,103,105,106,108,110,111,114,115,116,118,9537,9547,9575,9582,9595,9600,9679,9684,9694,9700,9705,9725,9773,9779,9785,9810,9917,512,59,108,9542,9544,32768,8807,59,32768,10892,768,99,109,112,9554,9560,9572,117,116,101,59,32768,501,109,97,512,59,100,9567,9569,32768,947,59,32768,989,59,32768,10886,114,101,118,101,59,32768,287,512,105,121,9587,9592,114,99,59,32768,285,59,32768,1075,111,116,59,32768,289,1024,59,108,113,115,9609,9611,9614,9633,32768,8805,59,32768,8923,768,59,113,115,9621,9623,9626,32768,8805,59,32768,8807,108,97,110,116,59,32768,10878,1024,59,99,100,108,9642,9644,9648,9667,32768,10878,99,59,32768,10921,111,116,512,59,111,9655,9657,32768,10880,512,59,108,9662,9664,32768,10882,59,32768,10884,512,59,101,9672,9675,32896,8923,65024,115,59,32768,10900,114,59,32896,55349,56612,512,59,103,9689,9691,32768,8811,59,32768,8921,109,101,108,59,32768,8503,99,121,59,32768,1107,1024,59,69,97,106,9714,9716,9719,9722,32768,8823,59,32768,10898,59,32768,10917,59,32768,10916,1024,69,97,101,115,9734,9737,9751,9768,59,32768,8809,112,512,59,112,9743,9745,32768,10890,114,111,120,59,32768,10890,512,59,113,9756,9758,32768,10888,512,59,113,9763,9765,32768,10888,59,32768,8809,105,109,59,32768,8935,112,102,59,32896,55349,56664,97,118,101,59,32768,96,512,99,105,9790,9794,114,59,32768,8458,109,768,59,101,108,9802,9804,9807,32768,8819,59,32768,10894,59,32768,10896,34304,62,59,99,100,108,113,114,9824,9826,9838,9843,9849,9856,32768,62,512,99,105,9831,9834,59,32768,10919,114,59,32768,10874,111,116,59,32768,8919,80,97,114,59,32768,10645,117,101,115,116,59,32768,10876,1280,97,100,101,108,115,9867,9882,9887,9906,9912,833,9872,0,9879,112,114,111,120,59,32768,10886,114,59,32768,10616,111,116,59,32768,8919,113,512,108,113,9893,9899,101,115,115,59,32768,8923,108,101,115,115,59,32768,10892,101,115,115,59,32768,8823,105,109,59,32768,8819,512,101,110,9922,9932,114,116,110,101,113,113,59,32896,8809,65024,69,59,32896,8809,65024,2560,65,97,98,99,101,102,107,111,115,121,9958,9963,10015,10020,10026,10060,10065,10085,10147,10171,114,114,59,32768,8660,1024,105,108,109,114,9972,9978,9982,9988,114,115,112,59,32768,8202,102,59,32768,189,105,108,116,59,32768,8459,512,100,114,9993,9998,99,121,59,32768,1098,768,59,99,119,10005,10007,10012,32768,8596,105,114,59,32768,10568,59,32768,8621,97,114,59,32768,8463,105,114,99,59,32768,293,768,97,108,114,10033,10048,10054,114,116,115,512,59,117,10041,10043,32768,9829,105,116,59,32768,9829,108,105,112,59,32768,8230,99,111,110,59,32768,8889,114,59,32896,55349,56613,115,512,101,119,10071,10078,97,114,111,119,59,32768,10533,97,114,111,119,59,32768,10534,1280,97,109,111,112,114,10096,10101,10107,10136,10141,114,114,59,32768,8703,116,104,116,59,32768,8763,107,512,108,114,10113,10124,101,102,116,97,114,114,111,119,59,32768,8617,105,103,104,116,97,114,114,111,119,59,32768,8618,102,59,32896,55349,56665,98,97,114,59,32768,8213,768,99,108,116,10154,10159,10165,114,59,32896,55349,56509,97,115,104,59,32768,8463,114,111,107,59,32768,295,512,98,112,10176,10182,117,108,108,59,32768,8259,104,101,110,59,32768,8208,5426,10211,0,10220,0,10239,10255,10267,0,10276,10312,0,0,10318,10371,10458,10485,10491,0,10500,10545,10558,99,117,116,101,33024,237,59,32768,237,768,59,105,121,10226,10228,10235,32768,8291,114,99,33024,238,59,32768,238,59,32768,1080,512,99,120,10243,10247,121,59,32768,1077,99,108,33024,161,59,32768,161,512,102,114,10259,10262,59,32768,8660,59,32896,55349,56614,114,97,118,101,33024,236,59,32768,236,1024,59,105,110,111,10284,10286,10300,10306,32768,8520,512,105,110,10291,10296,110,116,59,32768,10764,116,59,32768,8749,102,105,110,59,32768,10716,116,97,59,32768,8489,108,105,103,59,32768,307,768,97,111,112,10324,10361,10365,768,99,103,116,10331,10335,10357,114,59,32768,299,768,101,108,112,10342,10345,10351,59,32768,8465,105,110,101,59,32768,8464,97,114,116,59,32768,8465,104,59,32768,305,102,59,32768,8887,101,100,59,32768,437,1280,59,99,102,111,116,10381,10383,10389,10403,10409,32768,8712,97,114,101,59,32768,8453,105,110,512,59,116,10396,10398,32768,8734,105,101,59,32768,10717,100,111,116,59,32768,305,1280,59,99,101,108,112,10420,10422,10427,10444,10451,32768,8747,97,108,59,32768,8890,512,103,114,10432,10438,101,114,115,59,32768,8484,99,97,108,59,32768,8890,97,114,104,107,59,32768,10775,114,111,100,59,32768,10812,1024,99,103,112,116,10466,10470,10475,10480,121,59,32768,1105,111,110,59,32768,303,102,59,32896,55349,56666,97,59,32768,953,114,111,100,59,32768,10812,117,101,115,116,33024,191,59,32768,191,512,99,105,10504,10509,114,59,32896,55349,56510,110,1280,59,69,100,115,118,10521,10523,10526,10531,10541,32768,8712,59,32768,8953,111,116,59,32768,8949,512,59,118,10536,10538,32768,8948,59,32768,8947,59,32768,8712,512,59,105,10549,10551,32768,8290,108,100,101,59,32768,297,828,10562,0,10567,99,121,59,32768,1110,108,33024,239,59,32768,239,1536,99,102,109,111,115,117,10585,10598,10603,10609,10615,10630,512,105,121,10590,10595,114,99,59,32768,309,59,32768,1081,114,59,32896,55349,56615,97,116,104,59,32768,567,112,102,59,32896,55349,56667,820,10620,0,10625,114,59,32896,55349,56511,114,99,121,59,32768,1112,107,99,121,59,32768,1108,2048,97,99,102,103,104,106,111,115,10653,10666,10680,10685,10692,10697,10702,10708,112,112,97,512,59,118,10661,10663,32768,954,59,32768,1008,512,101,121,10671,10677,100,105,108,59,32768,311,59,32768,1082,114,59,32896,55349,56616,114,101,101,110,59,32768,312,99,121,59,32768,1093,99,121,59,32768,1116,112,102,59,32896,55349,56668,99,114,59,32896,55349,56512,5888,65,66,69,72,97,98,99,100,101,102,103,104,106,108,109,110,111,112,114,115,116,117,118,10761,10783,10789,10799,10804,10957,11011,11047,11094,11349,11372,11382,11409,11414,11451,11478,11526,11698,11711,11755,11823,11910,11929,768,97,114,116,10768,10773,10777,114,114,59,32768,8666,114,59,32768,8656,97,105,108,59,32768,10523,97,114,114,59,32768,10510,512,59,103,10794,10796,32768,8806,59,32768,10891,97,114,59,32768,10594,4660,10824,0,10830,0,10838,0,0,0,0,0,10844,10850,0,10867,10870,10877,0,10933,117,116,101,59,32768,314,109,112,116,121,118,59,32768,10676,114,97,110,59,32768,8466,98,100,97,59,32768,955,103,768,59,100,108,10857,10859,10862,32768,10216,59,32768,10641,101,59,32768,10216,59,32768,10885,117,111,33024,171,59,32768,171,114,2048,59,98,102,104,108,112,115,116,10894,10896,10907,10911,10915,10919,10923,10928,32768,8592,512,59,102,10901,10903,32768,8676,115,59,32768,10527,115,59,32768,10525,107,59,32768,8617,112,59,32768,8619,108,59,32768,10553,105,109,59,32768,10611,108,59,32768,8610,768,59,97,101,10939,10941,10946,32768,10923,105,108,59,32768,10521,512,59,115,10951,10953,32768,10925,59,32896,10925,65024,768,97,98,114,10964,10969,10974,114,114,59,32768,10508,114,107,59,32768,10098,512,97,107,10979,10991,99,512,101,107,10985,10988,59,32768,123,59,32768,91,512,101,115,10996,10999,59,32768,10635,108,512,100,117,11005,11008,59,32768,10639,59,32768,10637,1024,97,101,117,121,11020,11026,11040,11044,114,111,110,59,32768,318,512,100,105,11031,11036,105,108,59,32768,316,108,59,32768,8968,98,59,32768,123,59,32768,1083,1024,99,113,114,115,11056,11060,11072,11090,97,59,32768,10550,117,111,512,59,114,11067,11069,32768,8220,59,32768,8222,512,100,117,11077,11083,104,97,114,59,32768,10599,115,104,97,114,59,32768,10571,104,59,32768,8626,1280,59,102,103,113,115,11105,11107,11228,11231,11250,32768,8804,116,1280,97,104,108,114,116,11119,11136,11157,11169,11216,114,114,111,119,512,59,116,11128,11130,32768,8592,97,105,108,59,32768,8610,97,114,112,111,111,110,512,100,117,11147,11153,111,119,110,59,32768,8637,112,59,32768,8636,101,102,116,97,114,114,111,119,115,59,32768,8647,105,103,104,116,768,97,104,115,11180,11194,11204,114,114,111,119,512,59,115,11189,11191,32768,8596,59,32768,8646,97,114,112,111,111,110,115,59,32768,8651,113,117,105,103,97,114,114,111,119,59,32768,8621,104,114,101,101,116,105,109,101,115,59,32768,8907,59,32768,8922,768,59,113,115,11238,11240,11243,32768,8804,59,32768,8806,108,97,110,116,59,32768,10877,1280,59,99,100,103,115,11261,11263,11267,11286,11298,32768,10877,99,59,32768,10920,111,116,512,59,111,11274,11276,32768,10879,512,59,114,11281,11283,32768,10881,59,32768,10883,512,59,101,11291,11294,32896,8922,65024,115,59,32768,10899,1280,97,100,101,103,115,11309,11317,11322,11339,11344,112,112,114,111,120,59,32768,10885,111,116,59,32768,8918,113,512,103,113,11328,11333,116,114,59,32768,8922,103,116,114,59,32768,10891,116,114,59,32768,8822,105,109,59,32768,8818,768,105,108,114,11356,11362,11368,115,104,116,59,32768,10620,111,111,114,59,32768,8970,59,32896,55349,56617,512,59,69,11377,11379,32768,8822,59,32768,10897,562,11386,11405,114,512,100,117,11391,11394,59,32768,8637,512,59,108,11399,11401,32768,8636,59,32768,10602,108,107,59,32768,9604,99,121,59,32768,1113,1280,59,97,99,104,116,11425,11427,11432,11440,11446,32768,8810,114,114,59,32768,8647,111,114,110,101,114,59,32768,8990,97,114,100,59,32768,10603,114,105,59,32768,9722,512,105,111,11456,11462,100,111,116,59,32768,320,117,115,116,512,59,97,11470,11472,32768,9136,99,104,101,59,32768,9136,1024,69,97,101,115,11487,11490,11504,11521,59,32768,8808,112,512,59,112,11496,11498,32768,10889,114,111,120,59,32768,10889,512,59,113,11509,11511,32768,10887,512,59,113,11516,11518,32768,10887,59,32768,8808,105,109,59,32768,8934,2048,97,98,110,111,112,116,119,122,11543,11556,11561,11616,11640,11660,11667,11680,512,110,114,11548,11552,103,59,32768,10220,114,59,32768,8701,114,107,59,32768,10214,103,768,108,109,114,11569,11596,11604,101,102,116,512,97,114,11577,11584,114,114,111,119,59,32768,10229,105,103,104,116,97,114,114,111,119,59,32768,10231,97,112,115,116,111,59,32768,10236,105,103,104,116,97,114,114,111,119,59,32768,10230,112,97,114,114,111,119,512,108,114,11627,11633,101,102,116,59,32768,8619,105,103,104,116,59,32768,8620,768,97,102,108,11647,11651,11655,114,59,32768,10629,59,32896,55349,56669,117,115,59,32768,10797,105,109,101,115,59,32768,10804,562,11671,11676,115,116,59,32768,8727,97,114,59,32768,95,768,59,101,102,11687,11689,11695,32768,9674,110,103,101,59,32768,9674,59,32768,10731,97,114,512,59,108,11705,11707,32768,40,116,59,32768,10643,1280,97,99,104,109,116,11722,11727,11735,11747,11750,114,114,59,32768,8646,111,114,110,101,114,59,32768,8991,97,114,512,59,100,11742,11744,32768,8651,59,32768,10605,59,32768,8206,114,105,59,32768,8895,1536,97,99,104,105,113,116,11768,11774,11779,11782,11798,11817,113,117,111,59,32768,8249,114,59,32896,55349,56513,59,32768,8624,109,768,59,101,103,11790,11792,11795,32768,8818,59,32768,10893,59,32768,10895,512,98,117,11803,11806,59,32768,91,111,512,59,114,11812,11814,32768,8216,59,32768,8218,114,111,107,59,32768,322,34816,60,59,99,100,104,105,108,113,114,11841,11843,11855,11860,11866,11872,11878,11885,32768,60,512,99,105,11848,11851,59,32768,10918,114,59,32768,10873,111,116,59,32768,8918,114,101,101,59,32768,8907,109,101,115,59,32768,8905,97,114,114,59,32768,10614,117,101,115,116,59,32768,10875,512,80,105,11890,11895,97,114,59,32768,10646,768,59,101,102,11902,11904,11907,32768,9667,59,32768,8884,59,32768,9666,114,512,100,117,11916,11923,115,104,97,114,59,32768,10570,104,97,114,59,32768,10598,512,101,110,11934,11944,114,116,110,101,113,113,59,32896,8808,65024,69,59,32896,8808,65024,3584,68,97,99,100,101,102,104,105,108,110,111,112,115,117,11978,11984,12061,12075,12081,12095,12100,12104,12170,12181,12188,12204,12207,12223,68,111,116,59,32768,8762,1024,99,108,112,114,11993,11999,12019,12055,114,33024,175,59,32768,175,512,101,116,12004,12007,59,32768,9794,512,59,101,12012,12014,32768,10016,115,101,59,32768,10016,512,59,115,12024,12026,32768,8614,116,111,1024,59,100,108,117,12037,12039,12045,12051,32768,8614,111,119,110,59,32768,8615,101,102,116,59,32768,8612,112,59,32768,8613,107,101,114,59,32768,9646,512,111,121,12066,12072,109,109,97,59,32768,10793,59,32768,1084,97,115,104,59,32768,8212,97,115,117,114,101,100,97,110,103,108,101,59,32768,8737,114,59,32896,55349,56618,111,59,32768,8487,768,99,100,110,12111,12118,12146,114,111,33024,181,59,32768,181,1024,59,97,99,100,12127,12129,12134,12139,32768,8739,115,116,59,32768,42,105,114,59,32768,10992,111,116,33024,183,59,32768,183,117,115,768,59,98,100,12155,12157,12160,32768,8722,59,32768,8863,512,59,117,12165,12167,32768,8760,59,32768,10794,564,12174,12178,112,59,32768,10971,114,59,32768,8230,112,108,117,115,59,32768,8723,512,100,112,12193,12199,101,108,115,59,32768,8871,102,59,32896,55349,56670,59,32768,8723,512,99,116,12212,12217,114,59,32896,55349,56514,112,111,115,59,32768,8766,768,59,108,109,12230,12232,12240,32768,956,116,105,109,97,112,59,32768,8888,97,112,59,32768,8888,6144,71,76,82,86,97,98,99,100,101,102,103,104,105,106,108,109,111,112,114,115,116,117,118,119,12294,12315,12364,12376,12393,12472,12496,12547,12553,12636,12641,12703,12725,12747,12752,12876,12881,12957,13033,13089,13294,13359,13384,13499,512,103,116,12299,12303,59,32896,8921,824,512,59,118,12308,12311,32896,8811,8402,59,32896,8811,824,768,101,108,116,12322,12348,12352,102,116,512,97,114,12329,12336,114,114,111,119,59,32768,8653,105,103,104,116,97,114,114,111,119,59,32768,8654,59,32896,8920,824,512,59,118,12357,12360,32896,8810,8402,59,32896,8810,824,105,103,104,116,97,114,114,111,119,59,32768,8655,512,68,100,12381,12387,97,115,104,59,32768,8879,97,115,104,59,32768,8878,1280,98,99,110,112,116,12404,12409,12415,12420,12452,108,97,59,32768,8711,117,116,101,59,32768,324,103,59,32896,8736,8402,1280,59,69,105,111,112,12431,12433,12437,12442,12446,32768,8777,59,32896,10864,824,100,59,32896,8779,824,115,59,32768,329,114,111,120,59,32768,8777,117,114,512,59,97,12459,12461,32768,9838,108,512,59,115,12467,12469,32768,9838,59,32768,8469,836,12477,0,12483,112,33024,160,59,32768,160,109,112,512,59,101,12489,12492,32896,8782,824,59,32896,8783,824,1280,97,101,111,117,121,12507,12519,12525,12540,12544,833,12512,0,12515,59,32768,10819,111,110,59,32768,328,100,105,108,59,32768,326,110,103,512,59,100,12532,12534,32768,8775,111,116,59,32896,10861,824,112,59,32768,10818,59,32768,1085,97,115,104,59,32768,8211,1792,59,65,97,100,113,115,120,12568,12570,12575,12596,12602,12608,12623,32768,8800,114,114,59,32768,8663,114,512,104,114,12581,12585,107,59,32768,10532,512,59,111,12590,12592,32768,8599,119,59,32768,8599,111,116,59,32896,8784,824,117,105,118,59,32768,8802,512,101,105,12613,12618,97,114,59,32768,10536,109,59,32896,8770,824,105,115,116,512,59,115,12631,12633,32768,8708,59,32768,8708,114,59,32896,55349,56619,1024,69,101,115,116,12650,12654,12688,12693,59,32896,8807,824,768,59,113,115,12661,12663,12684,32768,8817,768,59,113,115,12670,12672,12676,32768,8817,59,32896,8807,824,108,97,110,116,59,32896,10878,824,59,32896,10878,824,105,109,59,32768,8821,512,59,114,12698,12700,32768,8815,59,32768,8815,768,65,97,112,12710,12715,12720,114,114,59,32768,8654,114,114,59,32768,8622,97,114,59,32768,10994,768,59,115,118,12732,12734,12744,32768,8715,512,59,100,12739,12741,32768,8956,59,32768,8954,59,32768,8715,99,121,59,32768,1114,1792,65,69,97,100,101,115,116,12767,12772,12776,12781,12785,12853,12858,114,114,59,32768,8653,59,32896,8806,824,114,114,59,32768,8602,114,59,32768,8229,1024,59,102,113,115,12794,12796,12821,12842,32768,8816,116,512,97,114,12802,12809,114,114,111,119,59,32768,8602,105,103,104,116,97,114,114,111,119,59,32768,8622,768,59,113,115,12828,12830,12834,32768,8816,59,32896,8806,824,108,97,110,116,59,32896,10877,824,512,59,115,12847,12850,32896,10877,824,59,32768,8814,105,109,59,32768,8820,512,59,114,12863,12865,32768,8814,105,512,59,101,12871,12873,32768,8938,59,32768,8940,105,100,59,32768,8740,512,112,116,12886,12891,102,59,32896,55349,56671,33536,172,59,105,110,12899,12901,12936,32768,172,110,1024,59,69,100,118,12911,12913,12917,12923,32768,8713,59,32896,8953,824,111,116,59,32896,8949,824,818,12928,12931,12934,59,32768,8713,59,32768,8951,59,32768,8950,105,512,59,118,12942,12944,32768,8716,818,12949,12952,12955,59,32768,8716,59,32768,8958,59,32768,8957,768,97,111,114,12964,12992,12999,114,1024,59,97,115,116,12974,12976,12983,12988,32768,8742,108,108,101,108,59,32768,8742,108,59,32896,11005,8421,59,32896,8706,824,108,105,110,116,59,32768,10772,768,59,99,101,13006,13008,13013,32768,8832,117,101,59,32768,8928,512,59,99,13018,13021,32896,10927,824,512,59,101,13026,13028,32768,8832,113,59,32896,10927,824,1024,65,97,105,116,13042,13047,13066,13077,114,114,59,32768,8655,114,114,768,59,99,119,13056,13058,13062,32768,8603,59,32896,10547,824,59,32896,8605,824,103,104,116,97,114,114,111,119,59,32768,8603,114,105,512,59,101,13084,13086,32768,8939,59,32768,8941,1792,99,104,105,109,112,113,117,13104,13128,13151,13169,13174,13179,13194,1024,59,99,101,114,13113,13115,13120,13124,32768,8833,117,101,59,32768,8929,59,32896,10928,824,59,32896,55349,56515,111,114,116,1086,13137,0,0,13142,105,100,59,32768,8740,97,114,97,108,108,101,108,59,32768,8742,109,512,59,101,13157,13159,32768,8769,512,59,113,13164,13166,32768,8772,59,32768,8772,105,100,59,32768,8740,97,114,59,32768,8742,115,117,512,98,112,13186,13190,101,59,32768,8930,101,59,32768,8931,768,98,99,112,13201,13241,13254,1024,59,69,101,115,13210,13212,13216,13219,32768,8836,59,32896,10949,824,59,32768,8840,101,116,512,59,101,13226,13229,32896,8834,8402,113,512,59,113,13235,13237,32768,8840,59,32896,10949,824,99,512,59,101,13247,13249,32768,8833,113,59,32896,10928,824,1024,59,69,101,115,13263,13265,13269,13272,32768,8837,59,32896,10950,824,59,32768,8841,101,116,512,59,101,13279,13282,32896,8835,8402,113,512,59,113,13288,13290,32768,8841,59,32896,10950,824,1024,103,105,108,114,13303,13307,13315,13319,108,59,32768,8825,108,100,101,33024,241,59,32768,241,103,59,32768,8824,105,97,110,103,108,101,512,108,114,13330,13344,101,102,116,512,59,101,13338,13340,32768,8938,113,59,32768,8940,105,103,104,116,512,59,101,13353,13355,32768,8939,113,59,32768,8941,512,59,109,13364,13366,32768,957,768,59,101,115,13373,13375,13380,32768,35,114,111,59,32768,8470,112,59,32768,8199,2304,68,72,97,100,103,105,108,114,115,13403,13409,13415,13420,13426,13439,13446,13476,13493,97,115,104,59,32768,8877,97,114,114,59,32768,10500,112,59,32896,8781,8402,97,115,104,59,32768,8876,512,101,116,13431,13435,59,32896,8805,8402,59,32896,62,8402,110,102,105,110,59,32768,10718,768,65,101,116,13453,13458,13462,114,114,59,32768,10498,59,32896,8804,8402,512,59,114,13467,13470,32896,60,8402,105,101,59,32896,8884,8402,512,65,116,13481,13486,114,114,59,32768,10499,114,105,101,59,32896,8885,8402,105,109,59,32896,8764,8402,768,65,97,110,13506,13511,13532,114,114,59,32768,8662,114,512,104,114,13517,13521,107,59,32768,10531,512,59,111,13526,13528,32768,8598,119,59,32768,8598,101,97,114,59,32768,10535,9252,13576,0,0,0,0,0,0,0,0,0,0,0,0,0,13579,0,13596,13617,13653,13659,13673,13695,13708,0,0,13713,13750,0,13788,13794,0,13815,13890,13913,13937,13944,59,32768,9416,512,99,115,13583,13591,117,116,101,33024,243,59,32768,243,116,59,32768,8859,512,105,121,13600,13613,114,512,59,99,13606,13608,32768,8858,33024,244,59,32768,244,59,32768,1086,1280,97,98,105,111,115,13627,13632,13638,13642,13646,115,104,59,32768,8861,108,97,99,59,32768,337,118,59,32768,10808,116,59,32768,8857,111,108,100,59,32768,10684,108,105,103,59,32768,339,512,99,114,13663,13668,105,114,59,32768,10687,59,32896,55349,56620,1600,13680,0,0,13684,0,13692,110,59,32768,731,97,118,101,33024,242,59,32768,242,59,32768,10689,512,98,109,13699,13704,97,114,59,32768,10677,59,32768,937,110,116,59,32768,8750,1024,97,99,105,116,13721,13726,13741,13746,114,114,59,32768,8634,512,105,114,13731,13735,114,59,32768,10686,111,115,115,59,32768,10683,110,101,59,32768,8254,59,32768,10688,768,97,101,105,13756,13761,13766,99,114,59,32768,333,103,97,59,32768,969,768,99,100,110,13773,13779,13782,114,111,110,59,32768,959,59,32768,10678,117,115,59,32768,8854,112,102,59,32896,55349,56672,768,97,101,108,13800,13804,13809,114,59,32768,10679,114,112,59,32768,10681,117,115,59,32768,8853,1792,59,97,100,105,111,115,118,13829,13831,13836,13869,13875,13879,13886,32768,8744,114,114,59,32768,8635,1024,59,101,102,109,13845,13847,13859,13864,32768,10845,114,512,59,111,13853,13855,32768,8500,102,59,32768,8500,33024,170,59,32768,170,33024,186,59,32768,186,103,111,102,59,32768,8886,114,59,32768,10838,108,111,112,101,59,32768,10839,59,32768,10843,768,99,108,111,13896,13900,13908,114,59,32768,8500,97,115,104,33024,248,59,32768,248,108,59,32768,8856,105,573,13917,13924,100,101,33024,245,59,32768,245,101,115,512,59,97,13930,13932,32768,8855,115,59,32768,10806,109,108,33024,246,59,32768,246,98,97,114,59,32768,9021,5426,13972,0,14013,0,14017,14053,0,14058,14086,0,0,14107,14199,0,14202,0,0,14229,14425,0,14438,114,1024,59,97,115,116,13981,13983,13997,14009,32768,8741,33280,182,59,108,13989,13991,32768,182,108,101,108,59,32768,8741,1082,14003,0,0,14007,109,59,32768,10995,59,32768,11005,59,32768,8706,121,59,32768,1087,114,1280,99,105,109,112,116,14028,14033,14038,14043,14046,110,116,59,32768,37,111,100,59,32768,46,105,108,59,32768,8240,59,32768,8869,101,110,107,59,32768,8241,114,59,32896,55349,56621,768,105,109,111,14064,14074,14080,512,59,118,14069,14071,32768,966,59,32768,981,109,97,116,59,32768,8499,110,101,59,32768,9742,768,59,116,118,14092,14094,14103,32768,960,99,104,102,111,114,107,59,32768,8916,59,32768,982,512,97,117,14111,14132,110,512,99,107,14117,14128,107,512,59,104,14123,14125,32768,8463,59,32768,8462,118,59,32768,8463,115,2304,59,97,98,99,100,101,109,115,116,14152,14154,14160,14163,14168,14179,14182,14188,14193,32768,43,99,105,114,59,32768,10787,59,32768,8862,105,114,59,32768,10786,512,111,117,14173,14176,59,32768,8724,59,32768,10789,59,32768,10866,110,33024,177,59,32768,177,105,109,59,32768,10790,119,111,59,32768,10791,59,32768,177,768,105,112,117,14208,14216,14221,110,116,105,110,116,59,32768,10773,102,59,32896,55349,56673,110,100,33024,163,59,32768,163,2560,59,69,97,99,101,105,110,111,115,117,14249,14251,14254,14258,14263,14336,14348,14367,14413,14418,32768,8826,59,32768,10931,112,59,32768,10935,117,101,59,32768,8828,512,59,99,14268,14270,32768,10927,1536,59,97,99,101,110,115,14283,14285,14293,14302,14306,14331,32768,8826,112,112,114,111,120,59,32768,10935,117,114,108,121,101,113,59,32768,8828,113,59,32768,10927,768,97,101,115,14313,14321,14326,112,112,114,111,120,59,32768,10937,113,113,59,32768,10933,105,109,59,32768,8936,105,109,59,32768,8830,109,101,512,59,115,14343,14345,32768,8242,59,32768,8473,768,69,97,115,14355,14358,14362,59,32768,10933,112,59,32768,10937,105,109,59,32768,8936,768,100,102,112,14374,14377,14402,59,32768,8719,768,97,108,115,14384,14390,14396,108,97,114,59,32768,9006,105,110,101,59,32768,8978,117,114,102,59,32768,8979,512,59,116,14407,14409,32768,8733,111,59,32768,8733,105,109,59,32768,8830,114,101,108,59,32768,8880,512,99,105,14429,14434,114,59,32896,55349,56517,59,32768,968,110,99,115,112,59,32768,8200,1536,102,105,111,112,115,117,14457,14462,14467,14473,14480,14486,114,59,32896,55349,56622,110,116,59,32768,10764,112,102,59,32896,55349,56674,114,105,109,101,59,32768,8279,99,114,59,32896,55349,56518,768,97,101,111,14493,14513,14526,116,512,101,105,14499,14508,114,110,105,111,110,115,59,32768,8461,110,116,59,32768,10774,115,116,512,59,101,14520,14522,32768,63,113,59,32768,8799,116,33024,34,59,32768,34,5376,65,66,72,97,98,99,100,101,102,104,105,108,109,110,111,112,114,115,116,117,120,14575,14597,14603,14608,14775,14829,14865,14901,14943,14966,15e3,15139,15159,15176,15182,15236,15261,15267,15309,15352,15360,768,97,114,116,14582,14587,14591,114,114,59,32768,8667,114,59,32768,8658,97,105,108,59,32768,10524,97,114,114,59,32768,10511,97,114,59,32768,10596,1792,99,100,101,110,113,114,116,14623,14637,14642,14650,14672,14679,14751,512,101,117,14628,14632,59,32896,8765,817,116,101,59,32768,341,105,99,59,32768,8730,109,112,116,121,118,59,32768,10675,103,1024,59,100,101,108,14660,14662,14665,14668,32768,10217,59,32768,10642,59,32768,10661,101,59,32768,10217,117,111,33024,187,59,32768,187,114,2816,59,97,98,99,102,104,108,112,115,116,119,14703,14705,14709,14720,14723,14727,14731,14735,14739,14744,14748,32768,8594,112,59,32768,10613,512,59,102,14714,14716,32768,8677,115,59,32768,10528,59,32768,10547,115,59,32768,10526,107,59,32768,8618,112,59,32768,8620,108,59,32768,10565,105,109,59,32768,10612,108,59,32768,8611,59,32768,8605,512,97,105,14756,14761,105,108,59,32768,10522,111,512,59,110,14767,14769,32768,8758,97,108,115,59,32768,8474,768,97,98,114,14782,14787,14792,114,114,59,32768,10509,114,107,59,32768,10099,512,97,107,14797,14809,99,512,101,107,14803,14806,59,32768,125,59,32768,93,512,101,115,14814,14817,59,32768,10636,108,512,100,117,14823,14826,59,32768,10638,59,32768,10640,1024,97,101,117,121,14838,14844,14858,14862,114,111,110,59,32768,345,512,100,105,14849,14854,105,108,59,32768,343,108,59,32768,8969,98,59,32768,125,59,32768,1088,1024,99,108,113,115,14874,14878,14885,14897,97,59,32768,10551,100,104,97,114,59,32768,10601,117,111,512,59,114,14892,14894,32768,8221,59,32768,8221,104,59,32768,8627,768,97,99,103,14908,14934,14938,108,1024,59,105,112,115,14918,14920,14925,14931,32768,8476,110,101,59,32768,8475,97,114,116,59,32768,8476,59,32768,8477,116,59,32768,9645,33024,174,59,32768,174,768,105,108,114,14950,14956,14962,115,104,116,59,32768,10621,111,111,114,59,32768,8971,59,32896,55349,56623,512,97,111,14971,14990,114,512,100,117,14977,14980,59,32768,8641,512,59,108,14985,14987,32768,8640,59,32768,10604,512,59,118,14995,14997,32768,961,59,32768,1009,768,103,110,115,15007,15123,15127,104,116,1536,97,104,108,114,115,116,15022,15039,15060,15086,15099,15111,114,114,111,119,512,59,116,15031,15033,32768,8594,97,105,108,59,32768,8611,97,114,112,111,111,110,512,100,117,15050,15056,111,119,110,59,32768,8641,112,59,32768,8640,101,102,116,512,97,104,15068,15076,114,114,111,119,115,59,32768,8644,97,114,112,111,111,110,115,59,32768,8652,105,103,104,116,97,114,114,111,119,115,59,32768,8649,113,117,105,103,97,114,114,111,119,59,32768,8605,104,114,101,101,116,105,109,101,115,59,32768,8908,103,59,32768,730,105,110,103,100,111,116,115,101,113,59,32768,8787,768,97,104,109,15146,15151,15156,114,114,59,32768,8644,97,114,59,32768,8652,59,32768,8207,111,117,115,116,512,59,97,15168,15170,32768,9137,99,104,101,59,32768,9137,109,105,100,59,32768,10990,1024,97,98,112,116,15191,15204,15209,15229,512,110,114,15196,15200,103,59,32768,10221,114,59,32768,8702,114,107,59,32768,10215,768,97,102,108,15216,15220,15224,114,59,32768,10630,59,32896,55349,56675,117,115,59,32768,10798,105,109,101,115,59,32768,10805,512,97,112,15241,15253,114,512,59,103,15247,15249,32768,41,116,59,32768,10644,111,108,105,110,116,59,32768,10770,97,114,114,59,32768,8649,1024,97,99,104,113,15276,15282,15287,15290,113,117,111,59,32768,8250,114,59,32896,55349,56519,59,32768,8625,512,98,117,15295,15298,59,32768,93,111,512,59,114,15304,15306,32768,8217,59,32768,8217,768,104,105,114,15316,15322,15328,114,101,101,59,32768,8908,109,101,115,59,32768,8906,105,1024,59,101,102,108,15338,15340,15343,15346,32768,9657,59,32768,8885,59,32768,9656,116,114,105,59,32768,10702,108,117,104,97,114,59,32768,10600,59,32768,8478,6706,15391,15398,15404,15499,15516,15592,0,15606,15660,0,0,15752,15758,0,15827,15863,15886,16e3,16006,16038,16086,0,16467,0,0,16506,99,117,116,101,59,32768,347,113,117,111,59,32768,8218,2560,59,69,97,99,101,105,110,112,115,121,15424,15426,15429,15441,15446,15458,15463,15482,15490,15495,32768,8827,59,32768,10932,833,15434,0,15437,59,32768,10936,111,110,59,32768,353,117,101,59,32768,8829,512,59,100,15451,15453,32768,10928,105,108,59,32768,351,114,99,59,32768,349,768,69,97,115,15470,15473,15477,59,32768,10934,112,59,32768,10938,105,109,59,32768,8937,111,108,105,110,116,59,32768,10771,105,109,59,32768,8831,59,32768,1089,111,116,768,59,98,101,15507,15509,15512,32768,8901,59,32768,8865,59,32768,10854,1792,65,97,99,109,115,116,120,15530,15535,15556,15562,15566,15572,15587,114,114,59,32768,8664,114,512,104,114,15541,15545,107,59,32768,10533,512,59,111,15550,15552,32768,8600,119,59,32768,8600,116,33024,167,59,32768,167,105,59,32768,59,119,97,114,59,32768,10537,109,512,105,110,15578,15584,110,117,115,59,32768,8726,59,32768,8726,116,59,32768,10038,114,512,59,111,15597,15600,32896,55349,56624,119,110,59,32768,8994,1024,97,99,111,121,15614,15619,15632,15654,114,112,59,32768,9839,512,104,121,15624,15629,99,121,59,32768,1097,59,32768,1096,114,116,1086,15640,0,0,15645,105,100,59,32768,8739,97,114,97,108,108,101,108,59,32768,8741,33024,173,59,32768,173,512,103,109,15664,15681,109,97,768,59,102,118,15673,15675,15678,32768,963,59,32768,962,59,32768,962,2048,59,100,101,103,108,110,112,114,15698,15700,15705,15715,15725,15735,15739,15745,32768,8764,111,116,59,32768,10858,512,59,113,15710,15712,32768,8771,59,32768,8771,512,59,69,15720,15722,32768,10910,59,32768,10912,512,59,69,15730,15732,32768,10909,59,32768,10911,101,59,32768,8774,108,117,115,59,32768,10788,97,114,114,59,32768,10610,97,114,114,59,32768,8592,1024,97,101,105,116,15766,15788,15796,15808,512,108,115,15771,15783,108,115,101,116,109,105,110,117,115,59,32768,8726,104,112,59,32768,10803,112,97,114,115,108,59,32768,10724,512,100,108,15801,15804,59,32768,8739,101,59,32768,8995,512,59,101,15813,15815,32768,10922,512,59,115,15820,15822,32768,10924,59,32896,10924,65024,768,102,108,112,15833,15839,15857,116,99,121,59,32768,1100,512,59,98,15844,15846,32768,47,512,59,97,15851,15853,32768,10692,114,59,32768,9023,102,59,32896,55349,56676,97,512,100,114,15868,15882,101,115,512,59,117,15875,15877,32768,9824,105,116,59,32768,9824,59,32768,8741,768,99,115,117,15892,15921,15977,512,97,117,15897,15909,112,512,59,115,15903,15905,32768,8851,59,32896,8851,65024,112,512,59,115,15915,15917,32768,8852,59,32896,8852,65024,117,512,98,112,15927,15952,768,59,101,115,15934,15936,15939,32768,8847,59,32768,8849,101,116,512,59,101,15946,15948,32768,8847,113,59,32768,8849,768,59,101,115,15959,15961,15964,32768,8848,59,32768,8850,101,116,512,59,101,15971,15973,32768,8848,113,59,32768,8850,768,59,97,102,15984,15986,15996,32768,9633,114,566,15991,15994,59,32768,9633,59,32768,9642,59,32768,9642,97,114,114,59,32768,8594,1024,99,101,109,116,16014,16019,16025,16031,114,59,32896,55349,56520,116,109,110,59,32768,8726,105,108,101,59,32768,8995,97,114,102,59,32768,8902,512,97,114,16042,16053,114,512,59,102,16048,16050,32768,9734,59,32768,9733,512,97,110,16058,16081,105,103,104,116,512,101,112,16067,16076,112,115,105,108,111,110,59,32768,1013,104,105,59,32768,981,115,59,32768,175,1280,98,99,109,110,112,16096,16221,16288,16291,16295,2304,59,69,100,101,109,110,112,114,115,16115,16117,16120,16125,16137,16143,16154,16160,16166,32768,8834,59,32768,10949,111,116,59,32768,10941,512,59,100,16130,16132,32768,8838,111,116,59,32768,10947,117,108,116,59,32768,10945,512,69,101,16148,16151,59,32768,10955,59,32768,8842,108,117,115,59,32768,10943,97,114,114,59,32768,10617,768,101,105,117,16173,16206,16210,116,768,59,101,110,16181,16183,16194,32768,8834,113,512,59,113,16189,16191,32768,8838,59,32768,10949,101,113,512,59,113,16201,16203,32768,8842,59,32768,10955,109,59,32768,10951,512,98,112,16215,16218,59,32768,10965,59,32768,10963,99,1536,59,97,99,101,110,115,16235,16237,16245,16254,16258,16283,32768,8827,112,112,114,111,120,59,32768,10936,117,114,108,121,101,113,59,32768,8829,113,59,32768,10928,768,97,101,115,16265,16273,16278,112,112,114,111,120,59,32768,10938,113,113,59,32768,10934,105,109,59,32768,8937,105,109,59,32768,8831,59,32768,8721,103,59,32768,9834,3328,49,50,51,59,69,100,101,104,108,109,110,112,115,16322,16327,16332,16337,16339,16342,16356,16368,16382,16388,16394,16405,16411,33024,185,59,32768,185,33024,178,59,32768,178,33024,179,59,32768,179,32768,8835,59,32768,10950,512,111,115,16347,16351,116,59,32768,10942,117,98,59,32768,10968,512,59,100,16361,16363,32768,8839,111,116,59,32768,10948,115,512,111,117,16374,16378,108,59,32768,10185,98,59,32768,10967,97,114,114,59,32768,10619,117,108,116,59,32768,10946,512,69,101,16399,16402,59,32768,10956,59,32768,8843,108,117,115,59,32768,10944,768,101,105,117,16418,16451,16455,116,768,59,101,110,16426,16428,16439,32768,8835,113,512,59,113,16434,16436,32768,8839,59,32768,10950,101,113,512,59,113,16446,16448,32768,8843,59,32768,10956,109,59,32768,10952,512,98,112,16460,16463,59,32768,10964,59,32768,10966,768,65,97,110,16473,16478,16499,114,114,59,32768,8665,114,512,104,114,16484,16488,107,59,32768,10534,512,59,111,16493,16495,32768,8601,119,59,32768,8601,119,97,114,59,32768,10538,108,105,103,33024,223,59,32768,223,5938,16538,16552,16557,16579,16584,16591,0,16596,16692,0,0,0,0,0,16731,16780,0,16787,16908,0,0,0,16938,1091,16543,0,0,16549,103,101,116,59,32768,8982,59,32768,964,114,107,59,32768,9140,768,97,101,121,16563,16569,16575,114,111,110,59,32768,357,100,105,108,59,32768,355,59,32768,1090,111,116,59,32768,8411,108,114,101,99,59,32768,8981,114,59,32896,55349,56625,1024,101,105,107,111,16604,16641,16670,16684,835,16609,0,16624,101,512,52,102,16614,16617,59,32768,8756,111,114,101,59,32768,8756,97,768,59,115,118,16631,16633,16638,32768,952,121,109,59,32768,977,59,32768,977,512,99,110,16646,16665,107,512,97,115,16652,16660,112,112,114,111,120,59,32768,8776,105,109,59,32768,8764,115,112,59,32768,8201,512,97,115,16675,16679,112,59,32768,8776,105,109,59,32768,8764,114,110,33024,254,59,32768,254,829,16696,16701,16727,100,101,59,32768,732,101,115,33536,215,59,98,100,16710,16712,16723,32768,215,512,59,97,16717,16719,32768,8864,114,59,32768,10801,59,32768,10800,116,59,32768,8749,768,101,112,115,16737,16741,16775,97,59,32768,10536,1024,59,98,99,102,16750,16752,16757,16762,32768,8868,111,116,59,32768,9014,105,114,59,32768,10993,512,59,111,16767,16770,32896,55349,56677,114,107,59,32768,10970,97,59,32768,10537,114,105,109,101,59,32768,8244,768,97,105,112,16793,16798,16899,100,101,59,32768,8482,1792,97,100,101,109,112,115,116,16813,16868,16873,16876,16883,16889,16893,110,103,108,101,1280,59,100,108,113,114,16828,16830,16836,16850,16853,32768,9653,111,119,110,59,32768,9663,101,102,116,512,59,101,16844,16846,32768,9667,113,59,32768,8884,59,32768,8796,105,103,104,116,512,59,101,16862,16864,32768,9657,113,59,32768,8885,111,116,59,32768,9708,59,32768,8796,105,110,117,115,59,32768,10810,108,117,115,59,32768,10809,98,59,32768,10701,105,109,101,59,32768,10811,101,122,105,117,109,59,32768,9186,768,99,104,116,16914,16926,16931,512,114,121,16919,16923,59,32896,55349,56521,59,32768,1094,99,121,59,32768,1115,114,111,107,59,32768,359,512,105,111,16942,16947,120,116,59,32768,8812,104,101,97,100,512,108,114,16956,16967,101,102,116,97,114,114,111,119,59,32768,8606,105,103,104,116,97,114,114,111,119,59,32768,8608,4608,65,72,97,98,99,100,102,103,104,108,109,111,112,114,115,116,117,119,17016,17021,17026,17043,17057,17072,17095,17110,17119,17139,17172,17187,17202,17290,17330,17336,17365,17381,114,114,59,32768,8657,97,114,59,32768,10595,512,99,114,17031,17039,117,116,101,33024,250,59,32768,250,114,59,32768,8593,114,820,17049,0,17053,121,59,32768,1118,118,101,59,32768,365,512,105,121,17062,17069,114,99,33024,251,59,32768,251,59,32768,1091,768,97,98,104,17079,17084,17090,114,114,59,32768,8645,108,97,99,59,32768,369,97,114,59,32768,10606,512,105,114,17100,17106,115,104,116,59,32768,10622,59,32896,55349,56626,114,97,118,101,33024,249,59,32768,249,562,17123,17135,114,512,108,114,17128,17131,59,32768,8639,59,32768,8638,108,107,59,32768,9600,512,99,116,17144,17167,1088,17150,0,0,17163,114,110,512,59,101,17156,17158,32768,8988,114,59,32768,8988,111,112,59,32768,8975,114,105,59,32768,9720,512,97,108,17177,17182,99,114,59,32768,363,33024,168,59,32768,168,512,103,112,17192,17197,111,110,59,32768,371,102,59,32896,55349,56678,1536,97,100,104,108,115,117,17215,17222,17233,17257,17262,17280,114,114,111,119,59,32768,8593,111,119,110,97,114,114,111,119,59,32768,8597,97,114,112,111,111,110,512,108,114,17244,17250,101,102,116,59,32768,8639,105,103,104,116,59,32768,8638,117,115,59,32768,8846,105,768,59,104,108,17270,17272,17275,32768,965,59,32768,978,111,110,59,32768,965,112,97,114,114,111,119,115,59,32768,8648,768,99,105,116,17297,17320,17325,1088,17303,0,0,17316,114,110,512,59,101,17309,17311,32768,8989,114,59,32768,8989,111,112,59,32768,8974,110,103,59,32768,367,114,105,59,32768,9721,99,114,59,32896,55349,56522,768,100,105,114,17343,17348,17354,111,116,59,32768,8944,108,100,101,59,32768,361,105,512,59,102,17360,17362,32768,9653,59,32768,9652,512,97,109,17370,17375,114,114,59,32768,8648,108,33024,252,59,32768,252,97,110,103,108,101,59,32768,10663,3840,65,66,68,97,99,100,101,102,108,110,111,112,114,115,122,17420,17425,17437,17443,17613,17617,17623,17667,17672,17678,17693,17699,17705,17711,17754,114,114,59,32768,8661,97,114,512,59,118,17432,17434,32768,10984,59,32768,10985,97,115,104,59,32768,8872,512,110,114,17448,17454,103,114,116,59,32768,10652,1792,101,107,110,112,114,115,116,17469,17478,17485,17494,17515,17526,17578,112,115,105,108,111,110,59,32768,1013,97,112,112,97,59,32768,1008,111,116,104,105,110,103,59,32768,8709,768,104,105,114,17501,17505,17508,105,59,32768,981,59,32768,982,111,112,116,111,59,32768,8733,512,59,104,17520,17522,32768,8597,111,59,32768,1009,512,105,117,17531,17537,103,109,97,59,32768,962,512,98,112,17542,17560,115,101,116,110,101,113,512,59,113,17553,17556,32896,8842,65024,59,32896,10955,65024,115,101,116,110,101,113,512,59,113,17571,17574,32896,8843,65024,59,32896,10956,65024,512,104,114,17583,17589,101,116,97,59,32768,977,105,97,110,103,108,101,512,108,114,17600,17606,101,102,116,59,32768,8882,105,103,104,116,59,32768,8883,121,59,32768,1074,97,115,104,59,32768,8866,768,101,108,114,17630,17648,17654,768,59,98,101,17637,17639,17644,32768,8744,97,114,59,32768,8891,113,59,32768,8794,108,105,112,59,32768,8942,512,98,116,17659,17664,97,114,59,32768,124,59,32768,124,114,59,32896,55349,56627,116,114,105,59,32768,8882,115,117,512,98,112,17685,17689,59,32896,8834,8402,59,32896,8835,8402,112,102,59,32896,55349,56679,114,111,112,59,32768,8733,116,114,105,59,32768,8883,512,99,117,17716,17721,114,59,32896,55349,56523,512,98,112,17726,17740,110,512,69,101,17732,17736,59,32896,10955,65024,59,32896,8842,65024,110,512,69,101,17746,17750,59,32896,10956,65024,59,32896,8843,65024,105,103,122,97,103,59,32768,10650,1792,99,101,102,111,112,114,115,17777,17783,17815,17820,17826,17829,17842,105,114,99,59,32768,373,512,100,105,17788,17809,512,98,103,17793,17798,97,114,59,32768,10847,101,512,59,113,17804,17806,32768,8743,59,32768,8793,101,114,112,59,32768,8472,114,59,32896,55349,56628,112,102,59,32896,55349,56680,59,32768,8472,512,59,101,17834,17836,32768,8768,97,116,104,59,32768,8768,99,114,59,32896,55349,56524,5428,17871,17891,0,17897,0,17902,17917,0,0,17920,17935,17940,17945,0,0,17977,17992,0,18008,18024,18029,768,97,105,117,17877,17881,17886,112,59,32768,8898,114,99,59,32768,9711,112,59,32768,8899,116,114,105,59,32768,9661,114,59,32896,55349,56629,512,65,97,17906,17911,114,114,59,32768,10234,114,114,59,32768,10231,59,32768,958,512,65,97,17924,17929,114,114,59,32768,10232,114,114,59,32768,10229,97,112,59,32768,10236,105,115,59,32768,8955,768,100,112,116,17951,17956,17970,111,116,59,32768,10752,512,102,108,17961,17965,59,32896,55349,56681,117,115,59,32768,10753,105,109,101,59,32768,10754,512,65,97,17981,17986,114,114,59,32768,10233,114,114,59,32768,10230,512,99,113,17996,18001,114,59,32896,55349,56525,99,117,112,59,32768,10758,512,112,116,18012,18018,108,117,115,59,32768,10756,114,105,59,32768,9651,101,101,59,32768,8897,101,100,103,101,59,32768,8896,2048,97,99,101,102,105,111,115,117,18052,18068,18081,18087,18092,18097,18103,18109,99,512,117,121,18058,18065,116,101,33024,253,59,32768,253,59,32768,1103,512,105,121,18073,18078,114,99,59,32768,375,59,32768,1099,110,33024,165,59,32768,165,114,59,32896,55349,56630,99,121,59,32768,1111,112,102,59,32896,55349,56682,99,114,59,32896,55349,56526,512,99,109,18114,18118,121,59,32768,1102,108,33024,255,59,32768,255,2560,97,99,100,101,102,104,105,111,115,119,18145,18152,18166,18171,18186,18191,18196,18204,18210,18216,99,117,116,101,59,32768,378,512,97,121,18157,18163,114,111,110,59,32768,382,59,32768,1079,111,116,59,32768,380,512,101,116,18176,18182,116,114,102,59,32768,8488,97,59,32768,950,114,59,32896,55349,56631,99,121,59,32768,1078,103,114,97,114,114,59,32768,8669,112,102,59,32896,55349,56683,99,114,59,32896,55349,56527,512,106,110,18221,18224,59,32768,8205,106,59,32768,8204]);var F0={};Object.defineProperty(F0,"__esModule",{value:!0});F0.default=new Uint16Array([1024,97,103,108,113,9,23,27,31,1086,15,0,0,19,112,59,32768,38,111,115,59,32768,39,116,59,32768,62,116,59,32768,60,117,111,116,59,32768,34]);(function(e){var t=V&&V.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(e,"__esModule",{value:!0}),e.decodeXML=e.decodeHTMLStrict=e.decodeHTML=e.determineBranch=e.JUMP_OFFSET_BASE=e.BinTrieFlags=e.xmlDecodeTree=e.htmlDecodeTree=void 0;var n=t(q0);e.htmlDecodeTree=n.default;var r=t(F0);e.xmlDecodeTree=r.default;var i=t(ya),o;(function(p){p[p.HAS_VALUE=32768]="HAS_VALUE",p[p.BRANCH_LENGTH=32512]="BRANCH_LENGTH",p[p.MULTI_BYTE=128]="MULTI_BYTE",p[p.JUMP_TABLE=127]="JUMP_TABLE"})(o=e.BinTrieFlags||(e.BinTrieFlags={})),e.JUMP_OFFSET_BASE=47;function s(p){return function(h,_){for(var m="",g=0,y=0;(y=h.indexOf("&",y))>=0;){if(m+=h.slice(g,y),g=y,y+=1,h.charCodeAt(y)===35){var w=y+1,x=10,S=h.charCodeAt(w);for((S|32)===120&&(x=16,y+=1,w+=1);(S=h.charCodeAt(++y))>=48&&S<=57||x===16&&(S|32)>=97&&(S|32)<=102;);if(w!==y){var T=h.substring(w,y),b=parseInt(T,x);if(h.charCodeAt(y)===59)y+=1;else if(_)continue;m+=i.default(b),g=y}continue}for(var C=null,E=1,L=0,O=p[L];y<h.length&&(L=a(p,O,L+1,h.charCodeAt(y)),!(L<0));y++,E++)O=p[L],O&o.HAS_VALUE&&(_&&h.charCodeAt(y)!==59?L+=1:(C=O&o.MULTI_BYTE?String.fromCharCode(p[++L],p[++L]):String.fromCharCode(p[++L]),E=0));C!=null&&(m+=C,g=y-E+1)}return m+h.slice(g)}}function a(p,v,h,_){if(v<=128)return _===v?h:-1;var m=(v&o.BRANCH_LENGTH)>>8;if(m===0)return-1;if(m===1)return _===p[h]?h+1:-1;var g=v&o.JUMP_TABLE;if(g){var y=_-e.JUMP_OFFSET_BASE-g;return y<0||y>m?-1:p[h+y]-1}for(var w=h,x=w+m-1;w<=x;){var S=w+x>>>1,T=p[S];if(T<_)w=S+1;else if(T>_)x=S-1;else return p[S+m]}return-1}e.determineBranch=a;var l=s(n.default),u=s(r.default);function c(p){return l(p,!1)}e.decodeHTML=c;function f(p){return l(p,!0)}e.decodeHTMLStrict=f;function d(p){return u(p,!0)}e.decodeXML=d})(Fh);var lw=V&&V.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(va,"__esModule",{value:!0});var uw=lw(ya),W1=Fh;function nn(e){return e===32||e===10||e===9||e===12||e===13}function No(e){return e===47||e===62||nn(e)}function N8(e){return e>=48&&e<=57}function cw(e){return e>=97&&e<=122||e>=65&&e<=90}var Ht={Cdata:new Uint16Array([67,68,65,84,65,91]),CdataEnd:new Uint16Array([93,93,62]),CommentEnd:new Uint16Array([45,45,62]),ScriptEnd:new Uint16Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint16Array([60,47,115,116,121,108,101]),TitleEnd:new Uint16Array([60,47,116,105,116,108,101])},fw=function(){function e(t,n){var r=t.xmlMode,i=r===void 0?!1:r,o=t.decodeEntities,s=o===void 0?!0:o;this.cbs=n,this._state=1,this.buffer="",this.sectionStart=0,this._index=0,this.bufferOffset=0,this.baseState=1,this.isSpecial=!1,this.running=!0,this.ended=!1,this.sequenceIndex=0,this.trieIndex=0,this.trieCurrent=0,this.trieResult=null,this.entityExcess=0,this.xmlMode=i,this.decodeEntities=s,this.entityTrie=i?W1.xmlDecodeTree:W1.htmlDecodeTree}return e.prototype.reset=function(){this._state=1,this.buffer="",this.sectionStart=0,this._index=0,this.bufferOffset=0,this.baseState=1,this.currentSequence=void 0,this.running=!0,this.ended=!1},e.prototype.write=function(t){if(this.ended)return this.cbs.onerror(Error(".write() after done!"));this.buffer+=t,this.parse()},e.prototype.end=function(t){if(this.ended)return this.cbs.onerror(Error(".end() after done!"));t&&this.write(t),this.ended=!0,this.running&&this.finish()},e.prototype.pause=function(){this.running=!1},e.prototype.resume=function(){this.running=!0,this._index<this.buffer.length&&this.parse(),this.ended&&this.finish()},e.prototype.getAbsoluteSectionStart=function(){return this.sectionStart+this.bufferOffset},e.prototype.getAbsoluteIndex=function(){return this.bufferOffset+this._index},e.prototype.stateText=function(t){t===60||!this.decodeEntities&&this.fastForwardTo(60)?(this._index>this.sectionStart&&this.cbs.ontext(this.getSection()),this._state=2,this.sectionStart=this._index):this.decodeEntities&&t===38&&(this._state=25)},e.prototype.stateSpecialStartSequence=function(t){var n=this.sequenceIndex===this.currentSequence.length,r=n?No(t):(t|32)===this.currentSequence[this.sequenceIndex];if(!r)this.isSpecial=!1;else if(!n){this.sequenceIndex++;return}this.sequenceIndex=0,this._state=3,this.stateInTagName(t)},e.prototype.stateInSpecialTag=function(t){if(this.sequenceIndex===this.currentSequence.length){if(t===62||nn(t)){var n=this._index-this.currentSequence.length;if(this.sectionStart<n){var r=this._index;this._index=n,this.cbs.ontext(this.getSection()),this._index=r}this.isSpecial=!1,this.sectionStart=n+2,this.stateInClosingTagName(t);return}this.sequenceIndex=0}(t|32)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:this.sequenceIndex===0?this.currentSequence===Ht.TitleEnd?this.decodeEntities&&t===38&&(this._state=25):this.fastForwardTo(60)&&(this.sequenceIndex=1):this.sequenceIndex=+(t===60)},e.prototype.stateCDATASequence=function(t){t===Ht.Cdata[this.sequenceIndex]?++this.sequenceIndex===Ht.Cdata.length&&(this._state=21,this.currentSequence=Ht.CdataEnd,this.sequenceIndex=0,this.sectionStart=this._index+1):(this.sequenceIndex=0,this._state=16,this.stateInDeclaration(t))},e.prototype.fastForwardTo=function(t){for(;++this._index<this.buffer.length;)if(this.buffer.charCodeAt(this._index)===t)return!0;return this._index=this.buffer.length-1,!1},e.prototype.stateInCommentLike=function(t){if(t===this.currentSequence[this.sequenceIndex]){if(++this.sequenceIndex===this.currentSequence.length){var n=this.buffer.slice(this.sectionStart,this._index-2);this.currentSequence===Ht.CdataEnd?this.cbs.oncdata(n):this.cbs.oncomment(n),this.sequenceIndex=0,this.sectionStart=this._index+1,this._state=1}}else this.sequenceIndex===0?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):t!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)},e.prototype.isTagStartChar=function(t){return this.xmlMode?!No(t):cw(t)},e.prototype.startSpecial=function(t,n){this.isSpecial=!0,this.currentSequence=t,this.sequenceIndex=n,this._state=23},e.prototype.stateBeforeTagName=function(t){if(t===33)this._state=15,this.sectionStart=this._index+1;else if(t===63)this._state=17,this.sectionStart=this._index+1;else if(this.isTagStartChar(t)){var n=t|32;this.sectionStart=this._index,!this.xmlMode&&n===Ht.TitleEnd[2]?this.startSpecial(Ht.TitleEnd,3):this._state=!this.xmlMode&&n===Ht.ScriptEnd[2]?22:3}else t===47?this._state=5:(this._state=1,this.stateText(t))},e.prototype.stateInTagName=function(t){No(t)&&(this.cbs.onopentagname(this.getSection()),this.sectionStart=-1,this._state=8,this.stateBeforeAttributeName(t))},e.prototype.stateBeforeClosingTagName=function(t){nn(t)||(t===62?this._state=1:(this._state=this.isTagStartChar(t)?6:20,this.sectionStart=this._index))},e.prototype.stateInClosingTagName=function(t){(t===62||nn(t))&&(this.cbs.onclosetag(this.getSection()),this.sectionStart=-1,this._state=7,this.stateAfterClosingTagName(t))},e.prototype.stateAfterClosingTagName=function(t){(t===62||this.fastForwardTo(62))&&(this._state=1,this.sectionStart=this._index+1)},e.prototype.stateBeforeAttributeName=function(t){t===62?(this.cbs.onopentagend(),this.isSpecial?(this._state=24,this.sequenceIndex=0):this._state=1,this.baseState=this._state,this.sectionStart=this._index+1):t===47?this._state=4:nn(t)||(this._state=9,this.sectionStart=this._index)},e.prototype.stateInSelfClosingTag=function(t){t===62?(this.cbs.onselfclosingtag(),this._state=1,this.baseState=1,this.sectionStart=this._index+1,this.isSpecial=!1):nn(t)||(this._state=8,this.stateBeforeAttributeName(t))},e.prototype.stateInAttributeName=function(t){(t===61||No(t))&&(this.cbs.onattribname(this.getSection()),this.sectionStart=-1,this._state=10,this.stateAfterAttributeName(t))},e.prototype.stateAfterAttributeName=function(t){t===61?this._state=11:t===47||t===62?(this.cbs.onattribend(void 0),this._state=8,this.stateBeforeAttributeName(t)):nn(t)||(this.cbs.onattribend(void 0),this._state=9,this.sectionStart=this._index)},e.prototype.stateBeforeAttributeValue=function(t){t===34?(this._state=12,this.sectionStart=this._index+1):t===39?(this._state=13,this.sectionStart=this._index+1):nn(t)||(this.sectionStart=this._index,this._state=14,this.stateInAttributeValueNoQuotes(t))},e.prototype.handleInAttributeValue=function(t,n){t===n||!this.decodeEntities&&this.fastForwardTo(n)?(this.cbs.onattribdata(this.getSection()),this.sectionStart=-1,this.cbs.onattribend(String.fromCharCode(n)),this._state=8):this.decodeEntities&&t===38&&(this.baseState=this._state,this._state=25)},e.prototype.stateInAttributeValueDoubleQuotes=function(t){this.handleInAttributeValue(t,34)},e.prototype.stateInAttributeValueSingleQuotes=function(t){this.handleInAttributeValue(t,39)},e.prototype.stateInAttributeValueNoQuotes=function(t){nn(t)||t===62?(this.cbs.onattribdata(this.getSection()),this.sectionStart=-1,this.cbs.onattribend(null),this._state=8,this.stateBeforeAttributeName(t)):this.decodeEntities&&t===38&&(this.baseState=this._state,this._state=25)},e.prototype.stateBeforeDeclaration=function(t){t===91?(this._state=19,this.sequenceIndex=0):this._state=t===45?18:16},e.prototype.stateInDeclaration=function(t){(t===62||this.fastForwardTo(62))&&(this.cbs.ondeclaration(this.getSection()),this._state=1,this.sectionStart=this._index+1)},e.prototype.stateInProcessingInstruction=function(t){(t===62||this.fastForwardTo(62))&&(this.cbs.onprocessinginstruction(this.getSection()),this._state=1,this.sectionStart=this._index+1)},e.prototype.stateBeforeComment=function(t){t===45?(this._state=21,this.currentSequence=Ht.CommentEnd,this.sequenceIndex=2,this.sectionStart=this._index+1):this._state=16},e.prototype.stateInSpecialComment=function(t){(t===62||this.fastForwardTo(62))&&(this.cbs.oncomment(this.getSection()),this._state=1,this.sectionStart=this._index+1)},e.prototype.stateBeforeSpecialS=function(t){var n=t|32;n===Ht.ScriptEnd[3]?this.startSpecial(Ht.ScriptEnd,4):n===Ht.StyleEnd[3]?this.startSpecial(Ht.StyleEnd,4):(this._state=3,this.stateInTagName(t))},e.prototype.stateBeforeEntity=function(t){this.entityExcess=1,t===35?this._state=26:t===38||(this.trieIndex=0,this.trieCurrent=this.entityTrie[0],this.trieResult=null,this._state=27,this.stateInNamedEntity(t))},e.prototype.stateInNamedEntity=function(t){if(this.entityExcess+=1,this.trieIndex=(0,W1.determineBranch)(this.entityTrie,this.trieCurrent,this.trieIndex+1,t),this.trieIndex<0){this.emitNamedEntity(),this._index--;return}if(this.trieCurrent=this.entityTrie[this.trieIndex],this.trieCurrent&W1.BinTrieFlags.HAS_VALUE)if(!this.allowLegacyEntity()&&t!==59)this.trieIndex+=1;else{var n=this._index-this.entityExcess+1;n>this.sectionStart&&this.emitPartial(this.buffer.substring(this.sectionStart,n)),this.trieResult=this.trieCurrent&W1.BinTrieFlags.MULTI_BYTE?String.fromCharCode(this.entityTrie[++this.trieIndex],this.entityTrie[++this.trieIndex]):String.fromCharCode(this.entityTrie[++this.trieIndex]),this.entityExcess=0,this.sectionStart=this._index+1}},e.prototype.emitNamedEntity=function(){this.trieResult&&this.emitPartial(this.trieResult),this._state=this.baseState},e.prototype.stateBeforeNumericEntity=function(t){(t|32)===120?(this.entityExcess++,this._state=29):(this._state=28,this.stateInNumericEntity(t))},e.prototype.decodeNumericEntity=function(t,n){var r=this._index-this.entityExcess-1,i=r+2+(t>>4);if(i!==this._index){r>this.sectionStart&&this.emitPartial(this.buffer.substring(this.sectionStart,r));var o=this.buffer.substring(i,this._index),s=parseInt(o,t);this.emitPartial((0,uw.default)(s)),this.sectionStart=this._index+Number(n)}this._state=this.baseState},e.prototype.stateInNumericEntity=function(t){t===59?this.decodeNumericEntity(10,!0):N8(t)?this.entityExcess++:(this.allowLegacyEntity()?this.decodeNumericEntity(10,!1):this._state=this.baseState,this._index--)},e.prototype.stateInHexEntity=function(t){t===59?this.decodeNumericEntity(16,!0):(t<97||t>102)&&(t<65||t>70)&&!N8(t)?(this.allowLegacyEntity()?this.decodeNumericEntity(16,!1):this._state=this.baseState,this._index--):this.entityExcess++},e.prototype.allowLegacyEntity=function(){return!this.xmlMode&&(this.baseState===1||this.baseState===24)},e.prototype.cleanup=function(){this.running&&this.sectionStart!==this._index&&(this._state===1||this._state===24&&this.sequenceIndex===0)&&(this.cbs.ontext(this.buffer.substr(this.sectionStart)),this.sectionStart=this._index);var t=this.sectionStart<0?this._index:this.sectionStart;this.buffer=t===this.buffer.length?"":this.buffer.substr(t),this._index-=t,this.bufferOffset+=t,this.sectionStart>0&&(this.sectionStart=0)},e.prototype.shouldContinue=function(){return this._index<this.buffer.length&&this.running},e.prototype.parse=function(){for(;this.shouldContinue();){var t=this.buffer.charCodeAt(this._index);this._state===1?this.stateText(t):this._state===23?this.stateSpecialStartSequence(t):this._state===24?this.stateInSpecialTag(t):this._state===19?this.stateCDATASequence(t):this._state===12?this.stateInAttributeValueDoubleQuotes(t):this._state===9?this.stateInAttributeName(t):this._state===21?this.stateInCommentLike(t):this._state===20?this.stateInSpecialComment(t):this._state===8?this.stateBeforeAttributeName(t):this._state===3?this.stateInTagName(t):this._state===6?this.stateInClosingTagName(t):this._state===2?this.stateBeforeTagName(t):this._state===10?this.stateAfterAttributeName(t):this._state===13?this.stateInAttributeValueSingleQuotes(t):this._state===11?this.stateBeforeAttributeValue(t):this._state===5?this.stateBeforeClosingTagName(t):this._state===7?this.stateAfterClosingTagName(t):this._state===22?this.stateBeforeSpecialS(t):this._state===14?this.stateInAttributeValueNoQuotes(t):this._state===4?this.stateInSelfClosingTag(t):this._state===16?this.stateInDeclaration(t):this._state===15?this.stateBeforeDeclaration(t):this._state===18?this.stateBeforeComment(t):this._state===17?this.stateInProcessingInstruction(t):this._state===27?this.stateInNamedEntity(t):this._state===25?this.stateBeforeEntity(t):this._state===29?this.stateInHexEntity(t):this._state===28?this.stateInNumericEntity(t):this.stateBeforeNumericEntity(t),this._index++}this.cleanup()},e.prototype.finish=function(){this._state===27&&this.emitNamedEntity(),this.sectionStart<this._index&&this.handleTrailingData(),this.cbs.onend()},e.prototype.handleTrailingData=function(){var t=this.buffer.substr(this.sectionStart);this._state===21?this.currentSequence===Ht.CdataEnd?this.cbs.oncdata(t):this.cbs.oncomment(t):this._state===28&&this.allowLegacyEntity()?this.decodeNumericEntity(10,!1):this._state===29&&this.allowLegacyEntity()?this.decodeNumericEntity(16,!1):this._state===3||this._state===8||this._state===11||this._state===10||this._state===9||this._state===13||this._state===12||this._state===14||this._state===6||this.cbs.ontext(t)},e.prototype.getSection=function(){return this.buffer.substring(this.sectionStart,this._index)},e.prototype.emitPartial=function(t){this.baseState!==1&&this.baseState!==24?this.cbs.onattribdata(t):this.cbs.ontext(t)},e}();va.default=fw;var dw=V&&V.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(so,"__esModule",{value:!0});so.Parser=void 0;var hw=dw(va),zr=new Set(["input","option","optgroup","select","button","datalist","textarea"]),X=new Set(["p"]),R8=new Set(["thead","tbody"]),I8=new Set(["dd","dt"]),V8=new Set(["rt","rp"]),pw=new Map([["tr",new Set(["tr","th","td"])],["th",new Set(["th"])],["td",new Set(["thead","th","td"])],["body",new Set(["head","link","script"])],["li",new Set(["li"])],["p",X],["h1",X],["h2",X],["h3",X],["h4",X],["h5",X],["h6",X],["select",zr],["input",zr],["output",zr],["button",zr],["datalist",zr],["textarea",zr],["option",new Set(["option"])],["optgroup",new Set(["optgroup","option"])],["dd",I8],["dt",I8],["address",X],["article",X],["aside",X],["blockquote",X],["details",X],["div",X],["dl",X],["fieldset",X],["figcaption",X],["figure",X],["footer",X],["form",X],["header",X],["hr",X],["main",X],["nav",X],["ol",X],["pre",X],["section",X],["table",X],["ul",X],["rt",V8],["rp",V8],["tbody",R8],["tfoot",R8]]),mw=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]),q8=new Set(["math","svg"]),F8=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignobject","desc","title"]),gw=/\s|\//,vw=function(){function e(t,n){n===void 0&&(n={});var r,i,o,s,a;this.options=n,this.startIndex=0,this.endIndex=0,this.openTagStart=0,this.tagname="",this.attribname="",this.attribvalue="",this.attribs=null,this.stack=[],this.foreignContext=[],this.cbs=t!=null?t:{},this.lowerCaseTagNames=(r=n.lowerCaseTags)!==null&&r!==void 0?r:!n.xmlMode,this.lowerCaseAttributeNames=(i=n.lowerCaseAttributeNames)!==null&&i!==void 0?i:!n.xmlMode,this.tokenizer=new((o=n.Tokenizer)!==null&&o!==void 0?o:hw.default)(this.options,this),(a=(s=this.cbs).onparserinit)===null||a===void 0||a.call(s,this)}return e.prototype.ontext=function(t){var n,r,i=this.tokenizer.getAbsoluteIndex();this.endIndex=i-1,(r=(n=this.cbs).ontext)===null||r===void 0||r.call(n,t),this.startIndex=i},e.prototype.isVoidElement=function(t){return!this.options.xmlMode&&mw.has(t)},e.prototype.onopentagname=function(t){this.endIndex=this.tokenizer.getAbsoluteIndex(),this.lowerCaseTagNames&&(t=t.toLowerCase()),this.emitOpenTag(t)},e.prototype.emitOpenTag=function(t){var n,r,i,o;this.openTagStart=this.startIndex,this.tagname=t;var s=!this.options.xmlMode&&pw.get(t);if(s)for(;this.stack.length>0&&s.has(this.stack[this.stack.length-1]);){var a=this.stack.pop();(r=(n=this.cbs).onclosetag)===null||r===void 0||r.call(n,a,!0)}this.isVoidElement(t)||(this.stack.push(t),q8.has(t)?this.foreignContext.push(!0):F8.has(t)&&this.foreignContext.push(!1)),(o=(i=this.cbs).onopentagname)===null||o===void 0||o.call(i,t),this.cbs.onopentag&&(this.attribs={})},e.prototype.endOpenTag=function(t){var n,r;this.startIndex=this.openTagStart,this.endIndex=this.tokenizer.getAbsoluteIndex(),this.attribs&&((r=(n=this.cbs).onopentag)===null||r===void 0||r.call(n,this.tagname,this.attribs,t),this.attribs=null),this.cbs.onclosetag&&this.isVoidElement(this.tagname)&&this.cbs.onclosetag(this.tagname,!0),this.tagname=""},e.prototype.onopentagend=function(){this.endOpenTag(!1),this.startIndex=this.endIndex+1},e.prototype.onclosetag=function(t){var n,r,i,o,s,a;if(this.endIndex=this.tokenizer.getAbsoluteIndex(),this.lowerCaseTagNames&&(t=t.toLowerCase()),(q8.has(t)||F8.has(t))&&this.foreignContext.pop(),this.isVoidElement(t))!this.options.xmlMode&&t==="br"&&((r=(n=this.cbs).onopentagname)===null||r===void 0||r.call(n,t),(o=(i=this.cbs).onopentag)===null||o===void 0||o.call(i,t,{},!0),(a=(s=this.cbs).onclosetag)===null||a===void 0||a.call(s,t,!1));else{var l=this.stack.lastIndexOf(t);if(l!==-1)if(this.cbs.onclosetag)for(var u=this.stack.length-l;u--;)this.cbs.onclosetag(this.stack.pop(),u!==0);else this.stack.length=l;else!this.options.xmlMode&&t==="p"&&(this.emitOpenTag(t),this.closeCurrentTag(!0))}this.startIndex=this.endIndex+1},e.prototype.onselfclosingtag=function(){this.options.xmlMode||this.options.recognizeSelfClosing||this.foreignContext[this.foreignContext.length-1]?(this.closeCurrentTag(!1),this.startIndex=this.endIndex+1):this.onopentagend()},e.prototype.closeCurrentTag=function(t){var n,r,i=this.tagname;this.endOpenTag(t),this.stack[this.stack.length-1]===i&&((r=(n=this.cbs).onclosetag)===null||r===void 0||r.call(n,i,!t),this.stack.pop())},e.prototype.onattribname=function(t){this.startIndex=this.tokenizer.getAbsoluteSectionStart(),this.lowerCaseAttributeNames&&(t=t.toLowerCase()),this.attribname=t},e.prototype.onattribdata=function(t){this.attribvalue+=t},e.prototype.onattribend=function(t){var n,r;this.endIndex=this.tokenizer.getAbsoluteIndex(),(r=(n=this.cbs).onattribute)===null||r===void 0||r.call(n,this.attribname,this.attribvalue,t),this.attribs&&!Object.prototype.hasOwnProperty.call(this.attribs,this.attribname)&&(this.attribs[this.attribname]=this.attribvalue),this.attribname="",this.attribvalue=""},e.prototype.getInstructionName=function(t){var n=t.search(gw),r=n<0?t:t.substr(0,n);return this.lowerCaseTagNames&&(r=r.toLowerCase()),r},e.prototype.ondeclaration=function(t){if(this.endIndex=this.tokenizer.getAbsoluteIndex(),this.cbs.onprocessinginstruction){var n=this.getInstructionName(t);this.cbs.onprocessinginstruction("!"+n,"!"+t)}this.startIndex=this.endIndex+1},e.prototype.onprocessinginstruction=function(t){if(this.endIndex=this.tokenizer.getAbsoluteIndex(),this.cbs.onprocessinginstruction){var n=this.getInstructionName(t);this.cbs.onprocessinginstruction("?"+n,"?"+t)}this.startIndex=this.endIndex+1},e.prototype.oncomment=function(t){var n,r,i,o;this.endIndex=this.tokenizer.getAbsoluteIndex(),(r=(n=this.cbs).oncomment)===null||r===void 0||r.call(n,t),(o=(i=this.cbs).oncommentend)===null||o===void 0||o.call(i),this.startIndex=this.endIndex+1},e.prototype.oncdata=function(t){var n,r,i,o,s,a,l,u,c,f;this.endIndex=this.tokenizer.getAbsoluteIndex(),this.options.xmlMode||this.options.recognizeCDATA?((r=(n=this.cbs).oncdatastart)===null||r===void 0||r.call(n),(o=(i=this.cbs).ontext)===null||o===void 0||o.call(i,t),(a=(s=this.cbs).oncdataend)===null||a===void 0||a.call(s)):((u=(l=this.cbs).oncomment)===null||u===void 0||u.call(l,"[CDATA["+t+"]]"),(f=(c=this.cbs).oncommentend)===null||f===void 0||f.call(c)),this.startIndex=this.endIndex+1},e.prototype.onerror=function(t){var n,r;(r=(n=this.cbs).onerror)===null||r===void 0||r.call(n,t)},e.prototype.onend=function(){var t,n;if(this.cbs.onclosetag){this.endIndex=this.startIndex;for(var r=this.stack.length;r>0;this.cbs.onclosetag(this.stack[--r],!0));}(n=(t=this.cbs).onend)===null||n===void 0||n.call(t)},e.prototype.reset=function(){var t,n,r,i;(n=(t=this.cbs).onreset)===null||n===void 0||n.call(t),this.tokenizer.reset(),this.tagname="",this.attribname="",this.attribs=null,this.stack=[],this.startIndex=0,this.endIndex=0,(i=(r=this.cbs).onparserinit)===null||i===void 0||i.call(r,this)},e.prototype.parseComplete=function(t){this.reset(),this.end(t)},e.prototype.write=function(t){this.tokenizer.write(t)},e.prototype.end=function(t){this.tokenizer.end(t)},e.prototype.pause=function(){this.tokenizer.pause()},e.prototype.resume=function(){this.tokenizer.resume()},e.prototype.parseChunk=function(t){this.write(t)},e.prototype.done=function(t){this.end(t)},e}();so.Parser=vw;var wn={},B0={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.Doctype=e.CDATA=e.Tag=e.Style=e.Script=e.Comment=e.Directive=e.Text=e.Root=e.isTag=e.ElementType=void 0;var t;(function(r){r.Root="root",r.Text="text",r.Directive="directive",r.Comment="comment",r.Script="script",r.Style="style",r.Tag="tag",r.CDATA="cdata",r.Doctype="doctype"})(t=e.ElementType||(e.ElementType={}));function n(r){return r.type===t.Tag||r.type===t.Script||r.type===t.Style}e.isTag=n,e.Root=t.Root,e.Text=t.Text,e.Directive=t.Directive,e.Comment=t.Comment,e.Script=t.Script,e.Style=t.Style,e.Tag=t.Tag,e.CDATA=t.CDATA,e.Doctype=t.Doctype})(B0);var $={},Vr=V&&V.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(r[o]=i[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),wi=V&&V.__assign||function(){return wi=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++){t=arguments[n];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e},wi.apply(this,arguments)};Object.defineProperty($,"__esModule",{value:!0});$.cloneNode=$.hasChildren=$.isDocument=$.isDirective=$.isComment=$.isText=$.isCDATA=$.isTag=$.Element=$.Document=$.NodeWithChildren=$.ProcessingInstruction=$.Comment=$.Text=$.DataNode=$.Node=void 0;var ft=B0,yw=new Map([[ft.ElementType.Tag,1],[ft.ElementType.Script,1],[ft.ElementType.Style,1],[ft.ElementType.Directive,1],[ft.ElementType.Text,3],[ft.ElementType.CDATA,4],[ft.ElementType.Comment,8],[ft.ElementType.Root,9]]),z0=function(){function e(t){this.type=t,this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}return Object.defineProperty(e.prototype,"nodeType",{get:function(){var t;return(t=yw.get(this.type))!==null&&t!==void 0?t:1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"parentNode",{get:function(){return this.parent},set:function(t){this.parent=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"previousSibling",{get:function(){return this.prev},set:function(t){this.prev=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"nextSibling",{get:function(){return this.next},set:function(t){this.next=t},enumerable:!1,configurable:!0}),e.prototype.cloneNode=function(t){return t===void 0&&(t=!1),j0(this,t)},e}();$.Node=z0;var _a=function(e){Vr(t,e);function t(n,r){var i=e.call(this,n)||this;return i.data=r,i}return Object.defineProperty(t.prototype,"nodeValue",{get:function(){return this.data},set:function(n){this.data=n},enumerable:!1,configurable:!0}),t}(z0);$.DataNode=_a;var Bh=function(e){Vr(t,e);function t(n){return e.call(this,ft.ElementType.Text,n)||this}return t}(_a);$.Text=Bh;var zh=function(e){Vr(t,e);function t(n){return e.call(this,ft.ElementType.Comment,n)||this}return t}(_a);$.Comment=zh;var jh=function(e){Vr(t,e);function t(n,r){var i=e.call(this,ft.ElementType.Directive,r)||this;return i.name=n,i}return t}(_a);$.ProcessingInstruction=jh;var wa=function(e){Vr(t,e);function t(n,r){var i=e.call(this,n)||this;return i.children=r,i}return Object.defineProperty(t.prototype,"firstChild",{get:function(){var n;return(n=this.children[0])!==null&&n!==void 0?n:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lastChild",{get:function(){return this.children.length>0?this.children[this.children.length-1]:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"childNodes",{get:function(){return this.children},set:function(n){this.children=n},enumerable:!1,configurable:!0}),t}(z0);$.NodeWithChildren=wa;var Uh=function(e){Vr(t,e);function t(n){return e.call(this,ft.ElementType.Root,n)||this}return t}(wa);$.Document=Uh;var $h=function(e){Vr(t,e);function t(n,r,i,o){i===void 0&&(i=[]),o===void 0&&(o=n==="script"?ft.ElementType.Script:n==="style"?ft.ElementType.Style:ft.ElementType.Tag);var s=e.call(this,o,i)||this;return s.name=n,s.attribs=r,s}return Object.defineProperty(t.prototype,"tagName",{get:function(){return this.name},set:function(n){this.name=n},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"attributes",{get:function(){var n=this;return Object.keys(this.attribs).map(function(r){var i,o;return{name:r,value:n.attribs[r],namespace:(i=n["x-attribsNamespace"])===null||i===void 0?void 0:i[r],prefix:(o=n["x-attribsPrefix"])===null||o===void 0?void 0:o[r]}})},enumerable:!1,configurable:!0}),t}(wa);$.Element=$h;function Hh(e){return(0,ft.isTag)(e)}$.isTag=Hh;function Gh(e){return e.type===ft.ElementType.CDATA}$.isCDATA=Gh;function Wh(e){return e.type===ft.ElementType.Text}$.isText=Wh;function Yh(e){return e.type===ft.ElementType.Comment}$.isComment=Yh;function Xh(e){return e.type===ft.ElementType.Directive}$.isDirective=Xh;function Qh(e){return e.type===ft.ElementType.Root}$.isDocument=Qh;function _w(e){return Object.prototype.hasOwnProperty.call(e,"children")}$.hasChildren=_w;function j0(e,t){t===void 0&&(t=!1);var n;if(Wh(e))n=new Bh(e.data);else if(Yh(e))n=new zh(e.data);else if(Hh(e)){var r=t?wl(e.children):[],i=new $h(e.name,wi({},e.attribs),r);r.forEach(function(l){return l.parent=i}),e.namespace!=null&&(i.namespace=e.namespace),e["x-attribsNamespace"]&&(i["x-attribsNamespace"]=wi({},e["x-attribsNamespace"])),e["x-attribsPrefix"]&&(i["x-attribsPrefix"]=wi({},e["x-attribsPrefix"])),n=i}else if(Gh(e)){var r=t?wl(e.children):[],o=new wa(ft.ElementType.CDATA,r);r.forEach(function(u){return u.parent=o}),n=o}else if(Qh(e)){var r=t?wl(e.children):[],s=new Uh(r);r.forEach(function(u){return u.parent=s}),e["x-mode"]&&(s["x-mode"]=e["x-mode"]),n=s}else if(Xh(e)){var a=new jh(e.name,e.data);e["x-name"]!=null&&(a["x-name"]=e["x-name"],a["x-publicId"]=e["x-publicId"],a["x-systemId"]=e["x-systemId"]),n=a}else throw new Error("Not implemented yet: ".concat(e.type));return n.startIndex=e.startIndex,n.endIndex=e.endIndex,e.sourceCodeLocation!=null&&(n.sourceCodeLocation=e.sourceCodeLocation),n}$.cloneNode=j0;function wl(e){for(var t=e.map(function(r){return j0(r,!0)}),n=1;n<t.length;n++)t[n].prev=t[n-1],t[n-1].next=t[n];return t}(function(e){var t=V&&V.__createBinding||(Object.create?function(l,u,c,f){f===void 0&&(f=c);var d=Object.getOwnPropertyDescriptor(u,c);(!d||("get"in d?!u.__esModule:d.writable||d.configurable))&&(d={enumerable:!0,get:function(){return u[c]}}),Object.defineProperty(l,f,d)}:function(l,u,c,f){f===void 0&&(f=c),l[f]=u[c]}),n=V&&V.__exportStar||function(l,u){for(var c in l)c!=="default"&&!Object.prototype.hasOwnProperty.call(u,c)&&t(u,l,c)};Object.defineProperty(e,"__esModule",{value:!0}),e.DomHandler=void 0;var r=B0,i=$;n($,e);var o=/\s+/g,s={normalizeWhitespace:!1,withStartIndices:!1,withEndIndices:!1,xmlMode:!1},a=function(){function l(u,c,f){this.dom=[],this.root=new i.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,typeof c=="function"&&(f=c,c=s),typeof u=="object"&&(c=u,u=void 0),this.callback=u!=null?u:null,this.options=c!=null?c:s,this.elementCB=f!=null?f:null}return l.prototype.onparserinit=function(u){this.parser=u},l.prototype.onreset=function(){this.dom=[],this.root=new i.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null},l.prototype.onend=function(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))},l.prototype.onerror=function(u){this.handleCallback(u)},l.prototype.onclosetag=function(){this.lastNode=null;var u=this.tagStack.pop();this.options.withEndIndices&&(u.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(u)},l.prototype.onopentag=function(u,c){var f=this.options.xmlMode?r.ElementType.Tag:void 0,d=new i.Element(u,c,void 0,f);this.addNode(d),this.tagStack.push(d)},l.prototype.ontext=function(u){var c=this.options.normalizeWhitespace,f=this.lastNode;if(f&&f.type===r.ElementType.Text)c?f.data=(f.data+u).replace(o," "):f.data+=u,this.options.withEndIndices&&(f.endIndex=this.parser.endIndex);else{c&&(u=u.replace(o," "));var d=new i.Text(u);this.addNode(d),this.lastNode=d}},l.prototype.oncomment=function(u){if(this.lastNode&&this.lastNode.type===r.ElementType.Comment){this.lastNode.data+=u;return}var c=new i.Comment(u);this.addNode(c),this.lastNode=c},l.prototype.oncommentend=function(){this.lastNode=null},l.prototype.oncdatastart=function(){var u=new i.Text(""),c=new i.NodeWithChildren(r.ElementType.CDATA,[u]);this.addNode(c),u.parent=c,this.lastNode=u},l.prototype.oncdataend=function(){this.lastNode=null},l.prototype.onprocessinginstruction=function(u,c){var f=new i.ProcessingInstruction(u,c);this.addNode(f)},l.prototype.handleCallback=function(u){if(typeof this.callback=="function")this.callback(u,this.dom);else if(u)throw u},l.prototype.addNode=function(u){var c=this.tagStack[this.tagStack.length-1],f=c.children[c.children.length-1];this.options.withStartIndices&&(u.startIndex=this.parser.startIndex),this.options.withEndIndices&&(u.endIndex=this.parser.endIndex),c.children.push(u),f&&(u.prev=f,f.next=u),u.parent=c,this.lastNode=null},l}();e.DomHandler=a,e.default=a})(wn);var Kh={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.Doctype=e.CDATA=e.Tag=e.Style=e.Script=e.Comment=e.Directive=e.Text=e.Root=e.isTag=e.ElementType=void 0;var t;(function(r){r.Root="root",r.Text="text",r.Directive="directive",r.Comment="comment",r.Script="script",r.Style="style",r.Tag="tag",r.CDATA="cdata",r.Doctype="doctype"})(t=e.ElementType||(e.ElementType={}));function n(r){return r.type===t.Tag||r.type===t.Script||r.type===t.Style}e.isTag=n,e.Root=t.Root,e.Text=t.Text,e.Directive=t.Directive,e.Comment=t.Comment,e.Script=t.Script,e.Style=t.Style,e.Tag=t.Tag,e.CDATA=t.CDATA,e.Doctype=t.Doctype})(Kh);var Wu={},U0={},le={},$0={},H0={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.Doctype=e.CDATA=e.Tag=e.Style=e.Script=e.Comment=e.Directive=e.Text=e.Root=e.isTag=e.ElementType=void 0;var t;(function(r){r.Root="root",r.Text="text",r.Directive="directive",r.Comment="comment",r.Script="script",r.Style="style",r.Tag="tag",r.CDATA="cdata",r.Doctype="doctype"})(t=e.ElementType||(e.ElementType={}));function n(r){return r.type===t.Tag||r.type===t.Script||r.type===t.Style}e.isTag=n,e.Root=t.Root,e.Text=t.Text,e.Directive=t.Directive,e.Comment=t.Comment,e.Script=t.Script,e.Style=t.Style,e.Tag=t.Tag,e.CDATA=t.CDATA,e.Doctype=t.Doctype})(H0);var Zh={},dn={};const ww="Á",xw="á",Sw="Ă",Tw="ă",bw="∾",Cw="∿",Ew="∾̳",kw="Â",Pw="â",Dw="´",Aw="А",Lw="а",Ow="Æ",Mw="æ",Nw="⁡",Rw="𝔄",Iw="𝔞",Vw="À",qw="à",Fw="ℵ",Bw="ℵ",zw="Α",jw="α",Uw="Ā",$w="ā",Hw="⨿",Gw="&",Ww="&",Yw="⩕",Xw="⩓",Qw="∧",Kw="⩜",Zw="⩘",Jw="⩚",tx="∠",ex="⦤",nx="∠",rx="⦨",ix="⦩",ox="⦪",sx="⦫",ax="⦬",lx="⦭",ux="⦮",cx="⦯",fx="∡",dx="∟",hx="⊾",px="⦝",mx="∢",gx="Å",vx="⍼",yx="Ą",_x="ą",wx="𝔸",xx="𝕒",Sx="⩯",Tx="≈",bx="⩰",Cx="≊",Ex="≋",kx="'",Px="⁡",Dx="≈",Ax="≊",Lx="Å",Ox="å",Mx="𝒜",Nx="𝒶",Rx="≔",Ix="*",Vx="≈",qx="≍",Fx="Ã",Bx="ã",zx="Ä",jx="ä",Ux="∳",$x="⨑",Hx="≌",Gx="϶",Wx="‵",Yx="∽",Xx="⋍",Qx="∖",Kx="⫧",Zx="⊽",Jx="⌅",tS="⌆",eS="⌅",nS="⎵",rS="⎶",iS="≌",oS="Б",sS="б",aS="„",lS="∵",uS="∵",cS="∵",fS="⦰",dS="϶",hS="ℬ",pS="ℬ",mS="Β",gS="β",vS="ℶ",yS="≬",_S="𝔅",wS="𝔟",xS="⋂",SS="◯",TS="⋃",bS="⨀",CS="⨁",ES="⨂",kS="⨆",PS="★",DS="▽",AS="△",LS="⨄",OS="⋁",MS="⋀",NS="⤍",RS="⧫",IS="▪",VS="▴",qS="▾",FS="◂",BS="▸",zS="␣",jS="▒",US="░",$S="▓",HS="█",GS="=⃥",WS="≡⃥",YS="⫭",XS="⌐",QS="𝔹",KS="𝕓",ZS="⊥",JS="⊥",tT="⋈",eT="⧉",nT="┐",rT="╕",iT="╖",oT="╗",sT="┌",aT="╒",lT="╓",uT="╔",cT="─",fT="═",dT="┬",hT="╤",pT="╥",mT="╦",gT="┴",vT="╧",yT="╨",_T="╩",wT="⊟",xT="⊞",ST="⊠",TT="┘",bT="╛",CT="╜",ET="╝",kT="└",PT="╘",DT="╙",AT="╚",LT="│",OT="║",MT="┼",NT="╪",RT="╫",IT="╬",VT="┤",qT="╡",FT="╢",BT="╣",zT="├",jT="╞",UT="╟",$T="╠",HT="‵",GT="˘",WT="˘",YT="¦",XT="𝒷",QT="ℬ",KT="⁏",ZT="∽",JT="⋍",tb="⧅",eb="\\",nb="⟈",rb="•",ib="•",ob="≎",sb="⪮",ab="≏",lb="≎",ub="≏",cb="Ć",fb="ć",db="⩄",hb="⩉",pb="⩋",mb="∩",gb="⋒",vb="⩇",yb="⩀",_b="ⅅ",wb="∩︀",xb="⁁",Sb="ˇ",Tb="ℭ",bb="⩍",Cb="Č",Eb="č",kb="Ç",Pb="ç",Db="Ĉ",Ab="ĉ",Lb="∰",Ob="⩌",Mb="⩐",Nb="Ċ",Rb="ċ",Ib="¸",Vb="¸",qb="⦲",Fb="¢",Bb="·",zb="·",jb="𝔠",Ub="ℭ",$b="Ч",Hb="ч",Gb="✓",Wb="✓",Yb="Χ",Xb="χ",Qb="ˆ",Kb="≗",Zb="↺",Jb="↻",tC="⊛",eC="⊚",nC="⊝",rC="⊙",iC="®",oC="Ⓢ",sC="⊖",aC="⊕",lC="⊗",uC="○",cC="⧃",fC="≗",dC="⨐",hC="⫯",pC="⧂",mC="∲",gC="”",vC="’",yC="♣",_C="♣",wC=":",xC="∷",SC="⩴",TC="≔",bC="≔",CC=",",EC="@",kC="∁",PC="∘",DC="∁",AC="ℂ",LC="≅",OC="⩭",MC="≡",NC="∮",RC="∯",IC="∮",VC="𝕔",qC="ℂ",FC="∐",BC="∐",zC="©",jC="©",UC="℗",$C="∳",HC="↵",GC="✗",WC="⨯",YC="𝒞",XC="𝒸",QC="⫏",KC="⫑",ZC="⫐",JC="⫒",tE="⋯",eE="⤸",nE="⤵",rE="⋞",iE="⋟",oE="↶",sE="⤽",aE="⩈",lE="⩆",uE="≍",cE="∪",fE="⋓",dE="⩊",hE="⊍",pE="⩅",mE="∪︀",gE="↷",vE="⤼",yE="⋞",_E="⋟",wE="⋎",xE="⋏",SE="¤",TE="↶",bE="↷",CE="⋎",EE="⋏",kE="∲",PE="∱",DE="⌭",AE="†",LE="‡",OE="ℸ",ME="↓",NE="↡",RE="⇓",IE="‐",VE="⫤",qE="⊣",FE="⤏",BE="˝",zE="Ď",jE="ď",UE="Д",$E="д",HE="‡",GE="⇊",WE="ⅅ",YE="ⅆ",XE="⤑",QE="⩷",KE="°",ZE="∇",JE="Δ",tk="δ",ek="⦱",nk="⥿",rk="𝔇",ik="𝔡",ok="⥥",sk="⇃",ak="⇂",lk="´",uk="˙",ck="˝",fk="`",dk="˜",hk="⋄",pk="⋄",mk="⋄",gk="♦",vk="♦",yk="¨",_k="ⅆ",wk="ϝ",xk="⋲",Sk="÷",Tk="÷",bk="⋇",Ck="⋇",Ek="Ђ",kk="ђ",Pk="⌞",Dk="⌍",Ak="$",Lk="𝔻",Ok="𝕕",Mk="¨",Nk="˙",Rk="⃜",Ik="≐",Vk="≑",qk="≐",Fk="∸",Bk="∔",zk="⊡",jk="⌆",Uk="∯",$k="¨",Hk="⇓",Gk="⇐",Wk="⇔",Yk="⫤",Xk="⟸",Qk="⟺",Kk="⟹",Zk="⇒",Jk="⊨",tP="⇑",eP="⇕",nP="∥",rP="⤓",iP="↓",oP="↓",sP="⇓",aP="⇵",lP="̑",uP="⇊",cP="⇃",fP="⇂",dP="⥐",hP="⥞",pP="⥖",mP="↽",gP="⥟",vP="⥗",yP="⇁",_P="↧",wP="⊤",xP="⤐",SP="⌟",TP="⌌",bP="𝒟",CP="𝒹",EP="Ѕ",kP="ѕ",PP="⧶",DP="Đ",AP="đ",LP="⋱",OP="▿",MP="▾",NP="⇵",RP="⥯",IP="⦦",VP="Џ",qP="џ",FP="⟿",BP="É",zP="é",jP="⩮",UP="Ě",$P="ě",HP="Ê",GP="ê",WP="≖",YP="≕",XP="Э",QP="э",KP="⩷",ZP="Ė",JP="ė",tD="≑",eD="ⅇ",nD="≒",rD="𝔈",iD="𝔢",oD="⪚",sD="È",aD="è",lD="⪖",uD="⪘",cD="⪙",fD="∈",dD="⏧",hD="ℓ",pD="⪕",mD="⪗",gD="Ē",vD="ē",yD="∅",_D="∅",wD="◻",xD="∅",SD="▫",TD=" ",bD=" ",CD=" ",ED="Ŋ",kD="ŋ",PD=" ",DD="Ę",AD="ę",LD="𝔼",OD="𝕖",MD="⋕",ND="⧣",RD="⩱",ID="ε",VD="Ε",qD="ε",FD="ϵ",BD="≖",zD="≕",jD="≂",UD="⪖",$D="⪕",HD="⩵",GD="=",WD="≂",YD="≟",XD="⇌",QD="≡",KD="⩸",ZD="⧥",JD="⥱",tA="≓",eA="ℯ",nA="ℰ",rA="≐",iA="⩳",oA="≂",sA="Η",aA="η",lA="Ð",uA="ð",cA="Ë",fA="ë",dA="€",hA="!",pA="∃",mA="∃",gA="ℰ",vA="ⅇ",yA="ⅇ",_A="≒",wA="Ф",xA="ф",SA="♀",TA="ﬃ",bA="ﬀ",CA="ﬄ",EA="𝔉",kA="𝔣",PA="ﬁ",DA="◼",AA="▪",LA="fj",OA="♭",MA="ﬂ",NA="▱",RA="ƒ",IA="𝔽",VA="𝕗",qA="∀",FA="∀",BA="⋔",zA="⫙",jA="ℱ",UA="⨍",$A="½",HA="⅓",GA="¼",WA="⅕",YA="⅙",XA="⅛",QA="⅔",KA="⅖",ZA="¾",JA="⅗",tL="⅜",eL="⅘",nL="⅚",rL="⅝",iL="⅞",oL="⁄",sL="⌢",aL="𝒻",lL="ℱ",uL="ǵ",cL="Γ",fL="γ",dL="Ϝ",hL="ϝ",pL="⪆",mL="Ğ",gL="ğ",vL="Ģ",yL="Ĝ",_L="ĝ",wL="Г",xL="г",SL="Ġ",TL="ġ",bL="≥",CL="≧",EL="⪌",kL="⋛",PL="≥",DL="≧",AL="⩾",LL="⪩",OL="⩾",ML="⪀",NL="⪂",RL="⪄",IL="⋛︀",VL="⪔",qL="𝔊",FL="𝔤",BL="≫",zL="⋙",jL="⋙",UL="ℷ",$L="Ѓ",HL="ѓ",GL="⪥",WL="≷",YL="⪒",XL="⪤",QL="⪊",KL="⪊",ZL="⪈",JL="≩",tO="⪈",eO="≩",nO="⋧",rO="𝔾",iO="𝕘",oO="`",sO="≥",aO="⋛",lO="≧",uO="⪢",cO="≷",fO="⩾",dO="≳",hO="𝒢",pO="ℊ",mO="≳",gO="⪎",vO="⪐",yO="⪧",_O="⩺",wO=">",xO=">",SO="≫",TO="⋗",bO="⦕",CO="⩼",EO="⪆",kO="⥸",PO="⋗",DO="⋛",AO="⪌",LO="≷",OO="≳",MO="≩︀",NO="≩︀",RO="ˇ",IO=" ",VO="½",qO="ℋ",FO="Ъ",BO="ъ",zO="⥈",jO="↔",UO="⇔",$O="↭",HO="^",GO="ℏ",WO="Ĥ",YO="ĥ",XO="♥",QO="♥",KO="…",ZO="⊹",JO="𝔥",tM="ℌ",eM="ℋ",nM="⤥",rM="⤦",iM="⇿",oM="∻",sM="↩",aM="↪",lM="𝕙",uM="ℍ",cM="―",fM="─",dM="𝒽",hM="ℋ",pM="ℏ",mM="Ħ",gM="ħ",vM="≎",yM="≏",_M="⁃",wM="‐",xM="Í",SM="í",TM="⁣",bM="Î",CM="î",EM="И",kM="и",PM="İ",DM="Е",AM="е",LM="¡",OM="⇔",MM="𝔦",NM="ℑ",RM="Ì",IM="ì",VM="ⅈ",qM="⨌",FM="∭",BM="⧜",zM="℩",jM="Ĳ",UM="ĳ",$M="Ī",HM="ī",GM="ℑ",WM="ⅈ",YM="ℐ",XM="ℑ",QM="ı",KM="ℑ",ZM="⊷",JM="Ƶ",tN="⇒",eN="℅",nN="∞",rN="⧝",iN="ı",oN="⊺",sN="∫",aN="∬",lN="ℤ",uN="∫",cN="⊺",fN="⋂",dN="⨗",hN="⨼",pN="⁣",mN="⁢",gN="Ё",vN="ё",yN="Į",_N="į",wN="𝕀",xN="𝕚",SN="Ι",TN="ι",bN="⨼",CN="¿",EN="𝒾",kN="ℐ",PN="∈",DN="⋵",AN="⋹",LN="⋴",ON="⋳",MN="∈",NN="⁢",RN="Ĩ",IN="ĩ",VN="І",qN="і",FN="Ï",BN="ï",zN="Ĵ",jN="ĵ",UN="Й",$N="й",HN="𝔍",GN="𝔧",WN="ȷ",YN="𝕁",XN="𝕛",QN="𝒥",KN="𝒿",ZN="Ј",JN="ј",tR="Є",eR="є",nR="Κ",rR="κ",iR="ϰ",oR="Ķ",sR="ķ",aR="К",lR="к",uR="𝔎",cR="𝔨",fR="ĸ",dR="Х",hR="х",pR="Ќ",mR="ќ",gR="𝕂",vR="𝕜",yR="𝒦",_R="𝓀",wR="⇚",xR="Ĺ",SR="ĺ",TR="⦴",bR="ℒ",CR="Λ",ER="λ",kR="⟨",PR="⟪",DR="⦑",AR="⟨",LR="⪅",OR="ℒ",MR="«",NR="⇤",RR="⤟",IR="←",VR="↞",qR="⇐",FR="⤝",BR="↩",zR="↫",jR="⤹",UR="⥳",$R="↢",HR="⤙",GR="⤛",WR="⪫",YR="⪭",XR="⪭︀",QR="⤌",KR="⤎",ZR="❲",JR="{",tI="[",eI="⦋",nI="⦏",rI="⦍",iI="Ľ",oI="ľ",sI="Ļ",aI="ļ",lI="⌈",uI="{",cI="Л",fI="л",dI="⤶",hI="“",pI="„",mI="⥧",gI="⥋",vI="↲",yI="≤",_I="≦",wI="⟨",xI="⇤",SI="←",TI="←",bI="⇐",CI="⇆",EI="↢",kI="⌈",PI="⟦",DI="⥡",AI="⥙",LI="⇃",OI="⌊",MI="↽",NI="↼",RI="⇇",II="↔",VI="↔",qI="⇔",FI="⇆",BI="⇋",zI="↭",jI="⥎",UI="↤",$I="⊣",HI="⥚",GI="⋋",WI="⧏",YI="⊲",XI="⊴",QI="⥑",KI="⥠",ZI="⥘",JI="↿",tV="⥒",eV="↼",nV="⪋",rV="⋚",iV="≤",oV="≦",sV="⩽",aV="⪨",lV="⩽",uV="⩿",cV="⪁",fV="⪃",dV="⋚︀",hV="⪓",pV="⪅",mV="⋖",gV="⋚",vV="⪋",yV="⋚",_V="≦",wV="≶",xV="≶",SV="⪡",TV="≲",bV="⩽",CV="≲",EV="⥼",kV="⌊",PV="𝔏",DV="𝔩",AV="≶",LV="⪑",OV="⥢",MV="↽",NV="↼",RV="⥪",IV="▄",VV="Љ",qV="љ",FV="⇇",BV="≪",zV="⋘",jV="⌞",UV="⇚",$V="⥫",HV="◺",GV="Ŀ",WV="ŀ",YV="⎰",XV="⎰",QV="⪉",KV="⪉",ZV="⪇",JV="≨",tq="⪇",eq="≨",nq="⋦",rq="⟬",iq="⇽",oq="⟦",sq="⟵",aq="⟵",lq="⟸",uq="⟷",cq="⟷",fq="⟺",dq="⟼",hq="⟶",pq="⟶",mq="⟹",gq="↫",vq="↬",yq="⦅",_q="𝕃",wq="𝕝",xq="⨭",Sq="⨴",Tq="∗",bq="_",Cq="↙",Eq="↘",kq="◊",Pq="◊",Dq="⧫",Aq="(",Lq="⦓",Oq="⇆",Mq="⌟",Nq="⇋",Rq="⥭",Iq="‎",Vq="⊿",qq="‹",Fq="𝓁",Bq="ℒ",zq="↰",jq="↰",Uq="≲",$q="⪍",Hq="⪏",Gq="[",Wq="‘",Yq="‚",Xq="Ł",Qq="ł",Kq="⪦",Zq="⩹",Jq="<",tF="<",eF="≪",nF="⋖",rF="⋋",iF="⋉",oF="⥶",sF="⩻",aF="◃",lF="⊴",uF="◂",cF="⦖",fF="⥊",dF="⥦",hF="≨︀",pF="≨︀",mF="¯",gF="♂",vF="✠",yF="✠",_F="↦",wF="↦",xF="↧",SF="↤",TF="↥",bF="▮",CF="⨩",EF="М",kF="м",PF="—",DF="∺",AF="∡",LF=" ",OF="ℳ",MF="𝔐",NF="𝔪",RF="℧",IF="µ",VF="*",qF="⫰",FF="∣",BF="·",zF="⊟",jF="−",UF="∸",$F="⨪",HF="∓",GF="⫛",WF="…",YF="∓",XF="⊧",QF="𝕄",KF="𝕞",ZF="∓",JF="𝓂",tB="ℳ",eB="∾",nB="Μ",rB="μ",iB="⊸",oB="⊸",sB="∇",aB="Ń",lB="ń",uB="∠⃒",cB="≉",fB="⩰̸",dB="≋̸",hB="ŉ",pB="≉",mB="♮",gB="ℕ",vB="♮",yB=" ",_B="≎̸",wB="≏̸",xB="⩃",SB="Ň",TB="ň",bB="Ņ",CB="ņ",EB="≇",kB="⩭̸",PB="⩂",DB="Н",AB="н",LB="–",OB="⤤",MB="↗",NB="⇗",RB="↗",IB="≠",VB="≐̸",qB="​",FB="​",BB="​",zB="​",jB="≢",UB="⤨",$B="≂̸",HB="≫",GB="≪",WB=`
`,YB="∄",XB="∄",QB="𝔑",KB="𝔫",ZB="≧̸",JB="≱",tz="≱",ez="≧̸",nz="⩾̸",rz="⩾̸",iz="⋙̸",oz="≵",sz="≫⃒",az="≯",lz="≯",uz="≫̸",cz="↮",fz="⇎",dz="⫲",hz="∋",pz="⋼",mz="⋺",gz="∋",vz="Њ",yz="њ",_z="↚",wz="⇍",xz="‥",Sz="≦̸",Tz="≰",bz="↚",Cz="⇍",Ez="↮",kz="⇎",Pz="≰",Dz="≦̸",Az="⩽̸",Lz="⩽̸",Oz="≮",Mz="⋘̸",Nz="≴",Rz="≪⃒",Iz="≮",Vz="⋪",qz="⋬",Fz="≪̸",Bz="∤",zz="⁠",jz=" ",Uz="𝕟",$z="ℕ",Hz="⫬",Gz="¬",Wz="≢",Yz="≭",Xz="∦",Qz="∉",Kz="≠",Zz="≂̸",Jz="∄",tj="≯",ej="≱",nj="≧̸",rj="≫̸",ij="≹",oj="⩾̸",sj="≵",aj="≎̸",lj="≏̸",uj="∉",cj="⋵̸",fj="⋹̸",dj="∉",hj="⋷",pj="⋶",mj="⧏̸",gj="⋪",vj="⋬",yj="≮",_j="≰",wj="≸",xj="≪̸",Sj="⩽̸",Tj="≴",bj="⪢̸",Cj="⪡̸",Ej="∌",kj="∌",Pj="⋾",Dj="⋽",Aj="⊀",Lj="⪯̸",Oj="⋠",Mj="∌",Nj="⧐̸",Rj="⋫",Ij="⋭",Vj="⊏̸",qj="⋢",Fj="⊐̸",Bj="⋣",zj="⊂⃒",jj="⊈",Uj="⊁",$j="⪰̸",Hj="⋡",Gj="≿̸",Wj="⊃⃒",Yj="⊉",Xj="≁",Qj="≄",Kj="≇",Zj="≉",Jj="∤",tU="∦",eU="∦",nU="⫽⃥",rU="∂̸",iU="⨔",oU="⊀",sU="⋠",aU="⊀",lU="⪯̸",uU="⪯̸",cU="⤳̸",fU="↛",dU="⇏",hU="↝̸",pU="↛",mU="⇏",gU="⋫",vU="⋭",yU="⊁",_U="⋡",wU="⪰̸",xU="𝒩",SU="𝓃",TU="∤",bU="∦",CU="≁",EU="≄",kU="≄",PU="∤",DU="∦",AU="⋢",LU="⋣",OU="⊄",MU="⫅̸",NU="⊈",RU="⊂⃒",IU="⊈",VU="⫅̸",qU="⊁",FU="⪰̸",BU="⊅",zU="⫆̸",jU="⊉",UU="⊃⃒",$U="⊉",HU="⫆̸",GU="≹",WU="Ñ",YU="ñ",XU="≸",QU="⋪",KU="⋬",ZU="⋫",JU="⋭",t$="Ν",e$="ν",n$="#",r$="№",i$=" ",o$="≍⃒",s$="⊬",a$="⊭",l$="⊮",u$="⊯",c$="≥⃒",f$=">⃒",d$="⤄",h$="⧞",p$="⤂",m$="≤⃒",g$="<⃒",v$="⊴⃒",y$="⤃",_$="⊵⃒",w$="∼⃒",x$="⤣",S$="↖",T$="⇖",b$="↖",C$="⤧",E$="Ó",k$="ó",P$="⊛",D$="Ô",A$="ô",L$="⊚",O$="О",M$="о",N$="⊝",R$="Ő",I$="ő",V$="⨸",q$="⊙",F$="⦼",B$="Œ",z$="œ",j$="⦿",U$="𝔒",$$="𝔬",H$="˛",G$="Ò",W$="ò",Y$="⧁",X$="⦵",Q$="Ω",K$="∮",Z$="↺",J$="⦾",tH="⦻",eH="‾",nH="⧀",rH="Ō",iH="ō",oH="Ω",sH="ω",aH="Ο",lH="ο",uH="⦶",cH="⊖",fH="𝕆",dH="𝕠",hH="⦷",pH="“",mH="‘",gH="⦹",vH="⊕",yH="↻",_H="⩔",wH="∨",xH="⩝",SH="ℴ",TH="ℴ",bH="ª",CH="º",EH="⊶",kH="⩖",PH="⩗",DH="⩛",AH="Ⓢ",LH="𝒪",OH="ℴ",MH="Ø",NH="ø",RH="⊘",IH="Õ",VH="õ",qH="⨶",FH="⨷",BH="⊗",zH="Ö",jH="ö",UH="⌽",$H="‾",HH="⏞",GH="⎴",WH="⏜",YH="¶",XH="∥",QH="∥",KH="⫳",ZH="⫽",JH="∂",tG="∂",eG="П",nG="п",rG="%",iG=".",oG="‰",sG="⊥",aG="‱",lG="𝔓",uG="𝔭",cG="Φ",fG="φ",dG="ϕ",hG="ℳ",pG="☎",mG="Π",gG="π",vG="⋔",yG="ϖ",_G="ℏ",wG="ℎ",xG="ℏ",SG="⨣",TG="⊞",bG="⨢",CG="+",EG="∔",kG="⨥",PG="⩲",DG="±",AG="±",LG="⨦",OG="⨧",MG="±",NG="ℌ",RG="⨕",IG="𝕡",VG="ℙ",qG="£",FG="⪷",BG="⪻",zG="≺",jG="≼",UG="⪷",$G="≺",HG="≼",GG="≺",WG="⪯",YG="≼",XG="≾",QG="⪯",KG="⪹",ZG="⪵",JG="⋨",tW="⪯",eW="⪳",nW="≾",rW="′",iW="″",oW="ℙ",sW="⪹",aW="⪵",lW="⋨",uW="∏",cW="∏",fW="⌮",dW="⌒",hW="⌓",pW="∝",mW="∝",gW="∷",vW="∝",yW="≾",_W="⊰",wW="𝒫",xW="𝓅",SW="Ψ",TW="ψ",bW=" ",CW="𝔔",EW="𝔮",kW="⨌",PW="𝕢",DW="ℚ",AW="⁗",LW="𝒬",OW="𝓆",MW="ℍ",NW="⨖",RW="?",IW="≟",VW='"',qW='"',FW="⇛",BW="∽̱",zW="Ŕ",jW="ŕ",UW="√",$W="⦳",HW="⟩",GW="⟫",WW="⦒",YW="⦥",XW="⟩",QW="»",KW="⥵",ZW="⇥",JW="⤠",tY="⤳",eY="→",nY="↠",rY="⇒",iY="⤞",oY="↪",sY="↬",aY="⥅",lY="⥴",uY="⤖",cY="↣",fY="↝",dY="⤚",hY="⤜",pY="∶",mY="ℚ",gY="⤍",vY="⤏",yY="⤐",_Y="❳",wY="}",xY="]",SY="⦌",TY="⦎",bY="⦐",CY="Ř",EY="ř",kY="Ŗ",PY="ŗ",DY="⌉",AY="}",LY="Р",OY="р",MY="⤷",NY="⥩",RY="”",IY="”",VY="↳",qY="ℜ",FY="ℛ",BY="ℜ",zY="ℝ",jY="ℜ",UY="▭",$Y="®",HY="®",GY="∋",WY="⇋",YY="⥯",XY="⥽",QY="⌋",KY="𝔯",ZY="ℜ",JY="⥤",tX="⇁",eX="⇀",nX="⥬",rX="Ρ",iX="ρ",oX="ϱ",sX="⟩",aX="⇥",lX="→",uX="→",cX="⇒",fX="⇄",dX="↣",hX="⌉",pX="⟧",mX="⥝",gX="⥕",vX="⇂",yX="⌋",_X="⇁",wX="⇀",xX="⇄",SX="⇌",TX="⇉",bX="↝",CX="↦",EX="⊢",kX="⥛",PX="⋌",DX="⧐",AX="⊳",LX="⊵",OX="⥏",MX="⥜",NX="⥔",RX="↾",IX="⥓",VX="⇀",qX="˚",FX="≓",BX="⇄",zX="⇌",jX="‏",UX="⎱",$X="⎱",HX="⫮",GX="⟭",WX="⇾",YX="⟧",XX="⦆",QX="𝕣",KX="ℝ",ZX="⨮",JX="⨵",tQ="⥰",eQ=")",nQ="⦔",rQ="⨒",iQ="⇉",oQ="⇛",sQ="›",aQ="𝓇",lQ="ℛ",uQ="↱",cQ="↱",fQ="]",dQ="’",hQ="’",pQ="⋌",mQ="⋊",gQ="▹",vQ="⊵",yQ="▸",_Q="⧎",wQ="⧴",xQ="⥨",SQ="℞",TQ="Ś",bQ="ś",CQ="‚",EQ="⪸",kQ="Š",PQ="š",DQ="⪼",AQ="≻",LQ="≽",OQ="⪰",MQ="⪴",NQ="Ş",RQ="ş",IQ="Ŝ",VQ="ŝ",qQ="⪺",FQ="⪶",BQ="⋩",zQ="⨓",jQ="≿",UQ="С",$Q="с",HQ="⊡",GQ="⋅",WQ="⩦",YQ="⤥",XQ="↘",QQ="⇘",KQ="↘",ZQ="§",JQ=";",tK="⤩",eK="∖",nK="∖",rK="✶",iK="𝔖",oK="𝔰",sK="⌢",aK="♯",lK="Щ",uK="щ",cK="Ш",fK="ш",dK="↓",hK="←",pK="∣",mK="∥",gK="→",vK="↑",yK="­",_K="Σ",wK="σ",xK="ς",SK="ς",TK="∼",bK="⩪",CK="≃",EK="≃",kK="⪞",PK="⪠",DK="⪝",AK="⪟",LK="≆",OK="⨤",MK="⥲",NK="←",RK="∘",IK="∖",VK="⨳",qK="⧤",FK="∣",BK="⌣",zK="⪪",jK="⪬",UK="⪬︀",$K="Ь",HK="ь",GK="⌿",WK="⧄",YK="/",XK="𝕊",QK="𝕤",KK="♠",ZK="♠",JK="∥",tZ="⊓",eZ="⊓︀",nZ="⊔",rZ="⊔︀",iZ="√",oZ="⊏",sZ="⊑",aZ="⊏",lZ="⊑",uZ="⊐",cZ="⊒",fZ="⊐",dZ="⊒",hZ="□",pZ="□",mZ="⊓",gZ="⊏",vZ="⊑",yZ="⊐",_Z="⊒",wZ="⊔",xZ="▪",SZ="□",TZ="▪",bZ="→",CZ="𝒮",EZ="𝓈",kZ="∖",PZ="⌣",DZ="⋆",AZ="⋆",LZ="☆",OZ="★",MZ="ϵ",NZ="ϕ",RZ="¯",IZ="⊂",VZ="⋐",qZ="⪽",FZ="⫅",BZ="⊆",zZ="⫃",jZ="⫁",UZ="⫋",$Z="⊊",HZ="⪿",GZ="⥹",WZ="⊂",YZ="⋐",XZ="⊆",QZ="⫅",KZ="⊆",ZZ="⊊",JZ="⫋",tJ="⫇",eJ="⫕",nJ="⫓",rJ="⪸",iJ="≻",oJ="≽",sJ="≻",aJ="⪰",lJ="≽",uJ="≿",cJ="⪰",fJ="⪺",dJ="⪶",hJ="⋩",pJ="≿",mJ="∋",gJ="∑",vJ="∑",yJ="♪",_J="¹",wJ="²",xJ="³",SJ="⊃",TJ="⋑",bJ="⪾",CJ="⫘",EJ="⫆",kJ="⊇",PJ="⫄",DJ="⊃",AJ="⊇",LJ="⟉",OJ="⫗",MJ="⥻",NJ="⫂",RJ="⫌",IJ="⊋",VJ="⫀",qJ="⊃",FJ="⋑",BJ="⊇",zJ="⫆",jJ="⊋",UJ="⫌",$J="⫈",HJ="⫔",GJ="⫖",WJ="⤦",YJ="↙",XJ="⇙",QJ="↙",KJ="⤪",ZJ="ß",JJ="	",ttt="⌖",ett="Τ",ntt="τ",rtt="⎴",itt="Ť",ott="ť",stt="Ţ",att="ţ",ltt="Т",utt="т",ctt="⃛",ftt="⌕",dtt="𝔗",htt="𝔱",ptt="∴",mtt="∴",gtt="∴",vtt="Θ",ytt="θ",_tt="ϑ",wtt="ϑ",xtt="≈",Stt="∼",Ttt="  ",btt=" ",Ctt=" ",Ett="≈",ktt="∼",Ptt="Þ",Dtt="þ",Att="˜",Ltt="∼",Ott="≃",Mtt="≅",Ntt="≈",Rtt="⨱",Itt="⊠",Vtt="×",qtt="⨰",Ftt="∭",Btt="⤨",ztt="⌶",jtt="⫱",Utt="⊤",$tt="𝕋",Htt="𝕥",Gtt="⫚",Wtt="⤩",Ytt="‴",Xtt="™",Qtt="™",Ktt="▵",Ztt="▿",Jtt="◃",tet="⊴",eet="≜",net="▹",ret="⊵",iet="◬",oet="≜",set="⨺",aet="⃛",uet="⨹",cet="⧍",fet="⨻",det="⏢",het="𝒯",pet="𝓉",met="Ц",get="ц",vet="Ћ",yet="ћ",_et="Ŧ",wet="ŧ",xet="≬",Tet="↞",bet="↠",Cet="Ú",Eet="ú",ket="↑",Pet="↟",Det="⇑",Aet="⥉",Let="Ў",Oet="ў",Met="Ŭ",Net="ŭ",Ret="Û",Iet="û",Vet="У",qet="у",Fet="⇅",Bet="Ű",zet="ű",jet="⥮",Uet="⥾",$et="𝔘",Het="𝔲",Get="Ù",Wet="ù",Yet="⥣",Xet="↿",Qet="↾",Ket="▀",Zet="⌜",Jet="⌜",tnt="⌏",ent="◸",nnt="Ū",rnt="ū",int="¨",ont="_",snt="⏟",ant="⎵",lnt="⏝",unt="⋃",cnt="⊎",fnt="Ų",dnt="ų",hnt="𝕌",pnt="𝕦",mnt="⤒",gnt="↑",vnt="↑",ynt="⇑",_nt="⇅",wnt="↕",xnt="↕",Snt="⇕",Tnt="⥮",bnt="↿",Cnt="↾",Ent="⊎",knt="↖",Pnt="↗",Dnt="υ",Ant="ϒ",Lnt="ϒ",Ont="Υ",Mnt="υ",Nnt="↥",Rnt="⊥",Int="⇈",Vnt="⌝",qnt="⌝",Fnt="⌎",Bnt="Ů",znt="ů",jnt="◹",Unt="𝒰",$nt="𝓊",Hnt="⋰",Gnt="Ũ",Wnt="ũ",Ynt="▵",Xnt="▴",Qnt="⇈",Knt="Ü",Znt="ü",Jnt="⦧",trt="⦜",ert="ϵ",nrt="ϰ",rrt="∅",irt="ϕ",ort="ϖ",srt="∝",art="↕",lrt="⇕",urt="ϱ",crt="ς",frt="⊊︀",drt="⫋︀",hrt="⊋︀",prt="⫌︀",mrt="ϑ",grt="⊲",vrt="⊳",yrt="⫨",_rt="⫫",wrt="⫩",xrt="В",Srt="в",Trt="⊢",brt="⊨",Crt="⊩",Ert="⊫",krt="⫦",Prt="⊻",Drt="∨",Art="⋁",Lrt="≚",Ort="⋮",Mrt="|",Nrt="‖",Rrt="|",Irt="‖",Vrt="∣",qrt="|",Frt="❘",Brt="≀",zrt=" ",jrt="𝔙",Urt="𝔳",$rt="⊲",Hrt="⊂⃒",Grt="⊃⃒",Wrt="𝕍",Yrt="𝕧",Xrt="∝",Qrt="⊳",Krt="𝒱",Zrt="𝓋",Jrt="⫋︀",t1t="⊊︀",e1t="⫌︀",n1t="⊋︀",r1t="⊪",i1t="⦚",o1t="Ŵ",s1t="ŵ",a1t="⩟",l1t="∧",u1t="⋀",c1t="≙",f1t="℘",d1t="𝔚",h1t="𝔴",p1t="𝕎",m1t="𝕨",g1t="℘",v1t="≀",y1t="≀",_1t="𝒲",w1t="𝓌",x1t="⋂",S1t="◯",T1t="⋃",b1t="▽",C1t="𝔛",E1t="𝔵",k1t="⟷",P1t="⟺",D1t="Ξ",A1t="ξ",L1t="⟵",O1t="⟸",M1t="⟼",N1t="⋻",R1t="⨀",I1t="𝕏",V1t="𝕩",q1t="⨁",F1t="⨂",B1t="⟶",z1t="⟹",j1t="𝒳",U1t="𝓍",$1t="⨆",H1t="⨄",G1t="△",W1t="⋁",Y1t="⋀",X1t="Ý",Q1t="ý",K1t="Я",Z1t="я",J1t="Ŷ",tit="ŷ",eit="Ы",nit="ы",rit="¥",iit="𝔜",oit="𝔶",sit="Ї",ait="ї",lit="𝕐",uit="𝕪",cit="𝒴",fit="𝓎",dit="Ю",hit="ю",pit="ÿ",mit="Ÿ",git="Ź",vit="ź",yit="Ž",_it="ž",wit="З",xit="з",Sit="Ż",Tit="ż",bit="ℨ",Cit="​",Eit="Ζ",kit="ζ",Pit="𝔷",Dit="ℨ",Ait="Ж",Lit="ж",Oit="⇝",Mit="𝕫",Nit="ℤ",Rit="𝒵",Iit="𝓏",Vit="‍",qit="‌",Jh={Aacute:ww,aacute:xw,Abreve:Sw,abreve:Tw,ac:bw,acd:Cw,acE:Ew,Acirc:kw,acirc:Pw,acute:Dw,Acy:Aw,acy:Lw,AElig:Ow,aelig:Mw,af:Nw,Afr:Rw,afr:Iw,Agrave:Vw,agrave:qw,alefsym:Fw,aleph:Bw,Alpha:zw,alpha:jw,Amacr:Uw,amacr:$w,amalg:Hw,amp:Gw,AMP:Ww,andand:Yw,And:Xw,and:Qw,andd:Kw,andslope:Zw,andv:Jw,ang:tx,ange:ex,angle:nx,angmsdaa:rx,angmsdab:ix,angmsdac:ox,angmsdad:sx,angmsdae:ax,angmsdaf:lx,angmsdag:ux,angmsdah:cx,angmsd:fx,angrt:dx,angrtvb:hx,angrtvbd:px,angsph:mx,angst:gx,angzarr:vx,Aogon:yx,aogon:_x,Aopf:wx,aopf:xx,apacir:Sx,ap:Tx,apE:bx,ape:Cx,apid:Ex,apos:kx,ApplyFunction:Px,approx:Dx,approxeq:Ax,Aring:Lx,aring:Ox,Ascr:Mx,ascr:Nx,Assign:Rx,ast:Ix,asymp:Vx,asympeq:qx,Atilde:Fx,atilde:Bx,Auml:zx,auml:jx,awconint:Ux,awint:$x,backcong:Hx,backepsilon:Gx,backprime:Wx,backsim:Yx,backsimeq:Xx,Backslash:Qx,Barv:Kx,barvee:Zx,barwed:Jx,Barwed:tS,barwedge:eS,bbrk:nS,bbrktbrk:rS,bcong:iS,Bcy:oS,bcy:sS,bdquo:aS,becaus:lS,because:uS,Because:cS,bemptyv:fS,bepsi:dS,bernou:hS,Bernoullis:pS,Beta:mS,beta:gS,beth:vS,between:yS,Bfr:_S,bfr:wS,bigcap:xS,bigcirc:SS,bigcup:TS,bigodot:bS,bigoplus:CS,bigotimes:ES,bigsqcup:kS,bigstar:PS,bigtriangledown:DS,bigtriangleup:AS,biguplus:LS,bigvee:OS,bigwedge:MS,bkarow:NS,blacklozenge:RS,blacksquare:IS,blacktriangle:VS,blacktriangledown:qS,blacktriangleleft:FS,blacktriangleright:BS,blank:zS,blk12:jS,blk14:US,blk34:$S,block:HS,bne:GS,bnequiv:WS,bNot:YS,bnot:XS,Bopf:QS,bopf:KS,bot:ZS,bottom:JS,bowtie:tT,boxbox:eT,boxdl:nT,boxdL:rT,boxDl:iT,boxDL:oT,boxdr:sT,boxdR:aT,boxDr:lT,boxDR:uT,boxh:cT,boxH:fT,boxhd:dT,boxHd:hT,boxhD:pT,boxHD:mT,boxhu:gT,boxHu:vT,boxhU:yT,boxHU:_T,boxminus:wT,boxplus:xT,boxtimes:ST,boxul:TT,boxuL:bT,boxUl:CT,boxUL:ET,boxur:kT,boxuR:PT,boxUr:DT,boxUR:AT,boxv:LT,boxV:OT,boxvh:MT,boxvH:NT,boxVh:RT,boxVH:IT,boxvl:VT,boxvL:qT,boxVl:FT,boxVL:BT,boxvr:zT,boxvR:jT,boxVr:UT,boxVR:$T,bprime:HT,breve:GT,Breve:WT,brvbar:YT,bscr:XT,Bscr:QT,bsemi:KT,bsim:ZT,bsime:JT,bsolb:tb,bsol:eb,bsolhsub:nb,bull:rb,bullet:ib,bump:ob,bumpE:sb,bumpe:ab,Bumpeq:lb,bumpeq:ub,Cacute:cb,cacute:fb,capand:db,capbrcup:hb,capcap:pb,cap:mb,Cap:gb,capcup:vb,capdot:yb,CapitalDifferentialD:_b,caps:wb,caret:xb,caron:Sb,Cayleys:Tb,ccaps:bb,Ccaron:Cb,ccaron:Eb,Ccedil:kb,ccedil:Pb,Ccirc:Db,ccirc:Ab,Cconint:Lb,ccups:Ob,ccupssm:Mb,Cdot:Nb,cdot:Rb,cedil:Ib,Cedilla:Vb,cemptyv:qb,cent:Fb,centerdot:Bb,CenterDot:zb,cfr:jb,Cfr:Ub,CHcy:$b,chcy:Hb,check:Gb,checkmark:Wb,Chi:Yb,chi:Xb,circ:Qb,circeq:Kb,circlearrowleft:Zb,circlearrowright:Jb,circledast:tC,circledcirc:eC,circleddash:nC,CircleDot:rC,circledR:iC,circledS:oC,CircleMinus:sC,CirclePlus:aC,CircleTimes:lC,cir:uC,cirE:cC,cire:fC,cirfnint:dC,cirmid:hC,cirscir:pC,ClockwiseContourIntegral:mC,CloseCurlyDoubleQuote:gC,CloseCurlyQuote:vC,clubs:yC,clubsuit:_C,colon:wC,Colon:xC,Colone:SC,colone:TC,coloneq:bC,comma:CC,commat:EC,comp:kC,compfn:PC,complement:DC,complexes:AC,cong:LC,congdot:OC,Congruent:MC,conint:NC,Conint:RC,ContourIntegral:IC,copf:VC,Copf:qC,coprod:FC,Coproduct:BC,copy:zC,COPY:jC,copysr:UC,CounterClockwiseContourIntegral:$C,crarr:HC,cross:GC,Cross:WC,Cscr:YC,cscr:XC,csub:QC,csube:KC,csup:ZC,csupe:JC,ctdot:tE,cudarrl:eE,cudarrr:nE,cuepr:rE,cuesc:iE,cularr:oE,cularrp:sE,cupbrcap:aE,cupcap:lE,CupCap:uE,cup:cE,Cup:fE,cupcup:dE,cupdot:hE,cupor:pE,cups:mE,curarr:gE,curarrm:vE,curlyeqprec:yE,curlyeqsucc:_E,curlyvee:wE,curlywedge:xE,curren:SE,curvearrowleft:TE,curvearrowright:bE,cuvee:CE,cuwed:EE,cwconint:kE,cwint:PE,cylcty:DE,dagger:AE,Dagger:LE,daleth:OE,darr:ME,Darr:NE,dArr:RE,dash:IE,Dashv:VE,dashv:qE,dbkarow:FE,dblac:BE,Dcaron:zE,dcaron:jE,Dcy:UE,dcy:$E,ddagger:HE,ddarr:GE,DD:WE,dd:YE,DDotrahd:XE,ddotseq:QE,deg:KE,Del:ZE,Delta:JE,delta:tk,demptyv:ek,dfisht:nk,Dfr:rk,dfr:ik,dHar:ok,dharl:sk,dharr:ak,DiacriticalAcute:lk,DiacriticalDot:uk,DiacriticalDoubleAcute:ck,DiacriticalGrave:fk,DiacriticalTilde:dk,diam:hk,diamond:pk,Diamond:mk,diamondsuit:gk,diams:vk,die:yk,DifferentialD:_k,digamma:wk,disin:xk,div:Sk,divide:Tk,divideontimes:bk,divonx:Ck,DJcy:Ek,djcy:kk,dlcorn:Pk,dlcrop:Dk,dollar:Ak,Dopf:Lk,dopf:Ok,Dot:Mk,dot:Nk,DotDot:Rk,doteq:Ik,doteqdot:Vk,DotEqual:qk,dotminus:Fk,dotplus:Bk,dotsquare:zk,doublebarwedge:jk,DoubleContourIntegral:Uk,DoubleDot:$k,DoubleDownArrow:Hk,DoubleLeftArrow:Gk,DoubleLeftRightArrow:Wk,DoubleLeftTee:Yk,DoubleLongLeftArrow:Xk,DoubleLongLeftRightArrow:Qk,DoubleLongRightArrow:Kk,DoubleRightArrow:Zk,DoubleRightTee:Jk,DoubleUpArrow:tP,DoubleUpDownArrow:eP,DoubleVerticalBar:nP,DownArrowBar:rP,downarrow:iP,DownArrow:oP,Downarrow:sP,DownArrowUpArrow:aP,DownBreve:lP,downdownarrows:uP,downharpoonleft:cP,downharpoonright:fP,DownLeftRightVector:dP,DownLeftTeeVector:hP,DownLeftVectorBar:pP,DownLeftVector:mP,DownRightTeeVector:gP,DownRightVectorBar:vP,DownRightVector:yP,DownTeeArrow:_P,DownTee:wP,drbkarow:xP,drcorn:SP,drcrop:TP,Dscr:bP,dscr:CP,DScy:EP,dscy:kP,dsol:PP,Dstrok:DP,dstrok:AP,dtdot:LP,dtri:OP,dtrif:MP,duarr:NP,duhar:RP,dwangle:IP,DZcy:VP,dzcy:qP,dzigrarr:FP,Eacute:BP,eacute:zP,easter:jP,Ecaron:UP,ecaron:$P,Ecirc:HP,ecirc:GP,ecir:WP,ecolon:YP,Ecy:XP,ecy:QP,eDDot:KP,Edot:ZP,edot:JP,eDot:tD,ee:eD,efDot:nD,Efr:rD,efr:iD,eg:oD,Egrave:sD,egrave:aD,egs:lD,egsdot:uD,el:cD,Element:fD,elinters:dD,ell:hD,els:pD,elsdot:mD,Emacr:gD,emacr:vD,empty:yD,emptyset:_D,EmptySmallSquare:wD,emptyv:xD,EmptyVerySmallSquare:SD,emsp13:TD,emsp14:bD,emsp:CD,ENG:ED,eng:kD,ensp:PD,Eogon:DD,eogon:AD,Eopf:LD,eopf:OD,epar:MD,eparsl:ND,eplus:RD,epsi:ID,Epsilon:VD,epsilon:qD,epsiv:FD,eqcirc:BD,eqcolon:zD,eqsim:jD,eqslantgtr:UD,eqslantless:$D,Equal:HD,equals:GD,EqualTilde:WD,equest:YD,Equilibrium:XD,equiv:QD,equivDD:KD,eqvparsl:ZD,erarr:JD,erDot:tA,escr:eA,Escr:nA,esdot:rA,Esim:iA,esim:oA,Eta:sA,eta:aA,ETH:lA,eth:uA,Euml:cA,euml:fA,euro:dA,excl:hA,exist:pA,Exists:mA,expectation:gA,exponentiale:vA,ExponentialE:yA,fallingdotseq:_A,Fcy:wA,fcy:xA,female:SA,ffilig:TA,fflig:bA,ffllig:CA,Ffr:EA,ffr:kA,filig:PA,FilledSmallSquare:DA,FilledVerySmallSquare:AA,fjlig:LA,flat:OA,fllig:MA,fltns:NA,fnof:RA,Fopf:IA,fopf:VA,forall:qA,ForAll:FA,fork:BA,forkv:zA,Fouriertrf:jA,fpartint:UA,frac12:$A,frac13:HA,frac14:GA,frac15:WA,frac16:YA,frac18:XA,frac23:QA,frac25:KA,frac34:ZA,frac35:JA,frac38:tL,frac45:eL,frac56:nL,frac58:rL,frac78:iL,frasl:oL,frown:sL,fscr:aL,Fscr:lL,gacute:uL,Gamma:cL,gamma:fL,Gammad:dL,gammad:hL,gap:pL,Gbreve:mL,gbreve:gL,Gcedil:vL,Gcirc:yL,gcirc:_L,Gcy:wL,gcy:xL,Gdot:SL,gdot:TL,ge:bL,gE:CL,gEl:EL,gel:kL,geq:PL,geqq:DL,geqslant:AL,gescc:LL,ges:OL,gesdot:ML,gesdoto:NL,gesdotol:RL,gesl:IL,gesles:VL,Gfr:qL,gfr:FL,gg:BL,Gg:zL,ggg:jL,gimel:UL,GJcy:$L,gjcy:HL,gla:GL,gl:WL,glE:YL,glj:XL,gnap:QL,gnapprox:KL,gne:ZL,gnE:JL,gneq:tO,gneqq:eO,gnsim:nO,Gopf:rO,gopf:iO,grave:oO,GreaterEqual:sO,GreaterEqualLess:aO,GreaterFullEqual:lO,GreaterGreater:uO,GreaterLess:cO,GreaterSlantEqual:fO,GreaterTilde:dO,Gscr:hO,gscr:pO,gsim:mO,gsime:gO,gsiml:vO,gtcc:yO,gtcir:_O,gt:wO,GT:xO,Gt:SO,gtdot:TO,gtlPar:bO,gtquest:CO,gtrapprox:EO,gtrarr:kO,gtrdot:PO,gtreqless:DO,gtreqqless:AO,gtrless:LO,gtrsim:OO,gvertneqq:MO,gvnE:NO,Hacek:RO,hairsp:IO,half:VO,hamilt:qO,HARDcy:FO,hardcy:BO,harrcir:zO,harr:jO,hArr:UO,harrw:$O,Hat:HO,hbar:GO,Hcirc:WO,hcirc:YO,hearts:XO,heartsuit:QO,hellip:KO,hercon:ZO,hfr:JO,Hfr:tM,HilbertSpace:eM,hksearow:nM,hkswarow:rM,hoarr:iM,homtht:oM,hookleftarrow:sM,hookrightarrow:aM,hopf:lM,Hopf:uM,horbar:cM,HorizontalLine:fM,hscr:dM,Hscr:hM,hslash:pM,Hstrok:mM,hstrok:gM,HumpDownHump:vM,HumpEqual:yM,hybull:_M,hyphen:wM,Iacute:xM,iacute:SM,ic:TM,Icirc:bM,icirc:CM,Icy:EM,icy:kM,Idot:PM,IEcy:DM,iecy:AM,iexcl:LM,iff:OM,ifr:MM,Ifr:NM,Igrave:RM,igrave:IM,ii:VM,iiiint:qM,iiint:FM,iinfin:BM,iiota:zM,IJlig:jM,ijlig:UM,Imacr:$M,imacr:HM,image:GM,ImaginaryI:WM,imagline:YM,imagpart:XM,imath:QM,Im:KM,imof:ZM,imped:JM,Implies:tN,incare:eN,in:"∈",infin:nN,infintie:rN,inodot:iN,intcal:oN,int:sN,Int:aN,integers:lN,Integral:uN,intercal:cN,Intersection:fN,intlarhk:dN,intprod:hN,InvisibleComma:pN,InvisibleTimes:mN,IOcy:gN,iocy:vN,Iogon:yN,iogon:_N,Iopf:wN,iopf:xN,Iota:SN,iota:TN,iprod:bN,iquest:CN,iscr:EN,Iscr:kN,isin:PN,isindot:DN,isinE:AN,isins:LN,isinsv:ON,isinv:MN,it:NN,Itilde:RN,itilde:IN,Iukcy:VN,iukcy:qN,Iuml:FN,iuml:BN,Jcirc:zN,jcirc:jN,Jcy:UN,jcy:$N,Jfr:HN,jfr:GN,jmath:WN,Jopf:YN,jopf:XN,Jscr:QN,jscr:KN,Jsercy:ZN,jsercy:JN,Jukcy:tR,jukcy:eR,Kappa:nR,kappa:rR,kappav:iR,Kcedil:oR,kcedil:sR,Kcy:aR,kcy:lR,Kfr:uR,kfr:cR,kgreen:fR,KHcy:dR,khcy:hR,KJcy:pR,kjcy:mR,Kopf:gR,kopf:vR,Kscr:yR,kscr:_R,lAarr:wR,Lacute:xR,lacute:SR,laemptyv:TR,lagran:bR,Lambda:CR,lambda:ER,lang:kR,Lang:PR,langd:DR,langle:AR,lap:LR,Laplacetrf:OR,laquo:MR,larrb:NR,larrbfs:RR,larr:IR,Larr:VR,lArr:qR,larrfs:FR,larrhk:BR,larrlp:zR,larrpl:jR,larrsim:UR,larrtl:$R,latail:HR,lAtail:GR,lat:WR,late:YR,lates:XR,lbarr:QR,lBarr:KR,lbbrk:ZR,lbrace:JR,lbrack:tI,lbrke:eI,lbrksld:nI,lbrkslu:rI,Lcaron:iI,lcaron:oI,Lcedil:sI,lcedil:aI,lceil:lI,lcub:uI,Lcy:cI,lcy:fI,ldca:dI,ldquo:hI,ldquor:pI,ldrdhar:mI,ldrushar:gI,ldsh:vI,le:yI,lE:_I,LeftAngleBracket:wI,LeftArrowBar:xI,leftarrow:SI,LeftArrow:TI,Leftarrow:bI,LeftArrowRightArrow:CI,leftarrowtail:EI,LeftCeiling:kI,LeftDoubleBracket:PI,LeftDownTeeVector:DI,LeftDownVectorBar:AI,LeftDownVector:LI,LeftFloor:OI,leftharpoondown:MI,leftharpoonup:NI,leftleftarrows:RI,leftrightarrow:II,LeftRightArrow:VI,Leftrightarrow:qI,leftrightarrows:FI,leftrightharpoons:BI,leftrightsquigarrow:zI,LeftRightVector:jI,LeftTeeArrow:UI,LeftTee:$I,LeftTeeVector:HI,leftthreetimes:GI,LeftTriangleBar:WI,LeftTriangle:YI,LeftTriangleEqual:XI,LeftUpDownVector:QI,LeftUpTeeVector:KI,LeftUpVectorBar:ZI,LeftUpVector:JI,LeftVectorBar:tV,LeftVector:eV,lEg:nV,leg:rV,leq:iV,leqq:oV,leqslant:sV,lescc:aV,les:lV,lesdot:uV,lesdoto:cV,lesdotor:fV,lesg:dV,lesges:hV,lessapprox:pV,lessdot:mV,lesseqgtr:gV,lesseqqgtr:vV,LessEqualGreater:yV,LessFullEqual:_V,LessGreater:wV,lessgtr:xV,LessLess:SV,lesssim:TV,LessSlantEqual:bV,LessTilde:CV,lfisht:EV,lfloor:kV,Lfr:PV,lfr:DV,lg:AV,lgE:LV,lHar:OV,lhard:MV,lharu:NV,lharul:RV,lhblk:IV,LJcy:VV,ljcy:qV,llarr:FV,ll:BV,Ll:zV,llcorner:jV,Lleftarrow:UV,llhard:$V,lltri:HV,Lmidot:GV,lmidot:WV,lmoustache:YV,lmoust:XV,lnap:QV,lnapprox:KV,lne:ZV,lnE:JV,lneq:tq,lneqq:eq,lnsim:nq,loang:rq,loarr:iq,lobrk:oq,longleftarrow:sq,LongLeftArrow:aq,Longleftarrow:lq,longleftrightarrow:uq,LongLeftRightArrow:cq,Longleftrightarrow:fq,longmapsto:dq,longrightarrow:hq,LongRightArrow:pq,Longrightarrow:mq,looparrowleft:gq,looparrowright:vq,lopar:yq,Lopf:_q,lopf:wq,loplus:xq,lotimes:Sq,lowast:Tq,lowbar:bq,LowerLeftArrow:Cq,LowerRightArrow:Eq,loz:kq,lozenge:Pq,lozf:Dq,lpar:Aq,lparlt:Lq,lrarr:Oq,lrcorner:Mq,lrhar:Nq,lrhard:Rq,lrm:Iq,lrtri:Vq,lsaquo:qq,lscr:Fq,Lscr:Bq,lsh:zq,Lsh:jq,lsim:Uq,lsime:$q,lsimg:Hq,lsqb:Gq,lsquo:Wq,lsquor:Yq,Lstrok:Xq,lstrok:Qq,ltcc:Kq,ltcir:Zq,lt:Jq,LT:tF,Lt:eF,ltdot:nF,lthree:rF,ltimes:iF,ltlarr:oF,ltquest:sF,ltri:aF,ltrie:lF,ltrif:uF,ltrPar:cF,lurdshar:fF,luruhar:dF,lvertneqq:hF,lvnE:pF,macr:mF,male:gF,malt:vF,maltese:yF,Map:"⤅",map:_F,mapsto:wF,mapstodown:xF,mapstoleft:SF,mapstoup:TF,marker:bF,mcomma:CF,Mcy:EF,mcy:kF,mdash:PF,mDDot:DF,measuredangle:AF,MediumSpace:LF,Mellintrf:OF,Mfr:MF,mfr:NF,mho:RF,micro:IF,midast:VF,midcir:qF,mid:FF,middot:BF,minusb:zF,minus:jF,minusd:UF,minusdu:$F,MinusPlus:HF,mlcp:GF,mldr:WF,mnplus:YF,models:XF,Mopf:QF,mopf:KF,mp:ZF,mscr:JF,Mscr:tB,mstpos:eB,Mu:nB,mu:rB,multimap:iB,mumap:oB,nabla:sB,Nacute:aB,nacute:lB,nang:uB,nap:cB,napE:fB,napid:dB,napos:hB,napprox:pB,natural:mB,naturals:gB,natur:vB,nbsp:yB,nbump:_B,nbumpe:wB,ncap:xB,Ncaron:SB,ncaron:TB,Ncedil:bB,ncedil:CB,ncong:EB,ncongdot:kB,ncup:PB,Ncy:DB,ncy:AB,ndash:LB,nearhk:OB,nearr:MB,neArr:NB,nearrow:RB,ne:IB,nedot:VB,NegativeMediumSpace:qB,NegativeThickSpace:FB,NegativeThinSpace:BB,NegativeVeryThinSpace:zB,nequiv:jB,nesear:UB,nesim:$B,NestedGreaterGreater:HB,NestedLessLess:GB,NewLine:WB,nexist:YB,nexists:XB,Nfr:QB,nfr:KB,ngE:ZB,nge:JB,ngeq:tz,ngeqq:ez,ngeqslant:nz,nges:rz,nGg:iz,ngsim:oz,nGt:sz,ngt:az,ngtr:lz,nGtv:uz,nharr:cz,nhArr:fz,nhpar:dz,ni:hz,nis:pz,nisd:mz,niv:gz,NJcy:vz,njcy:yz,nlarr:_z,nlArr:wz,nldr:xz,nlE:Sz,nle:Tz,nleftarrow:bz,nLeftarrow:Cz,nleftrightarrow:Ez,nLeftrightarrow:kz,nleq:Pz,nleqq:Dz,nleqslant:Az,nles:Lz,nless:Oz,nLl:Mz,nlsim:Nz,nLt:Rz,nlt:Iz,nltri:Vz,nltrie:qz,nLtv:Fz,nmid:Bz,NoBreak:zz,NonBreakingSpace:jz,nopf:Uz,Nopf:$z,Not:Hz,not:Gz,NotCongruent:Wz,NotCupCap:Yz,NotDoubleVerticalBar:Xz,NotElement:Qz,NotEqual:Kz,NotEqualTilde:Zz,NotExists:Jz,NotGreater:tj,NotGreaterEqual:ej,NotGreaterFullEqual:nj,NotGreaterGreater:rj,NotGreaterLess:ij,NotGreaterSlantEqual:oj,NotGreaterTilde:sj,NotHumpDownHump:aj,NotHumpEqual:lj,notin:uj,notindot:cj,notinE:fj,notinva:dj,notinvb:hj,notinvc:pj,NotLeftTriangleBar:mj,NotLeftTriangle:gj,NotLeftTriangleEqual:vj,NotLess:yj,NotLessEqual:_j,NotLessGreater:wj,NotLessLess:xj,NotLessSlantEqual:Sj,NotLessTilde:Tj,NotNestedGreaterGreater:bj,NotNestedLessLess:Cj,notni:Ej,notniva:kj,notnivb:Pj,notnivc:Dj,NotPrecedes:Aj,NotPrecedesEqual:Lj,NotPrecedesSlantEqual:Oj,NotReverseElement:Mj,NotRightTriangleBar:Nj,NotRightTriangle:Rj,NotRightTriangleEqual:Ij,NotSquareSubset:Vj,NotSquareSubsetEqual:qj,NotSquareSuperset:Fj,NotSquareSupersetEqual:Bj,NotSubset:zj,NotSubsetEqual:jj,NotSucceeds:Uj,NotSucceedsEqual:$j,NotSucceedsSlantEqual:Hj,NotSucceedsTilde:Gj,NotSuperset:Wj,NotSupersetEqual:Yj,NotTilde:Xj,NotTildeEqual:Qj,NotTildeFullEqual:Kj,NotTildeTilde:Zj,NotVerticalBar:Jj,nparallel:tU,npar:eU,nparsl:nU,npart:rU,npolint:iU,npr:oU,nprcue:sU,nprec:aU,npreceq:lU,npre:uU,nrarrc:cU,nrarr:fU,nrArr:dU,nrarrw:hU,nrightarrow:pU,nRightarrow:mU,nrtri:gU,nrtrie:vU,nsc:yU,nsccue:_U,nsce:wU,Nscr:xU,nscr:SU,nshortmid:TU,nshortparallel:bU,nsim:CU,nsime:EU,nsimeq:kU,nsmid:PU,nspar:DU,nsqsube:AU,nsqsupe:LU,nsub:OU,nsubE:MU,nsube:NU,nsubset:RU,nsubseteq:IU,nsubseteqq:VU,nsucc:qU,nsucceq:FU,nsup:BU,nsupE:zU,nsupe:jU,nsupset:UU,nsupseteq:$U,nsupseteqq:HU,ntgl:GU,Ntilde:WU,ntilde:YU,ntlg:XU,ntriangleleft:QU,ntrianglelefteq:KU,ntriangleright:ZU,ntrianglerighteq:JU,Nu:t$,nu:e$,num:n$,numero:r$,numsp:i$,nvap:o$,nvdash:s$,nvDash:a$,nVdash:l$,nVDash:u$,nvge:c$,nvgt:f$,nvHarr:d$,nvinfin:h$,nvlArr:p$,nvle:m$,nvlt:g$,nvltrie:v$,nvrArr:y$,nvrtrie:_$,nvsim:w$,nwarhk:x$,nwarr:S$,nwArr:T$,nwarrow:b$,nwnear:C$,Oacute:E$,oacute:k$,oast:P$,Ocirc:D$,ocirc:A$,ocir:L$,Ocy:O$,ocy:M$,odash:N$,Odblac:R$,odblac:I$,odiv:V$,odot:q$,odsold:F$,OElig:B$,oelig:z$,ofcir:j$,Ofr:U$,ofr:$$,ogon:H$,Ograve:G$,ograve:W$,ogt:Y$,ohbar:X$,ohm:Q$,oint:K$,olarr:Z$,olcir:J$,olcross:tH,oline:eH,olt:nH,Omacr:rH,omacr:iH,Omega:oH,omega:sH,Omicron:aH,omicron:lH,omid:uH,ominus:cH,Oopf:fH,oopf:dH,opar:hH,OpenCurlyDoubleQuote:pH,OpenCurlyQuote:mH,operp:gH,oplus:vH,orarr:yH,Or:_H,or:wH,ord:xH,order:SH,orderof:TH,ordf:bH,ordm:CH,origof:EH,oror:kH,orslope:PH,orv:DH,oS:AH,Oscr:LH,oscr:OH,Oslash:MH,oslash:NH,osol:RH,Otilde:IH,otilde:VH,otimesas:qH,Otimes:FH,otimes:BH,Ouml:zH,ouml:jH,ovbar:UH,OverBar:$H,OverBrace:HH,OverBracket:GH,OverParenthesis:WH,para:YH,parallel:XH,par:QH,parsim:KH,parsl:ZH,part:JH,PartialD:tG,Pcy:eG,pcy:nG,percnt:rG,period:iG,permil:oG,perp:sG,pertenk:aG,Pfr:lG,pfr:uG,Phi:cG,phi:fG,phiv:dG,phmmat:hG,phone:pG,Pi:mG,pi:gG,pitchfork:vG,piv:yG,planck:_G,planckh:wG,plankv:xG,plusacir:SG,plusb:TG,pluscir:bG,plus:CG,plusdo:EG,plusdu:kG,pluse:PG,PlusMinus:DG,plusmn:AG,plussim:LG,plustwo:OG,pm:MG,Poincareplane:NG,pointint:RG,popf:IG,Popf:VG,pound:qG,prap:FG,Pr:BG,pr:zG,prcue:jG,precapprox:UG,prec:$G,preccurlyeq:HG,Precedes:GG,PrecedesEqual:WG,PrecedesSlantEqual:YG,PrecedesTilde:XG,preceq:QG,precnapprox:KG,precneqq:ZG,precnsim:JG,pre:tW,prE:eW,precsim:nW,prime:rW,Prime:iW,primes:oW,prnap:sW,prnE:aW,prnsim:lW,prod:uW,Product:cW,profalar:fW,profline:dW,profsurf:hW,prop:pW,Proportional:mW,Proportion:gW,propto:vW,prsim:yW,prurel:_W,Pscr:wW,pscr:xW,Psi:SW,psi:TW,puncsp:bW,Qfr:CW,qfr:EW,qint:kW,qopf:PW,Qopf:DW,qprime:AW,Qscr:LW,qscr:OW,quaternions:MW,quatint:NW,quest:RW,questeq:IW,quot:VW,QUOT:qW,rAarr:FW,race:BW,Racute:zW,racute:jW,radic:UW,raemptyv:$W,rang:HW,Rang:GW,rangd:WW,range:YW,rangle:XW,raquo:QW,rarrap:KW,rarrb:ZW,rarrbfs:JW,rarrc:tY,rarr:eY,Rarr:nY,rArr:rY,rarrfs:iY,rarrhk:oY,rarrlp:sY,rarrpl:aY,rarrsim:lY,Rarrtl:uY,rarrtl:cY,rarrw:fY,ratail:dY,rAtail:hY,ratio:pY,rationals:mY,rbarr:gY,rBarr:vY,RBarr:yY,rbbrk:_Y,rbrace:wY,rbrack:xY,rbrke:SY,rbrksld:TY,rbrkslu:bY,Rcaron:CY,rcaron:EY,Rcedil:kY,rcedil:PY,rceil:DY,rcub:AY,Rcy:LY,rcy:OY,rdca:MY,rdldhar:NY,rdquo:RY,rdquor:IY,rdsh:VY,real:qY,realine:FY,realpart:BY,reals:zY,Re:jY,rect:UY,reg:$Y,REG:HY,ReverseElement:GY,ReverseEquilibrium:WY,ReverseUpEquilibrium:YY,rfisht:XY,rfloor:QY,rfr:KY,Rfr:ZY,rHar:JY,rhard:tX,rharu:eX,rharul:nX,Rho:rX,rho:iX,rhov:oX,RightAngleBracket:sX,RightArrowBar:aX,rightarrow:lX,RightArrow:uX,Rightarrow:cX,RightArrowLeftArrow:fX,rightarrowtail:dX,RightCeiling:hX,RightDoubleBracket:pX,RightDownTeeVector:mX,RightDownVectorBar:gX,RightDownVector:vX,RightFloor:yX,rightharpoondown:_X,rightharpoonup:wX,rightleftarrows:xX,rightleftharpoons:SX,rightrightarrows:TX,rightsquigarrow:bX,RightTeeArrow:CX,RightTee:EX,RightTeeVector:kX,rightthreetimes:PX,RightTriangleBar:DX,RightTriangle:AX,RightTriangleEqual:LX,RightUpDownVector:OX,RightUpTeeVector:MX,RightUpVectorBar:NX,RightUpVector:RX,RightVectorBar:IX,RightVector:VX,ring:qX,risingdotseq:FX,rlarr:BX,rlhar:zX,rlm:jX,rmoustache:UX,rmoust:$X,rnmid:HX,roang:GX,roarr:WX,robrk:YX,ropar:XX,ropf:QX,Ropf:KX,roplus:ZX,rotimes:JX,RoundImplies:tQ,rpar:eQ,rpargt:nQ,rppolint:rQ,rrarr:iQ,Rrightarrow:oQ,rsaquo:sQ,rscr:aQ,Rscr:lQ,rsh:uQ,Rsh:cQ,rsqb:fQ,rsquo:dQ,rsquor:hQ,rthree:pQ,rtimes:mQ,rtri:gQ,rtrie:vQ,rtrif:yQ,rtriltri:_Q,RuleDelayed:wQ,ruluhar:xQ,rx:SQ,Sacute:TQ,sacute:bQ,sbquo:CQ,scap:EQ,Scaron:kQ,scaron:PQ,Sc:DQ,sc:AQ,sccue:LQ,sce:OQ,scE:MQ,Scedil:NQ,scedil:RQ,Scirc:IQ,scirc:VQ,scnap:qQ,scnE:FQ,scnsim:BQ,scpolint:zQ,scsim:jQ,Scy:UQ,scy:$Q,sdotb:HQ,sdot:GQ,sdote:WQ,searhk:YQ,searr:XQ,seArr:QQ,searrow:KQ,sect:ZQ,semi:JQ,seswar:tK,setminus:eK,setmn:nK,sext:rK,Sfr:iK,sfr:oK,sfrown:sK,sharp:aK,SHCHcy:lK,shchcy:uK,SHcy:cK,shcy:fK,ShortDownArrow:dK,ShortLeftArrow:hK,shortmid:pK,shortparallel:mK,ShortRightArrow:gK,ShortUpArrow:vK,shy:yK,Sigma:_K,sigma:wK,sigmaf:xK,sigmav:SK,sim:TK,simdot:bK,sime:CK,simeq:EK,simg:kK,simgE:PK,siml:DK,simlE:AK,simne:LK,simplus:OK,simrarr:MK,slarr:NK,SmallCircle:RK,smallsetminus:IK,smashp:VK,smeparsl:qK,smid:FK,smile:BK,smt:zK,smte:jK,smtes:UK,SOFTcy:$K,softcy:HK,solbar:GK,solb:WK,sol:YK,Sopf:XK,sopf:QK,spades:KK,spadesuit:ZK,spar:JK,sqcap:tZ,sqcaps:eZ,sqcup:nZ,sqcups:rZ,Sqrt:iZ,sqsub:oZ,sqsube:sZ,sqsubset:aZ,sqsubseteq:lZ,sqsup:uZ,sqsupe:cZ,sqsupset:fZ,sqsupseteq:dZ,square:hZ,Square:pZ,SquareIntersection:mZ,SquareSubset:gZ,SquareSubsetEqual:vZ,SquareSuperset:yZ,SquareSupersetEqual:_Z,SquareUnion:wZ,squarf:xZ,squ:SZ,squf:TZ,srarr:bZ,Sscr:CZ,sscr:EZ,ssetmn:kZ,ssmile:PZ,sstarf:DZ,Star:AZ,star:LZ,starf:OZ,straightepsilon:MZ,straightphi:NZ,strns:RZ,sub:IZ,Sub:VZ,subdot:qZ,subE:FZ,sube:BZ,subedot:zZ,submult:jZ,subnE:UZ,subne:$Z,subplus:HZ,subrarr:GZ,subset:WZ,Subset:YZ,subseteq:XZ,subseteqq:QZ,SubsetEqual:KZ,subsetneq:ZZ,subsetneqq:JZ,subsim:tJ,subsub:eJ,subsup:nJ,succapprox:rJ,succ:iJ,succcurlyeq:oJ,Succeeds:sJ,SucceedsEqual:aJ,SucceedsSlantEqual:lJ,SucceedsTilde:uJ,succeq:cJ,succnapprox:fJ,succneqq:dJ,succnsim:hJ,succsim:pJ,SuchThat:mJ,sum:gJ,Sum:vJ,sung:yJ,sup1:_J,sup2:wJ,sup3:xJ,sup:SJ,Sup:TJ,supdot:bJ,supdsub:CJ,supE:EJ,supe:kJ,supedot:PJ,Superset:DJ,SupersetEqual:AJ,suphsol:LJ,suphsub:OJ,suplarr:MJ,supmult:NJ,supnE:RJ,supne:IJ,supplus:VJ,supset:qJ,Supset:FJ,supseteq:BJ,supseteqq:zJ,supsetneq:jJ,supsetneqq:UJ,supsim:$J,supsub:HJ,supsup:GJ,swarhk:WJ,swarr:YJ,swArr:XJ,swarrow:QJ,swnwar:KJ,szlig:ZJ,Tab:JJ,target:ttt,Tau:ett,tau:ntt,tbrk:rtt,Tcaron:itt,tcaron:ott,Tcedil:stt,tcedil:att,Tcy:ltt,tcy:utt,tdot:ctt,telrec:ftt,Tfr:dtt,tfr:htt,there4:ptt,therefore:mtt,Therefore:gtt,Theta:vtt,theta:ytt,thetasym:_tt,thetav:wtt,thickapprox:xtt,thicksim:Stt,ThickSpace:Ttt,ThinSpace:btt,thinsp:Ctt,thkap:Ett,thksim:ktt,THORN:Ptt,thorn:Dtt,tilde:Att,Tilde:Ltt,TildeEqual:Ott,TildeFullEqual:Mtt,TildeTilde:Ntt,timesbar:Rtt,timesb:Itt,times:Vtt,timesd:qtt,tint:Ftt,toea:Btt,topbot:ztt,topcir:jtt,top:Utt,Topf:$tt,topf:Htt,topfork:Gtt,tosa:Wtt,tprime:Ytt,trade:Xtt,TRADE:Qtt,triangle:Ktt,triangledown:Ztt,triangleleft:Jtt,trianglelefteq:tet,triangleq:eet,triangleright:net,trianglerighteq:ret,tridot:iet,trie:oet,triminus:set,TripleDot:aet,triplus:uet,trisb:cet,tritime:fet,trpezium:det,Tscr:het,tscr:pet,TScy:met,tscy:get,TSHcy:vet,tshcy:yet,Tstrok:_et,tstrok:wet,twixt:xet,twoheadleftarrow:Tet,twoheadrightarrow:bet,Uacute:Cet,uacute:Eet,uarr:ket,Uarr:Pet,uArr:Det,Uarrocir:Aet,Ubrcy:Let,ubrcy:Oet,Ubreve:Met,ubreve:Net,Ucirc:Ret,ucirc:Iet,Ucy:Vet,ucy:qet,udarr:Fet,Udblac:Bet,udblac:zet,udhar:jet,ufisht:Uet,Ufr:$et,ufr:Het,Ugrave:Get,ugrave:Wet,uHar:Yet,uharl:Xet,uharr:Qet,uhblk:Ket,ulcorn:Zet,ulcorner:Jet,ulcrop:tnt,ultri:ent,Umacr:nnt,umacr:rnt,uml:int,UnderBar:ont,UnderBrace:snt,UnderBracket:ant,UnderParenthesis:lnt,Union:unt,UnionPlus:cnt,Uogon:fnt,uogon:dnt,Uopf:hnt,uopf:pnt,UpArrowBar:mnt,uparrow:gnt,UpArrow:vnt,Uparrow:ynt,UpArrowDownArrow:_nt,updownarrow:wnt,UpDownArrow:xnt,Updownarrow:Snt,UpEquilibrium:Tnt,upharpoonleft:bnt,upharpoonright:Cnt,uplus:Ent,UpperLeftArrow:knt,UpperRightArrow:Pnt,upsi:Dnt,Upsi:Ant,upsih:Lnt,Upsilon:Ont,upsilon:Mnt,UpTeeArrow:Nnt,UpTee:Rnt,upuparrows:Int,urcorn:Vnt,urcorner:qnt,urcrop:Fnt,Uring:Bnt,uring:znt,urtri:jnt,Uscr:Unt,uscr:$nt,utdot:Hnt,Utilde:Gnt,utilde:Wnt,utri:Ynt,utrif:Xnt,uuarr:Qnt,Uuml:Knt,uuml:Znt,uwangle:Jnt,vangrt:trt,varepsilon:ert,varkappa:nrt,varnothing:rrt,varphi:irt,varpi:ort,varpropto:srt,varr:art,vArr:lrt,varrho:urt,varsigma:crt,varsubsetneq:frt,varsubsetneqq:drt,varsupsetneq:hrt,varsupsetneqq:prt,vartheta:mrt,vartriangleleft:grt,vartriangleright:vrt,vBar:yrt,Vbar:_rt,vBarv:wrt,Vcy:xrt,vcy:Srt,vdash:Trt,vDash:brt,Vdash:Crt,VDash:Ert,Vdashl:krt,veebar:Prt,vee:Drt,Vee:Art,veeeq:Lrt,vellip:Ort,verbar:Mrt,Verbar:Nrt,vert:Rrt,Vert:Irt,VerticalBar:Vrt,VerticalLine:qrt,VerticalSeparator:Frt,VerticalTilde:Brt,VeryThinSpace:zrt,Vfr:jrt,vfr:Urt,vltri:$rt,vnsub:Hrt,vnsup:Grt,Vopf:Wrt,vopf:Yrt,vprop:Xrt,vrtri:Qrt,Vscr:Krt,vscr:Zrt,vsubnE:Jrt,vsubne:t1t,vsupnE:e1t,vsupne:n1t,Vvdash:r1t,vzigzag:i1t,Wcirc:o1t,wcirc:s1t,wedbar:a1t,wedge:l1t,Wedge:u1t,wedgeq:c1t,weierp:f1t,Wfr:d1t,wfr:h1t,Wopf:p1t,wopf:m1t,wp:g1t,wr:v1t,wreath:y1t,Wscr:_1t,wscr:w1t,xcap:x1t,xcirc:S1t,xcup:T1t,xdtri:b1t,Xfr:C1t,xfr:E1t,xharr:k1t,xhArr:P1t,Xi:D1t,xi:A1t,xlarr:L1t,xlArr:O1t,xmap:M1t,xnis:N1t,xodot:R1t,Xopf:I1t,xopf:V1t,xoplus:q1t,xotime:F1t,xrarr:B1t,xrArr:z1t,Xscr:j1t,xscr:U1t,xsqcup:$1t,xuplus:H1t,xutri:G1t,xvee:W1t,xwedge:Y1t,Yacute:X1t,yacute:Q1t,YAcy:K1t,yacy:Z1t,Ycirc:J1t,ycirc:tit,Ycy:eit,ycy:nit,yen:rit,Yfr:iit,yfr:oit,YIcy:sit,yicy:ait,Yopf:lit,yopf:uit,Yscr:cit,yscr:fit,YUcy:dit,yucy:hit,yuml:pit,Yuml:mit,Zacute:git,zacute:vit,Zcaron:yit,zcaron:_it,Zcy:wit,zcy:xit,Zdot:Sit,zdot:Tit,zeetrf:bit,ZeroWidthSpace:Cit,Zeta:Eit,zeta:kit,zfr:Pit,Zfr:Dit,ZHcy:Ait,zhcy:Lit,zigrarr:Oit,zopf:Mit,Zopf:Nit,Zscr:Rit,zscr:Iit,zwj:Vit,zwnj:qit},Fit="Á",Bit="á",zit="Â",jit="â",Uit="´",$it="Æ",Hit="æ",Git="À",Wit="à",Yit="&",Xit="&",Qit="Å",Kit="å",Zit="Ã",Jit="ã",tot="Ä",eot="ä",not="¦",rot="Ç",iot="ç",oot="¸",sot="¢",aot="©",lot="©",uot="¤",cot="°",fot="÷",dot="É",hot="é",pot="Ê",mot="ê",got="È",vot="è",yot="Ð",_ot="ð",wot="Ë",xot="ë",Sot="½",Tot="¼",bot="¾",Cot=">",Eot=">",kot="Í",Pot="í",Dot="Î",Aot="î",Lot="¡",Oot="Ì",Mot="ì",Not="¿",Rot="Ï",Iot="ï",Vot="«",qot="<",Fot="<",Bot="¯",zot="µ",jot="·",Uot=" ",$ot="¬",Hot="Ñ",Got="ñ",Wot="Ó",Yot="ó",Xot="Ô",Qot="ô",Kot="Ò",Zot="ò",Jot="ª",tst="º",est="Ø",nst="ø",rst="Õ",ist="õ",ost="Ö",sst="ö",ast="¶",lst="±",ust="£",cst='"',fst='"',dst="»",hst="®",pst="®",mst="§",gst="­",vst="¹",yst="²",_st="³",wst="ß",xst="Þ",Sst="þ",Tst="×",bst="Ú",Cst="ú",Est="Û",kst="û",Pst="Ù",Dst="ù",Ast="¨",Lst="Ü",Ost="ü",Mst="Ý",Nst="ý",Rst="¥",Ist="ÿ",Vst={Aacute:Fit,aacute:Bit,Acirc:zit,acirc:jit,acute:Uit,AElig:$it,aelig:Hit,Agrave:Git,agrave:Wit,amp:Yit,AMP:Xit,Aring:Qit,aring:Kit,Atilde:Zit,atilde:Jit,Auml:tot,auml:eot,brvbar:not,Ccedil:rot,ccedil:iot,cedil:oot,cent:sot,copy:aot,COPY:lot,curren:uot,deg:cot,divide:fot,Eacute:dot,eacute:hot,Ecirc:pot,ecirc:mot,Egrave:got,egrave:vot,ETH:yot,eth:_ot,Euml:wot,euml:xot,frac12:Sot,frac14:Tot,frac34:bot,gt:Cot,GT:Eot,Iacute:kot,iacute:Pot,Icirc:Dot,icirc:Aot,iexcl:Lot,Igrave:Oot,igrave:Mot,iquest:Not,Iuml:Rot,iuml:Iot,laquo:Vot,lt:qot,LT:Fot,macr:Bot,micro:zot,middot:jot,nbsp:Uot,not:$ot,Ntilde:Hot,ntilde:Got,Oacute:Wot,oacute:Yot,Ocirc:Xot,ocirc:Qot,Ograve:Kot,ograve:Zot,ordf:Jot,ordm:tst,Oslash:est,oslash:nst,Otilde:rst,otilde:ist,Ouml:ost,ouml:sst,para:ast,plusmn:lst,pound:ust,quot:cst,QUOT:fst,raquo:dst,reg:hst,REG:pst,sect:mst,shy:gst,sup1:vst,sup2:yst,sup3:_st,szlig:wst,THORN:xst,thorn:Sst,times:Tst,Uacute:bst,uacute:Cst,Ucirc:Est,ucirc:kst,Ugrave:Pst,ugrave:Dst,uml:Ast,Uuml:Lst,uuml:Ost,Yacute:Mst,yacute:Nst,yen:Rst,yuml:Ist},qst="&",Fst="'",Bst=">",zst="<",jst='"',tp={amp:qst,apos:Fst,gt:Bst,lt:zst,quot:jst};var G0={};const Ust={0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376};var $st=V&&V.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(G0,"__esModule",{value:!0});var B8=$st(Ust),Hst=String.fromCodePoint||function(e){var t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|e&1023),t+=String.fromCharCode(e),t};function Gst(e){return e>=55296&&e<=57343||e>1114111?"�":(e in B8.default&&(e=B8.default[e]),Hst(e))}G0.default=Gst;var xa=V&&V.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(dn,"__esModule",{value:!0});dn.decodeHTML=dn.decodeHTMLStrict=dn.decodeXML=void 0;var Yu=xa(Jh),Wst=xa(Vst),Yst=xa(tp),z8=xa(G0),Xst=/&(?:[a-zA-Z0-9]+|#[xX][\da-fA-F]+|#\d+);/g;dn.decodeXML=ep(Yst.default);dn.decodeHTMLStrict=ep(Yu.default);function ep(e){var t=np(e);return function(n){return String(n).replace(Xst,t)}}var j8=function(e,t){return e<t?1:-1};dn.decodeHTML=function(){for(var e=Object.keys(Wst.default).sort(j8),t=Object.keys(Yu.default).sort(j8),n=0,r=0;n<t.length;n++)e[r]===t[n]?(t[n]+=";?",r++):t[n]+=";";var i=new RegExp("&(?:"+t.join("|")+"|#[xX][\\da-fA-F]+;?|#\\d+;?)","g"),o=np(Yu.default);function s(a){return a.substr(-1)!==";"&&(a+=";"),o(a)}return function(a){return String(a).replace(i,s)}}();function np(e){return function(n){if(n.charAt(1)==="#"){var r=n.charAt(2);return r==="X"||r==="x"?z8.default(parseInt(n.substr(3),16)):z8.default(parseInt(n.substr(2),10))}return e[n.slice(1,-1)]||n}}var ue={},rp=V&&V.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ue,"__esModule",{value:!0});ue.escapeUTF8=ue.escape=ue.encodeNonAsciiHTML=ue.encodeHTML=ue.encodeXML=void 0;var Qst=rp(tp),ip=sp(Qst.default),op=ap(ip);ue.encodeXML=cp(ip);var Kst=rp(Jh),W0=sp(Kst.default),Zst=ap(W0);ue.encodeHTML=tat(W0,Zst);ue.encodeNonAsciiHTML=cp(W0);function sp(e){return Object.keys(e).sort().reduce(function(t,n){return t[e[n]]="&"+n+";",t},{})}function ap(e){for(var t=[],n=[],r=0,i=Object.keys(e);r<i.length;r++){var o=i[r];o.length===1?t.push("\\"+o):n.push(o)}t.sort();for(var s=0;s<t.length-1;s++){for(var a=s;a<t.length-1&&t[a].charCodeAt(1)+1===t[a+1].charCodeAt(1);)a+=1;var l=1+a-s;l<3||t.splice(s,l,t[s]+"-"+t[a])}return n.unshift("["+t.join("")+"]"),new RegExp(n.join("|"),"g")}var lp=/(?:[\x80-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/g,Jst=String.prototype.codePointAt!=null?function(e){return e.codePointAt(0)}:function(e){return(e.charCodeAt(0)-55296)*1024+e.charCodeAt(1)-56320+65536};function Sa(e){return"&#x"+(e.length>1?Jst(e):e.charCodeAt(0)).toString(16).toUpperCase()+";"}function tat(e,t){return function(n){return n.replace(t,function(r){return e[r]}).replace(lp,Sa)}}var up=new RegExp(op.source+"|"+lp.source,"g");function eat(e){return e.replace(up,Sa)}ue.escape=eat;function nat(e){return e.replace(op,Sa)}ue.escapeUTF8=nat;function cp(e){return function(t){return t.replace(up,function(n){return e[n]||Sa(n)})}}(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.decodeXMLStrict=e.decodeHTML5Strict=e.decodeHTML4Strict=e.decodeHTML5=e.decodeHTML4=e.decodeHTMLStrict=e.decodeHTML=e.decodeXML=e.encodeHTML5=e.encodeHTML4=e.escapeUTF8=e.escape=e.encodeNonAsciiHTML=e.encodeHTML=e.encodeXML=e.encode=e.decodeStrict=e.decode=void 0;var t=dn,n=ue;function r(l,u){return(!u||u<=0?t.decodeXML:t.decodeHTML)(l)}e.decode=r;function i(l,u){return(!u||u<=0?t.decodeXML:t.decodeHTMLStrict)(l)}e.decodeStrict=i;function o(l,u){return(!u||u<=0?n.encodeXML:n.encodeHTML)(l)}e.encode=o;var s=ue;Object.defineProperty(e,"encodeXML",{enumerable:!0,get:function(){return s.encodeXML}}),Object.defineProperty(e,"encodeHTML",{enumerable:!0,get:function(){return s.encodeHTML}}),Object.defineProperty(e,"encodeNonAsciiHTML",{enumerable:!0,get:function(){return s.encodeNonAsciiHTML}}),Object.defineProperty(e,"escape",{enumerable:!0,get:function(){return s.escape}}),Object.defineProperty(e,"escapeUTF8",{enumerable:!0,get:function(){return s.escapeUTF8}}),Object.defineProperty(e,"encodeHTML4",{enumerable:!0,get:function(){return s.encodeHTML}}),Object.defineProperty(e,"encodeHTML5",{enumerable:!0,get:function(){return s.encodeHTML}});var a=dn;Object.defineProperty(e,"decodeXML",{enumerable:!0,get:function(){return a.decodeXML}}),Object.defineProperty(e,"decodeHTML",{enumerable:!0,get:function(){return a.decodeHTML}}),Object.defineProperty(e,"decodeHTMLStrict",{enumerable:!0,get:function(){return a.decodeHTMLStrict}}),Object.defineProperty(e,"decodeHTML4",{enumerable:!0,get:function(){return a.decodeHTML}}),Object.defineProperty(e,"decodeHTML5",{enumerable:!0,get:function(){return a.decodeHTML}}),Object.defineProperty(e,"decodeHTML4Strict",{enumerable:!0,get:function(){return a.decodeHTMLStrict}}),Object.defineProperty(e,"decodeHTML5Strict",{enumerable:!0,get:function(){return a.decodeHTMLStrict}}),Object.defineProperty(e,"decodeXMLStrict",{enumerable:!0,get:function(){return a.decodeXML}})})(Zh);var L1={};Object.defineProperty(L1,"__esModule",{value:!0});L1.attributeNames=L1.elementNames=void 0;L1.elementNames=new Map([["altglyph","altGlyph"],["altglyphdef","altGlyphDef"],["altglyphitem","altGlyphItem"],["animatecolor","animateColor"],["animatemotion","animateMotion"],["animatetransform","animateTransform"],["clippath","clipPath"],["feblend","feBlend"],["fecolormatrix","feColorMatrix"],["fecomponenttransfer","feComponentTransfer"],["fecomposite","feComposite"],["feconvolvematrix","feConvolveMatrix"],["fediffuselighting","feDiffuseLighting"],["fedisplacementmap","feDisplacementMap"],["fedistantlight","feDistantLight"],["fedropshadow","feDropShadow"],["feflood","feFlood"],["fefunca","feFuncA"],["fefuncb","feFuncB"],["fefuncg","feFuncG"],["fefuncr","feFuncR"],["fegaussianblur","feGaussianBlur"],["feimage","feImage"],["femerge","feMerge"],["femergenode","feMergeNode"],["femorphology","feMorphology"],["feoffset","feOffset"],["fepointlight","fePointLight"],["fespecularlighting","feSpecularLighting"],["fespotlight","feSpotLight"],["fetile","feTile"],["feturbulence","feTurbulence"],["foreignobject","foreignObject"],["glyphref","glyphRef"],["lineargradient","linearGradient"],["radialgradient","radialGradient"],["textpath","textPath"]]);L1.attributeNames=new Map([["definitionurl","definitionURL"],["attributename","attributeName"],["attributetype","attributeType"],["basefrequency","baseFrequency"],["baseprofile","baseProfile"],["calcmode","calcMode"],["clippathunits","clipPathUnits"],["diffuseconstant","diffuseConstant"],["edgemode","edgeMode"],["filterunits","filterUnits"],["glyphref","glyphRef"],["gradienttransform","gradientTransform"],["gradientunits","gradientUnits"],["kernelmatrix","kernelMatrix"],["kernelunitlength","kernelUnitLength"],["keypoints","keyPoints"],["keysplines","keySplines"],["keytimes","keyTimes"],["lengthadjust","lengthAdjust"],["limitingconeangle","limitingConeAngle"],["markerheight","markerHeight"],["markerunits","markerUnits"],["markerwidth","markerWidth"],["maskcontentunits","maskContentUnits"],["maskunits","maskUnits"],["numoctaves","numOctaves"],["pathlength","pathLength"],["patterncontentunits","patternContentUnits"],["patterntransform","patternTransform"],["patternunits","patternUnits"],["pointsatx","pointsAtX"],["pointsaty","pointsAtY"],["pointsatz","pointsAtZ"],["preservealpha","preserveAlpha"],["preserveaspectratio","preserveAspectRatio"],["primitiveunits","primitiveUnits"],["refx","refX"],["refy","refY"],["repeatcount","repeatCount"],["repeatdur","repeatDur"],["requiredextensions","requiredExtensions"],["requiredfeatures","requiredFeatures"],["specularconstant","specularConstant"],["specularexponent","specularExponent"],["spreadmethod","spreadMethod"],["startoffset","startOffset"],["stddeviation","stdDeviation"],["stitchtiles","stitchTiles"],["surfacescale","surfaceScale"],["systemlanguage","systemLanguage"],["tablevalues","tableValues"],["targetx","targetX"],["targety","targetY"],["textlength","textLength"],["viewbox","viewBox"],["viewtarget","viewTarget"],["xchannelselector","xChannelSelector"],["ychannelselector","yChannelSelector"],["zoomandpan","zoomAndPan"]]);var o1=V&&V.__assign||function(){return o1=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++){t=arguments[n];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e},o1.apply(this,arguments)},rat=V&&V.__createBinding||(Object.create?function(e,t,n,r){r===void 0&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){r===void 0&&(r=n),e[r]=t[n]}),iat=V&&V.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),oat=V&&V.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)n!=="default"&&Object.prototype.hasOwnProperty.call(e,n)&&rat(t,e,n);return iat(t,e),t};Object.defineProperty($0,"__esModule",{value:!0});var en=oat(H0),fp=Zh,dp=L1,sat=new Set(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]);function aat(e,t){if(e)return Object.keys(e).map(function(n){var r,i,o=(r=e[n])!==null&&r!==void 0?r:"";return t.xmlMode==="foreign"&&(n=(i=dp.attributeNames.get(n))!==null&&i!==void 0?i:n),!t.emptyAttrs&&!t.xmlMode&&o===""?n:n+'="'+(t.decodeEntities!==!1?fp.encodeXML(o):o.replace(/"/g,"&quot;"))+'"'}).join(" ")}var U8=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);function Y0(e,t){t===void 0&&(t={});for(var n=("length"in e)?e:[e],r="",i=0;i<n.length;i++)r+=lat(n[i],t);return r}$0.default=Y0;function lat(e,t){switch(e.type){case en.Root:return Y0(e.children,t);case en.Directive:case en.Doctype:return dat(e);case en.Comment:return mat(e);case en.CDATA:return pat(e);case en.Script:case en.Style:case en.Tag:return fat(e,t);case en.Text:return hat(e,t)}}var uat=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]),cat=new Set(["svg","math"]);function fat(e,t){var n;t.xmlMode==="foreign"&&(e.name=(n=dp.elementNames.get(e.name))!==null&&n!==void 0?n:e.name,e.parent&&uat.has(e.parent.name)&&(t=o1(o1({},t),{xmlMode:!1}))),!t.xmlMode&&cat.has(e.name)&&(t=o1(o1({},t),{xmlMode:"foreign"}));var r="<"+e.name,i=aat(e.attribs,t);return i&&(r+=" "+i),e.children.length===0&&(t.xmlMode?t.selfClosingTags!==!1:t.selfClosingTags&&U8.has(e.name))?(t.xmlMode||(r+=" "),r+="/>"):(r+=">",e.children.length>0&&(r+=Y0(e.children,t)),(t.xmlMode||!U8.has(e.name))&&(r+="</"+e.name+">")),r}function dat(e){return"<"+e.data+">"}function hat(e,t){var n=e.data||"";return t.decodeEntities!==!1&&!(!t.xmlMode&&e.parent&&sat.has(e.parent.name))&&(n=fp.encodeXML(n)),n}function pat(e){return"<![CDATA["+e.children[0].data+"]]>"}function mat(e){return"<!--"+e.data+"-->"}var gat=V&&V.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(le,"__esModule",{value:!0});le.innerText=le.textContent=le.getText=le.getInnerHTML=le.getOuterHTML=void 0;var Ke=wn,vat=gat($0),yat=H0;function hp(e,t){return(0,vat.default)(e,t)}le.getOuterHTML=hp;function _at(e,t){return(0,Ke.hasChildren)(e)?e.children.map(function(n){return hp(n,t)}).join(""):""}le.getInnerHTML=_at;function Jo(e){return Array.isArray(e)?e.map(Jo).join(""):(0,Ke.isTag)(e)?e.name==="br"?`
`:Jo(e.children):(0,Ke.isCDATA)(e)?Jo(e.children):(0,Ke.isText)(e)?e.data:""}le.getText=Jo;function Xu(e){return Array.isArray(e)?e.map(Xu).join(""):(0,Ke.hasChildren)(e)&&!(0,Ke.isComment)(e)?Xu(e.children):(0,Ke.isText)(e)?e.data:""}le.textContent=Xu;function Qu(e){return Array.isArray(e)?e.map(Qu).join(""):(0,Ke.hasChildren)(e)&&(e.type===yat.ElementType.Tag||(0,Ke.isCDATA)(e))?Qu(e.children):(0,Ke.isText)(e)?e.data:""}le.innerText=Qu;var St={};Object.defineProperty(St,"__esModule",{value:!0});St.prevElementSibling=St.nextElementSibling=St.getName=St.hasAttrib=St.getAttributeValue=St.getSiblings=St.getParent=St.getChildren=void 0;var pp=wn,wat=[];function mp(e){var t;return(t=e.children)!==null&&t!==void 0?t:wat}St.getChildren=mp;function gp(e){return e.parent||null}St.getParent=gp;function xat(e){var t,n,r=gp(e);if(r!=null)return mp(r);for(var i=[e],o=e.prev,s=e.next;o!=null;)i.unshift(o),t=o,o=t.prev;for(;s!=null;)i.push(s),n=s,s=n.next;return i}St.getSiblings=xat;function Sat(e,t){var n;return(n=e.attribs)===null||n===void 0?void 0:n[t]}St.getAttributeValue=Sat;function Tat(e,t){return e.attribs!=null&&Object.prototype.hasOwnProperty.call(e.attribs,t)&&e.attribs[t]!=null}St.hasAttrib=Tat;function bat(e){return e.name}St.getName=bat;function Cat(e){for(var t,n=e.next;n!==null&&!(0,pp.isTag)(n);)t=n,n=t.next;return n}St.nextElementSibling=Cat;function Eat(e){for(var t,n=e.prev;n!==null&&!(0,pp.isTag)(n);)t=n,n=t.prev;return n}St.prevElementSibling=Eat;var Xt={};Object.defineProperty(Xt,"__esModule",{value:!0});Xt.prepend=Xt.prependChild=Xt.append=Xt.appendChild=Xt.replaceElement=Xt.removeElement=void 0;function ao(e){if(e.prev&&(e.prev.next=e.next),e.next&&(e.next.prev=e.prev),e.parent){var t=e.parent.children;t.splice(t.lastIndexOf(e),1)}}Xt.removeElement=ao;function kat(e,t){var n=t.prev=e.prev;n&&(n.next=t);var r=t.next=e.next;r&&(r.prev=t);var i=t.parent=e.parent;if(i){var o=i.children;o[o.lastIndexOf(e)]=t}}Xt.replaceElement=kat;function Pat(e,t){if(ao(t),t.next=null,t.parent=e,e.children.push(t)>1){var n=e.children[e.children.length-2];n.next=t,t.prev=n}else t.prev=null}Xt.appendChild=Pat;function Dat(e,t){ao(t);var n=e.parent,r=e.next;if(t.next=r,t.prev=e,e.next=t,t.parent=n,r){if(r.prev=t,n){var i=n.children;i.splice(i.lastIndexOf(r),0,t)}}else n&&n.children.push(t)}Xt.append=Dat;function Aat(e,t){if(ao(t),t.parent=e,t.prev=null,e.children.unshift(t)!==1){var n=e.children[1];n.prev=t,t.next=n}else t.next=null}Xt.prependChild=Aat;function Lat(e,t){ao(t);var n=e.parent;if(n){var r=n.children;r.splice(r.indexOf(e),0,t)}e.prev&&(e.prev.next=t),t.parent=n,t.prev=e.prev,t.next=e,e.prev=t}Xt.prepend=Lat;var Bt={};Object.defineProperty(Bt,"__esModule",{value:!0});Bt.findAll=Bt.existsOne=Bt.findOne=Bt.findOneChild=Bt.find=Bt.filter=void 0;var Qi=wn;function Oat(e,t,n,r){return n===void 0&&(n=!0),r===void 0&&(r=1/0),Array.isArray(t)||(t=[t]),X0(e,t,n,r)}Bt.filter=Oat;function X0(e,t,n,r){for(var i=[],o=0,s=t;o<s.length;o++){var a=s[o];if(e(a)&&(i.push(a),--r<=0))break;if(n&&(0,Qi.hasChildren)(a)&&a.children.length>0){var l=X0(e,a.children,n,r);if(i.push.apply(i,l),r-=l.length,r<=0)break}}return i}Bt.find=X0;function Mat(e,t){return t.find(e)}Bt.findOneChild=Mat;function vp(e,t,n){n===void 0&&(n=!0);for(var r=null,i=0;i<t.length&&!r;i++){var o=t[i];if((0,Qi.isTag)(o))e(o)?r=o:n&&o.children.length>0&&(r=vp(e,o.children));else continue}return r}Bt.findOne=vp;function yp(e,t){return t.some(function(n){return(0,Qi.isTag)(n)&&(e(n)||n.children.length>0&&yp(e,n.children))})}Bt.existsOne=yp;function Nat(e,t){for(var n,r=[],i=t.filter(Qi.isTag),o;o=i.shift();){var s=(n=o.children)===null||n===void 0?void 0:n.filter(Qi.isTag);s&&s.length>0&&i.unshift.apply(i,s),e(o)&&r.push(o)}return r}Bt.findAll=Nat;var ce={};Object.defineProperty(ce,"__esModule",{value:!0});ce.getElementsByTagType=ce.getElementsByTagName=ce.getElementById=ce.getElements=ce.testElement=void 0;var fr=wn,Ta=Bt,js={tag_name:function(e){return typeof e=="function"?function(t){return(0,fr.isTag)(t)&&e(t.name)}:e==="*"?fr.isTag:function(t){return(0,fr.isTag)(t)&&t.name===e}},tag_type:function(e){return typeof e=="function"?function(t){return e(t.type)}:function(t){return t.type===e}},tag_contains:function(e){return typeof e=="function"?function(t){return(0,fr.isText)(t)&&e(t.data)}:function(t){return(0,fr.isText)(t)&&t.data===e}}};function _p(e,t){return typeof t=="function"?function(n){return(0,fr.isTag)(n)&&t(n.attribs[e])}:function(n){return(0,fr.isTag)(n)&&n.attribs[e]===t}}function Rat(e,t){return function(n){return e(n)||t(n)}}function wp(e){var t=Object.keys(e).map(function(n){var r=e[n];return Object.prototype.hasOwnProperty.call(js,n)?js[n](r):_p(n,r)});return t.length===0?null:t.reduce(Rat)}function Iat(e,t){var n=wp(e);return n?n(t):!0}ce.testElement=Iat;function Vat(e,t,n,r){r===void 0&&(r=1/0);var i=wp(e);return i?(0,Ta.filter)(i,t,n,r):[]}ce.getElements=Vat;function qat(e,t,n){return n===void 0&&(n=!0),Array.isArray(t)||(t=[t]),(0,Ta.findOne)(_p("id",e),t,n)}ce.getElementById=qat;function Fat(e,t,n,r){return n===void 0&&(n=!0),r===void 0&&(r=1/0),(0,Ta.filter)(js.tag_name(e),t,n,r)}ce.getElementsByTagName=Fat;function Bat(e,t,n,r){return n===void 0&&(n=!0),r===void 0&&(r=1/0),(0,Ta.filter)(js.tag_type(e),t,n,r)}ce.getElementsByTagType=Bat;var $n={};Object.defineProperty($n,"__esModule",{value:!0});$n.uniqueSort=$n.compareDocumentPosition=$n.removeSubsets=void 0;var $8=wn;function zat(e){for(var t=e.length;--t>=0;){var n=e[t];if(t>0&&e.lastIndexOf(n,t-1)>=0){e.splice(t,1);continue}for(var r=n.parent;r;r=r.parent)if(e.includes(r)){e.splice(t,1);break}}return e}$n.removeSubsets=zat;function xp(e,t){var n=[],r=[];if(e===t)return 0;for(var i=(0,$8.hasChildren)(e)?e:e.parent;i;)n.unshift(i),i=i.parent;for(i=(0,$8.hasChildren)(t)?t:t.parent;i;)r.unshift(i),i=i.parent;for(var o=Math.min(n.length,r.length),s=0;s<o&&n[s]===r[s];)s++;if(s===0)return 1;var a=n[s-1],l=a.children,u=n[s],c=r[s];return l.indexOf(u)>l.indexOf(c)?a===t?20:4:a===e?10:2}$n.compareDocumentPosition=xp;function jat(e){return e=e.filter(function(t,n,r){return!r.includes(t,n+1)}),e.sort(function(t,n){var r=xp(t,n);return r&2?-1:r&4?1:0}),e}$n.uniqueSort=jat;var ba={};Object.defineProperty(ba,"__esModule",{value:!0});ba.getFeed=void 0;var Uat=le,lo=ce;function $at(e){var t=Us(Xat,e);return t?t.name==="feed"?Hat(t):Gat(t):null}ba.getFeed=$at;function Hat(e){var t,n=e.children,r={type:"atom",items:(0,lo.getElementsByTagName)("entry",n).map(function(s){var a,l=s.children,u={media:Sp(l)};Wt(u,"id","id",l),Wt(u,"title","title",l);var c=(a=Us("link",l))===null||a===void 0?void 0:a.attribs.href;c&&(u.link=c);var f=vr("summary",l)||vr("content",l);f&&(u.description=f);var d=vr("updated",l);return d&&(u.pubDate=new Date(d)),u})};Wt(r,"id","id",n),Wt(r,"title","title",n);var i=(t=Us("link",n))===null||t===void 0?void 0:t.attribs.href;i&&(r.link=i),Wt(r,"description","subtitle",n);var o=vr("updated",n);return o&&(r.updated=new Date(o)),Wt(r,"author","email",n,!0),r}function Gat(e){var t,n,r=(n=(t=Us("channel",e.children))===null||t===void 0?void 0:t.children)!==null&&n!==void 0?n:[],i={type:e.name.substr(0,3),id:"",items:(0,lo.getElementsByTagName)("item",e.children).map(function(s){var a=s.children,l={media:Sp(a)};Wt(l,"id","guid",a),Wt(l,"title","title",a),Wt(l,"link","link",a),Wt(l,"description","description",a);var u=vr("pubDate",a);return u&&(l.pubDate=new Date(u)),l})};Wt(i,"title","title",r),Wt(i,"link","link",r),Wt(i,"description","description",r);var o=vr("lastBuildDate",r);return o&&(i.updated=new Date(o)),Wt(i,"author","managingEditor",r,!0),i}var Wat=["url","type","lang"],Yat=["fileSize","bitrate","framerate","samplingrate","channels","duration","height","width"];function Sp(e){return(0,lo.getElementsByTagName)("media:content",e).map(function(t){for(var n=t.attribs,r={medium:n.medium,isDefault:!!n.isDefault},i=0,o=Wat;i<o.length;i++){var s=o[i];n[s]&&(r[s]=n[s])}for(var a=0,l=Yat;a<l.length;a++){var s=l[a];n[s]&&(r[s]=parseInt(n[s],10))}return n.expression&&(r.expression=n.expression),r})}function Us(e,t){return(0,lo.getElementsByTagName)(e,t,!0,1)[0]}function vr(e,t,n){return n===void 0&&(n=!1),(0,Uat.textContent)((0,lo.getElementsByTagName)(e,t,n,1)).trim()}function Wt(e,t,n,r,i){i===void 0&&(i=!1);var o=vr(n,r,i);o&&(e[t]=o)}function Xat(e){return e==="rss"||e==="feed"||e==="rdf:RDF"}(function(e){var t=V&&V.__createBinding||(Object.create?function(i,o,s,a){a===void 0&&(a=s),Object.defineProperty(i,a,{enumerable:!0,get:function(){return o[s]}})}:function(i,o,s,a){a===void 0&&(a=s),i[a]=o[s]}),n=V&&V.__exportStar||function(i,o){for(var s in i)s!=="default"&&!Object.prototype.hasOwnProperty.call(o,s)&&t(o,i,s)};Object.defineProperty(e,"__esModule",{value:!0}),e.hasChildren=e.isDocument=e.isComment=e.isText=e.isCDATA=e.isTag=void 0,n(le,e),n(St,e),n(Xt,e),n(Bt,e),n(ce,e),n($n,e),n(ba,e);var r=wn;Object.defineProperty(e,"isTag",{enumerable:!0,get:function(){return r.isTag}}),Object.defineProperty(e,"isCDATA",{enumerable:!0,get:function(){return r.isCDATA}}),Object.defineProperty(e,"isText",{enumerable:!0,get:function(){return r.isText}}),Object.defineProperty(e,"isComment",{enumerable:!0,get:function(){return r.isComment}}),Object.defineProperty(e,"isDocument",{enumerable:!0,get:function(){return r.isDocument}}),Object.defineProperty(e,"hasChildren",{enumerable:!0,get:function(){return r.hasChildren}})})(U0);(function(e){var t=V&&V.__extends||function(){var l=function(u,c){return l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,d){f.__proto__=d}||function(f,d){for(var p in d)Object.prototype.hasOwnProperty.call(d,p)&&(f[p]=d[p])},l(u,c)};return function(u,c){if(typeof c!="function"&&c!==null)throw new TypeError("Class extends value "+String(c)+" is not a constructor or null");l(u,c);function f(){this.constructor=u}u.prototype=c===null?Object.create(c):(f.prototype=c.prototype,new f)}}(),n=V&&V.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(e,"__esModule",{value:!0}),e.parseFeed=e.FeedHandler=e.getFeed=void 0;var r=n(wn),i=U0;Object.defineProperty(e,"getFeed",{enumerable:!0,get:function(){return i.getFeed}});var o=so,s=function(l){t(u,l);function u(c,f){var d=this;return typeof c=="object"&&(c=void 0,f=c),d=l.call(this,c,f)||this,d}return u.prototype.onend=function(){var c=(0,i.getFeed)(this.dom);c?(this.feed=c,this.handleCallback(null)):this.handleCallback(new Error("couldn't find root of feed"))},u}(r.default);e.FeedHandler=s;function a(l,u){u===void 0&&(u={xmlMode:!0});var c=new r.default(null,u);return new o.Parser(c,u).end(l),(0,i.getFeed)(c.dom)}e.parseFeed=a})(Wu);(function(e){var t=V&&V.__createBinding||(Object.create?function(v,h,_,m){m===void 0&&(m=_),Object.defineProperty(v,m,{enumerable:!0,get:function(){return h[_]}})}:function(v,h,_,m){m===void 0&&(m=_),v[m]=h[_]}),n=V&&V.__setModuleDefault||(Object.create?function(v,h){Object.defineProperty(v,"default",{enumerable:!0,value:h})}:function(v,h){v.default=h}),r=V&&V.__importStar||function(v){if(v&&v.__esModule)return v;var h={};if(v!=null)for(var _ in v)_!=="default"&&Object.prototype.hasOwnProperty.call(v,_)&&t(h,v,_);return n(h,v),h},i=V&&V.__exportStar||function(v,h){for(var _ in v)_!=="default"&&!Object.prototype.hasOwnProperty.call(h,_)&&t(h,v,_)},o=V&&V.__importDefault||function(v){return v&&v.__esModule?v:{default:v}};Object.defineProperty(e,"__esModule",{value:!0}),e.RssHandler=e.DefaultHandler=e.DomUtils=e.ElementType=e.Tokenizer=e.createDomStream=e.parseDOM=e.parseDocument=e.DomHandler=e.Parser=void 0;var s=so;Object.defineProperty(e,"Parser",{enumerable:!0,get:function(){return s.Parser}});var a=wn;Object.defineProperty(e,"DomHandler",{enumerable:!0,get:function(){return a.DomHandler}}),Object.defineProperty(e,"DefaultHandler",{enumerable:!0,get:function(){return a.DomHandler}});function l(v,h){var _=new a.DomHandler(void 0,h);return new s.Parser(_,h).end(v),_.root}e.parseDocument=l;function u(v,h){return l(v,h).children}e.parseDOM=u;function c(v,h,_){var m=new a.DomHandler(v,h,_);return new s.Parser(m,h)}e.createDomStream=c;var f=va;Object.defineProperty(e,"Tokenizer",{enumerable:!0,get:function(){return o(f).default}});var d=r(Kh);e.ElementType=d,i(Wu,e),e.DomUtils=r(U0);var p=Wu;Object.defineProperty(e,"RssHandler",{enumerable:!0,get:function(){return p.FeedHandler}})})(qh);var Qat=(e,t)=>{var[n,r]=K.useState(null);return K.useEffect(()=>{if(!e){r(null);return}var i=!1;function o(){return s.apply(this,arguments)}function s(){return s=k4(function*(){try{var a=yield fetch(e).then(l=>l.text()).then(l=>qh.parseFeed(l));i||r(a)}catch(l){console.error("Unable to get data from ".concat(e),l)}}),s.apply(this,arguments)}return o(),()=>{i=!0}},[e]),n},Kat={exports:{}};(function(e){(function(){function t(h,_){document.addEventListener?h.addEventListener("scroll",_,!1):h.attachEvent("scroll",_)}function n(h){document.body?h():document.addEventListener?document.addEventListener("DOMContentLoaded",function _(){document.removeEventListener("DOMContentLoaded",_),h()}):document.attachEvent("onreadystatechange",function _(){(document.readyState=="interactive"||document.readyState=="complete")&&(document.detachEvent("onreadystatechange",_),h())})}function r(h){this.g=document.createElement("div"),this.g.setAttribute("aria-hidden","true"),this.g.appendChild(document.createTextNode(h)),this.h=document.createElement("span"),this.i=document.createElement("span"),this.m=document.createElement("span"),this.j=document.createElement("span"),this.l=-1,this.h.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.i.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.j.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.m.style.cssText="display:inline-block;width:200%;height:200%;font-size:16px;max-width:none;",this.h.appendChild(this.m),this.i.appendChild(this.j),this.g.appendChild(this.h),this.g.appendChild(this.i)}function i(h,_){h.g.style.cssText="max-width:none;min-width:20px;min-height:20px;display:inline-block;overflow:hidden;position:absolute;width:auto;margin:0;padding:0;top:-999px;white-space:nowrap;font-synthesis:none;font:"+_+";"}function o(h){var _=h.g.offsetWidth,m=_+100;return h.j.style.width=m+"px",h.i.scrollLeft=m,h.h.scrollLeft=h.h.scrollWidth+100,h.l!==_?(h.l=_,!0):!1}function s(h,_){function m(){var y=g;o(y)&&y.g.parentNode!==null&&_(y.l)}var g=h;t(h.h,m),t(h.i,m),o(h)}function a(h,_,m){_=_||{},m=m||window,this.family=h,this.style=_.style||"normal",this.weight=_.weight||"normal",this.stretch=_.stretch||"normal",this.context=m}var l=null,u=null,c=null,f=null;function d(h){return u===null&&(p(h)&&/Apple/.test(window.navigator.vendor)?(h=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))(?:\.([0-9]+))/.exec(window.navigator.userAgent),u=!!h&&603>parseInt(h[1],10)):u=!1),u}function p(h){return f===null&&(f=!!h.document.fonts),f}function v(h,_){var m=h.style,g=h.weight;if(c===null){var y=document.createElement("div");try{y.style.font="condensed 100px sans-serif"}catch{}c=y.style.font!==""}return[m,g,c?h.stretch:"","100px",_].join(" ")}a.prototype.load=function(h,_){var m=this,g=h||"BESbswy",y=0,w=_||3e3,x=new Date().getTime();return new Promise(function(S,T){if(p(m.context)&&!d(m.context)){var b=new Promise(function(E,L){function O(){new Date().getTime()-x>=w?L(Error(""+w+"ms timeout exceeded")):m.context.document.fonts.load(v(m,'"'+m.family+'"'),g).then(function(B){1<=B.length?E():setTimeout(O,25)},L)}O()}),C=new Promise(function(E,L){y=setTimeout(function(){L(Error(""+w+"ms timeout exceeded"))},w)});Promise.race([C,b]).then(function(){clearTimeout(y),S(m)},T)}else n(function(){function E(){var q;(q=I!=-1&&F!=-1||I!=-1&&z!=-1||F!=-1&&z!=-1)&&((q=I!=F&&I!=z&&F!=z)||(l===null&&(q=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))/.exec(window.navigator.userAgent),l=!!q&&(536>parseInt(q[1],10)||parseInt(q[1],10)===536&&11>=parseInt(q[2],10))),q=l&&(I==P&&F==P&&z==P||I==M&&F==M&&z==M||I==N&&F==N&&z==N)),q=!q),q&&(j.parentNode!==null&&j.parentNode.removeChild(j),clearTimeout(y),S(m))}function L(){if(new Date().getTime()-x>=w)j.parentNode!==null&&j.parentNode.removeChild(j),T(Error(""+w+"ms timeout exceeded"));else{var q=m.context.document.hidden;(q===!0||q===void 0)&&(I=O.g.offsetWidth,F=B.g.offsetWidth,z=G.g.offsetWidth,E()),y=setTimeout(L,50)}}var O=new r(g),B=new r(g),G=new r(g),I=-1,F=-1,z=-1,P=-1,M=-1,N=-1,j=document.createElement("div");j.dir="ltr",i(O,v(m,"sans-serif")),i(B,v(m,"serif")),i(G,v(m,"monospace")),j.appendChild(O.g),j.appendChild(B.g),j.appendChild(G.g),m.context.document.body.appendChild(j),P=O.g.offsetWidth,M=B.g.offsetWidth,N=G.g.offsetWidth,L(),s(O,function(q){I=q,E()}),i(O,v(m,'"'+m.family+'",sans-serif')),s(B,function(q){F=q,E()}),i(B,v(m,'"'+m.family+'",serif')),s(G,function(q){z=q,E()}),i(G,v(m,'"'+m.family+'",monospace'))})})},e.exports=a})()})(Kat);function Zat(){var r;const{isPlaying:e}=_6(),{feed:t}=w6();console.log("Feed URL:",t);const n=Qat(t);return console.log("RSS Feed Data:",n),qr.jsx(M4,{hide:!e,onPlay:i=>{i.from(".tickerFeed",{y:100,duration:.8})},onStop:i=>{i.to(".tickerFeed",{y:100,duration:.8})},children:qr.jsx("div",{className:"tickerFeed",children:qr.jsx("div",{className:"tickerFeed-content",children:(r=n==null?void 0:n.items)!=null&&r.length?qr.jsx(nw,{play:e,items:n.items.slice(0,10),renderItem:({id:i,title:o})=>qr.jsx("div",{className:"tickerFeed-item",children:o},i)}):qr.jsx("div",{children:"No data available"})})})})}cm(Zat);

</script>
    <style>
.tickerFeed{position:fixed;bottom:0;width:100%;height:7.5%;background-color:#1e2832;overflow:hidden;padding-top:10px}.tickerFeed-content{display:flex;align-items:center;font-size:40px;font-family:Arial,Helvetica,sans-serif;color:#fff}.tickerFeed-item{margin-right:50px;white-space:nowrap}

</style>
  </head>
  <body>
  </body>
</html>
