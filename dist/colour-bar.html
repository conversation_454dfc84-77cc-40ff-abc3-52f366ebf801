<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <script type="module" crossorigin>
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function Bf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Uf={exports:{}},_i={},$f={exports:{}},N={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jr=Symbol.for("react.element"),Mm=Symbol.for("react.portal"),Nm=Symbol.for("react.fragment"),Om=Symbol.for("react.strict_mode"),Fm=Symbol.for("react.profiler"),zm=Symbol.for("react.provider"),Im=Symbol.for("react.context"),jm=Symbol.for("react.forward_ref"),Bm=Symbol.for("react.suspense"),Um=Symbol.for("react.memo"),$m=Symbol.for("react.lazy"),xu=Symbol.iterator;function Wm(e){return e===null||typeof e!="object"?null:(e=xu&&e[xu]||e["@@iterator"],typeof e=="function"?e:null)}var Wf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Hf=Object.assign,Kf={};function Gn(e,t,n){this.props=e,this.context=t,this.refs=Kf,this.updater=n||Wf}Gn.prototype.isReactComponent={};Gn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Gn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Qf(){}Qf.prototype=Gn.prototype;function Xl(e,t,n){this.props=e,this.context=t,this.refs=Kf,this.updater=n||Wf}var Zl=Xl.prototype=new Qf;Zl.constructor=Xl;Hf(Zl,Gn.prototype);Zl.isPureReactComponent=!0;var Su=Array.isArray,Gf=Object.prototype.hasOwnProperty,Jl={current:null},Yf={key:!0,ref:!0,__self:!0,__source:!0};function Xf(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)Gf.call(t,r)&&!Yf.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)o[r]===void 0&&(o[r]=l[r]);return{$$typeof:Jr,type:e,key:i,ref:s,props:o,_owner:Jl.current}}function Hm(e,t){return{$$typeof:Jr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ql(e){return typeof e=="object"&&e!==null&&e.$$typeof===Jr}function Km(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Cu=/\/+/g;function qi(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Km(""+e.key):t.toString(36)}function Do(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Jr:case Mm:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+qi(s,0):r,Su(o)?(n="",e!=null&&(n=e.replace(Cu,"$&/")+"/"),Do(o,t,n,"",function(u){return u})):o!=null&&(ql(o)&&(o=Hm(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(Cu,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",Su(e))for(var l=0;l<e.length;l++){i=e[l];var a=r+qi(i,l);s+=Do(i,t,n,a,o)}else if(a=Wm(e),typeof a=="function")for(e=a.call(e),l=0;!(i=e.next()).done;)i=i.value,a=r+qi(i,l++),s+=Do(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function ao(e,t,n){if(e==null)return e;var r=[],o=0;return Do(e,r,"","",function(i){return t.call(n,i,o++)}),r}function Qm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var xe={current:null},Ro={transition:null},Gm={ReactCurrentDispatcher:xe,ReactCurrentBatchConfig:Ro,ReactCurrentOwner:Jl};function Zf(){throw Error("act(...) is not supported in production builds of React.")}N.Children={map:ao,forEach:function(e,t,n){ao(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ao(e,function(){t++}),t},toArray:function(e){return ao(e,function(t){return t})||[]},only:function(e){if(!ql(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};N.Component=Gn;N.Fragment=Nm;N.Profiler=Fm;N.PureComponent=Xl;N.StrictMode=Om;N.Suspense=Bm;N.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Gm;N.act=Zf;N.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Hf({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=Jl.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)Gf.call(t,a)&&!Yf.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:Jr,type:e.type,key:o,ref:i,props:r,_owner:s}};N.createContext=function(e){return e={$$typeof:Im,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:zm,_context:e},e.Consumer=e};N.createElement=Xf;N.createFactory=function(e){var t=Xf.bind(null,e);return t.type=e,t};N.createRef=function(){return{current:null}};N.forwardRef=function(e){return{$$typeof:jm,render:e}};N.isValidElement=ql;N.lazy=function(e){return{$$typeof:$m,_payload:{_status:-1,_result:e},_init:Qm}};N.memo=function(e,t){return{$$typeof:Um,type:e,compare:t===void 0?null:t}};N.startTransition=function(e){var t=Ro.transition;Ro.transition={};try{e()}finally{Ro.transition=t}};N.unstable_act=Zf;N.useCallback=function(e,t){return xe.current.useCallback(e,t)};N.useContext=function(e){return xe.current.useContext(e)};N.useDebugValue=function(){};N.useDeferredValue=function(e){return xe.current.useDeferredValue(e)};N.useEffect=function(e,t){return xe.current.useEffect(e,t)};N.useId=function(){return xe.current.useId()};N.useImperativeHandle=function(e,t,n){return xe.current.useImperativeHandle(e,t,n)};N.useInsertionEffect=function(e,t){return xe.current.useInsertionEffect(e,t)};N.useLayoutEffect=function(e,t){return xe.current.useLayoutEffect(e,t)};N.useMemo=function(e,t){return xe.current.useMemo(e,t)};N.useReducer=function(e,t,n){return xe.current.useReducer(e,t,n)};N.useRef=function(e){return xe.current.useRef(e)};N.useState=function(e){return xe.current.useState(e)};N.useSyncExternalStore=function(e,t,n){return xe.current.useSyncExternalStore(e,t,n)};N.useTransition=function(){return xe.current.useTransition()};N.version="18.3.1";$f.exports=N;var S=$f.exports;const Le=Bf(S);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ym=S,Xm=Symbol.for("react.element"),Zm=Symbol.for("react.fragment"),Jm=Object.prototype.hasOwnProperty,qm=Ym.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,bm={key:!0,ref:!0,__self:!0,__source:!0};function Jf(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Jm.call(t,r)&&!bm.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Xm,type:e,key:i,ref:s,props:o,_owner:qm.current}}_i.Fragment=Zm;_i.jsx=Jf;_i.jsxs=Jf;Uf.exports=_i;var Ht=Uf.exports,Ve={loading:0,loaded:1,playing:2,paused:3,stopped:4,removed:5},qf={exports:{}},Oe={},bf={exports:{}},ed={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,D){var R=T.length;T.push(D);e:for(;0<R;){var F=R-1>>>1,M=T[F];if(0<o(M,D))T[F]=D,T[R]=M,R=F;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var D=T[0],R=T.pop();if(R!==D){T[0]=R;e:for(var F=0,M=T.length,so=M>>>1;F<so;){var $t=2*(F+1)-1,Ji=T[$t],Wt=$t+1,lo=T[Wt];if(0>o(Ji,R))Wt<M&&0>o(lo,Ji)?(T[F]=lo,T[Wt]=R,F=Wt):(T[F]=Ji,T[$t]=R,F=$t);else if(Wt<M&&0>o(lo,R))T[F]=lo,T[Wt]=R,F=Wt;else break e}}return D}function o(T,D){var R=T.sortIndex-D.sortIndex;return R!==0?R:T.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],c=1,f=null,d=3,v=!1,g=!1,y=!1,x=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(T){for(var D=n(u);D!==null;){if(D.callback===null)r(u);else if(D.startTime<=T)r(u),D.sortIndex=D.expirationTime,t(a,D);else break;D=n(u)}}function w(T){if(y=!1,m(T),!g)if(n(a)!==null)g=!0,W(C);else{var D=n(u);D!==null&&ee(w,D.startTime-T)}}function C(T,D){g=!1,y&&(y=!1,p(_),_=-1),v=!0;var R=d;try{for(m(D),f=n(a);f!==null&&(!(f.expirationTime>D)||T&&!I());){var F=f.callback;if(typeof F=="function"){f.callback=null,d=f.priorityLevel;var M=F(f.expirationTime<=D);D=e.unstable_now(),typeof M=="function"?f.callback=M:f===n(a)&&r(a),m(D)}else r(a);f=n(a)}if(f!==null)var so=!0;else{var $t=n(u);$t!==null&&ee(w,$t.startTime-D),so=!1}return so}finally{f=null,d=R,v=!1}}var E=!1,P=null,_=-1,O=5,A=-1;function I(){return!(e.unstable_now()-A<O)}function b(){if(P!==null){var T=e.unstable_now();A=T;var D=!0;try{D=P(!0,T)}finally{D?ie():(E=!1,P=null)}}else E=!1}var ie;if(typeof h=="function")ie=function(){h(b)};else if(typeof MessageChannel<"u"){var ve=new MessageChannel,j=ve.port2;ve.port1.onmessage=b,ie=function(){j.postMessage(null)}}else ie=function(){x(b,0)};function W(T){P=T,E||(E=!0,ie())}function ee(T,D){_=x(function(){T(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){g||v||(g=!0,W(C))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(T){switch(d){case 1:case 2:case 3:var D=3;break;default:D=d}var R=d;d=D;try{return T()}finally{d=R}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,D){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var R=d;d=T;try{return D()}finally{d=R}},e.unstable_scheduleCallback=function(T,D,R){var F=e.unstable_now();switch(typeof R=="object"&&R!==null?(R=R.delay,R=typeof R=="number"&&0<R?F+R:F):R=F,T){case 1:var M=-1;break;case 2:M=250;break;case 5:M=**********;break;case 4:M=1e4;break;default:M=5e3}return M=R+M,T={id:c++,callback:D,priorityLevel:T,startTime:R,expirationTime:M,sortIndex:-1},R>F?(T.sortIndex=R,t(u,T),n(a)===null&&T===n(u)&&(y?(p(_),_=-1):y=!0,ee(w,R-F))):(T.sortIndex=M,t(a,T),g||v||(g=!0,W(C))),T},e.unstable_shouldYield=I,e.unstable_wrapCallback=function(T){var D=d;return function(){var R=d;d=D;try{return T.apply(this,arguments)}finally{d=R}}}})(ed);bf.exports=ed;var e0=bf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var t0=S,Me=e0;function k(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var td=new Set,Ar={};function an(e,t){In(e,t),In(e+"Capture",t)}function In(e,t){for(Ar[e]=t,e=0;e<t.length;e++)td.add(t[e])}var dt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Is=Object.prototype.hasOwnProperty,n0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ku={},Eu={};function r0(e){return Is.call(Eu,e)?!0:Is.call(ku,e)?!1:n0.test(e)?Eu[e]=!0:(ku[e]=!0,!1)}function o0(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function i0(e,t,n,r){if(t===null||typeof t>"u"||o0(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Se(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var fe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){fe[e]=new Se(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];fe[t]=new Se(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){fe[e]=new Se(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){fe[e]=new Se(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){fe[e]=new Se(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){fe[e]=new Se(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){fe[e]=new Se(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){fe[e]=new Se(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){fe[e]=new Se(e,5,!1,e.toLowerCase(),null,!1,!1)});var bl=/[\-:]([a-z])/g;function ea(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(bl,ea);fe[t]=new Se(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(bl,ea);fe[t]=new Se(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(bl,ea);fe[t]=new Se(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){fe[e]=new Se(e,1,!1,e.toLowerCase(),null,!1,!1)});fe.xlinkHref=new Se("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){fe[e]=new Se(e,1,!1,e.toLowerCase(),null,!0,!0)});function ta(e,t,n,r){var o=fe.hasOwnProperty(t)?fe[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(i0(t,n,o,r)&&(n=null),r||o===null?r0(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var vt=t0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,uo=Symbol.for("react.element"),mn=Symbol.for("react.portal"),vn=Symbol.for("react.fragment"),na=Symbol.for("react.strict_mode"),js=Symbol.for("react.profiler"),nd=Symbol.for("react.provider"),rd=Symbol.for("react.context"),ra=Symbol.for("react.forward_ref"),Bs=Symbol.for("react.suspense"),Us=Symbol.for("react.suspense_list"),oa=Symbol.for("react.memo"),wt=Symbol.for("react.lazy"),od=Symbol.for("react.offscreen"),Pu=Symbol.iterator;function Jn(e){return e===null||typeof e!="object"?null:(e=Pu&&e[Pu]||e["@@iterator"],typeof e=="function"?e:null)}var X=Object.assign,bi;function lr(e){if(bi===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);bi=t&&t[1]||""}return`
`+bi+e}var es=!1;function ts(e,t){if(!e||es)return"";es=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,l=i.length-1;1<=s&&0<=l&&o[s]!==i[l];)l--;for(;1<=s&&0<=l;s--,l--)if(o[s]!==i[l]){if(s!==1||l!==1)do if(s--,l--,0>l||o[s]!==i[l]){var a=`
`+o[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{es=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?lr(e):""}function s0(e){switch(e.tag){case 5:return lr(e.type);case 16:return lr("Lazy");case 13:return lr("Suspense");case 19:return lr("SuspenseList");case 0:case 2:case 15:return e=ts(e.type,!1),e;case 11:return e=ts(e.type.render,!1),e;case 1:return e=ts(e.type,!0),e;default:return""}}function $s(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case vn:return"Fragment";case mn:return"Portal";case js:return"Profiler";case na:return"StrictMode";case Bs:return"Suspense";case Us:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case rd:return(e.displayName||"Context")+".Consumer";case nd:return(e._context.displayName||"Context")+".Provider";case ra:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case oa:return t=e.displayName||null,t!==null?t:$s(e.type)||"Memo";case wt:t=e._payload,e=e._init;try{return $s(e(t))}catch{}}return null}function l0(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return $s(t);case 8:return t===na?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Ot(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function id(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function a0(e){var t=id(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function co(e){e._valueTracker||(e._valueTracker=a0(e))}function sd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=id(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Qo(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ws(e,t){var n=t.checked;return X({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n!=null?n:e._wrapperState.initialChecked})}function Tu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Ot(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function ld(e,t){t=t.checked,t!=null&&ta(e,"checked",t,!1)}function Hs(e,t){ld(e,t);var n=Ot(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ks(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ks(e,t.type,Ot(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function _u(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ks(e,t,n){(t!=="number"||Qo(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var ar=Array.isArray;function Ln(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Ot(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Qs(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(k(91));return X({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Vu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(k(92));if(ar(n)){if(1<n.length)throw Error(k(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Ot(n)}}function ad(e,t){var n=Ot(t.value),r=Ot(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Au(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ud(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Gs(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ud(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var fo,cd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(fo=fo||document.createElement("div"),fo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=fo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Lr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var pr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},u0=["Webkit","ms","Moz","O"];Object.keys(pr).forEach(function(e){u0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pr[t]=pr[e]})});function fd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||pr.hasOwnProperty(e)&&pr[e]?(""+t).trim():t+"px"}function dd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=fd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var c0=X({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ys(e,t){if(t){if(c0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(k(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(k(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(k(61))}if(t.style!=null&&typeof t.style!="object")throw Error(k(62))}}function Xs(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Zs=null;function ia(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Js=null,Dn=null,Rn=null;function Lu(e){if(e=eo(e)){if(typeof Js!="function")throw Error(k(280));var t=e.stateNode;t&&(t=Ri(t),Js(e.stateNode,e.type,t))}}function pd(e){Dn?Rn?Rn.push(e):Rn=[e]:Dn=e}function hd(){if(Dn){var e=Dn,t=Rn;if(Rn=Dn=null,Lu(e),t)for(e=0;e<t.length;e++)Lu(t[e])}}function md(e,t){return e(t)}function vd(){}var ns=!1;function yd(e,t,n){if(ns)return e(t,n);ns=!0;try{return md(e,t,n)}finally{ns=!1,(Dn!==null||Rn!==null)&&(vd(),hd())}}function Dr(e,t){var n=e.stateNode;if(n===null)return null;var r=Ri(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(k(231,t,typeof n));return n}var qs=!1;if(dt)try{var qn={};Object.defineProperty(qn,"passive",{get:function(){qs=!0}}),window.addEventListener("test",qn,qn),window.removeEventListener("test",qn,qn)}catch{qs=!1}function f0(e,t,n,r,o,i,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var hr=!1,Go=null,Yo=!1,bs=null,d0={onError:function(e){hr=!0,Go=e}};function p0(e,t,n,r,o,i,s,l,a){hr=!1,Go=null,f0.apply(d0,arguments)}function h0(e,t,n,r,o,i,s,l,a){if(p0.apply(this,arguments),hr){if(hr){var u=Go;hr=!1,Go=null}else throw Error(k(198));Yo||(Yo=!0,bs=u)}}function un(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function gd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Du(e){if(un(e)!==e)throw Error(k(188))}function m0(e){var t=e.alternate;if(!t){if(t=un(e),t===null)throw Error(k(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Du(o),e;if(i===r)return Du(o),t;i=i.sibling}throw Error(k(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s){for(l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s)throw Error(k(189))}}if(n.alternate!==r)throw Error(k(190))}if(n.tag!==3)throw Error(k(188));return n.stateNode.current===n?e:t}function wd(e){return e=m0(e),e!==null?xd(e):null}function xd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=xd(e);if(t!==null)return t;e=e.sibling}return null}var Sd=Me.unstable_scheduleCallback,Ru=Me.unstable_cancelCallback,v0=Me.unstable_shouldYield,y0=Me.unstable_requestPaint,q=Me.unstable_now,g0=Me.unstable_getCurrentPriorityLevel,sa=Me.unstable_ImmediatePriority,Cd=Me.unstable_UserBlockingPriority,Xo=Me.unstable_NormalPriority,w0=Me.unstable_LowPriority,kd=Me.unstable_IdlePriority,Vi=null,tt=null;function x0(e){if(tt&&typeof tt.onCommitFiberRoot=="function")try{tt.onCommitFiberRoot(Vi,e,void 0,(e.current.flags&128)===128)}catch{}}var Ye=Math.clz32?Math.clz32:k0,S0=Math.log,C0=Math.LN2;function k0(e){return e>>>=0,e===0?32:31-(S0(e)/C0|0)|0}var po=64,ho=4194304;function ur(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Zo(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~o;l!==0?r=ur(l):(i&=s,i!==0&&(r=ur(i)))}else s=n&~o,s!==0?r=ur(s):i!==0&&(r=ur(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ye(t),o=1<<n,r|=e[n],t&=~o;return r}function E0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function P0(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-Ye(i),l=1<<s,a=o[s];a===-1?(!(l&n)||l&r)&&(o[s]=E0(l,t)):a<=t&&(e.expiredLanes|=l),i&=~l}}function el(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Ed(){var e=po;return po<<=1,!(po&4194240)&&(po=64),e}function rs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function qr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ye(t),e[t]=n}function T0(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Ye(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function la(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ye(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var B=0;function Pd(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Td,aa,_d,Vd,Ad,tl=!1,mo=[],_t=null,Vt=null,At=null,Rr=new Map,Mr=new Map,Ct=[],_0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Mu(e,t){switch(e){case"focusin":case"focusout":_t=null;break;case"dragenter":case"dragleave":Vt=null;break;case"mouseover":case"mouseout":At=null;break;case"pointerover":case"pointerout":Rr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Mr.delete(t.pointerId)}}function bn(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=eo(t),t!==null&&aa(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function V0(e,t,n,r,o){switch(t){case"focusin":return _t=bn(_t,e,t,n,r,o),!0;case"dragenter":return Vt=bn(Vt,e,t,n,r,o),!0;case"mouseover":return At=bn(At,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return Rr.set(i,bn(Rr.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Mr.set(i,bn(Mr.get(i)||null,e,t,n,r,o)),!0}return!1}function Ld(e){var t=Zt(e.target);if(t!==null){var n=un(t);if(n!==null){if(t=n.tag,t===13){if(t=gd(n),t!==null){e.blockedOn=t,Ad(e.priority,function(){_d(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Mo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=nl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Zs=r,n.target.dispatchEvent(r),Zs=null}else return t=eo(n),t!==null&&aa(t),e.blockedOn=n,!1;t.shift()}return!0}function Nu(e,t,n){Mo(e)&&n.delete(t)}function A0(){tl=!1,_t!==null&&Mo(_t)&&(_t=null),Vt!==null&&Mo(Vt)&&(Vt=null),At!==null&&Mo(At)&&(At=null),Rr.forEach(Nu),Mr.forEach(Nu)}function er(e,t){e.blockedOn===t&&(e.blockedOn=null,tl||(tl=!0,Me.unstable_scheduleCallback(Me.unstable_NormalPriority,A0)))}function Nr(e){function t(o){return er(o,e)}if(0<mo.length){er(mo[0],e);for(var n=1;n<mo.length;n++){var r=mo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(_t!==null&&er(_t,e),Vt!==null&&er(Vt,e),At!==null&&er(At,e),Rr.forEach(t),Mr.forEach(t),n=0;n<Ct.length;n++)r=Ct[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Ct.length&&(n=Ct[0],n.blockedOn===null);)Ld(n),n.blockedOn===null&&Ct.shift()}var Mn=vt.ReactCurrentBatchConfig,Jo=!0;function L0(e,t,n,r){var o=B,i=Mn.transition;Mn.transition=null;try{B=1,ua(e,t,n,r)}finally{B=o,Mn.transition=i}}function D0(e,t,n,r){var o=B,i=Mn.transition;Mn.transition=null;try{B=4,ua(e,t,n,r)}finally{B=o,Mn.transition=i}}function ua(e,t,n,r){if(Jo){var o=nl(e,t,n,r);if(o===null)ps(e,t,r,qo,n),Mu(e,r);else if(V0(o,e,t,n,r))r.stopPropagation();else if(Mu(e,r),t&4&&-1<_0.indexOf(e)){for(;o!==null;){var i=eo(o);if(i!==null&&Td(i),i=nl(e,t,n,r),i===null&&ps(e,t,r,qo,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else ps(e,t,r,null,n)}}var qo=null;function nl(e,t,n,r){if(qo=null,e=ia(r),e=Zt(e),e!==null)if(t=un(e),t===null)e=null;else if(n=t.tag,n===13){if(e=gd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return qo=e,null}function Dd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(g0()){case sa:return 1;case Cd:return 4;case Xo:case w0:return 16;case kd:return 536870912;default:return 16}default:return 16}}var Et=null,ca=null,No=null;function Rd(){if(No)return No;var e,t=ca,n=t.length,r,o="value"in Et?Et.value:Et.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return No=o.slice(e,1<r?1-r:void 0)}function Oo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function vo(){return!0}function Ou(){return!1}function Fe(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?vo:Ou,this.isPropagationStopped=Ou,this}return X(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=vo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=vo)},persist:function(){},isPersistent:vo}),t}var Yn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},fa=Fe(Yn),br=X({},Yn,{view:0,detail:0}),R0=Fe(br),os,is,tr,Ai=X({},br,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:da,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==tr&&(tr&&e.type==="mousemove"?(os=e.screenX-tr.screenX,is=e.screenY-tr.screenY):is=os=0,tr=e),os)},movementY:function(e){return"movementY"in e?e.movementY:is}}),Fu=Fe(Ai),M0=X({},Ai,{dataTransfer:0}),N0=Fe(M0),O0=X({},br,{relatedTarget:0}),ss=Fe(O0),F0=X({},Yn,{animationName:0,elapsedTime:0,pseudoElement:0}),z0=Fe(F0),I0=X({},Yn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),j0=Fe(I0),B0=X({},Yn,{data:0}),zu=Fe(B0),U0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},$0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},W0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function H0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=W0[e])?!!t[e]:!1}function da(){return H0}var K0=X({},br,{key:function(e){if(e.key){var t=U0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Oo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?$0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:da,charCode:function(e){return e.type==="keypress"?Oo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Oo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Q0=Fe(K0),G0=X({},Ai,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Iu=Fe(G0),Y0=X({},br,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:da}),X0=Fe(Y0),Z0=X({},Yn,{propertyName:0,elapsedTime:0,pseudoElement:0}),J0=Fe(Z0),q0=X({},Ai,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),b0=Fe(q0),ev=[9,13,27,32],pa=dt&&"CompositionEvent"in window,mr=null;dt&&"documentMode"in document&&(mr=document.documentMode);var tv=dt&&"TextEvent"in window&&!mr,Md=dt&&(!pa||mr&&8<mr&&11>=mr),ju=" ",Bu=!1;function Nd(e,t){switch(e){case"keyup":return ev.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Od(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var yn=!1;function nv(e,t){switch(e){case"compositionend":return Od(t);case"keypress":return t.which!==32?null:(Bu=!0,ju);case"textInput":return e=t.data,e===ju&&Bu?null:e;default:return null}}function rv(e,t){if(yn)return e==="compositionend"||!pa&&Nd(e,t)?(e=Rd(),No=ca=Et=null,yn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Md&&t.locale!=="ko"?null:t.data;default:return null}}var ov={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Uu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!ov[e.type]:t==="textarea"}function Fd(e,t,n,r){pd(r),t=bo(t,"onChange"),0<t.length&&(n=new fa("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var vr=null,Or=null;function iv(e){Gd(e,0)}function Li(e){var t=xn(e);if(sd(t))return e}function sv(e,t){if(e==="change")return t}var zd=!1;if(dt){var ls;if(dt){var as="oninput"in document;if(!as){var $u=document.createElement("div");$u.setAttribute("oninput","return;"),as=typeof $u.oninput=="function"}ls=as}else ls=!1;zd=ls&&(!document.documentMode||9<document.documentMode)}function Wu(){vr&&(vr.detachEvent("onpropertychange",Id),Or=vr=null)}function Id(e){if(e.propertyName==="value"&&Li(Or)){var t=[];Fd(t,Or,e,ia(e)),yd(iv,t)}}function lv(e,t,n){e==="focusin"?(Wu(),vr=t,Or=n,vr.attachEvent("onpropertychange",Id)):e==="focusout"&&Wu()}function av(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Li(Or)}function uv(e,t){if(e==="click")return Li(t)}function cv(e,t){if(e==="input"||e==="change")return Li(t)}function fv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ze=typeof Object.is=="function"?Object.is:fv;function Fr(e,t){if(Ze(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Is.call(t,o)||!Ze(e[o],t[o]))return!1}return!0}function Hu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ku(e,t){var n=Hu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Hu(n)}}function jd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?jd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Bd(){for(var e=window,t=Qo();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Qo(e.document)}return t}function ha(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function dv(e){var t=Bd(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&jd(n.ownerDocument.documentElement,n)){if(r!==null&&ha(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Ku(n,i);var s=Ku(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var pv=dt&&"documentMode"in document&&11>=document.documentMode,gn=null,rl=null,yr=null,ol=!1;function Qu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ol||gn==null||gn!==Qo(r)||(r=gn,"selectionStart"in r&&ha(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),yr&&Fr(yr,r)||(yr=r,r=bo(rl,"onSelect"),0<r.length&&(t=new fa("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gn)))}function yo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var wn={animationend:yo("Animation","AnimationEnd"),animationiteration:yo("Animation","AnimationIteration"),animationstart:yo("Animation","AnimationStart"),transitionend:yo("Transition","TransitionEnd")},us={},Ud={};dt&&(Ud=document.createElement("div").style,"AnimationEvent"in window||(delete wn.animationend.animation,delete wn.animationiteration.animation,delete wn.animationstart.animation),"TransitionEvent"in window||delete wn.transitionend.transition);function Di(e){if(us[e])return us[e];if(!wn[e])return e;var t=wn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Ud)return us[e]=t[n];return e}var $d=Di("animationend"),Wd=Di("animationiteration"),Hd=Di("animationstart"),Kd=Di("transitionend"),Qd=new Map,Gu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function jt(e,t){Qd.set(e,t),an(t,[e])}for(var cs=0;cs<Gu.length;cs++){var fs=Gu[cs],hv=fs.toLowerCase(),mv=fs[0].toUpperCase()+fs.slice(1);jt(hv,"on"+mv)}jt($d,"onAnimationEnd");jt(Wd,"onAnimationIteration");jt(Hd,"onAnimationStart");jt("dblclick","onDoubleClick");jt("focusin","onFocus");jt("focusout","onBlur");jt(Kd,"onTransitionEnd");In("onMouseEnter",["mouseout","mouseover"]);In("onMouseLeave",["mouseout","mouseover"]);In("onPointerEnter",["pointerout","pointerover"]);In("onPointerLeave",["pointerout","pointerover"]);an("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));an("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));an("onBeforeInput",["compositionend","keypress","textInput","paste"]);an("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));an("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));an("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var cr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),vv=new Set("cancel close invalid load scroll toggle".split(" ").concat(cr));function Yu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,h0(r,t,void 0,e),e.currentTarget=null}function Gd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==i&&o.isPropagationStopped())break e;Yu(o,l,u),i=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==i&&o.isPropagationStopped())break e;Yu(o,l,u),i=a}}}if(Yo)throw e=bs,Yo=!1,bs=null,e}function H(e,t){var n=t[ul];n===void 0&&(n=t[ul]=new Set);var r=e+"__bubble";n.has(r)||(Yd(t,e,2,!1),n.add(r))}function ds(e,t,n){var r=0;t&&(r|=4),Yd(n,e,r,t)}var go="_reactListening"+Math.random().toString(36).slice(2);function zr(e){if(!e[go]){e[go]=!0,td.forEach(function(n){n!=="selectionchange"&&(vv.has(n)||ds(n,!1,e),ds(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[go]||(t[go]=!0,ds("selectionchange",!1,t))}}function Yd(e,t,n,r){switch(Dd(t)){case 1:var o=L0;break;case 4:o=D0;break;default:o=ua}n=o.bind(null,t,n,e),o=void 0,!qs||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function ps(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===o||l.nodeType===8&&l.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;s=s.return}for(;l!==null;){if(s=Zt(l),s===null)return;if(a=s.tag,a===5||a===6){r=i=s;continue e}l=l.parentNode}}r=r.return}yd(function(){var u=i,c=ia(n),f=[];e:{var d=Qd.get(e);if(d!==void 0){var v=fa,g=e;switch(e){case"keypress":if(Oo(n)===0)break e;case"keydown":case"keyup":v=Q0;break;case"focusin":g="focus",v=ss;break;case"focusout":g="blur",v=ss;break;case"beforeblur":case"afterblur":v=ss;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=Fu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=N0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=X0;break;case $d:case Wd:case Hd:v=z0;break;case Kd:v=J0;break;case"scroll":v=R0;break;case"wheel":v=b0;break;case"copy":case"cut":case"paste":v=j0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=Iu}var y=(t&4)!==0,x=!y&&e==="scroll",p=y?d!==null?d+"Capture":null:d;y=[];for(var h=u,m;h!==null;){m=h;var w=m.stateNode;if(m.tag===5&&w!==null&&(m=w,p!==null&&(w=Dr(h,p),w!=null&&y.push(Ir(h,w,m)))),x)break;h=h.return}0<y.length&&(d=new v(d,g,null,n,c),f.push({event:d,listeners:y}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",d&&n!==Zs&&(g=n.relatedTarget||n.fromElement)&&(Zt(g)||g[pt]))break e;if((v||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,v?(g=n.relatedTarget||n.toElement,v=u,g=g?Zt(g):null,g!==null&&(x=un(g),g!==x||g.tag!==5&&g.tag!==6)&&(g=null)):(v=null,g=u),v!==g)){if(y=Fu,w="onMouseLeave",p="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(y=Iu,w="onPointerLeave",p="onPointerEnter",h="pointer"),x=v==null?d:xn(v),m=g==null?d:xn(g),d=new y(w,h+"leave",v,n,c),d.target=x,d.relatedTarget=m,w=null,Zt(c)===u&&(y=new y(p,h+"enter",g,n,c),y.target=m,y.relatedTarget=x,w=y),x=w,v&&g)t:{for(y=v,p=g,h=0,m=y;m;m=pn(m))h++;for(m=0,w=p;w;w=pn(w))m++;for(;0<h-m;)y=pn(y),h--;for(;0<m-h;)p=pn(p),m--;for(;h--;){if(y===p||p!==null&&y===p.alternate)break t;y=pn(y),p=pn(p)}y=null}else y=null;v!==null&&Xu(f,d,v,y,!1),g!==null&&x!==null&&Xu(f,x,g,y,!0)}}e:{if(d=u?xn(u):window,v=d.nodeName&&d.nodeName.toLowerCase(),v==="select"||v==="input"&&d.type==="file")var C=sv;else if(Uu(d))if(zd)C=cv;else{C=av;var E=lv}else(v=d.nodeName)&&v.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(C=uv);if(C&&(C=C(e,u))){Fd(f,C,n,c);break e}E&&E(e,d,u),e==="focusout"&&(E=d._wrapperState)&&E.controlled&&d.type==="number"&&Ks(d,"number",d.value)}switch(E=u?xn(u):window,e){case"focusin":(Uu(E)||E.contentEditable==="true")&&(gn=E,rl=u,yr=null);break;case"focusout":yr=rl=gn=null;break;case"mousedown":ol=!0;break;case"contextmenu":case"mouseup":case"dragend":ol=!1,Qu(f,n,c);break;case"selectionchange":if(pv)break;case"keydown":case"keyup":Qu(f,n,c)}var P;if(pa)e:{switch(e){case"compositionstart":var _="onCompositionStart";break e;case"compositionend":_="onCompositionEnd";break e;case"compositionupdate":_="onCompositionUpdate";break e}_=void 0}else yn?Nd(e,n)&&(_="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(_="onCompositionStart");_&&(Md&&n.locale!=="ko"&&(yn||_!=="onCompositionStart"?_==="onCompositionEnd"&&yn&&(P=Rd()):(Et=c,ca="value"in Et?Et.value:Et.textContent,yn=!0)),E=bo(u,_),0<E.length&&(_=new zu(_,e,null,n,c),f.push({event:_,listeners:E}),P?_.data=P:(P=Od(n),P!==null&&(_.data=P)))),(P=tv?nv(e,n):rv(e,n))&&(u=bo(u,"onBeforeInput"),0<u.length&&(c=new zu("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=P))}Gd(f,t)})}function Ir(e,t,n){return{instance:e,listener:t,currentTarget:n}}function bo(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=Dr(e,n),i!=null&&r.unshift(Ir(e,i,o)),i=Dr(e,t),i!=null&&r.push(Ir(e,i,o))),e=e.return}return r}function pn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Xu(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,o?(a=Dr(n,i),a!=null&&s.unshift(Ir(n,a,l))):o||(a=Dr(n,i),a!=null&&s.push(Ir(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var yv=/\r\n?/g,gv=/\u0000|\uFFFD/g;function Zu(e){return(typeof e=="string"?e:""+e).replace(yv,`
`).replace(gv,"")}function wo(e,t,n){if(t=Zu(t),Zu(e)!==t&&n)throw Error(k(425))}function ei(){}var il=null,sl=null;function ll(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var al=typeof setTimeout=="function"?setTimeout:void 0,wv=typeof clearTimeout=="function"?clearTimeout:void 0,Ju=typeof Promise=="function"?Promise:void 0,xv=typeof queueMicrotask=="function"?queueMicrotask:typeof Ju<"u"?function(e){return Ju.resolve(null).then(e).catch(Sv)}:al;function Sv(e){setTimeout(function(){throw e})}function hs(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Nr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Nr(t)}function Lt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function qu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Xn=Math.random().toString(36).slice(2),et="__reactFiber$"+Xn,jr="__reactProps$"+Xn,pt="__reactContainer$"+Xn,ul="__reactEvents$"+Xn,Cv="__reactListeners$"+Xn,kv="__reactHandles$"+Xn;function Zt(e){var t=e[et];if(t)return t;for(var n=e.parentNode;n;){if(t=n[pt]||n[et]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=qu(e);e!==null;){if(n=e[et])return n;e=qu(e)}return t}e=n,n=e.parentNode}return null}function eo(e){return e=e[et]||e[pt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function xn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(k(33))}function Ri(e){return e[jr]||null}var cl=[],Sn=-1;function Bt(e){return{current:e}}function K(e){0>Sn||(e.current=cl[Sn],cl[Sn]=null,Sn--)}function $(e,t){Sn++,cl[Sn]=e.current,e.current=t}var Ft={},me=Bt(Ft),Ee=Bt(!1),nn=Ft;function jn(e,t){var n=e.type.contextTypes;if(!n)return Ft;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Pe(e){return e=e.childContextTypes,e!=null}function ti(){K(Ee),K(me)}function bu(e,t,n){if(me.current!==Ft)throw Error(k(168));$(me,t),$(Ee,n)}function Xd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(k(108,l0(e)||"Unknown",o));return X({},n,r)}function ni(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ft,nn=me.current,$(me,e),$(Ee,Ee.current),!0}function ec(e,t,n){var r=e.stateNode;if(!r)throw Error(k(169));n?(e=Xd(e,t,nn),r.__reactInternalMemoizedMergedChildContext=e,K(Ee),K(me),$(me,e)):K(Ee),$(Ee,n)}var lt=null,Mi=!1,ms=!1;function Zd(e){lt===null?lt=[e]:lt.push(e)}function Ev(e){Mi=!0,Zd(e)}function Ut(){if(!ms&&lt!==null){ms=!0;var e=0,t=B;try{var n=lt;for(B=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}lt=null,Mi=!1}catch(o){throw lt!==null&&(lt=lt.slice(e+1)),Sd(sa,Ut),o}finally{B=t,ms=!1}}return null}var Cn=[],kn=0,ri=null,oi=0,ze=[],Ie=0,rn=null,at=1,ut="";function Gt(e,t){Cn[kn++]=oi,Cn[kn++]=ri,ri=e,oi=t}function Jd(e,t,n){ze[Ie++]=at,ze[Ie++]=ut,ze[Ie++]=rn,rn=e;var r=at;e=ut;var o=32-Ye(r)-1;r&=~(1<<o),n+=1;var i=32-Ye(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,at=1<<32-Ye(t)+o|n<<o|r,ut=i+e}else at=1<<i|n<<o|r,ut=e}function ma(e){e.return!==null&&(Gt(e,1),Jd(e,1,0))}function va(e){for(;e===ri;)ri=Cn[--kn],Cn[kn]=null,oi=Cn[--kn],Cn[kn]=null;for(;e===rn;)rn=ze[--Ie],ze[Ie]=null,ut=ze[--Ie],ze[Ie]=null,at=ze[--Ie],ze[Ie]=null}var Re=null,De=null,Q=!1,Ge=null;function qd(e,t){var n=je(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function tc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Re=e,De=Lt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Re=e,De=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=rn!==null?{id:at,overflow:ut}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=je(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Re=e,De=null,!0):!1;default:return!1}}function fl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function dl(e){if(Q){var t=De;if(t){var n=t;if(!tc(e,t)){if(fl(e))throw Error(k(418));t=Lt(n.nextSibling);var r=Re;t&&tc(e,t)?qd(r,n):(e.flags=e.flags&-4097|2,Q=!1,Re=e)}}else{if(fl(e))throw Error(k(418));e.flags=e.flags&-4097|2,Q=!1,Re=e}}}function nc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Re=e}function xo(e){if(e!==Re)return!1;if(!Q)return nc(e),Q=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ll(e.type,e.memoizedProps)),t&&(t=De)){if(fl(e))throw bd(),Error(k(418));for(;t;)qd(e,t),t=Lt(t.nextSibling)}if(nc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(k(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){De=Lt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}De=null}}else De=Re?Lt(e.stateNode.nextSibling):null;return!0}function bd(){for(var e=De;e;)e=Lt(e.nextSibling)}function Bn(){De=Re=null,Q=!1}function ya(e){Ge===null?Ge=[e]:Ge.push(e)}var Pv=vt.ReactCurrentBatchConfig;function nr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(k(309));var r=n.stateNode}if(!r)throw Error(k(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var l=o.refs;s===null?delete l[i]:l[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(k(284));if(!n._owner)throw Error(k(290,e))}return e}function So(e,t){throw e=Object.prototype.toString.call(t),Error(k(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function rc(e){var t=e._init;return t(e._payload)}function ep(e){function t(p,h){if(e){var m=p.deletions;m===null?(p.deletions=[h],p.flags|=16):m.push(h)}}function n(p,h){if(!e)return null;for(;h!==null;)t(p,h),h=h.sibling;return null}function r(p,h){for(p=new Map;h!==null;)h.key!==null?p.set(h.key,h):p.set(h.index,h),h=h.sibling;return p}function o(p,h){return p=Nt(p,h),p.index=0,p.sibling=null,p}function i(p,h,m){return p.index=m,e?(m=p.alternate,m!==null?(m=m.index,m<h?(p.flags|=2,h):m):(p.flags|=2,h)):(p.flags|=1048576,h)}function s(p){return e&&p.alternate===null&&(p.flags|=2),p}function l(p,h,m,w){return h===null||h.tag!==6?(h=Cs(m,p.mode,w),h.return=p,h):(h=o(h,m),h.return=p,h)}function a(p,h,m,w){var C=m.type;return C===vn?c(p,h,m.props.children,w,m.key):h!==null&&(h.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===wt&&rc(C)===h.type)?(w=o(h,m.props),w.ref=nr(p,h,m),w.return=p,w):(w=$o(m.type,m.key,m.props,null,p.mode,w),w.ref=nr(p,h,m),w.return=p,w)}function u(p,h,m,w){return h===null||h.tag!==4||h.stateNode.containerInfo!==m.containerInfo||h.stateNode.implementation!==m.implementation?(h=ks(m,p.mode,w),h.return=p,h):(h=o(h,m.children||[]),h.return=p,h)}function c(p,h,m,w,C){return h===null||h.tag!==7?(h=tn(m,p.mode,w,C),h.return=p,h):(h=o(h,m),h.return=p,h)}function f(p,h,m){if(typeof h=="string"&&h!==""||typeof h=="number")return h=Cs(""+h,p.mode,m),h.return=p,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case uo:return m=$o(h.type,h.key,h.props,null,p.mode,m),m.ref=nr(p,null,h),m.return=p,m;case mn:return h=ks(h,p.mode,m),h.return=p,h;case wt:var w=h._init;return f(p,w(h._payload),m)}if(ar(h)||Jn(h))return h=tn(h,p.mode,m,null),h.return=p,h;So(p,h)}return null}function d(p,h,m,w){var C=h!==null?h.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return C!==null?null:l(p,h,""+m,w);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case uo:return m.key===C?a(p,h,m,w):null;case mn:return m.key===C?u(p,h,m,w):null;case wt:return C=m._init,d(p,h,C(m._payload),w)}if(ar(m)||Jn(m))return C!==null?null:c(p,h,m,w,null);So(p,m)}return null}function v(p,h,m,w,C){if(typeof w=="string"&&w!==""||typeof w=="number")return p=p.get(m)||null,l(h,p,""+w,C);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case uo:return p=p.get(w.key===null?m:w.key)||null,a(h,p,w,C);case mn:return p=p.get(w.key===null?m:w.key)||null,u(h,p,w,C);case wt:var E=w._init;return v(p,h,m,E(w._payload),C)}if(ar(w)||Jn(w))return p=p.get(m)||null,c(h,p,w,C,null);So(h,w)}return null}function g(p,h,m,w){for(var C=null,E=null,P=h,_=h=0,O=null;P!==null&&_<m.length;_++){P.index>_?(O=P,P=null):O=P.sibling;var A=d(p,P,m[_],w);if(A===null){P===null&&(P=O);break}e&&P&&A.alternate===null&&t(p,P),h=i(A,h,_),E===null?C=A:E.sibling=A,E=A,P=O}if(_===m.length)return n(p,P),Q&&Gt(p,_),C;if(P===null){for(;_<m.length;_++)P=f(p,m[_],w),P!==null&&(h=i(P,h,_),E===null?C=P:E.sibling=P,E=P);return Q&&Gt(p,_),C}for(P=r(p,P);_<m.length;_++)O=v(P,p,_,m[_],w),O!==null&&(e&&O.alternate!==null&&P.delete(O.key===null?_:O.key),h=i(O,h,_),E===null?C=O:E.sibling=O,E=O);return e&&P.forEach(function(I){return t(p,I)}),Q&&Gt(p,_),C}function y(p,h,m,w){var C=Jn(m);if(typeof C!="function")throw Error(k(150));if(m=C.call(m),m==null)throw Error(k(151));for(var E=C=null,P=h,_=h=0,O=null,A=m.next();P!==null&&!A.done;_++,A=m.next()){P.index>_?(O=P,P=null):O=P.sibling;var I=d(p,P,A.value,w);if(I===null){P===null&&(P=O);break}e&&P&&I.alternate===null&&t(p,P),h=i(I,h,_),E===null?C=I:E.sibling=I,E=I,P=O}if(A.done)return n(p,P),Q&&Gt(p,_),C;if(P===null){for(;!A.done;_++,A=m.next())A=f(p,A.value,w),A!==null&&(h=i(A,h,_),E===null?C=A:E.sibling=A,E=A);return Q&&Gt(p,_),C}for(P=r(p,P);!A.done;_++,A=m.next())A=v(P,p,_,A.value,w),A!==null&&(e&&A.alternate!==null&&P.delete(A.key===null?_:A.key),h=i(A,h,_),E===null?C=A:E.sibling=A,E=A);return e&&P.forEach(function(b){return t(p,b)}),Q&&Gt(p,_),C}function x(p,h,m,w){if(typeof m=="object"&&m!==null&&m.type===vn&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case uo:e:{for(var C=m.key,E=h;E!==null;){if(E.key===C){if(C=m.type,C===vn){if(E.tag===7){n(p,E.sibling),h=o(E,m.props.children),h.return=p,p=h;break e}}else if(E.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===wt&&rc(C)===E.type){n(p,E.sibling),h=o(E,m.props),h.ref=nr(p,E,m),h.return=p,p=h;break e}n(p,E);break}else t(p,E);E=E.sibling}m.type===vn?(h=tn(m.props.children,p.mode,w,m.key),h.return=p,p=h):(w=$o(m.type,m.key,m.props,null,p.mode,w),w.ref=nr(p,h,m),w.return=p,p=w)}return s(p);case mn:e:{for(E=m.key;h!==null;){if(h.key===E)if(h.tag===4&&h.stateNode.containerInfo===m.containerInfo&&h.stateNode.implementation===m.implementation){n(p,h.sibling),h=o(h,m.children||[]),h.return=p,p=h;break e}else{n(p,h);break}else t(p,h);h=h.sibling}h=ks(m,p.mode,w),h.return=p,p=h}return s(p);case wt:return E=m._init,x(p,h,E(m._payload),w)}if(ar(m))return g(p,h,m,w);if(Jn(m))return y(p,h,m,w);So(p,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,h!==null&&h.tag===6?(n(p,h.sibling),h=o(h,m),h.return=p,p=h):(n(p,h),h=Cs(m,p.mode,w),h.return=p,p=h),s(p)):n(p,h)}return x}var Un=ep(!0),tp=ep(!1),ii=Bt(null),si=null,En=null,ga=null;function wa(){ga=En=si=null}function xa(e){var t=ii.current;K(ii),e._currentValue=t}function pl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Nn(e,t){si=e,ga=En=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ke=!0),e.firstContext=null)}function Ue(e){var t=e._currentValue;if(ga!==e)if(e={context:e,memoizedValue:t,next:null},En===null){if(si===null)throw Error(k(308));En=e,si.dependencies={lanes:0,firstContext:e}}else En=En.next=e;return t}var Jt=null;function Sa(e){Jt===null?Jt=[e]:Jt.push(e)}function np(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Sa(t)):(n.next=o.next,o.next=n),t.interleaved=n,ht(e,r)}function ht(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var xt=!1;function Ca(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function rp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ct(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Dt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,ht(e,n)}return o=r.interleaved,o===null?(t.next=t,Sa(r)):(t.next=o.next,o.next=t),r.interleaved=t,ht(e,n)}function Fo(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,la(e,n)}}function oc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function li(e,t,n,r){var o=e.updateQueue;xt=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,l=o.shared.pending;if(l!==null){o.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?i=u:s.next=u,s=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==s&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(i!==null){var f=o.baseState;s=0,c=u=a=null,l=i;do{var d=l.lane,v=l.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:v,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var g=e,y=l;switch(d=t,v=n,y.tag){case 1:if(g=y.payload,typeof g=="function"){f=g.call(v,f,d);break e}f=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=y.payload,d=typeof g=="function"?g.call(v,f,d):g,d==null)break e;f=X({},f,d);break e;case 2:xt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,d=o.effects,d===null?o.effects=[l]:d.push(l))}else v={eventTime:v,lane:d,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=v,a=f):c=c.next=v,s|=d;if(l=l.next,l===null){if(l=o.shared.pending,l===null)break;d=l,l=d.next,d.next=null,o.lastBaseUpdate=d,o.shared.pending=null}}while(!0);if(c===null&&(a=f),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);sn|=s,e.lanes=s,e.memoizedState=f}}function ic(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(k(191,o));o.call(r)}}}var to={},nt=Bt(to),Br=Bt(to),Ur=Bt(to);function qt(e){if(e===to)throw Error(k(174));return e}function ka(e,t){switch($(Ur,t),$(Br,e),$(nt,to),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Gs(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Gs(t,e)}K(nt),$(nt,t)}function $n(){K(nt),K(Br),K(Ur)}function op(e){qt(Ur.current);var t=qt(nt.current),n=Gs(t,e.type);t!==n&&($(Br,e),$(nt,n))}function Ea(e){Br.current===e&&(K(nt),K(Br))}var G=Bt(0);function ai(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var vs=[];function Pa(){for(var e=0;e<vs.length;e++)vs[e]._workInProgressVersionPrimary=null;vs.length=0}var zo=vt.ReactCurrentDispatcher,ys=vt.ReactCurrentBatchConfig,on=0,Y=null,re=null,se=null,ui=!1,gr=!1,$r=0,Tv=0;function de(){throw Error(k(321))}function Ta(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ze(e[n],t[n]))return!1;return!0}function _a(e,t,n,r,o,i){if(on=i,Y=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,zo.current=e===null||e.memoizedState===null?Lv:Dv,e=n(r,o),gr){i=0;do{if(gr=!1,$r=0,25<=i)throw Error(k(301));i+=1,se=re=null,t.updateQueue=null,zo.current=Rv,e=n(r,o)}while(gr)}if(zo.current=ci,t=re!==null&&re.next!==null,on=0,se=re=Y=null,ui=!1,t)throw Error(k(300));return e}function Va(){var e=$r!==0;return $r=0,e}function qe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return se===null?Y.memoizedState=se=e:se=se.next=e,se}function $e(){if(re===null){var e=Y.alternate;e=e!==null?e.memoizedState:null}else e=re.next;var t=se===null?Y.memoizedState:se.next;if(t!==null)se=t,re=e;else{if(e===null)throw Error(k(310));re=e,e={memoizedState:re.memoizedState,baseState:re.baseState,baseQueue:re.baseQueue,queue:re.queue,next:null},se===null?Y.memoizedState=se=e:se=se.next=e}return se}function Wr(e,t){return typeof t=="function"?t(e):t}function gs(e){var t=$e(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=re,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var l=s=null,a=null,u=i;do{var c=u.lane;if((on&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=f,s=r):a=a.next=f,Y.lanes|=c,sn|=c}u=u.next}while(u!==null&&u!==i);a===null?s=r:a.next=l,Ze(r,t.memoizedState)||(ke=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,Y.lanes|=i,sn|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ws(e){var t=$e(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);Ze(i,t.memoizedState)||(ke=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function ip(){}function sp(e,t){var n=Y,r=$e(),o=t(),i=!Ze(r.memoizedState,o);if(i&&(r.memoizedState=o,ke=!0),r=r.queue,Aa(up.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||se!==null&&se.memoizedState.tag&1){if(n.flags|=2048,Hr(9,ap.bind(null,n,r,o,t),void 0,null),le===null)throw Error(k(349));on&30||lp(n,t,o)}return o}function lp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function ap(e,t,n,r){t.value=n,t.getSnapshot=r,cp(t)&&fp(e)}function up(e,t,n){return n(function(){cp(t)&&fp(e)})}function cp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ze(e,n)}catch{return!0}}function fp(e){var t=ht(e,1);t!==null&&Xe(t,e,1,-1)}function sc(e){var t=qe();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Wr,lastRenderedState:e},t.queue=e,e=e.dispatch=Av.bind(null,Y,e),[t.memoizedState,e]}function Hr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function dp(){return $e().memoizedState}function Io(e,t,n,r){var o=qe();Y.flags|=e,o.memoizedState=Hr(1|t,n,void 0,r===void 0?null:r)}function Ni(e,t,n,r){var o=$e();r=r===void 0?null:r;var i=void 0;if(re!==null){var s=re.memoizedState;if(i=s.destroy,r!==null&&Ta(r,s.deps)){o.memoizedState=Hr(t,n,i,r);return}}Y.flags|=e,o.memoizedState=Hr(1|t,n,i,r)}function lc(e,t){return Io(8390656,8,e,t)}function Aa(e,t){return Ni(2048,8,e,t)}function pp(e,t){return Ni(4,2,e,t)}function hp(e,t){return Ni(4,4,e,t)}function mp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function vp(e,t,n){return n=n!=null?n.concat([e]):null,Ni(4,4,mp.bind(null,t,e),n)}function La(){}function yp(e,t){var n=$e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ta(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function gp(e,t){var n=$e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ta(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function wp(e,t,n){return on&21?(Ze(n,t)||(n=Ed(),Y.lanes|=n,sn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ke=!0),e.memoizedState=n)}function _v(e,t){var n=B;B=n!==0&&4>n?n:4,e(!0);var r=ys.transition;ys.transition={};try{e(!1),t()}finally{B=n,ys.transition=r}}function xp(){return $e().memoizedState}function Vv(e,t,n){var r=Mt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Sp(e))Cp(t,n);else if(n=np(e,t,n,r),n!==null){var o=we();Xe(n,e,r,o),kp(n,t,r)}}function Av(e,t,n){var r=Mt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Sp(e))Cp(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,l=i(s,n);if(o.hasEagerState=!0,o.eagerState=l,Ze(l,s)){var a=t.interleaved;a===null?(o.next=o,Sa(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=np(e,t,o,r),n!==null&&(o=we(),Xe(n,e,r,o),kp(n,t,r))}}function Sp(e){var t=e.alternate;return e===Y||t!==null&&t===Y}function Cp(e,t){gr=ui=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function kp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,la(e,n)}}var ci={readContext:Ue,useCallback:de,useContext:de,useEffect:de,useImperativeHandle:de,useInsertionEffect:de,useLayoutEffect:de,useMemo:de,useReducer:de,useRef:de,useState:de,useDebugValue:de,useDeferredValue:de,useTransition:de,useMutableSource:de,useSyncExternalStore:de,useId:de,unstable_isNewReconciler:!1},Lv={readContext:Ue,useCallback:function(e,t){return qe().memoizedState=[e,t===void 0?null:t],e},useContext:Ue,useEffect:lc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Io(4194308,4,mp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Io(4194308,4,e,t)},useInsertionEffect:function(e,t){return Io(4,2,e,t)},useMemo:function(e,t){var n=qe();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=qe();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Vv.bind(null,Y,e),[r.memoizedState,e]},useRef:function(e){var t=qe();return e={current:e},t.memoizedState=e},useState:sc,useDebugValue:La,useDeferredValue:function(e){return qe().memoizedState=e},useTransition:function(){var e=sc(!1),t=e[0];return e=_v.bind(null,e[1]),qe().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Y,o=qe();if(Q){if(n===void 0)throw Error(k(407));n=n()}else{if(n=t(),le===null)throw Error(k(349));on&30||lp(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,lc(up.bind(null,r,i,e),[e]),r.flags|=2048,Hr(9,ap.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=qe(),t=le.identifierPrefix;if(Q){var n=ut,r=at;n=(r&~(1<<32-Ye(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=$r++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Tv++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Dv={readContext:Ue,useCallback:yp,useContext:Ue,useEffect:Aa,useImperativeHandle:vp,useInsertionEffect:pp,useLayoutEffect:hp,useMemo:gp,useReducer:gs,useRef:dp,useState:function(){return gs(Wr)},useDebugValue:La,useDeferredValue:function(e){var t=$e();return wp(t,re.memoizedState,e)},useTransition:function(){var e=gs(Wr)[0],t=$e().memoizedState;return[e,t]},useMutableSource:ip,useSyncExternalStore:sp,useId:xp,unstable_isNewReconciler:!1},Rv={readContext:Ue,useCallback:yp,useContext:Ue,useEffect:Aa,useImperativeHandle:vp,useInsertionEffect:pp,useLayoutEffect:hp,useMemo:gp,useReducer:ws,useRef:dp,useState:function(){return ws(Wr)},useDebugValue:La,useDeferredValue:function(e){var t=$e();return re===null?t.memoizedState=e:wp(t,re.memoizedState,e)},useTransition:function(){var e=ws(Wr)[0],t=$e().memoizedState;return[e,t]},useMutableSource:ip,useSyncExternalStore:sp,useId:xp,unstable_isNewReconciler:!1};function Ke(e,t){if(e&&e.defaultProps){t=X({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function hl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:X({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Oi={isMounted:function(e){return(e=e._reactInternals)?un(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=we(),o=Mt(e),i=ct(r,o);i.payload=t,n!=null&&(i.callback=n),t=Dt(e,i,o),t!==null&&(Xe(t,e,o,r),Fo(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=we(),o=Mt(e),i=ct(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Dt(e,i,o),t!==null&&(Xe(t,e,o,r),Fo(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=we(),r=Mt(e),o=ct(n,r);o.tag=2,t!=null&&(o.callback=t),t=Dt(e,o,r),t!==null&&(Xe(t,e,r,n),Fo(t,e,r))}};function ac(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!Fr(n,r)||!Fr(o,i):!0}function Ep(e,t,n){var r=!1,o=Ft,i=t.contextType;return typeof i=="object"&&i!==null?i=Ue(i):(o=Pe(t)?nn:me.current,r=t.contextTypes,i=(r=r!=null)?jn(e,o):Ft),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Oi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function uc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Oi.enqueueReplaceState(t,t.state,null)}function ml(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Ca(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=Ue(i):(i=Pe(t)?nn:me.current,o.context=jn(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(hl(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Oi.enqueueReplaceState(o,o.state,null),li(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Wn(e,t){try{var n="",r=t;do n+=s0(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function xs(e,t,n){return{value:e,source:null,stack:n!=null?n:null,digest:t!=null?t:null}}function vl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Mv=typeof WeakMap=="function"?WeakMap:Map;function Pp(e,t,n){n=ct(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){di||(di=!0,Tl=r),vl(e,t)},n}function Tp(e,t,n){n=ct(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){vl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){vl(e,t),typeof r!="function"&&(Rt===null?Rt=new Set([this]):Rt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function cc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Mv;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Gv.bind(null,e,t,n),t.then(e,e))}function fc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function dc(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ct(-1,1),t.tag=2,Dt(n,t,1))),n.lanes|=1),e)}var Nv=vt.ReactCurrentOwner,ke=!1;function ge(e,t,n,r){t.child=e===null?tp(t,null,n,r):Un(t,e.child,n,r)}function pc(e,t,n,r,o){n=n.render;var i=t.ref;return Nn(t,o),r=_a(e,t,n,r,i,o),n=Va(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,mt(e,t,o)):(Q&&n&&ma(t),t.flags|=1,ge(e,t,r,o),t.child)}function hc(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Ia(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,_p(e,t,i,r,o)):(e=$o(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:Fr,n(s,r)&&e.ref===t.ref)return mt(e,t,o)}return t.flags|=1,e=Nt(i,r),e.ref=t.ref,e.return=t,t.child=e}function _p(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(Fr(i,r)&&e.ref===t.ref)if(ke=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(ke=!0);else return t.lanes=e.lanes,mt(e,t,o)}return yl(e,t,n,r,o)}function Vp(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},$(Tn,Ae),Ae|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,$(Tn,Ae),Ae|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,$(Tn,Ae),Ae|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,$(Tn,Ae),Ae|=r;return ge(e,t,o,n),t.child}function Ap(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function yl(e,t,n,r,o){var i=Pe(n)?nn:me.current;return i=jn(t,i),Nn(t,o),n=_a(e,t,n,r,i,o),r=Va(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,mt(e,t,o)):(Q&&r&&ma(t),t.flags|=1,ge(e,t,n,o),t.child)}function mc(e,t,n,r,o){if(Pe(n)){var i=!0;ni(t)}else i=!1;if(Nn(t,o),t.stateNode===null)jo(e,t),Ep(t,n,r),ml(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=Ue(u):(u=Pe(n)?nn:me.current,u=jn(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&uc(t,s,r,u),xt=!1;var d=t.memoizedState;s.state=d,li(t,r,s,o),a=t.memoizedState,l!==r||d!==a||Ee.current||xt?(typeof c=="function"&&(hl(t,n,c,r),a=t.memoizedState),(l=xt||ac(t,n,l,r,d,a,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,rp(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:Ke(t.type,l),s.props=u,f=t.pendingProps,d=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=Ue(a):(a=Pe(n)?nn:me.current,a=jn(t,a));var v=n.getDerivedStateFromProps;(c=typeof v=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==f||d!==a)&&uc(t,s,r,a),xt=!1,d=t.memoizedState,s.state=d,li(t,r,s,o);var g=t.memoizedState;l!==f||d!==g||Ee.current||xt?(typeof v=="function"&&(hl(t,n,v,r),g=t.memoizedState),(u=xt||ac(t,n,u,r,d,g,a)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,g,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,g,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=g),s.props=r,s.state=g,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return gl(e,t,n,r,i,o)}function gl(e,t,n,r,o,i){Ap(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&ec(t,n,!1),mt(e,t,i);r=t.stateNode,Nv.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Un(t,e.child,null,i),t.child=Un(t,null,l,i)):ge(e,t,l,i),t.memoizedState=r.state,o&&ec(t,n,!0),t.child}function Lp(e){var t=e.stateNode;t.pendingContext?bu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&bu(e,t.context,!1),ka(e,t.containerInfo)}function vc(e,t,n,r,o){return Bn(),ya(o),t.flags|=256,ge(e,t,n,r),t.child}var wl={dehydrated:null,treeContext:null,retryLane:0};function xl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Dp(e,t,n){var r=t.pendingProps,o=G.current,i=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(o&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),$(G,o&1),e===null)return dl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=Ii(s,r,0,null),e=tn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=xl(n),t.memoizedState=wl,e):Da(t,s));if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null))return Ov(e,t,s,r,l,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,l=o.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Nt(o,a),r.subtreeFlags=o.subtreeFlags&14680064),l!==null?i=Nt(l,i):(i=tn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?xl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=wl,r}return i=e.child,e=i.sibling,r=Nt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Da(e,t){return t=Ii({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Co(e,t,n,r){return r!==null&&ya(r),Un(t,e.child,null,n),e=Da(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Ov(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=xs(Error(k(422))),Co(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Ii({mode:"visible",children:r.children},o,0,null),i=tn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Un(t,e.child,null,s),t.child.memoizedState=xl(s),t.memoizedState=wl,i);if(!(t.mode&1))return Co(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var l=r.dgst;return r=l,i=Error(k(419)),r=xs(i,r,void 0),Co(e,t,s,r)}if(l=(s&e.childLanes)!==0,ke||l){if(r=le,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,ht(e,o),Xe(r,e,o,-1))}return za(),r=xs(Error(k(421))),Co(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Yv.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,De=Lt(o.nextSibling),Re=t,Q=!0,Ge=null,e!==null&&(ze[Ie++]=at,ze[Ie++]=ut,ze[Ie++]=rn,at=e.id,ut=e.overflow,rn=t),t=Da(t,r.children),t.flags|=4096,t)}function yc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),pl(e.return,t,n)}function Ss(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function Rp(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(ge(e,t,r.children,n),r=G.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&yc(e,n,t);else if(e.tag===19)yc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if($(G,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&ai(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Ss(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&ai(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Ss(t,!0,n,null,i);break;case"together":Ss(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function jo(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function mt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),sn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(k(153));if(t.child!==null){for(e=t.child,n=Nt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Nt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Fv(e,t,n){switch(t.tag){case 3:Lp(t),Bn();break;case 5:op(t);break;case 1:Pe(t.type)&&ni(t);break;case 4:ka(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;$(ii,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?($(G,G.current&1),t.flags|=128,null):n&t.child.childLanes?Dp(e,t,n):($(G,G.current&1),e=mt(e,t,n),e!==null?e.sibling:null);$(G,G.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Rp(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),$(G,G.current),r)break;return null;case 22:case 23:return t.lanes=0,Vp(e,t,n)}return mt(e,t,n)}var Mp,Sl,Np,Op;Mp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Sl=function(){};Np=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,qt(nt.current);var i=null;switch(n){case"input":o=Ws(e,o),r=Ws(e,r),i=[];break;case"select":o=X({},o,{value:void 0}),r=X({},r,{value:void 0}),i=[];break;case"textarea":o=Qs(e,o),r=Qs(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ei)}Ys(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var l=o[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Ar.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(l=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Ar.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&H("scroll",e),i||l===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Op=function(e,t,n,r){n!==r&&(t.flags|=4)};function rr(e,t){if(!Q)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function pe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function zv(e,t,n){var r=t.pendingProps;switch(va(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return pe(t),null;case 1:return Pe(t.type)&&ti(),pe(t),null;case 3:return r=t.stateNode,$n(),K(Ee),K(me),Pa(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(xo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ge!==null&&(Al(Ge),Ge=null))),Sl(e,t),pe(t),null;case 5:Ea(t);var o=qt(Ur.current);if(n=t.type,e!==null&&t.stateNode!=null)Np(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(k(166));return pe(t),null}if(e=qt(nt.current),xo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[et]=t,r[jr]=i,e=(t.mode&1)!==0,n){case"dialog":H("cancel",r),H("close",r);break;case"iframe":case"object":case"embed":H("load",r);break;case"video":case"audio":for(o=0;o<cr.length;o++)H(cr[o],r);break;case"source":H("error",r);break;case"img":case"image":case"link":H("error",r),H("load",r);break;case"details":H("toggle",r);break;case"input":Tu(r,i),H("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},H("invalid",r);break;case"textarea":Vu(r,i),H("invalid",r)}Ys(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var l=i[s];s==="children"?typeof l=="string"?r.textContent!==l&&(i.suppressHydrationWarning!==!0&&wo(r.textContent,l,e),o=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&wo(r.textContent,l,e),o=["children",""+l]):Ar.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&H("scroll",r)}switch(n){case"input":co(r),_u(r,i,!0);break;case"textarea":co(r),Au(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=ei)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ud(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[et]=t,e[jr]=r,Mp(e,t,!1,!1),t.stateNode=e;e:{switch(s=Xs(n,r),n){case"dialog":H("cancel",e),H("close",e),o=r;break;case"iframe":case"object":case"embed":H("load",e),o=r;break;case"video":case"audio":for(o=0;o<cr.length;o++)H(cr[o],e);o=r;break;case"source":H("error",e),o=r;break;case"img":case"image":case"link":H("error",e),H("load",e),o=r;break;case"details":H("toggle",e),o=r;break;case"input":Tu(e,r),o=Ws(e,r),H("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=X({},r,{value:void 0}),H("invalid",e);break;case"textarea":Vu(e,r),o=Qs(e,r),H("invalid",e);break;default:o=r}Ys(n,o),l=o;for(i in l)if(l.hasOwnProperty(i)){var a=l[i];i==="style"?dd(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&cd(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Lr(e,a):typeof a=="number"&&Lr(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Ar.hasOwnProperty(i)?a!=null&&i==="onScroll"&&H("scroll",e):a!=null&&ta(e,i,a,s))}switch(n){case"input":co(e),_u(e,r,!1);break;case"textarea":co(e),Au(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Ot(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Ln(e,!!r.multiple,i,!1):r.defaultValue!=null&&Ln(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=ei)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return pe(t),null;case 6:if(e&&t.stateNode!=null)Op(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(k(166));if(n=qt(Ur.current),qt(nt.current),xo(t)){if(r=t.stateNode,n=t.memoizedProps,r[et]=t,(i=r.nodeValue!==n)&&(e=Re,e!==null))switch(e.tag){case 3:wo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&wo(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[et]=t,t.stateNode=r}return pe(t),null;case 13:if(K(G),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Q&&De!==null&&t.mode&1&&!(t.flags&128))bd(),Bn(),t.flags|=98560,i=!1;else if(i=xo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(k(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(k(317));i[et]=t}else Bn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;pe(t),i=!1}else Ge!==null&&(Al(Ge),Ge=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||G.current&1?oe===0&&(oe=3):za())),t.updateQueue!==null&&(t.flags|=4),pe(t),null);case 4:return $n(),Sl(e,t),e===null&&zr(t.stateNode.containerInfo),pe(t),null;case 10:return xa(t.type._context),pe(t),null;case 17:return Pe(t.type)&&ti(),pe(t),null;case 19:if(K(G),i=t.memoizedState,i===null)return pe(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)rr(i,!1);else{if(oe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=ai(e),s!==null){for(t.flags|=128,rr(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return $(G,G.current&1|2),t.child}e=e.sibling}i.tail!==null&&q()>Hn&&(t.flags|=128,r=!0,rr(i,!1),t.lanes=4194304)}else{if(!r)if(e=ai(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),rr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!Q)return pe(t),null}else 2*q()-i.renderingStartTime>Hn&&n!==1073741824&&(t.flags|=128,r=!0,rr(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=q(),t.sibling=null,n=G.current,$(G,r?n&1|2:n&1),t):(pe(t),null);case 22:case 23:return Fa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ae&1073741824&&(pe(t),t.subtreeFlags&6&&(t.flags|=8192)):pe(t),null;case 24:return null;case 25:return null}throw Error(k(156,t.tag))}function Iv(e,t){switch(va(t),t.tag){case 1:return Pe(t.type)&&ti(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return $n(),K(Ee),K(me),Pa(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ea(t),null;case 13:if(K(G),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(k(340));Bn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return K(G),null;case 4:return $n(),null;case 10:return xa(t.type._context),null;case 22:case 23:return Fa(),null;case 24:return null;default:return null}}var ko=!1,he=!1,jv=typeof WeakSet=="function"?WeakSet:Set,V=null;function Pn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Z(e,t,r)}else n.current=null}function Cl(e,t,n){try{n()}catch(r){Z(e,t,r)}}var gc=!1;function Bv(e,t){if(il=Jo,e=Bd(),ha(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,c=0,f=e,d=null;t:for(;;){for(var v;f!==n||o!==0&&f.nodeType!==3||(l=s+o),f!==i||r!==0&&f.nodeType!==3||(a=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(v=f.firstChild)!==null;)d=f,f=v;for(;;){if(f===e)break t;if(d===n&&++u===o&&(l=s),d===i&&++c===r&&(a=s),(v=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=v}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(sl={focusedElem:e,selectionRange:n},Jo=!1,V=t;V!==null;)if(t=V,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,V=e;else for(;V!==null;){t=V;try{var g=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var y=g.memoizedProps,x=g.memoizedState,p=t.stateNode,h=p.getSnapshotBeforeUpdate(t.elementType===t.type?y:Ke(t.type,y),x);p.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(k(163))}}catch(w){Z(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,V=e;break}V=t.return}return g=gc,gc=!1,g}function wr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Cl(t,n,i)}o=o.next}while(o!==r)}}function Fi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function kl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Fp(e){var t=e.alternate;t!==null&&(e.alternate=null,Fp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[et],delete t[jr],delete t[ul],delete t[Cv],delete t[kv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function zp(e){return e.tag===5||e.tag===3||e.tag===4}function wc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||zp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function El(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ei));else if(r!==4&&(e=e.child,e!==null))for(El(e,t,n),e=e.sibling;e!==null;)El(e,t,n),e=e.sibling}function Pl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Pl(e,t,n),e=e.sibling;e!==null;)Pl(e,t,n),e=e.sibling}var ae=null,Qe=!1;function yt(e,t,n){for(n=n.child;n!==null;)Ip(e,t,n),n=n.sibling}function Ip(e,t,n){if(tt&&typeof tt.onCommitFiberUnmount=="function")try{tt.onCommitFiberUnmount(Vi,n)}catch{}switch(n.tag){case 5:he||Pn(n,t);case 6:var r=ae,o=Qe;ae=null,yt(e,t,n),ae=r,Qe=o,ae!==null&&(Qe?(e=ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ae.removeChild(n.stateNode));break;case 18:ae!==null&&(Qe?(e=ae,n=n.stateNode,e.nodeType===8?hs(e.parentNode,n):e.nodeType===1&&hs(e,n),Nr(e)):hs(ae,n.stateNode));break;case 4:r=ae,o=Qe,ae=n.stateNode.containerInfo,Qe=!0,yt(e,t,n),ae=r,Qe=o;break;case 0:case 11:case 14:case 15:if(!he&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&Cl(n,t,s),o=o.next}while(o!==r)}yt(e,t,n);break;case 1:if(!he&&(Pn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Z(n,t,l)}yt(e,t,n);break;case 21:yt(e,t,n);break;case 22:n.mode&1?(he=(r=he)||n.memoizedState!==null,yt(e,t,n),he=r):yt(e,t,n);break;default:yt(e,t,n)}}function xc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new jv),t.forEach(function(r){var o=Xv.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function We(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:ae=l.stateNode,Qe=!1;break e;case 3:ae=l.stateNode.containerInfo,Qe=!0;break e;case 4:ae=l.stateNode.containerInfo,Qe=!0;break e}l=l.return}if(ae===null)throw Error(k(160));Ip(i,s,o),ae=null,Qe=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){Z(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)jp(t,e),t=t.sibling}function jp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(We(t,e),Je(e),r&4){try{wr(3,e,e.return),Fi(3,e)}catch(y){Z(e,e.return,y)}try{wr(5,e,e.return)}catch(y){Z(e,e.return,y)}}break;case 1:We(t,e),Je(e),r&512&&n!==null&&Pn(n,n.return);break;case 5:if(We(t,e),Je(e),r&512&&n!==null&&Pn(n,n.return),e.flags&32){var o=e.stateNode;try{Lr(o,"")}catch(y){Z(e,e.return,y)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&ld(o,i),Xs(l,s);var u=Xs(l,i);for(s=0;s<a.length;s+=2){var c=a[s],f=a[s+1];c==="style"?dd(o,f):c==="dangerouslySetInnerHTML"?cd(o,f):c==="children"?Lr(o,f):ta(o,c,f,u)}switch(l){case"input":Hs(o,i);break;case"textarea":ad(o,i);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var v=i.value;v!=null?Ln(o,!!i.multiple,v,!1):d!==!!i.multiple&&(i.defaultValue!=null?Ln(o,!!i.multiple,i.defaultValue,!0):Ln(o,!!i.multiple,i.multiple?[]:"",!1))}o[jr]=i}catch(y){Z(e,e.return,y)}}break;case 6:if(We(t,e),Je(e),r&4){if(e.stateNode===null)throw Error(k(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(y){Z(e,e.return,y)}}break;case 3:if(We(t,e),Je(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Nr(t.containerInfo)}catch(y){Z(e,e.return,y)}break;case 4:We(t,e),Je(e);break;case 13:We(t,e),Je(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Na=q())),r&4&&xc(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(he=(u=he)||c,We(t,e),he=u):We(t,e),Je(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(V=e,c=e.child;c!==null;){for(f=V=c;V!==null;){switch(d=V,v=d.child,d.tag){case 0:case 11:case 14:case 15:wr(4,d,d.return);break;case 1:Pn(d,d.return);var g=d.stateNode;if(typeof g.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(y){Z(r,n,y)}}break;case 5:Pn(d,d.return);break;case 22:if(d.memoizedState!==null){Cc(f);continue}}v!==null?(v.return=d,V=v):Cc(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{o=f.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=f.stateNode,a=f.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=fd("display",s))}catch(y){Z(e,e.return,y)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(y){Z(e,e.return,y)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:We(t,e),Je(e),r&4&&xc(e);break;case 21:break;default:We(t,e),Je(e)}}function Je(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(zp(n)){var r=n;break e}n=n.return}throw Error(k(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Lr(o,""),r.flags&=-33);var i=wc(e);Pl(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,l=wc(e);El(e,l,s);break;default:throw Error(k(161))}}catch(a){Z(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Uv(e,t,n){V=e,Bp(e)}function Bp(e,t,n){for(var r=(e.mode&1)!==0;V!==null;){var o=V,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||ko;if(!s){var l=o.alternate,a=l!==null&&l.memoizedState!==null||he;l=ko;var u=he;if(ko=s,(he=a)&&!u)for(V=o;V!==null;)s=V,a=s.child,s.tag===22&&s.memoizedState!==null?kc(o):a!==null?(a.return=s,V=a):kc(o);for(;i!==null;)V=i,Bp(i),i=i.sibling;V=o,ko=l,he=u}Sc(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,V=i):Sc(e)}}function Sc(e){for(;V!==null;){var t=V;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:he||Fi(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!he)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ke(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&ic(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ic(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&Nr(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(k(163))}he||t.flags&512&&kl(t)}catch(d){Z(t,t.return,d)}}if(t===e){V=null;break}if(n=t.sibling,n!==null){n.return=t.return,V=n;break}V=t.return}}function Cc(e){for(;V!==null;){var t=V;if(t===e){V=null;break}var n=t.sibling;if(n!==null){n.return=t.return,V=n;break}V=t.return}}function kc(e){for(;V!==null;){var t=V;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Fi(4,t)}catch(a){Z(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){Z(t,o,a)}}var i=t.return;try{kl(t)}catch(a){Z(t,i,a)}break;case 5:var s=t.return;try{kl(t)}catch(a){Z(t,s,a)}}}catch(a){Z(t,t.return,a)}if(t===e){V=null;break}var l=t.sibling;if(l!==null){l.return=t.return,V=l;break}V=t.return}}var $v=Math.ceil,fi=vt.ReactCurrentDispatcher,Ra=vt.ReactCurrentOwner,Be=vt.ReactCurrentBatchConfig,z=0,le=null,te=null,ce=0,Ae=0,Tn=Bt(0),oe=0,Kr=null,sn=0,zi=0,Ma=0,xr=null,Ce=null,Na=0,Hn=1/0,st=null,di=!1,Tl=null,Rt=null,Eo=!1,Pt=null,pi=0,Sr=0,_l=null,Bo=-1,Uo=0;function we(){return z&6?q():Bo!==-1?Bo:Bo=q()}function Mt(e){return e.mode&1?z&2&&ce!==0?ce&-ce:Pv.transition!==null?(Uo===0&&(Uo=Ed()),Uo):(e=B,e!==0||(e=window.event,e=e===void 0?16:Dd(e.type)),e):1}function Xe(e,t,n,r){if(50<Sr)throw Sr=0,_l=null,Error(k(185));qr(e,n,r),(!(z&2)||e!==le)&&(e===le&&(!(z&2)&&(zi|=n),oe===4&&kt(e,ce)),Te(e,r),n===1&&z===0&&!(t.mode&1)&&(Hn=q()+500,Mi&&Ut()))}function Te(e,t){var n=e.callbackNode;P0(e,t);var r=Zo(e,e===le?ce:0);if(r===0)n!==null&&Ru(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ru(n),t===1)e.tag===0?Ev(Ec.bind(null,e)):Zd(Ec.bind(null,e)),xv(function(){!(z&6)&&Ut()}),n=null;else{switch(Pd(r)){case 1:n=sa;break;case 4:n=Cd;break;case 16:n=Xo;break;case 536870912:n=kd;break;default:n=Xo}n=Yp(n,Up.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Up(e,t){if(Bo=-1,Uo=0,z&6)throw Error(k(327));var n=e.callbackNode;if(On()&&e.callbackNode!==n)return null;var r=Zo(e,e===le?ce:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=hi(e,r);else{t=r;var o=z;z|=2;var i=Wp();(le!==e||ce!==t)&&(st=null,Hn=q()+500,en(e,t));do try{Kv();break}catch(l){$p(e,l)}while(!0);wa(),fi.current=i,z=o,te!==null?t=0:(le=null,ce=0,t=oe)}if(t!==0){if(t===2&&(o=el(e),o!==0&&(r=o,t=Vl(e,o))),t===1)throw n=Kr,en(e,0),kt(e,r),Te(e,q()),n;if(t===6)kt(e,r);else{if(o=e.current.alternate,!(r&30)&&!Wv(o)&&(t=hi(e,r),t===2&&(i=el(e),i!==0&&(r=i,t=Vl(e,i))),t===1))throw n=Kr,en(e,0),kt(e,r),Te(e,q()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(k(345));case 2:Yt(e,Ce,st);break;case 3:if(kt(e,r),(r&130023424)===r&&(t=Na+500-q(),10<t)){if(Zo(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){we(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=al(Yt.bind(null,e,Ce,st),t);break}Yt(e,Ce,st);break;case 4:if(kt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-Ye(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=q()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*$v(r/1960))-r,10<r){e.timeoutHandle=al(Yt.bind(null,e,Ce,st),r);break}Yt(e,Ce,st);break;case 5:Yt(e,Ce,st);break;default:throw Error(k(329))}}}return Te(e,q()),e.callbackNode===n?Up.bind(null,e):null}function Vl(e,t){var n=xr;return e.current.memoizedState.isDehydrated&&(en(e,t).flags|=256),e=hi(e,t),e!==2&&(t=Ce,Ce=n,t!==null&&Al(t)),e}function Al(e){Ce===null?Ce=e:Ce.push.apply(Ce,e)}function Wv(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Ze(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function kt(e,t){for(t&=~Ma,t&=~zi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ye(t),r=1<<n;e[n]=-1,t&=~r}}function Ec(e){if(z&6)throw Error(k(327));On();var t=Zo(e,0);if(!(t&1))return Te(e,q()),null;var n=hi(e,t);if(e.tag!==0&&n===2){var r=el(e);r!==0&&(t=r,n=Vl(e,r))}if(n===1)throw n=Kr,en(e,0),kt(e,t),Te(e,q()),n;if(n===6)throw Error(k(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Yt(e,Ce,st),Te(e,q()),null}function Oa(e,t){var n=z;z|=1;try{return e(t)}finally{z=n,z===0&&(Hn=q()+500,Mi&&Ut())}}function ln(e){Pt!==null&&Pt.tag===0&&!(z&6)&&On();var t=z;z|=1;var n=Be.transition,r=B;try{if(Be.transition=null,B=1,e)return e()}finally{B=r,Be.transition=n,z=t,!(z&6)&&Ut()}}function Fa(){Ae=Tn.current,K(Tn)}function en(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,wv(n)),te!==null)for(n=te.return;n!==null;){var r=n;switch(va(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ti();break;case 3:$n(),K(Ee),K(me),Pa();break;case 5:Ea(r);break;case 4:$n();break;case 13:K(G);break;case 19:K(G);break;case 10:xa(r.type._context);break;case 22:case 23:Fa()}n=n.return}if(le=e,te=e=Nt(e.current,null),ce=Ae=t,oe=0,Kr=null,Ma=zi=sn=0,Ce=xr=null,Jt!==null){for(t=0;t<Jt.length;t++)if(n=Jt[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}Jt=null}return e}function $p(e,t){do{var n=te;try{if(wa(),zo.current=ci,ui){for(var r=Y.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}ui=!1}if(on=0,se=re=Y=null,gr=!1,$r=0,Ra.current=null,n===null||n.return===null){oe=1,Kr=t,te=null;break}e:{var i=e,s=n.return,l=n,a=t;if(t=ce,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var v=fc(s);if(v!==null){v.flags&=-257,dc(v,s,l,i,t),v.mode&1&&cc(i,u,t),t=v,a=u;var g=t.updateQueue;if(g===null){var y=new Set;y.add(a),t.updateQueue=y}else g.add(a);break e}else{if(!(t&1)){cc(i,u,t),za();break e}a=Error(k(426))}}else if(Q&&l.mode&1){var x=fc(s);if(x!==null){!(x.flags&65536)&&(x.flags|=256),dc(x,s,l,i,t),ya(Wn(a,l));break e}}i=a=Wn(a,l),oe!==4&&(oe=2),xr===null?xr=[i]:xr.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var p=Pp(i,a,t);oc(i,p);break e;case 1:l=a;var h=i.type,m=i.stateNode;if(!(i.flags&128)&&(typeof h.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(Rt===null||!Rt.has(m)))){i.flags|=65536,t&=-t,i.lanes|=t;var w=Tp(i,l,t);oc(i,w);break e}}i=i.return}while(i!==null)}Kp(n)}catch(C){t=C,te===n&&n!==null&&(te=n=n.return);continue}break}while(!0)}function Wp(){var e=fi.current;return fi.current=ci,e===null?ci:e}function za(){(oe===0||oe===3||oe===2)&&(oe=4),le===null||!(sn&268435455)&&!(zi&268435455)||kt(le,ce)}function hi(e,t){var n=z;z|=2;var r=Wp();(le!==e||ce!==t)&&(st=null,en(e,t));do try{Hv();break}catch(o){$p(e,o)}while(!0);if(wa(),z=n,fi.current=r,te!==null)throw Error(k(261));return le=null,ce=0,oe}function Hv(){for(;te!==null;)Hp(te)}function Kv(){for(;te!==null&&!v0();)Hp(te)}function Hp(e){var t=Gp(e.alternate,e,Ae);e.memoizedProps=e.pendingProps,t===null?Kp(e):te=t,Ra.current=null}function Kp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Iv(n,t),n!==null){n.flags&=32767,te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{oe=6,te=null;return}}else if(n=zv(n,t,Ae),n!==null){te=n;return}if(t=t.sibling,t!==null){te=t;return}te=t=e}while(t!==null);oe===0&&(oe=5)}function Yt(e,t,n){var r=B,o=Be.transition;try{Be.transition=null,B=1,Qv(e,t,n,r)}finally{Be.transition=o,B=r}return null}function Qv(e,t,n,r){do On();while(Pt!==null);if(z&6)throw Error(k(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(k(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(T0(e,i),e===le&&(te=le=null,ce=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Eo||(Eo=!0,Yp(Xo,function(){return On(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Be.transition,Be.transition=null;var s=B;B=1;var l=z;z|=4,Ra.current=null,Bv(e,n),jp(n,e),dv(sl),Jo=!!il,sl=il=null,e.current=n,Uv(n),y0(),z=l,B=s,Be.transition=i}else e.current=n;if(Eo&&(Eo=!1,Pt=e,pi=o),i=e.pendingLanes,i===0&&(Rt=null),x0(n.stateNode),Te(e,q()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(di)throw di=!1,e=Tl,Tl=null,e;return pi&1&&e.tag!==0&&On(),i=e.pendingLanes,i&1?e===_l?Sr++:(Sr=0,_l=e):Sr=0,Ut(),null}function On(){if(Pt!==null){var e=Pd(pi),t=Be.transition,n=B;try{if(Be.transition=null,B=16>e?16:e,Pt===null)var r=!1;else{if(e=Pt,Pt=null,pi=0,z&6)throw Error(k(331));var o=z;for(z|=4,V=e.current;V!==null;){var i=V,s=i.child;if(V.flags&16){var l=i.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(V=u;V!==null;){var c=V;switch(c.tag){case 0:case 11:case 15:wr(8,c,i)}var f=c.child;if(f!==null)f.return=c,V=f;else for(;V!==null;){c=V;var d=c.sibling,v=c.return;if(Fp(c),c===u){V=null;break}if(d!==null){d.return=v,V=d;break}V=v}}}var g=i.alternate;if(g!==null){var y=g.child;if(y!==null){g.child=null;do{var x=y.sibling;y.sibling=null,y=x}while(y!==null)}}V=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,V=s;else e:for(;V!==null;){if(i=V,i.flags&2048)switch(i.tag){case 0:case 11:case 15:wr(9,i,i.return)}var p=i.sibling;if(p!==null){p.return=i.return,V=p;break e}V=i.return}}var h=e.current;for(V=h;V!==null;){s=V;var m=s.child;if(s.subtreeFlags&2064&&m!==null)m.return=s,V=m;else e:for(s=h;V!==null;){if(l=V,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Fi(9,l)}}catch(C){Z(l,l.return,C)}if(l===s){V=null;break e}var w=l.sibling;if(w!==null){w.return=l.return,V=w;break e}V=l.return}}if(z=o,Ut(),tt&&typeof tt.onPostCommitFiberRoot=="function")try{tt.onPostCommitFiberRoot(Vi,e)}catch{}r=!0}return r}finally{B=n,Be.transition=t}}return!1}function Pc(e,t,n){t=Wn(n,t),t=Pp(e,t,1),e=Dt(e,t,1),t=we(),e!==null&&(qr(e,1,t),Te(e,t))}function Z(e,t,n){if(e.tag===3)Pc(e,e,n);else for(;t!==null;){if(t.tag===3){Pc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Rt===null||!Rt.has(r))){e=Wn(n,e),e=Tp(t,e,1),t=Dt(t,e,1),e=we(),t!==null&&(qr(t,1,e),Te(t,e));break}}t=t.return}}function Gv(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=we(),e.pingedLanes|=e.suspendedLanes&n,le===e&&(ce&n)===n&&(oe===4||oe===3&&(ce&130023424)===ce&&500>q()-Na?en(e,0):Ma|=n),Te(e,t)}function Qp(e,t){t===0&&(e.mode&1?(t=ho,ho<<=1,!(ho&130023424)&&(ho=4194304)):t=1);var n=we();e=ht(e,t),e!==null&&(qr(e,t,n),Te(e,n))}function Yv(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Qp(e,n)}function Xv(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(k(314))}r!==null&&r.delete(t),Qp(e,n)}var Gp;Gp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ee.current)ke=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ke=!1,Fv(e,t,n);ke=!!(e.flags&131072)}else ke=!1,Q&&t.flags&1048576&&Jd(t,oi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;jo(e,t),e=t.pendingProps;var o=jn(t,me.current);Nn(t,n),o=_a(null,t,r,e,o,n);var i=Va();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Pe(r)?(i=!0,ni(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Ca(t),o.updater=Oi,t.stateNode=o,o._reactInternals=t,ml(t,r,e,n),t=gl(null,t,r,!0,i,n)):(t.tag=0,Q&&i&&ma(t),ge(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(jo(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=Jv(r),e=Ke(r,e),o){case 0:t=yl(null,t,r,e,n);break e;case 1:t=mc(null,t,r,e,n);break e;case 11:t=pc(null,t,r,e,n);break e;case 14:t=hc(null,t,r,Ke(r.type,e),n);break e}throw Error(k(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ke(r,o),yl(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ke(r,o),mc(e,t,r,o,n);case 3:e:{if(Lp(t),e===null)throw Error(k(387));r=t.pendingProps,i=t.memoizedState,o=i.element,rp(e,t),li(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=Wn(Error(k(423)),t),t=vc(e,t,r,n,o);break e}else if(r!==o){o=Wn(Error(k(424)),t),t=vc(e,t,r,n,o);break e}else for(De=Lt(t.stateNode.containerInfo.firstChild),Re=t,Q=!0,Ge=null,n=tp(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Bn(),r===o){t=mt(e,t,n);break e}ge(e,t,r,n)}t=t.child}return t;case 5:return op(t),e===null&&dl(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,ll(r,o)?s=null:i!==null&&ll(r,i)&&(t.flags|=32),Ap(e,t),ge(e,t,s,n),t.child;case 6:return e===null&&dl(t),null;case 13:return Dp(e,t,n);case 4:return ka(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Un(t,null,r,n):ge(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ke(r,o),pc(e,t,r,o,n);case 7:return ge(e,t,t.pendingProps,n),t.child;case 8:return ge(e,t,t.pendingProps.children,n),t.child;case 12:return ge(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,$(ii,r._currentValue),r._currentValue=s,i!==null)if(Ze(i.value,s)){if(i.children===o.children&&!Ee.current){t=mt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){s=i.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=ct(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),pl(i.return,n,t),l.lanes|=n;break}a=a.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(k(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),pl(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}ge(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Nn(t,n),o=Ue(o),r=r(o),t.flags|=1,ge(e,t,r,n),t.child;case 14:return r=t.type,o=Ke(r,t.pendingProps),o=Ke(r.type,o),hc(e,t,r,o,n);case 15:return _p(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ke(r,o),jo(e,t),t.tag=1,Pe(r)?(e=!0,ni(t)):e=!1,Nn(t,n),Ep(t,r,o),ml(t,r,o,n),gl(null,t,r,!0,e,n);case 19:return Rp(e,t,n);case 22:return Vp(e,t,n)}throw Error(k(156,t.tag))};function Yp(e,t){return Sd(e,t)}function Zv(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function je(e,t,n,r){return new Zv(e,t,n,r)}function Ia(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Jv(e){if(typeof e=="function")return Ia(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ra)return 11;if(e===oa)return 14}return 2}function Nt(e,t){var n=e.alternate;return n===null?(n=je(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function $o(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")Ia(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case vn:return tn(n.children,o,i,t);case na:s=8,o|=8;break;case js:return e=je(12,n,t,o|2),e.elementType=js,e.lanes=i,e;case Bs:return e=je(13,n,t,o),e.elementType=Bs,e.lanes=i,e;case Us:return e=je(19,n,t,o),e.elementType=Us,e.lanes=i,e;case od:return Ii(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case nd:s=10;break e;case rd:s=9;break e;case ra:s=11;break e;case oa:s=14;break e;case wt:s=16,r=null;break e}throw Error(k(130,e==null?e:typeof e,""))}return t=je(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function tn(e,t,n,r){return e=je(7,e,r,t),e.lanes=n,e}function Ii(e,t,n,r){return e=je(22,e,r,t),e.elementType=od,e.lanes=n,e.stateNode={isHidden:!1},e}function Cs(e,t,n){return e=je(6,e,null,t),e.lanes=n,e}function ks(e,t,n){return t=je(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function qv(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=rs(0),this.expirationTimes=rs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=rs(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function ja(e,t,n,r,o,i,s,l,a){return e=new qv(e,t,n,l,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=je(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ca(i),e}function bv(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:mn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Xp(e){if(!e)return Ft;e=e._reactInternals;e:{if(un(e)!==e||e.tag!==1)throw Error(k(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Pe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(k(171))}if(e.tag===1){var n=e.type;if(Pe(n))return Xd(e,n,t)}return t}function Zp(e,t,n,r,o,i,s,l,a){return e=ja(n,r,!0,e,o,i,s,l,a),e.context=Xp(null),n=e.current,r=we(),o=Mt(n),i=ct(r,o),i.callback=t!=null?t:null,Dt(n,i,o),e.current.lanes=o,qr(e,o,r),Te(e,r),e}function ji(e,t,n,r){var o=t.current,i=we(),s=Mt(o);return n=Xp(n),t.context===null?t.context=n:t.pendingContext=n,t=ct(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Dt(o,t,s),e!==null&&(Xe(e,o,s,i),Fo(e,o,s)),s}function mi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Tc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Ba(e,t){Tc(e,t),(e=e.alternate)&&Tc(e,t)}function ey(){return null}var Jp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ua(e){this._internalRoot=e}Bi.prototype.render=Ua.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(k(409));ji(e,t,null,null)};Bi.prototype.unmount=Ua.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;ln(function(){ji(null,e,null,null)}),t[pt]=null}};function Bi(e){this._internalRoot=e}Bi.prototype.unstable_scheduleHydration=function(e){if(e){var t=Vd();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ct.length&&t!==0&&t<Ct[n].priority;n++);Ct.splice(n,0,e),n===0&&Ld(e)}};function $a(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ui(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function _c(){}function ty(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=mi(s);i.call(u)}}var s=Zp(t,r,e,0,null,!1,!1,"",_c);return e._reactRootContainer=s,e[pt]=s.current,zr(e.nodeType===8?e.parentNode:e),ln(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var l=r;r=function(){var u=mi(a);l.call(u)}}var a=ja(e,0,!1,null,null,!1,!1,"",_c);return e._reactRootContainer=a,e[pt]=a.current,zr(e.nodeType===8?e.parentNode:e),ln(function(){ji(t,a,n,r)}),a}function $i(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var l=o;o=function(){var a=mi(s);l.call(a)}}ji(t,s,e,o)}else s=ty(n,t,e,o,r);return mi(s)}Td=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ur(t.pendingLanes);n!==0&&(la(t,n|1),Te(t,q()),!(z&6)&&(Hn=q()+500,Ut()))}break;case 13:ln(function(){var r=ht(e,1);if(r!==null){var o=we();Xe(r,e,1,o)}}),Ba(e,1)}};aa=function(e){if(e.tag===13){var t=ht(e,134217728);if(t!==null){var n=we();Xe(t,e,134217728,n)}Ba(e,134217728)}};_d=function(e){if(e.tag===13){var t=Mt(e),n=ht(e,t);if(n!==null){var r=we();Xe(n,e,t,r)}Ba(e,t)}};Vd=function(){return B};Ad=function(e,t){var n=B;try{return B=e,t()}finally{B=n}};Js=function(e,t,n){switch(t){case"input":if(Hs(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Ri(r);if(!o)throw Error(k(90));sd(r),Hs(r,o)}}}break;case"textarea":ad(e,n);break;case"select":t=n.value,t!=null&&Ln(e,!!n.multiple,t,!1)}};md=Oa;vd=ln;var ny={usingClientEntryPoint:!1,Events:[eo,xn,Ri,pd,hd,Oa]},or={findFiberByHostInstance:Zt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},ry={bundleType:or.bundleType,version:or.version,rendererPackageName:or.rendererPackageName,rendererConfig:or.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:vt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=wd(e),e===null?null:e.stateNode},findFiberByHostInstance:or.findFiberByHostInstance||ey,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Po=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Po.isDisabled&&Po.supportsFiber)try{Vi=Po.inject(ry),tt=Po}catch{}}Oe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ny;Oe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!$a(t))throw Error(k(200));return bv(e,t,null,n)};Oe.createRoot=function(e,t){if(!$a(e))throw Error(k(299));var n=!1,r="",o=Jp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=ja(e,1,!1,null,null,n,!1,r,o),e[pt]=t.current,zr(e.nodeType===8?e.parentNode:e),new Ua(t)};Oe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(k(188)):(e=Object.keys(e).join(","),Error(k(268,e)));return e=wd(t),e=e===null?null:e.stateNode,e};Oe.flushSync=function(e){return ln(e)};Oe.hydrate=function(e,t,n){if(!Ui(t))throw Error(k(200));return $i(null,e,t,!0,n)};Oe.hydrateRoot=function(e,t,n){if(!$a(e))throw Error(k(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=Jp;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Zp(t,null,e,1,n!=null?n:null,o,!1,i,s),e[pt]=t.current,zr(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Bi(t)};Oe.render=function(e,t,n){if(!Ui(t))throw Error(k(200));return $i(null,e,t,!1,n)};Oe.unmountComponentAtNode=function(e){if(!Ui(e))throw Error(k(40));return e._reactRootContainer?(ln(function(){$i(null,null,e,!1,function(){e._reactRootContainer=null,e[pt]=null})}),!0):!1};Oe.unstable_batchedUpdates=Oa;Oe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ui(n))throw Error(k(200));if(e==null||e._reactInternals===void 0)throw Error(k(38));return $i(e,t,n,!1,r)};Oe.version="18.3.1-next-f1338f8080-20240426";function qp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(qp)}catch(e){console.error(e)}}qp(),qf.exports=Oe;var bp=qf.exports;const oy=Bf(bp);var eh,Vc=oy;eh=Vc.createRoot,Vc.hydrateRoot;const th="3.7.7",iy=th,Zn=typeof Buffer=="function",Ac=typeof TextDecoder=="function"?new TextDecoder:void 0,Lc=typeof TextEncoder=="function"?new TextEncoder:void 0,sy="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",fr=Array.prototype.slice.call(sy),To=(e=>{let t={};return e.forEach((n,r)=>t[n]=r),t})(fr),ly=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,ue=String.fromCharCode.bind(String),Dc=typeof Uint8Array.from=="function"?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),nh=e=>e.replace(/=/g,"").replace(/[+\/]/g,t=>t=="+"?"-":"_"),rh=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),oh=e=>{let t,n,r,o,i="";const s=e.length%3;for(let l=0;l<e.length;){if((n=e.charCodeAt(l++))>255||(r=e.charCodeAt(l++))>255||(o=e.charCodeAt(l++))>255)throw new TypeError("invalid character found");t=n<<16|r<<8|o,i+=fr[t>>18&63]+fr[t>>12&63]+fr[t>>6&63]+fr[t&63]}return s?i.slice(0,s-3)+"===".substring(s):i},Wa=typeof btoa=="function"?e=>btoa(e):Zn?e=>Buffer.from(e,"binary").toString("base64"):oh,Ll=Zn?e=>Buffer.from(e).toString("base64"):e=>{let n=[];for(let r=0,o=e.length;r<o;r+=4096)n.push(ue.apply(null,e.subarray(r,r+4096)));return Wa(n.join(""))},Wo=(e,t=!1)=>t?nh(Ll(e)):Ll(e),ay=e=>{if(e.length<2){var t=e.charCodeAt(0);return t<128?e:t<2048?ue(192|t>>>6)+ue(128|t&63):ue(224|t>>>12&15)+ue(128|t>>>6&63)+ue(128|t&63)}else{var t=65536+(e.charCodeAt(0)-55296)*1024+(e.charCodeAt(1)-56320);return ue(240|t>>>18&7)+ue(128|t>>>12&63)+ue(128|t>>>6&63)+ue(128|t&63)}},uy=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,ih=e=>e.replace(uy,ay),Rc=Zn?e=>Buffer.from(e,"utf8").toString("base64"):Lc?e=>Ll(Lc.encode(e)):e=>Wa(ih(e)),Fn=(e,t=!1)=>t?nh(Rc(e)):Rc(e),Mc=e=>Fn(e,!0),cy=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,fy=e=>{switch(e.length){case 4:var t=(7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3),n=t-65536;return ue((n>>>10)+55296)+ue((n&1023)+56320);case 3:return ue((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return ue((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},sh=e=>e.replace(cy,fy),lh=e=>{if(e=e.replace(/\s+/g,""),!ly.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(e.length&3));let t,n="",r,o;for(let i=0;i<e.length;)t=To[e.charAt(i++)]<<18|To[e.charAt(i++)]<<12|(r=To[e.charAt(i++)])<<6|(o=To[e.charAt(i++)]),n+=r===64?ue(t>>16&255):o===64?ue(t>>16&255,t>>8&255):ue(t>>16&255,t>>8&255,t&255);return n},Ha=typeof atob=="function"?e=>atob(rh(e)):Zn?e=>Buffer.from(e,"base64").toString("binary"):lh,ah=Zn?e=>Dc(Buffer.from(e,"base64")):e=>Dc(Ha(e).split("").map(t=>t.charCodeAt(0))),uh=e=>ah(ch(e)),dy=Zn?e=>Buffer.from(e,"base64").toString("utf8"):Ac?e=>Ac.decode(ah(e)):e=>sh(Ha(e)),ch=e=>rh(e.replace(/[-_]/g,t=>t=="-"?"+":"/")),Dl=e=>dy(ch(e)),py=e=>{if(typeof e!="string")return!1;const t=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},fh=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),dh=function(){const e=(t,n)=>Object.defineProperty(String.prototype,t,fh(n));e("fromBase64",function(){return Dl(this)}),e("toBase64",function(t){return Fn(this,t)}),e("toBase64URI",function(){return Fn(this,!0)}),e("toBase64URL",function(){return Fn(this,!0)}),e("toUint8Array",function(){return uh(this)})},ph=function(){const e=(t,n)=>Object.defineProperty(Uint8Array.prototype,t,fh(n));e("toBase64",function(t){return Wo(this,t)}),e("toBase64URI",function(){return Wo(this,!0)}),e("toBase64URL",function(){return Wo(this,!0)})},hy=()=>{dh(),ph()},my={version:th,VERSION:iy,atob:Ha,atobPolyfill:lh,btoa:Wa,btoaPolyfill:oh,fromBase64:Dl,toBase64:Fn,encode:Fn,encodeURI:Mc,encodeURL:Mc,utob:ih,btou:sh,decode:Dl,isValid:py,fromUint8Array:Wo,toUint8Array:uh,extendString:dh,extendUint8Array:ph,extendBuiltins:hy};function vy(e){if(typeof e=="object")return e;if(typeof e!="string")return null;try{return e.startsWith("{")?JSON.parse(e):yy(e)}catch(t){return console.log("parse failed:"+t.message),null}}function yy(e){var t=new window.DOMParser().parseFromString(e,"text/xml"),n=t.getElementsByTagName("componentData");if(!n.length){var r,o=(r=e.match(/<templateData>(.*)<\/templateData>/))===null||r===void 0?void 0:r[1];return JSON.parse(my.decode(o))}var i={};for(var s of n)for(var l of s.getElementsByTagName("data")||[])if(l.getAttribute("value")!=null){i[s.getAttribute("id")]=l.getAttribute("value");break}return i}var Ka=Le.createContext(),gy=e=>{var{children:t,name:n}=e,[r,o]=S.useState(Ve.loading),[i,s]=S.useState({}),[l,a]=S.useState([]),[u,c]=S.useState(),[f,d]=S.useState(!1),v=x=>{console.log("".concat(n||"").concat(x))},g=S.useCallback(x=>(a(p=>[...p,x]),()=>{a(p=>p.filter(h=>h!==x))}),[]);S.useLayoutEffect(()=>{var x=!1;return window.load=()=>{o(Ve.loaded),v(".load()")},window.play=()=>{x=!0,d(!0),v(".play()")},window.pause=()=>{o(Ve.paused),v(".pause()")},window.stop=()=>{x?(o(Ve.stopped),v(".stop()")):(o(Ve.removed),v(".stop() without play"))},window.update=p=>{var h=vy(p);if(h&&(v(".update(".concat(h?JSON.stringify(h||{},null,2):"null",")")),s(h),!x)){var m=g("__initialData");c(()=>m)}},()=>{delete window.load,delete window.play,delete window.pause,delete window.stop,delete window.update}},[]),S.useEffect(()=>{u==null||u()},[u]),S.useEffect(()=>{r<Ve.playing&&f&&!l.length&&o(Ve.playing)},[r,i,f,l]),S.useEffect(()=>{if(r===Ve.removed){var x,p;v(".remove()"),(x=(p=window).remove)===null||x===void 0||x.call(p)}},[r]);var y=S.useCallback(()=>{o(Ve.removed)},[]);return Le.createElement(Ka.Provider,{value:{data:i,state:r,name:n,safeToRemove:y,delayPlay:g}},r!==Ve.removed?Le.createElement(wy,null,t):null)},wy=S.memo(e=>{var{children:t}=e;return t}),Es=null,xy=(e,t)=>{var{container:n=document.getElementById("root"),cssReset:r=!0,name:o=e.name}={};if(n||(n=document.createElement("div"),n.id="root",document.body.appendChild(n)),r){var i=` 
      width: 100vw;
      height: 100vh;
      overflow: hidden;
      margin: 0;
    `;document.body.style.cssText=i,n.style.cssText=i}Es||(Es=eh(n)),bp.flushSync(()=>{Es.render(S.createElement(gy,{name:o},S.createElement(e)))})};function Nc(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function Oc(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Nc(Object(n),!0).forEach(function(r){ky(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Nc(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function Sy(e,t){if(typeof e!="object"||!e)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Cy(e){var t=Sy(e,"string");return typeof t=="symbol"?t:String(t)}function ky(e,t,n){return t=Cy(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ey(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function Py(e,t){if(e==null)return{};var n=Ey(e,t),r,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}var Ty=(e,t)=>{var n=Le.useRef(null),r=Le.useRef(e);return Le.useEffect(()=>{r.current=e},[e]),Le.useEffect(()=>{var o=()=>r.current();if(typeof t=="number")return n.current=window.setTimeout(o,t),()=>window.clearTimeout(n.current)},[t]),n},_y=["state","safeToRemove"],Vy=e=>{var t=Le.useContext(Ka),{state:n,safeToRemove:r}=t,o=Py(t,_y),i=hh();return Ty(r,n===Ve.stopped&&Number.isFinite(void 0)?e.removeDelay*1e3:null),Oc(Oc({},o),{},{data:i,state:n,safeToRemove:r,isPlaying:n===Ve.playing,isStopped:n===Ve.stopped})},hh=e=>{var{data:t}=Le.useContext(Ka),{trim:n=!0}={};return S.useMemo(()=>{if(!n)return t;var r={};for(var[o,i]of Object.entries(t))r[o]=typeof i=="string"?i.trim():i;return r},[t,n])};const Qa=S.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Wi=S.createContext({});function Ay(){return S.useContext(Wi).visualElement}const no=S.createContext(null),cn=typeof document<"u",Cr=cn?S.useLayoutEffect:S.useEffect,mh=S.createContext({strict:!1});function Ly(e,t,n,r){const o=Ay(),i=S.useContext(mh),s=S.useContext(no),l=S.useContext(Qa).reducedMotion,a=S.useRef();r=r||i.renderer,!a.current&&r&&(a.current=r(e,{visualState:t,parent:o,props:n,presenceId:s?s.id:void 0,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:l}));const u=a.current;return Cr(()=>{u&&u.render()}),Cr(()=>{u&&u.animationState&&u.animationState.animateChanges()}),Cr(()=>()=>u&&u.notify("Unmount"),[]),u}function _n(e){return typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function Dy(e,t,n){return S.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):_n(n)&&(n.current=r))},[t])}function Qr(e){return typeof e=="string"||Array.isArray(e)}function Hi(e){return typeof e=="object"&&typeof e.start=="function"}const Ry=["initial","animate","exit","whileHover","whileDrag","whileTap","whileFocus","whileInView"];function Ki(e){return Hi(e.animate)||Ry.some(t=>Qr(e[t]))}function vh(e){return!!(Ki(e)||e.variants)}function My(e,t){if(Ki(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Qr(n)?n:void 0,animate:Qr(r)?r:void 0}}return e.inherit!==!1?t:{}}function Ny(e){const{initial:t,animate:n}=My(e,S.useContext(Wi));return S.useMemo(()=>({initial:t,animate:n}),[Fc(t),Fc(n)])}function Fc(e){return Array.isArray(e)?e.join(" "):e}const it=e=>({isEnabled:t=>e.some(n=>!!t[n])}),Gr={measureLayout:it(["layout","layoutId","drag"]),animation:it(["animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView"]),exit:it(["exit"]),drag:it(["drag","dragControls"]),focus:it(["whileFocus"]),hover:it(["whileHover","onHoverStart","onHoverEnd"]),tap:it(["whileTap","onTap","onTapStart","onTapCancel"]),pan:it(["onPan","onPanStart","onPanSessionStart","onPanEnd"]),inView:it(["whileInView","onViewportEnter","onViewportLeave"])};function Oy(e){for(const t in e)t==="projectionNodeConstructor"?Gr.projectionNodeConstructor=e[t]:Gr[t].Component=e[t]}function Qi(e){const t=S.useRef(null);return t.current===null&&(t.current=e()),t.current}const kr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};let Fy=1;function zy(){return Qi(()=>{if(kr.hasEverUpdated)return Fy++})}const Ga=S.createContext({});class Iy extends Le.Component{getSnapshotBeforeUpdate(){const{visualElement:t,props:n}=this.props;return t&&t.setProps(n),null}componentDidUpdate(){}render(){return this.props.children}}const yh=S.createContext({}),jy=Symbol.for("motionComponentSymbol");function By({preloadedFeatures:e,createVisualElement:t,projectionNodeConstructor:n,useRender:r,useVisualState:o,Component:i}){e&&Oy(e);function s(a,u){const c={...S.useContext(Qa),...a,layoutId:Uy(a)},{isStatic:f}=c;let d=null;const v=Ny(a),g=f?void 0:zy(),y=o(a,f);if(!f&&cn){v.visualElement=Ly(i,y,c,t);const x=S.useContext(mh).strict,p=S.useContext(yh);v.visualElement&&(d=v.visualElement.loadFeatures(c,x,e,g,n||Gr.projectionNodeConstructor,p))}return S.createElement(Iy,{visualElement:v.visualElement,props:c},d,S.createElement(Wi.Provider,{value:v},r(i,a,g,Dy(y,v.visualElement,u),y,f,v.visualElement)))}const l=S.forwardRef(s);return l[jy]=i,l}function Uy({layoutId:e}){const t=S.useContext(Ga).id;return t&&e!==void 0?t+"-"+e:e}function $y(e){function t(r,o={}){return By(e(r,o))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,o)=>(n.has(o)||n.set(o,t(o)),n.get(o))})}const Wy=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Ya(e){return typeof e!="string"||e.includes("-")?!1:!!(Wy.indexOf(e)>-1||/[A-Z]/.test(e))}const vi={};function Hy(e){Object.assign(vi,e)}const yi=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],fn=new Set(yi);function gh(e,{layout:t,layoutId:n}){return fn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!vi[e]||e==="opacity")}const ot=e=>!!(e!=null&&e.getVelocity),Ky={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Qy=(e,t)=>yi.indexOf(e)-yi.indexOf(t);function Gy({transform:e,transformKeys:t},{enableHardwareAcceleration:n=!0,allowTransformNone:r=!0},o,i){let s="";t.sort(Qy);for(const l of t)s+=`${Ky[l]||l}(${e[l]}) `;return n&&!e.z&&(s+="translateZ(0)"),s=s.trim(),i?s=i(e,o?"":s):r&&o&&(s="none"),s}function wh(e){return e.startsWith("--")}const Yy=(e,t)=>t&&typeof e=="number"?t.transform(e):e,Kn=(e,t,n)=>Math.min(Math.max(n,e),t),dn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Er={...dn,transform:e=>Kn(0,1,e)},_o={...dn,default:1},Pr=e=>Math.round(e*1e5)/1e5,Yr=/(-)?([\d]*\.?[\d])+/g,Rl=/(#[0-9a-f]{6}|#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Xy=/^(#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function ro(e){return typeof e=="string"}const oo=e=>({test:t=>ro(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),gt=oo("deg"),rt=oo("%"),L=oo("px"),Zy=oo("vh"),Jy=oo("vw"),zc={...rt,parse:e=>rt.parse(e)/100,transform:e=>rt.transform(e*100)},Ic={...dn,transform:Math.round},xh={borderWidth:L,borderTopWidth:L,borderRightWidth:L,borderBottomWidth:L,borderLeftWidth:L,borderRadius:L,radius:L,borderTopLeftRadius:L,borderTopRightRadius:L,borderBottomRightRadius:L,borderBottomLeftRadius:L,width:L,maxWidth:L,height:L,maxHeight:L,size:L,top:L,right:L,bottom:L,left:L,padding:L,paddingTop:L,paddingRight:L,paddingBottom:L,paddingLeft:L,margin:L,marginTop:L,marginRight:L,marginBottom:L,marginLeft:L,rotate:gt,rotateX:gt,rotateY:gt,rotateZ:gt,scale:_o,scaleX:_o,scaleY:_o,scaleZ:_o,skew:gt,skewX:gt,skewY:gt,distance:L,translateX:L,translateY:L,translateZ:L,x:L,y:L,z:L,perspective:L,transformPerspective:L,opacity:Er,originX:zc,originY:zc,originZ:L,zIndex:Ic,fillOpacity:Er,strokeOpacity:Er,numOctaves:Ic};function Xa(e,t,n,r){const{style:o,vars:i,transform:s,transformKeys:l,transformOrigin:a}=e;l.length=0;let u=!1,c=!1,f=!0;for(const d in t){const v=t[d];if(wh(d)){i[d]=v;continue}const g=xh[d],y=Yy(v,g);if(fn.has(d)){if(u=!0,s[d]=y,l.push(d),!f)continue;v!==(g.default||0)&&(f=!1)}else d.startsWith("origin")?(c=!0,a[d]=y):o[d]=y}if(t.transform||(u||r?o.transform=Gy(e,n,f,r):o.transform&&(o.transform="none")),c){const{originX:d="50%",originY:v="50%",originZ:g=0}=a;o.transformOrigin=`${d} ${v} ${g}`}}const Za=()=>({style:{},transform:{},transformKeys:[],transformOrigin:{},vars:{}});function Sh(e,t,n){for(const r in t)!ot(t[r])&&!gh(r,n)&&(e[r]=t[r])}function qy({transformTemplate:e},t,n){return S.useMemo(()=>{const r=Za();return Xa(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function by(e,t,n){const r=e.style||{},o={};return Sh(o,r,e),Object.assign(o,qy(e,t,n)),e.transformValues?e.transformValues(o):o}function eg(e,t,n){const r={},o=by(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,o.userSelect=o.WebkitUserSelect=o.WebkitTouchCallout="none",o.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),r.style=o,r}const tg=["animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView"],ng=["whileTap","onTap","onTapStart","onTapCancel"],rg=["onPan","onPanStart","onPanSessionStart","onPanEnd"],og=["whileInView","onViewportEnter","onViewportLeave","viewport"],ig=new Set(["initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","layout","layoutId","layoutDependency","onLayoutAnimationStart","onLayoutAnimationComplete","onLayoutMeasure","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","drag","dragControls","dragListener","dragConstraints","dragDirectionLock","dragSnapToOrigin","_dragX","_dragY","dragElastic","dragMomentum","dragPropagation","dragTransition","onHoverStart","onHoverEnd","layoutScroll",...og,...ng,...tg,...rg]);function gi(e){return ig.has(e)}let Ch=e=>!gi(e);function sg(e){e&&(Ch=t=>t.startsWith("on")?!gi(t):e(t))}try{sg(require("@emotion/is-prop-valid").default)}catch{}function lg(e,t,n){const r={};for(const o in e)(Ch(o)||n===!0&&gi(o)||!t&&!gi(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}function jc(e,t,n){return typeof e=="string"?e:L.transform(t+n*e)}function ag(e,t,n){const r=jc(t,e.x,e.width),o=jc(n,e.y,e.height);return`${r} ${o}`}const ug={offset:"stroke-dashoffset",array:"stroke-dasharray"},cg={offset:"strokeDashoffset",array:"strokeDasharray"};function fg(e,t,n=1,r=0,o=!0){e.pathLength=1;const i=o?ug:cg;e[i.offset]=L.transform(-r);const s=L.transform(t),l=L.transform(n);e[i.array]=`${s} ${l}`}function Ja(e,{attrX:t,attrY:n,originX:r,originY:o,pathLength:i,pathSpacing:s=1,pathOffset:l=0,...a},u,c,f){if(Xa(e,a,u,f),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:d,style:v,dimensions:g}=e;d.transform&&(g&&(v.transform=d.transform),delete d.transform),g&&(r!==void 0||o!==void 0||v.transform)&&(v.transformOrigin=ag(g,r!==void 0?r:.5,o!==void 0?o:.5)),t!==void 0&&(d.x=t),n!==void 0&&(d.y=n),i!==void 0&&fg(d,i,s,l,!1)}const kh=()=>({...Za(),attrs:{}}),qa=e=>typeof e=="string"&&e.toLowerCase()==="svg";function dg(e,t,n,r){const o=S.useMemo(()=>{const i=kh();return Ja(i,t,{enableHardwareAcceleration:!1},qa(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){const i={};Sh(i,e.style,e),o.style={...i,...o.style}}return o}function pg(e=!1){return(n,r,o,i,{latestValues:s},l)=>{const u=(Ya(n)?dg:eg)(r,s,l,n),f={...lg(r,typeof n=="string",e),...u,ref:i};return o&&(f["data-projection-id"]=o),S.createElement(n,f)}}const ba=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();function Eh(e,{style:t,vars:n},r,o){Object.assign(e.style,t,o&&o.getProjectionStyles(r));for(const i in n)e.style.setProperty(i,n[i])}const Ph=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Th(e,t,n,r){Eh(e,t,void 0,r);for(const o in t.attrs)e.setAttribute(Ph.has(o)?o:ba(o),t.attrs[o])}function eu(e){const{style:t}=e,n={};for(const r in t)(ot(t[r])||gh(r,e))&&(n[r]=t[r]);return n}function _h(e){const t=eu(e);for(const n in e)if(ot(e[n])){const r=n==="x"||n==="y"?"attr"+n.toUpperCase():n;t[r]=e[n]}return t}function tu(e,t,n,r={},o={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,o)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,o)),t}const wi=e=>Array.isArray(e),hg=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),mg=e=>wi(e)?e[e.length-1]||0:e;function Ho(e){const t=ot(e)?e.get():e;return hg(t)?t.toValue():t}function vg({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,o,i){const s={latestValues:yg(r,o,i,e),renderState:t()};return n&&(s.mount=l=>n(r,l,s)),s}const Vh=e=>(t,n)=>{const r=S.useContext(Wi),o=S.useContext(no),i=()=>vg(e,t,r,o);return n?i():Qi(i)};function yg(e,t,n,r){const o={},i=r(e);for(const d in i)o[d]=Ho(i[d]);let{initial:s,animate:l}=e;const a=Ki(e),u=vh(e);t&&u&&!a&&e.inherit!==!1&&(s===void 0&&(s=t.initial),l===void 0&&(l=t.animate));let c=n?n.initial===!1:!1;c=c||s===!1;const f=c?l:s;return f&&typeof f!="boolean"&&!Hi(f)&&(Array.isArray(f)?f:[f]).forEach(v=>{const g=tu(e,v);if(!g)return;const{transitionEnd:y,transition:x,...p}=g;for(const h in p){let m=p[h];if(Array.isArray(m)){const w=c?m.length-1:0;m=m[w]}m!==null&&(o[h]=m)}for(const h in y)o[h]=y[h]}),o}const gg={useVisualState:Vh({scrapeMotionValuesFromProps:_h,createRenderState:kh,onMount:(e,t,{renderState:n,latestValues:r})=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}Ja(n,r,{enableHardwareAcceleration:!1},qa(t.tagName),e.transformTemplate),Th(t,n)}})},wg={useVisualState:Vh({scrapeMotionValuesFromProps:eu,createRenderState:Za})};function xg(e,{forwardMotionProps:t=!1},n,r,o){return{...Ya(e)?gg:wg,preloadedFeatures:n,useRender:pg(t),createVisualElement:r,projectionNodeConstructor:o,Component:e}}var U;(function(e){e.Animate="animate",e.Hover="whileHover",e.Tap="whileTap",e.Drag="whileDrag",e.Focus="whileFocus",e.InView="whileInView",e.Exit="exit"})(U||(U={}));function Gi(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function Ml(e,t,n,r){S.useEffect(()=>{const o=e.current;if(n&&o)return Gi(o,t,n,r)},[e,t,n,r])}function Sg({whileFocus:e,visualElement:t}){const{animationState:n}=t,r=()=>{n&&n.setActive(U.Focus,!0)},o=()=>{n&&n.setActive(U.Focus,!1)};Ml(t,"focus",e?r:void 0),Ml(t,"blur",e?o:void 0)}function Ah(e){return typeof PointerEvent<"u"&&e instanceof PointerEvent?e.pointerType==="mouse":e instanceof MouseEvent}function Lh(e){return!!e.touches}function Cg(e){return t=>{const n=t instanceof MouseEvent;(!n||n&&t.button===0)&&e(t)}}const kg={pageX:0,pageY:0};function Eg(e,t="page"){const r=e.touches[0]||e.changedTouches[0]||kg;return{x:r[t+"X"],y:r[t+"Y"]}}function Pg(e,t="page"){return{x:e[t+"X"],y:e[t+"Y"]}}function nu(e,t="page"){return{point:Lh(e)?Eg(e,t):Pg(e,t)}}const Dh=(e,t=!1)=>{const n=r=>e(r,nu(r));return t?Cg(n):n},Tg=()=>cn&&window.onpointerdown===null,_g=()=>cn&&window.ontouchstart===null,Vg=()=>cn&&window.onmousedown===null,Ag={pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointercancel:"mousecancel",pointerover:"mouseover",pointerout:"mouseout",pointerenter:"mouseenter",pointerleave:"mouseleave"},Lg={pointerdown:"touchstart",pointermove:"touchmove",pointerup:"touchend",pointercancel:"touchcancel"};function Rh(e){return Tg()?e:_g()?Lg[e]:Vg()?Ag[e]:e}function zn(e,t,n,r){return Gi(e,Rh(t),Dh(n,t==="pointerdown"),r)}function xi(e,t,n,r){return Ml(e,Rh(t),n&&Dh(n,t==="pointerdown"),r)}function Mh(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const Bc=Mh("dragHorizontal"),Uc=Mh("dragVertical");function Nh(e){let t=!1;if(e==="y")t=Uc();else if(e==="x")t=Bc();else{const n=Bc(),r=Uc();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function Oh(){const e=Nh(!0);return e?(e(),!1):!0}function $c(e,t,n){return(r,o)=>{!Ah(r)||Oh()||(e.animationState&&e.animationState.setActive(U.Hover,t),n&&n(r,o))}}function Dg({onHoverStart:e,onHoverEnd:t,whileHover:n,visualElement:r}){xi(r,"pointerenter",e||n?$c(r,!0,e):void 0,{passive:!e}),xi(r,"pointerleave",t||n?$c(r,!1,t):void 0,{passive:!t})}const Fh=(e,t)=>t?e===t?!0:Fh(e,t.parentElement):!1;function ru(e){return S.useEffect(()=>()=>e(),[])}const Rg=(e,t)=>n=>t(e(n)),Yi=(...e)=>e.reduce(Rg);function Mg({onTap:e,onTapStart:t,onTapCancel:n,whileTap:r,visualElement:o}){const i=e||t||n||r,s=S.useRef(!1),l=S.useRef(null),a={passive:!(t||e||n||v)};function u(){l.current&&l.current(),l.current=null}function c(){return u(),s.current=!1,o.animationState&&o.animationState.setActive(U.Tap,!1),!Oh()}function f(g,y){c()&&(Fh(o.current,g.target)?e&&e(g,y):n&&n(g,y))}function d(g,y){c()&&n&&n(g,y)}function v(g,y){u(),!s.current&&(s.current=!0,l.current=Yi(zn(window,"pointerup",f,a),zn(window,"pointercancel",d,a)),o.animationState&&o.animationState.setActive(U.Tap,!0),t&&t(g,y))}xi(o,"pointerdown",i?v:void 0,a),ru(u)}var Ng={};const Og="production",zh=typeof process>"u"||Ng===void 0?Og:"production",Wc=new Set;function Ih(e,t,n){Wc.has(t)||(console.warn(t),Wc.add(t))}const Nl=new WeakMap,Ps=new WeakMap,Fg=e=>{const t=Nl.get(e.target);t&&t(e)},zg=e=>{e.forEach(Fg)};function Ig({root:e,...t}){const n=e||document;Ps.has(n)||Ps.set(n,{});const r=Ps.get(n),o=JSON.stringify(t);return r[o]||(r[o]=new IntersectionObserver(zg,{root:e,...t})),r[o]}function jg(e,t,n){const r=Ig(t);return Nl.set(e,n),r.observe(e),()=>{Nl.delete(e),r.unobserve(e)}}function Bg({visualElement:e,whileInView:t,onViewportEnter:n,onViewportLeave:r,viewport:o={}}){const i=S.useRef({hasEnteredView:!1,isInView:!1});let s=!!(t||n||r);o.once&&i.current.hasEnteredView&&(s=!1),(typeof IntersectionObserver>"u"?Wg:$g)(s,i.current,e,o)}const Ug={some:0,all:1};function $g(e,t,n,{root:r,margin:o,amount:i="some",once:s}){S.useEffect(()=>{if(!e||!n.current)return;const l={root:r==null?void 0:r.current,rootMargin:o,threshold:typeof i=="number"?i:Ug[i]},a=u=>{const{isIntersecting:c}=u;if(t.isInView===c||(t.isInView=c,s&&!c&&t.hasEnteredView))return;c&&(t.hasEnteredView=!0),n.animationState&&n.animationState.setActive(U.InView,c);const f=n.getProps(),d=c?f.onViewportEnter:f.onViewportLeave;d&&d(u)};return jg(n.current,l,a)},[e,r,o,i])}function Wg(e,t,n,{fallback:r=!0}){S.useEffect(()=>{!e||!r||(zh!=="production"&&Ih(!1,"IntersectionObserver not available on this device. whileInView animations will trigger on mount."),requestAnimationFrame(()=>{t.hasEnteredView=!0;const{onViewportEnter:o}=n.getProps();o&&o(null),n.animationState&&n.animationState.setActive(U.InView,!0)}))},[e])}const Tt=e=>t=>(e(t),null),Hg={inView:Tt(Bg),tap:Tt(Mg),focus:Tt(Sg),hover:Tt(Dg)};function jh(){const e=S.useContext(no);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,o=S.useId();return S.useEffect(()=>r(o),[]),!t&&n?[!1,()=>n&&n(o)]:[!0]}function Bh(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}const Kg=e=>/^\-?\d*\.?\d+$/.test(e),Qg=e=>/^0[^.\s]+$/.test(e),ft={delta:0,timestamp:0},Uh=1/60*1e3,Gg=typeof performance<"u"?()=>performance.now():()=>Date.now(),$h=typeof window<"u"?e=>window.requestAnimationFrame(e):e=>setTimeout(()=>e(Gg()),Uh);function Yg(e){let t=[],n=[],r=0,o=!1,i=!1;const s=new WeakSet,l={schedule:(a,u=!1,c=!1)=>{const f=c&&o,d=f?t:n;return u&&s.add(a),d.indexOf(a)===-1&&(d.push(a),f&&o&&(r=t.length)),a},cancel:a=>{const u=n.indexOf(a);u!==-1&&n.splice(u,1),s.delete(a)},process:a=>{if(o){i=!0;return}if(o=!0,[t,n]=[n,t],n.length=0,r=t.length,r)for(let u=0;u<r;u++){const c=t[u];c(a),s.has(c)&&(l.schedule(c),e())}o=!1,i&&(i=!1,l.process(a))}};return l}const Xg=40;let Ol=!0,Xr=!1,Fl=!1;const io=["read","update","preRender","render","postRender"],Xi=io.reduce((e,t)=>(e[t]=Yg(()=>Xr=!0),e),{}),_e=io.reduce((e,t)=>{const n=Xi[t];return e[t]=(r,o=!1,i=!1)=>(Xr||Jg(),n.schedule(r,o,i)),e},{}),zt=io.reduce((e,t)=>(e[t]=Xi[t].cancel,e),{}),Ts=io.reduce((e,t)=>(e[t]=()=>Xi[t].process(ft),e),{}),Zg=e=>Xi[e].process(ft),Wh=e=>{Xr=!1,ft.delta=Ol?Uh:Math.max(Math.min(e-ft.timestamp,Xg),1),ft.timestamp=e,Fl=!0,io.forEach(Zg),Fl=!1,Xr&&(Ol=!1,$h(Wh))},Jg=()=>{Xr=!0,Ol=!0,Fl||$h(Wh)};function ou(e,t){e.indexOf(t)===-1&&e.push(t)}function iu(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class su{constructor(){this.subscriptions=[]}add(t){return ou(this.subscriptions,t),()=>iu(this.subscriptions,t)}notify(t,n,r){const o=this.subscriptions.length;if(o)if(o===1)this.subscriptions[0](t,n,r);else for(let i=0;i<o;i++){const s=this.subscriptions[i];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function lu(e,t){return t?e*(1e3/t):0}const qg=e=>!isNaN(parseFloat(e));class bg{constructor(t,n={}){this.version="7.10.3",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,o=!0)=>{this.prev=this.current,this.current=r;const{delta:i,timestamp:s}=ft;this.lastUpdated!==s&&(this.timeDelta=i,this.lastUpdated=s,_e.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),o&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>_e.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=qg(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){return this.events[t]||(this.events[t]=new su),this.events[t].add(n)}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t){this.passiveEffect=t}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?lu(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.stopAnimation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.stopAnimation&&(this.stopAnimation(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.stopAnimation}clearAnimation(){this.stopAnimation=null}destroy(){this.clearListeners(),this.stop()}}function Qn(e,t){return new bg(e,t)}const au=(e,t)=>n=>!!(ro(n)&&Xy.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),Hh=(e,t,n)=>r=>{if(!ro(r))return r;const[o,i,s,l]=r.match(Yr);return{[e]:parseFloat(o),[t]:parseFloat(i),[n]:parseFloat(s),alpha:l!==void 0?parseFloat(l):1}},e1=e=>Kn(0,255,e),_s={...dn,transform:e=>Math.round(e1(e))},bt={test:au("rgb","red"),parse:Hh("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+_s.transform(e)+", "+_s.transform(t)+", "+_s.transform(n)+", "+Pr(Er.transform(r))+")"};function t1(e){let t="",n="",r="",o="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),o=e.substring(4,5),t+=t,n+=n,r+=r,o+=o),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const zl={test:au("#"),parse:t1,transform:bt.transform},Vn={test:au("hsl","hue"),parse:Hh("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+rt.transform(Pr(t))+", "+rt.transform(Pr(n))+", "+Pr(Er.transform(r))+")"},ye={test:e=>bt.test(e)||zl.test(e)||Vn.test(e),parse:e=>bt.test(e)?bt.parse(e):Vn.test(e)?Vn.parse(e):zl.parse(e),transform:e=>ro(e)?e:e.hasOwnProperty("red")?bt.transform(e):Vn.transform(e)},Kh="${c}",Qh="${n}";function n1(e){var t,n;return isNaN(e)&&ro(e)&&(((t=e.match(Yr))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Rl))===null||n===void 0?void 0:n.length)||0)>0}function Si(e){typeof e=="number"&&(e=`${e}`);const t=[];let n=0,r=0;const o=e.match(Rl);o&&(n=o.length,e=e.replace(Rl,Kh),t.push(...o.map(ye.parse)));const i=e.match(Yr);return i&&(r=i.length,e=e.replace(Yr,Qh),t.push(...i.map(dn.parse))),{values:t,numColors:n,numNumbers:r,tokenised:e}}function Gh(e){return Si(e).values}function Yh(e){const{values:t,numColors:n,tokenised:r}=Si(e),o=t.length;return i=>{let s=r;for(let l=0;l<o;l++)s=s.replace(l<n?Kh:Qh,l<n?ye.transform(i[l]):Pr(i[l]));return s}}const r1=e=>typeof e=="number"?0:e;function o1(e){const t=Gh(e);return Yh(e)(t.map(r1))}const It={test:n1,parse:Gh,createTransformer:Yh,getAnimatableNone:o1},i1=new Set(["brightness","contrast","saturate","opacity"]);function s1(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Yr)||[];if(!r)return e;const o=n.replace(r,"");let i=i1.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+o+")"}const l1=/([a-z-]*)\(.*?\)/g,Il={...It,getAnimatableNone:e=>{const t=e.match(l1);return t?t.map(s1).join(" "):e}},a1={...xh,color:ye,backgroundColor:ye,outlineColor:ye,fill:ye,stroke:ye,borderColor:ye,borderTopColor:ye,borderRightColor:ye,borderBottomColor:ye,borderLeftColor:ye,filter:Il,WebkitFilter:Il},uu=e=>a1[e];function cu(e,t){var n;let r=uu(e);return r!==Il&&(r=It),(n=r.getAnimatableNone)===null||n===void 0?void 0:n.call(r,t)}const Xh=e=>t=>t.test(e),u1={test:e=>e==="auto",parse:e=>e},Zh=[dn,L,rt,gt,Jy,Zy,u1],ir=e=>Zh.find(Xh(e)),c1=[...Zh,ye,It],f1=e=>c1.find(Xh(e));function d1(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function p1(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function Zi(e,t,n){const r=e.getProps();return tu(r,t,n!==void 0?n:r.custom,d1(e),p1(e))}function h1(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Qn(n))}function m1(e,t){const n=Zi(e,t);let{transitionEnd:r={},transition:o={},...i}=n?e.makeTargetAnimatable(n,!1):{};i={...i,...r};for(const s in i){const l=mg(i[s]);h1(e,s,l)}}function v1(e,t,n){var r,o;const i=Object.keys(t).filter(l=>!e.hasValue(l)),s=i.length;if(s)for(let l=0;l<s;l++){const a=i[l],u=t[a];let c=null;Array.isArray(u)&&(c=u[0]),c===null&&(c=(o=(r=n[a])!==null&&r!==void 0?r:e.readValue(a))!==null&&o!==void 0?o:t[a]),c!=null&&(typeof c=="string"&&(Kg(c)||Qg(c))?c=parseFloat(c):!f1(c)&&It.test(u)&&(c=cu(a,u)),e.addValue(a,Qn(c,{owner:e})),n[a]===void 0&&(n[a]=c),c!==null&&e.setBaseTarget(a,c))}}function y1(e,t){return t?(t[e]||t.default||t).from:void 0}function g1(e,t,n){var r;const o={};for(const i in e){const s=y1(i,t);o[i]=s!==void 0?s:(r=n.getValue(i))===null||r===void 0?void 0:r.get()}return o}function Ci(e){return!!(ot(e)&&e.add)}const w1=(e,t)=>`${e}: ${t}`;function x1(e,t){const{MotionAppearAnimations:n}=window,r=w1(e,fn.has(t)?"transform":t),o=n&&n.get(r);return o?(_e.render(()=>{try{o.cancel(),n.delete(r)}catch{}}),o.currentTime||0):0}const S1="framerAppearId",C1="data-"+ba(S1);var Zr=function(){};const Ko=e=>e*1e3,k1={current:!1},fu=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,du=e=>t=>1-e(1-t),pu=e=>e*e,E1=du(pu),hu=fu(pu),J=(e,t,n)=>-n*e+n*t+e;function Vs(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function P1({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let o=0,i=0,s=0;if(!t)o=i=s=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;o=Vs(a,l,e+1/3),i=Vs(a,l,e),s=Vs(a,l,e-1/3)}return{red:Math.round(o*255),green:Math.round(i*255),blue:Math.round(s*255),alpha:r}}const As=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},T1=[zl,bt,Vn],_1=e=>T1.find(t=>t.test(e));function Hc(e){const t=_1(e);let n=t.parse(e);return t===Vn&&(n=P1(n)),n}const Jh=(e,t)=>{const n=Hc(e),r=Hc(t),o={...n};return i=>(o.red=As(n.red,r.red,i),o.green=As(n.green,r.green,i),o.blue=As(n.blue,r.blue,i),o.alpha=J(n.alpha,r.alpha,i),bt.transform(o))};function qh(e,t){return typeof e=="number"?n=>J(e,t,n):ye.test(e)?Jh(e,t):em(e,t)}const bh=(e,t)=>{const n=[...e],r=n.length,o=e.map((i,s)=>qh(i,t[s]));return i=>{for(let s=0;s<r;s++)n[s]=o[s](i);return n}},V1=(e,t)=>{const n={...e,...t},r={};for(const o in n)e[o]!==void 0&&t[o]!==void 0&&(r[o]=qh(e[o],t[o]));return o=>{for(const i in r)n[i]=r[i](o);return n}},em=(e,t)=>{const n=It.createTransformer(t),r=Si(e),o=Si(t);return r.numColors===o.numColors&&r.numNumbers>=o.numNumbers?Yi(bh(r.values,o.values),n):s=>`${s>0?t:e}`},ki=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Kc=(e,t)=>n=>J(e,t,n);function A1(e){return typeof e=="number"?Kc:typeof e=="string"?ye.test(e)?Jh:em:Array.isArray(e)?bh:typeof e=="object"?V1:Kc}function L1(e,t,n){const r=[],o=n||A1(e[0]),i=e.length-1;for(let s=0;s<i;s++){let l=o(e[s],e[s+1]);if(t){const a=Array.isArray(t)?t[s]:t;l=Yi(a,l)}r.push(l)}return r}function tm(e,t,{clamp:n=!0,ease:r,mixer:o}={}){const i=e.length;Zr(i===t.length),Zr(!r||!Array.isArray(r)||r.length===i-1),e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=L1(t,r,o),l=s.length,a=u=>{let c=0;if(l>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const f=ki(e[c],e[c+1],u);return s[c](f)};return n?u=>a(Kn(e[0],e[i-1],u)):a}const mu=e=>e,nm=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,D1=1e-7,R1=12;function M1(e,t,n,r,o){let i,s,l=0;do s=t+(n-t)/2,i=nm(s,r,o)-e,i>0?n=s:t=s;while(Math.abs(i)>D1&&++l<R1);return s}function rm(e,t,n,r){if(e===t&&n===r)return mu;const o=i=>M1(i,0,1,e,n);return i=>i===0||i===1?i:nm(o(i),t,r)}const om=e=>1-Math.sin(Math.acos(e)),vu=du(om),N1=fu(vu),im=rm(.33,1.53,.69,.99),yu=du(im),O1=fu(yu),F1=e=>(e*=2)<1?.5*yu(e):.5*(2-Math.pow(2,-10*(e-1))),Qc={linear:mu,easeIn:pu,easeInOut:hu,easeOut:E1,circIn:om,circInOut:N1,circOut:vu,backIn:yu,backInOut:O1,backOut:im,anticipate:F1},Gc=e=>{if(Array.isArray(e)){Zr(e.length===4);const[t,n,r,o]=e;return rm(t,n,r,o)}else if(typeof e=="string")return Zr(Qc[e]!==void 0),Qc[e];return e},z1=e=>Array.isArray(e)&&typeof e[0]!="number";function I1(e,t){return e.map(()=>t||hu).splice(0,e.length-1)}function j1(e){const t=e.length;return e.map((n,r)=>r!==0?r/(t-1):0)}function B1(e,t){return e.map(n=>n*t)}function Ei({keyframes:e,ease:t=hu,times:n,duration:r=300}){e=[...e];const o=Ei[0],i=z1(t)?t.map(Gc):Gc(t),s={done:!1,value:o},l=B1(n&&n.length===Ei.length?n:j1(e),r);function a(){return tm(l,e,{ease:Array.isArray(i)?i:I1(e,i)})}let u=a();return{next:c=>(s.value=u(c),s.done=c>=r,s),flipTarget:()=>{e.reverse(),u=a()}}}const Ls=.001,U1=.01,$1=10,W1=.05,H1=1;function K1({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let o,i,s=1-t;s=Kn(W1,H1,s),e=Kn(U1,$1,e/1e3),s<1?(o=u=>{const c=u*s,f=c*e,d=c-n,v=jl(u,s),g=Math.exp(-f);return Ls-d/v*g},i=u=>{const f=u*s*e,d=f*n+n,v=Math.pow(s,2)*Math.pow(u,2)*e,g=Math.exp(-f),y=jl(Math.pow(u,2),s);return(-o(u)+Ls>0?-1:1)*((d-v)*g)/y}):(o=u=>{const c=Math.exp(-u*e),f=(u-n)*e+1;return-Ls+c*f},i=u=>{const c=Math.exp(-u*e),f=(n-u)*(e*e);return c*f});const l=5/e,a=G1(o,i,l);if(e=e*1e3,isNaN(a))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(a,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const Q1=12;function G1(e,t,n){let r=n;for(let o=1;o<Q1;o++)r=r-e(r)/t(r);return r}function jl(e,t){return e*Math.sqrt(1-t*t)}const Y1=["duration","bounce"],X1=["stiffness","damping","mass"];function Yc(e,t){return t.some(n=>e[n]!==void 0)}function Z1(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Yc(e,X1)&&Yc(e,Y1)){const n=K1(e);t={...t,...n,velocity:0,mass:1},t.isResolvedFromDuration=!0}return t}const J1=5;function sm({keyframes:e,restSpeed:t=2,restDelta:n=.01,...r}){let o=e[0],i=e[e.length-1];const s={done:!1,value:o},{stiffness:l,damping:a,mass:u,velocity:c,duration:f,isResolvedFromDuration:d}=Z1(r);let v=q1,g=c?-(c/1e3):0;const y=a/(2*Math.sqrt(l*u));function x(){const p=i-o,h=Math.sqrt(l/u)/1e3;if(n===void 0&&(n=Math.min(Math.abs(i-o)/100,.4)),y<1){const m=jl(h,y);v=w=>{const C=Math.exp(-y*h*w);return i-C*((g+y*h*p)/m*Math.sin(m*w)+p*Math.cos(m*w))}}else if(y===1)v=m=>i-Math.exp(-h*m)*(p+(g+h*p)*m);else{const m=h*Math.sqrt(y*y-1);v=w=>{const C=Math.exp(-y*h*w),E=Math.min(m*w,300);return i-C*((g+y*h*p)*Math.sinh(E)+m*p*Math.cosh(E))/m}}}return x(),{next:p=>{const h=v(p);if(d)s.done=p>=f;else{let m=g;if(p!==0)if(y<1){const E=Math.max(0,p-J1);m=lu(h-v(E),p-E)}else m=0;const w=Math.abs(m)<=t,C=Math.abs(i-h)<=n;s.done=w&&C}return s.value=s.done?i:h,s},flipTarget:()=>{g=-g,[o,i]=[i,o],x()}}}sm.needsInterpolation=(e,t)=>typeof e=="string"||typeof t=="string";const q1=e=>0;function b1({keyframes:e=[0],velocity:t=0,power:n=.8,timeConstant:r=350,restDelta:o=.5,modifyTarget:i}){const s=e[0],l={done:!1,value:s};let a=n*t;const u=s+a,c=i===void 0?u:i(u);return c!==u&&(a=c-s),{next:f=>{const d=-a*Math.exp(-f/r);return l.done=!(d>o||d<-o),l.value=l.done?c:c+d,l},flipTarget:()=>{}}}const ew={decay:b1,keyframes:Ei,tween:Ei,spring:sm};function lm(e,t,n=0){return e-t-n}function tw(e,t=0,n=0,r=!0){return r?lm(t+-e,t,n):t-(e-t)+n}function nw(e,t,n,r){return r?e>=t+n:e<=-n}const rw=e=>{const t=({delta:n})=>e(n);return{start:()=>_e.update(t,!0),stop:()=>zt.update(t)}};function Pi({duration:e,driver:t=rw,elapsed:n=0,repeat:r=0,repeatType:o="loop",repeatDelay:i=0,keyframes:s,autoplay:l=!0,onPlay:a,onStop:u,onComplete:c,onRepeat:f,onUpdate:d,type:v="keyframes",...g}){var y,x;let p,h=0,m=e,w,C=!1,E=!0,P;const _=ew[s.length>2?"keyframes":v],O=s[0],A=s[s.length-1];!((x=(y=_).needsInterpolation)===null||x===void 0)&&x.call(y,O,A)&&(P=tm([0,100],[O,A],{clamp:!1}),s=[0,100]);const I=_({...g,duration:e,keyframes:s});function b(){h++,o==="reverse"?(E=h%2===0,n=tw(n,m,i,E)):(n=lm(n,m,i),o==="mirror"&&I.flipTarget()),C=!1,f&&f()}function ie(){p.stop(),c&&c()}function ve(W){if(E||(W=-W),n+=W,!C){const ee=I.next(Math.max(0,n));w=ee.value,P&&(w=P(w)),C=E?ee.done:n<=0}d&&d(w),C&&(h===0&&(m=m!==void 0?m:n),h<r?nw(n,m,i,E)&&b():ie())}function j(){a&&a(),p=t(ve),p.start()}return l&&j(),{stop:()=>{u&&u(),p.stop()},sample:W=>I.next(Math.max(0,W))}}function ow(e){return!e||Array.isArray(e)||typeof e=="string"&&am[e]}const dr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,am={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:dr([0,.65,.55,1]),circOut:dr([.55,0,1,.45]),backIn:dr([.31,.01,.66,-.59]),backOut:dr([.33,1.53,.69,.99])};function iw(e){if(e)return Array.isArray(e)?dr(e):am[e]}function sw(e,t,n,{delay:r=0,duration:o,repeat:i=0,repeatType:s="loop",ease:l,times:a}={}){return e.animate({[t]:n,offset:a},{delay:r,duration:o,easing:iw(l),fill:"both",iterations:i+1,direction:s==="reverse"?"alternate":"normal"})}const Vo=10;function lw(e,t,{onUpdate:n,onComplete:r,...o}){let{keyframes:i,duration:s=.3,elapsed:l=0,ease:a}=o;if(o.type==="spring"||!ow(o.ease)){const c=Pi(o);let f={done:!1,value:i[0]};const d=[];let v=0;for(;!f.done;)f=c.sample(v),d.push(f.value),v+=Vo;i=d,s=v-Vo,a="linear"}const u=sw(e.owner.current,t,i,{...o,delay:-l,duration:s,ease:a});return u.onfinish=()=>{e.set(i[i.length-1]),r&&r()},()=>{const{currentTime:c}=u;if(c){const f=Pi(o);e.setWithVelocity(f.sample(c-Vo).value,f.sample(c).value,Vo)}_e.update(()=>u.cancel())}}function um(e,t){const n=performance.now(),r=({timestamp:o})=>{const i=o-n;i>=t&&(zt.read(r),e(i-t))};return _e.read(r,!0),()=>zt.read(r)}function aw({keyframes:e,elapsed:t,onUpdate:n,onComplete:r}){const o=()=>(n&&n(e[e.length-1]),r&&r(),()=>{});return t?um(o,-t):o()}function uw({keyframes:e,velocity:t=0,min:n,max:r,power:o=.8,timeConstant:i=750,bounceStiffness:s=500,bounceDamping:l=10,restDelta:a=1,modifyTarget:u,driver:c,onUpdate:f,onComplete:d,onStop:v}){const g=e[0];let y;function x(w){return n!==void 0&&w<n||r!==void 0&&w>r}function p(w){return n===void 0?r:r===void 0||Math.abs(n-w)<Math.abs(r-w)?n:r}function h(w){y==null||y.stop(),y=Pi({keyframes:[0,1],velocity:0,...w,driver:c,onUpdate:C=>{var E;f==null||f(C),(E=w.onUpdate)===null||E===void 0||E.call(w,C)},onComplete:d,onStop:v})}function m(w){h({type:"spring",stiffness:s,damping:l,restDelta:a,...w})}if(x(g))m({velocity:t,keyframes:[g,p(g)]});else{let w=o*t+g;typeof u<"u"&&(w=u(w));const C=p(w),E=C===n?-1:1;let P,_;const O=A=>{P=_,_=A,t=lu(A-P,ft.delta),(E===1&&A>C||E===-1&&A<C)&&m({keyframes:[A,C],velocity:t})};h({type:"decay",keyframes:[g,0],velocity:t,timeConstant:i,power:o,restDelta:a,modifyTarget:u,onUpdate:x(w)?O:void 0})}return{stop:()=>y==null?void 0:y.stop()}}const Kt=()=>({type:"spring",stiffness:500,damping:25,restSpeed:10}),Ao=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),Ds=()=>({type:"keyframes",ease:"linear",duration:.3}),cw={type:"keyframes",duration:.8},Xc={x:Kt,y:Kt,z:Kt,rotate:Kt,rotateX:Kt,rotateY:Kt,rotateZ:Kt,scaleX:Ao,scaleY:Ao,scale:Ao,opacity:Ds,backgroundColor:Ds,color:Ds,default:Ao},fw=(e,{keyframes:t})=>t.length>2?cw:(Xc[e]||Xc.default)(t[1]),Bl=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&It.test(t)&&!t.startsWith("url("));function dw({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:o,repeat:i,repeatType:s,repeatDelay:l,from:a,...u}){return!!Object.keys(u).length}function Zc(e){return e===0||typeof e=="string"&&parseFloat(e)===0&&e.indexOf(" ")===-1}function Jc(e){return typeof e=="number"?0:cu("",e)}function cm(e,t){return e[t]||e.default||e}function pw(e,t,n,r){const o=Bl(t,n);let i=r.from!==void 0?r.from:e.get();return i==="none"&&o&&typeof n=="string"?i=cu(t,n):Zc(i)&&typeof n=="string"?i=Jc(n):!Array.isArray(n)&&Zc(n)&&typeof i=="string"&&(n=Jc(i)),Array.isArray(n)?(n[0]===null&&(n[0]=i),n):[i,n]}const qc={waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate")},Rs={},fm={};for(const e in qc)fm[e]=()=>(Rs[e]===void 0&&(Rs[e]=qc[e]()),Rs[e]);const hw=new Set(["opacity"]),gu=(e,t,n,r={})=>o=>{const i=cm(r,e)||{},s=i.delay||r.delay||0;let{elapsed:l=0}=r;l=l-Ko(s);const a=pw(t,e,n,i),u=a[0],c=a[a.length-1],f=Bl(e,u),d=Bl(e,c);let v={keyframes:a,velocity:t.getVelocity(),...i,elapsed:l,onUpdate:p=>{t.set(p),i.onUpdate&&i.onUpdate(p)},onComplete:()=>{o(),i.onComplete&&i.onComplete()}};if(!f||!d||k1.current||i.type===!1)return aw(v);if(i.type==="inertia"){const p=uw(v);return()=>p.stop()}dw(i)||(v={...v,...fw(e,v)}),v.duration&&(v.duration=Ko(v.duration)),v.repeatDelay&&(v.repeatDelay=Ko(v.repeatDelay));const g=t.owner,y=g&&g.current;if(fm.waapi()&&hw.has(e)&&!v.repeatDelay&&v.repeatType!=="mirror"&&v.damping!==0&&g&&y instanceof HTMLElement&&!g.getProps().onUpdate)return lw(t,e,v);{const p=Pi(v);return()=>p.stop()}};function mw(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const o=t.map(i=>Ul(e,i,n));r=Promise.all(o)}else if(typeof t=="string")r=Ul(e,t,n);else{const o=typeof t=="function"?Zi(e,t,n.custom):t;r=dm(e,o,n)}return r.then(()=>e.notify("AnimationComplete",t))}function Ul(e,t,n={}){var r;const o=Zi(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=o||{};n.transitionOverride&&(i=n.transitionOverride);const s=o?()=>dm(e,o,n):()=>Promise.resolve(),l=!((r=e.variantChildren)===null||r===void 0)&&r.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:f,staggerDirection:d}=i;return vw(e,t,c+u,f,d,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[u,c]=a==="beforeChildren"?[s,l]:[l,s];return u().then(c)}else return Promise.all([s(),l(n.delay)])}function dm(e,t,{delay:n=0,transitionOverride:r,type:o}={}){var i;let{transition:s=e.getDefaultTransition(),transitionEnd:l,...a}=e.makeTargetAnimatable(t);const u=e.getValue("willChange");r&&(s=r);const c=[],f=o&&((i=e.animationState)===null||i===void 0?void 0:i.getState()[o]);for(const d in a){const v=e.getValue(d),g=a[d];if(!v||g===void 0||f&&gw(f,d))continue;let y={delay:n,elapsed:0,...s};if(e.shouldReduceMotion&&fn.has(d)&&(y={...y,type:!1,delay:0}),!v.hasAnimated){const p=e.getProps()[C1];p&&(y.elapsed=x1(p,d))}let x=v.start(gu(d,v,g,y));Ci(u)&&(u.add(d),x=x.then(()=>u.remove(d))),c.push(x)}return Promise.all(c).then(()=>{l&&m1(e,l)})}function vw(e,t,n=0,r=0,o=1,i){const s=[],l=(e.variantChildren.size-1)*r,a=o===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(e.variantChildren).sort(yw).forEach((u,c)=>{s.push(Ul(u,t,{...i,delay:n+a(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function yw(e,t){return e.sortNodePosition(t)}function gw({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}const wu=[U.Animate,U.InView,U.Focus,U.Hover,U.Tap,U.Drag,U.Exit],ww=[...wu].reverse(),xw=wu.length;function Sw(e){return t=>Promise.all(t.map(({animation:n,options:r})=>mw(e,n,r)))}function Cw(e){let t=Sw(e);const n=Ew();let r=!0;const o=(a,u)=>{const c=Zi(e,u);if(c){const{transition:f,transitionEnd:d,...v}=c;a={...a,...v,...d}}return a};function i(a){t=a(e)}function s(a,u){const c=e.getProps(),f=e.getVariantContext(!0)||{},d=[],v=new Set;let g={},y=1/0;for(let p=0;p<xw;p++){const h=ww[p],m=n[h],w=c[h]!==void 0?c[h]:f[h],C=Qr(w),E=h===u?m.isActive:null;E===!1&&(y=p);let P=w===f[h]&&w!==c[h]&&C;if(P&&r&&e.manuallyAnimateOnMount&&(P=!1),m.protectedKeys={...g},!m.isActive&&E===null||!w&&!m.prevProp||Hi(w)||typeof w=="boolean")continue;const _=kw(m.prevProp,w);let O=_||h===u&&m.isActive&&!P&&C||p>y&&C;const A=Array.isArray(w)?w:[w];let I=A.reduce(o,{});E===!1&&(I={});const{prevResolvedValues:b={}}=m,ie={...b,...I},ve=j=>{O=!0,v.delete(j),m.needsAnimating[j]=!0};for(const j in ie){const W=I[j],ee=b[j];g.hasOwnProperty(j)||(W!==ee?wi(W)&&wi(ee)?!Bh(W,ee)||_?ve(j):m.protectedKeys[j]=!0:W!==void 0?ve(j):v.add(j):W!==void 0&&v.has(j)?ve(j):m.protectedKeys[j]=!0)}m.prevProp=w,m.prevResolvedValues=I,m.isActive&&(g={...g,...I}),r&&e.blockInitialAnimation&&(O=!1),O&&!P&&d.push(...A.map(j=>({animation:j,options:{type:h,...a}})))}if(v.size){const p={};v.forEach(h=>{const m=e.getBaseTarget(h);m!==void 0&&(p[h]=m)}),d.push({animation:p})}let x=!!d.length;return r&&c.initial===!1&&!e.manuallyAnimateOnMount&&(x=!1),r=!1,x?t(d):Promise.resolve()}function l(a,u,c){var f;if(n[a].isActive===u)return Promise.resolve();(f=e.variantChildren)===null||f===void 0||f.forEach(v=>{var g;return(g=v.animationState)===null||g===void 0?void 0:g.setActive(a,u)}),n[a].isActive=u;const d=s(c,a);for(const v in n)n[v].protectedKeys={};return d}return{animateChanges:s,setActive:l,setAnimateFunction:i,getState:()=>n}}function kw(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Bh(t,e):!1}function Qt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Ew(){return{[U.Animate]:Qt(!0),[U.InView]:Qt(),[U.Hover]:Qt(),[U.Tap]:Qt(),[U.Drag]:Qt(),[U.Focus]:Qt(),[U.Exit]:Qt()}}const Pw={animation:Tt(({visualElement:e,animate:t})=>{e.animationState||(e.animationState=Cw(e)),Hi(t)&&S.useEffect(()=>t.subscribe(e),[t])}),exit:Tt(e=>{const{custom:t,visualElement:n}=e,[r,o]=jh(),i=S.useContext(no);S.useEffect(()=>{n.isPresent=r;const s=n.animationState&&n.animationState.setActive(U.Exit,!r,{custom:i&&i.custom||t});s&&!r&&s.then(o)},[r])})},bc=(e,t)=>Math.abs(e-t);function Tw(e,t){const n=bc(e.x,t.x),r=bc(e.y,t.y);return Math.sqrt(n**2+r**2)}class pm{constructor(t,n,{transformPagePoint:r}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const u=Ns(this.lastMoveEventInfo,this.history),c=this.startEvent!==null,f=Tw(u.offset,{x:0,y:0})>=3;if(!c&&!f)return;const{point:d}=u,{timestamp:v}=ft;this.history.push({...d,timestamp:v});const{onStart:g,onMove:y}=this.handlers;c||(g&&g(this.lastMoveEvent,u),this.startEvent=this.lastMoveEvent),y&&y(this.lastMoveEvent,u)},this.handlePointerMove=(u,c)=>{if(this.lastMoveEvent=u,this.lastMoveEventInfo=Ms(c,this.transformPagePoint),Ah(u)&&u.buttons===0){this.handlePointerUp(u,c);return}_e.update(this.updatePoint,!0)},this.handlePointerUp=(u,c)=>{this.end();const{onEnd:f,onSessionEnd:d}=this.handlers,v=Ns(Ms(c,this.transformPagePoint),this.history);this.startEvent&&f&&f(u,v),d&&d(u,v)},Lh(t)&&t.touches.length>1)return;this.handlers=n,this.transformPagePoint=r;const o=nu(t),i=Ms(o,this.transformPagePoint),{point:s}=i,{timestamp:l}=ft;this.history=[{...s,timestamp:l}];const{onSessionStart:a}=n;a&&a(t,Ns(i,this.history)),this.removeListeners=Yi(zn(window,"pointermove",this.handlePointerMove),zn(window,"pointerup",this.handlePointerUp),zn(window,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),zt.update(this.updatePoint)}}function Ms(e,t){return t?{point:t(e.point)}:e}function ef(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Ns({point:e},t){return{point:e,delta:ef(e,hm(t)),offset:ef(e,_w(t)),velocity:Vw(t,.1)}}function _w(e){return e[0]}function hm(e){return e[e.length-1]}function Vw(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const o=hm(e);for(;n>=0&&(r=e[n],!(o.timestamp-r.timestamp>Ko(t)));)n--;if(!r)return{x:0,y:0};const i=(o.timestamp-r.timestamp)/1e3;if(i===0)return{x:0,y:0};const s={x:(o.x-r.x)/i,y:(o.y-r.y)/i};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function Ne(e){return e.max-e.min}function $l(e,t=0,n=.01){return Math.abs(e-t)<=n}function tf(e,t,n,r=.5){e.origin=r,e.originPoint=J(t.min,t.max,e.origin),e.scale=Ne(n)/Ne(t),($l(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=J(n.min,n.max,e.origin)-e.originPoint,($l(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Tr(e,t,n,r){tf(e.x,t.x,n.x,r==null?void 0:r.originX),tf(e.y,t.y,n.y,r==null?void 0:r.originY)}function nf(e,t,n){e.min=n.min+t.min,e.max=e.min+Ne(t)}function Aw(e,t,n){nf(e.x,t.x,n.x),nf(e.y,t.y,n.y)}function rf(e,t,n){e.min=t.min-n.min,e.max=e.min+Ne(t)}function _r(e,t,n){rf(e.x,t.x,n.x),rf(e.y,t.y,n.y)}function Lw(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?J(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?J(n,e,r.max):Math.min(e,n)),e}function of(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function Dw(e,{top:t,left:n,bottom:r,right:o}){return{x:of(e.x,n,o),y:of(e.y,t,r)}}function sf(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function Rw(e,t){return{x:sf(e.x,t.x),y:sf(e.y,t.y)}}function Mw(e,t){let n=.5;const r=Ne(e),o=Ne(t);return o>r?n=ki(t.min,t.max-r,e.min):r>o&&(n=ki(e.min,e.max-o,t.min)),Kn(0,1,n)}function Nw(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Wl=.35;function Ow(e=Wl){return e===!1?e=0:e===!0&&(e=Wl),{x:lf(e,"left","right"),y:lf(e,"top","bottom")}}function lf(e,t,n){return{min:af(e,t),max:af(e,n)}}function af(e,t){return typeof e=="number"?e:e[t]||0}const uf=()=>({translate:0,scale:1,origin:0,originPoint:0}),Vr=()=>({x:uf(),y:uf()}),cf=()=>({min:0,max:0}),ne=()=>({x:cf(),y:cf()});function be(e){return[e("x"),e("y")]}function mm({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function Fw({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function zw(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Os(e){return e===void 0||e===1}function Hl({scale:e,scaleX:t,scaleY:n}){return!Os(e)||!Os(t)||!Os(n)}function Xt(e){return Hl(e)||vm(e)||e.z||e.rotate||e.rotateX||e.rotateY}function vm(e){return ff(e.x)||ff(e.y)}function ff(e){return e&&e!=="0%"}function Ti(e,t,n){const r=e-n,o=t*r;return n+o}function df(e,t,n,r,o){return o!==void 0&&(e=Ti(e,o,r)),Ti(e,n,r)+t}function Kl(e,t=0,n=1,r,o){e.min=df(e.min,t,n,r,o),e.max=df(e.max,t,n,r,o)}function ym(e,{x:t,y:n}){Kl(e.x,t.translate,t.scale,t.originPoint),Kl(e.y,n.translate,n.scale,n.originPoint)}function Iw(e,t,n,r=!1){var o,i;const s=n.length;if(!s)return;t.x=t.y=1;let l,a;for(let u=0;u<s;u++)l=n[u],a=l.projectionDelta,((i=(o=l.instance)===null||o===void 0?void 0:o.style)===null||i===void 0?void 0:i.display)!=="contents"&&(r&&l.options.layoutScroll&&l.scroll&&l!==l.root&&An(e,{x:-l.scroll.offset.x,y:-l.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,ym(e,a)),r&&Xt(l.latestValues)&&An(e,l.latestValues));t.x=pf(t.x),t.y=pf(t.y)}function pf(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function St(e,t){e.min=e.min+t,e.max=e.max+t}function hf(e,t,[n,r,o]){const i=t[o]!==void 0?t[o]:.5,s=J(e.min,e.max,i);Kl(e,t[n],t[r],s,t.scale)}const jw=["x","scaleX","originX"],Bw=["y","scaleY","originY"];function An(e,t){hf(e.x,t,jw),hf(e.y,t,Bw)}function gm(e,t){return mm(zw(e.getBoundingClientRect(),t))}function Uw(e,t,n){const r=gm(e,n),{scroll:o}=t;return o&&(St(r.x,o.offset.x),St(r.y,o.offset.y)),r}const $w=new WeakMap;class Ww{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ne(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){if(this.visualElement.isPresent===!1)return;const r=l=>{this.stopAnimation(),n&&this.snapToCursor(nu(l,"page").point)},o=(l,a)=>{var u;const{drag:c,dragPropagation:f,onDragStart:d}=this.getProps();c&&!f&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Nh(c),!this.openGlobalLock)||(this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),be(v=>{var g,y;let x=this.getAxisMotionValue(v).get()||0;if(rt.test(x)){const p=(y=(g=this.visualElement.projection)===null||g===void 0?void 0:g.layout)===null||y===void 0?void 0:y.layoutBox[v];p&&(x=Ne(p)*(parseFloat(x)/100))}this.originPoint[v]=x}),d==null||d(l,a),(u=this.visualElement.animationState)===null||u===void 0||u.setActive(U.Drag,!0))},i=(l,a)=>{const{dragPropagation:u,dragDirectionLock:c,onDirectionLock:f,onDrag:d}=this.getProps();if(!u&&!this.openGlobalLock)return;const{offset:v}=a;if(c&&this.currentDirection===null){this.currentDirection=Hw(v),this.currentDirection!==null&&(f==null||f(this.currentDirection));return}this.updateAxis("x",a.point,v),this.updateAxis("y",a.point,v),this.visualElement.render(),d==null||d(l,a)},s=(l,a)=>this.stop(l,a);this.panSession=new pm(t,{onSessionStart:r,onStart:o,onMove:i,onSessionEnd:s},{transformPagePoint:this.visualElement.getTransformPagePoint()})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:o}=n;this.startAnimation(o);const{onDragEnd:i}=this.getProps();i==null||i(t,n)}cancel(){var t,n;this.isDragging=!1,this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!1),(t=this.panSession)===null||t===void 0||t.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),(n=this.visualElement.animationState)===null||n===void 0||n.setActive(U.Drag,!1)}updateAxis(t,n,r){const{drag:o}=this.getProps();if(!r||!Lo(t,o,this.currentDirection))return;const i=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=Lw(s,this.constraints[t],this.elastic[t])),i.set(s)}resolveConstraints(){const{dragConstraints:t,dragElastic:n}=this.getProps(),{layout:r}=this.visualElement.projection||{},o=this.constraints;t&&_n(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=Dw(r.layoutBox,t):this.constraints=!1,this.elastic=Ow(n),o!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&be(i=>{this.getAxisMotionValue(i)&&(this.constraints[i]=Nw(r.layoutBox[i],this.constraints[i]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!_n(t))return!1;const r=t.current,{projection:o}=this.visualElement;if(!o||!o.layout)return!1;const i=Uw(r,o.root,this.visualElement.getTransformPagePoint());let s=Rw(o.layout.layoutBox,i);if(n){const l=n(Fw(s));this.hasMutatedConstraints=!!l,l&&(s=mm(l))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:o,dragTransition:i,dragSnapToOrigin:s,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},u=be(c=>{if(!Lo(c,n,this.currentDirection))return;let f=(a==null?void 0:a[c])||{};s&&(f={min:0,max:0});const d=o?200:1e6,v=o?40:1e7,g={type:"inertia",velocity:r?t[c]:0,bounceStiffness:d,bounceDamping:v,timeConstant:750,restDelta:1,restSpeed:10,...i,...f};return this.startAxisValueAnimation(c,g)});return Promise.all(u).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(gu(t,r,0,n))}stopAnimation(){be(t=>this.getAxisMotionValue(t).stop())}getAxisMotionValue(t){var n;const r="_drag"+t.toUpperCase(),o=this.visualElement.getProps()[r];return o||this.visualElement.getValue(t,((n=this.visualElement.getProps().initial)===null||n===void 0?void 0:n[t])||0)}snapToCursor(t){be(n=>{const{drag:r}=this.getProps();if(!Lo(n,r,this.currentDirection))return;const{projection:o}=this.visualElement,i=this.getAxisMotionValue(n);if(o&&o.layout){const{min:s,max:l}=o.layout.layoutBox[n];i.set(t[n]-J(s,l,.5))}})}scalePositionWithinConstraints(){var t;if(!this.visualElement.current)return;const{drag:n,dragConstraints:r}=this.getProps(),{projection:o}=this.visualElement;if(!_n(r)||!o||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};be(l=>{const a=this.getAxisMotionValue(l);if(a){const u=a.get();i[l]=Mw({min:u,max:u},this.constraints[l])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",(t=o.root)===null||t===void 0||t.updateScroll(),o.updateLayout(),this.resolveConstraints(),be(l=>{if(!Lo(l,n,null))return;const a=this.getAxisMotionValue(l),{min:u,max:c}=this.constraints[l];a.set(J(u,c,i[l]))})}addListeners(){var t;if(!this.visualElement.current)return;$w.set(this.visualElement,this);const n=this.visualElement.current,r=zn(n,"pointerdown",u=>{const{drag:c,dragListener:f=!0}=this.getProps();c&&f&&this.start(u)}),o=()=>{const{dragConstraints:u}=this.getProps();_n(u)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",o);i&&!i.layout&&((t=i.root)===null||t===void 0||t.updateScroll(),i.updateLayout()),o();const l=Gi(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:u,hasLayoutChanged:c})=>{this.isDragging&&c&&(be(f=>{const d=this.getAxisMotionValue(f);d&&(this.originPoint[f]+=u[f].translate,d.set(d.get()+u[f].translate))}),this.visualElement.render())});return()=>{l(),r(),s(),a==null||a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:o=!1,dragConstraints:i=!1,dragElastic:s=Wl,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:o,dragConstraints:i,dragElastic:s,dragMomentum:l}}}function Lo(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function Hw(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}function Kw(e){const{dragControls:t,visualElement:n}=e,r=Qi(()=>new Ww(n));S.useEffect(()=>t&&t.subscribe(r),[r,t]),S.useEffect(()=>r.addListeners(),[r])}function Qw({onPan:e,onPanStart:t,onPanEnd:n,onPanSessionStart:r,visualElement:o}){const i=e||t||n||r,s=S.useRef(null),{transformPagePoint:l}=S.useContext(Qa),a={onSessionStart:r,onStart:t,onMove:e,onEnd:(c,f)=>{s.current=null,n&&n(c,f)}};S.useEffect(()=>{s.current!==null&&s.current.updateHandlers(a)});function u(c){s.current=new pm(c,a,{transformPagePoint:l})}xi(o,"pointerdown",i&&u),ru(()=>s.current&&s.current.end())}const Gw={pan:Tt(Qw),drag:Tt(Kw)};function Ql(e){return typeof e=="string"&&e.startsWith("var(--")}const wm=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Yw(e){const t=wm.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function Gl(e,t,n=1){const[r,o]=Yw(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);return i?i.trim():Ql(o)?Gl(o,t,n+1):o}function Xw(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(o=>{const i=o.get();if(!Ql(i))return;const s=Gl(i,r);s&&o.set(s)});for(const o in t){const i=t[o];if(!Ql(i))continue;const s=Gl(i,r);s&&(t[o]=s,n&&n[o]===void 0&&(n[o]=i))}return{target:t,transitionEnd:n}}const Zw=new Set(["width","height","top","left","right","bottom","x","y"]),xm=e=>Zw.has(e),Jw=e=>Object.keys(e).some(xm),Sm=(e,t)=>{e.set(t,!1),e.set(t)},mf=e=>e===dn||e===L;var vf;(function(e){e.width="width",e.height="height",e.left="left",e.right="right",e.top="top",e.bottom="bottom"})(vf||(vf={}));const yf=(e,t)=>parseFloat(e.split(", ")[t]),gf=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const o=r.match(/^matrix3d\((.+)\)$/);if(o)return yf(o[1],t);{const i=r.match(/^matrix\((.+)\)$/);return i?yf(i[1],e):0}},qw=new Set(["x","y","z"]),bw=yi.filter(e=>!qw.has(e));function ex(e){const t=[];return bw.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const wf={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:gf(4,13),y:gf(5,14)},tx=(e,t,n)=>{const r=t.measureViewportBox(),o=t.current,i=getComputedStyle(o),{display:s}=i,l={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{l[u]=wf[u](r,i)}),t.render();const a=t.measureViewportBox();return n.forEach(u=>{const c=t.getValue(u);Sm(c,l[u]),e[u]=wf[u](a,i)}),e},nx=(e,t,n={},r={})=>{t={...t},r={...r};const o=Object.keys(t).filter(xm);let i=[],s=!1;const l=[];if(o.forEach(a=>{const u=e.getValue(a);if(!e.hasValue(a))return;let c=n[a],f=ir(c);const d=t[a];let v;if(wi(d)){const g=d.length,y=d[0]===null?1:0;c=d[y],f=ir(c);for(let x=y;x<g;x++)v?Zr(ir(d[x])===v):v=ir(d[x])}else v=ir(d);if(f!==v)if(mf(f)&&mf(v)){const g=u.get();typeof g=="string"&&u.set(parseFloat(g)),typeof d=="string"?t[a]=parseFloat(d):Array.isArray(d)&&v===L&&(t[a]=d.map(parseFloat))}else f!=null&&f.transform&&(v!=null&&v.transform)&&(c===0||d===0)?c===0?u.set(v.transform(c)):t[a]=f.transform(d):(s||(i=ex(e),s=!0),l.push(a),r[a]=r[a]!==void 0?r[a]:t[a],Sm(u,d))}),l.length){const a=l.indexOf("height")>=0?window.pageYOffset:null,u=tx(t,e,l);return i.length&&i.forEach(([c,f])=>{e.getValue(c).set(f)}),e.render(),cn&&a!==null&&window.scrollTo({top:a}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function rx(e,t,n,r){return Jw(t)?nx(e,t,n,r):{target:t,transitionEnd:r}}const ox=(e,t,n,r)=>{const o=Xw(e,t,r);return t=o.target,r=o.transitionEnd,rx(e,t,n,r)},Yl={current:null},Cm={current:!1};function ix(){if(Cm.current=!0,!!cn)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Yl.current=e.matches;e.addListener(t),t()}else Yl.current=!1}function sx(e,t,n){const{willChange:r}=t;for(const o in t){const i=t[o],s=n[o];if(ot(i))e.addValue(o,i),Ci(r)&&r.add(o);else if(ot(s))e.addValue(o,Qn(i,{owner:e})),Ci(r)&&r.remove(o);else if(s!==i)if(e.hasValue(o)){const l=e.getValue(o);!l.hasAnimated&&l.set(i)}else{const l=e.getStaticValue(o);e.addValue(o,Qn(l!==void 0?l:i))}}for(const o in n)t[o]===void 0&&e.removeValue(o);return t}const km=Object.keys(Gr),lx=km.length,xf=["AnimationStart","AnimationComplete","Update","Unmount","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ax{constructor({parent:t,props:n,reducedMotionConfig:r,visualState:o},i={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.isPresent=!0,this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>_e.render(this.render,!1,!0);const{latestValues:s,renderState:l}=o;this.latestValues=s,this.baseTarget={...s},this.initialValues=n.initial?{...s}:{},this.renderState=l,this.parent=t,this.props=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=i,this.isControllingVariants=Ki(n),this.isVariantNode=vh(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:a,...u}=this.scrapeMotionValuesFromProps(n);for(const c in u){const f=u[c];s[c]!==void 0&&ot(f)&&(f.set(s[c],!1),Ci(a)&&a.add(c))}}scrapeMotionValuesFromProps(t){return{}}mount(t){var n;this.current=t,this.projection&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=(n=this.parent)===null||n===void 0?void 0:n.addVariantChild(this)),this.values.forEach((r,o)=>this.bindToMotionValue(o,r)),Cm.current||ix(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Yl.current,this.parent&&this.parent.children.add(this),this.setProps(this.props)}unmount(){var t,n,r;(t=this.projection)===null||t===void 0||t.unmount(),zt.update(this.notifyUpdate),zt.render(this.render),this.valueSubscriptions.forEach(o=>o()),(n=this.removeFromVariantTree)===null||n===void 0||n.call(this),(r=this.parent)===null||r===void 0||r.children.delete(this);for(const o in this.events)this.events[o].clear();this.current=null}bindToMotionValue(t,n){const r=fn.has(t),o=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&_e.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{o(),i()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures(t,n,r,o,i,s){const l=[];for(let a=0;a<lx;a++){const u=km[a],{isEnabled:c,Component:f}=Gr[u];c(t)&&f&&l.push(S.createElement(f,{key:u,...t,visualElement:this}))}if(!this.projection&&i){this.projection=new i(o,this.latestValues,this.parent&&this.parent.projection);const{layoutId:a,layout:u,drag:c,dragConstraints:f,layoutScroll:d}=t;this.projection.setOptions({layoutId:a,layout:u,alwaysMeasureLayout:!!c||f&&_n(f),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:s,layoutScroll:d})}return l}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ne()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}setProps(t){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.props=t;for(let n=0;n<xf.length;n++){const r=xf[n];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);const o=t["on"+r];o&&(this.propEventSubscriptions[r]=this.on(r,o))}this.prevMotionValues=sx(this,this.scrapeMotionValuesFromProps(t),this.prevMotionValues)}getProps(){return this.props}getVariant(t){var n;return(n=this.props.variants)===null||n===void 0?void 0:n[t]}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){var t;return this.isVariantNode?this:(t=this.parent)===null||t===void 0?void 0:t.getClosestVariantNode()}getVariantContext(t=!1){var n,r;if(t)return(n=this.parent)===null||n===void 0?void 0:n.getVariantContext();if(!this.isControllingVariants){const i=((r=this.parent)===null||r===void 0?void 0:r.getVariantContext())||{};return this.props.initial!==void 0&&(i.initial=this.props.initial),i}const o={};for(let i=0;i<ux;i++){const s=Em[i],l=this.props[s];(Qr(l)||l===!1)&&(o[s]=l)}return o}addVariantChild(t){var n;const r=this.getClosestVariantNode();if(r)return(n=r.variantChildren)===null||n===void 0||n.add(t),()=>r.variantChildren.delete(t)}addValue(t,n){this.hasValue(t)&&this.removeValue(t),this.values.set(t,n),this.latestValues[t]=n.get(),this.bindToMotionValue(t,n)}removeValue(t){var n;this.values.delete(t),(n=this.valueSubscriptions.get(t))===null||n===void 0||n(),this.valueSubscriptions.delete(t),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Qn(n,{owner:this}),this.addValue(t,r)),r}readValue(t){return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,o=typeof r=="string"||typeof r=="object"?(n=tu(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&o!==void 0)return o;const i=this.getBaseTargetFromProps(this.props,t);return i!==void 0&&!ot(i)?i:this.initialValues[t]!==void 0&&o===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new su),this.events[t].add(n)}notify(t,...n){var r;(r=this.events[t])===null||r===void 0||r.notify(...n)}}const Em=["initial",...wu],ux=Em.length;class Pm extends ax{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){var r;return(r=t.style)===null||r===void 0?void 0:r[n]}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:o},i){let s=g1(r,t||{},this);if(o&&(n&&(n=o(n)),r&&(r=o(r)),s&&(s=o(s))),i){v1(this,r,s);const l=ox(this,r,s,n);n=l.transitionEnd,r=l.target}return{transition:t,transitionEnd:n,...r}}}function cx(e){return window.getComputedStyle(e)}class fx extends Pm{readValueFromInstance(t,n){if(fn.has(n)){const r=uu(n);return r&&r.default||0}else{const r=cx(t),o=(wh(n)?r.getPropertyValue(n):r[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(t,{transformPagePoint:n}){return gm(t,n)}build(t,n,r,o){Xa(t,n,r,o.transformTemplate)}scrapeMotionValuesFromProps(t){return eu(t)}renderInstance(t,n,r,o){Eh(t,n,r,o)}}class dx extends Pm{constructor(){super(...arguments),this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){var r;return fn.has(n)?((r=uu(n))===null||r===void 0?void 0:r.default)||0:(n=Ph.has(n)?n:ba(n),t.getAttribute(n))}measureInstanceViewportBox(){return ne()}scrapeMotionValuesFromProps(t){return _h(t)}build(t,n,r,o){Ja(t,n,r,this.isSVGTag,o.transformTemplate)}renderInstance(t,n,r,o){Th(t,n,r,o)}mount(t){this.isSVGTag=qa(t.tagName),super.mount(t)}}const px=(e,t)=>Ya(e)?new dx(t,{enableHardwareAcceleration:!1}):new fx(t,{enableHardwareAcceleration:!0});function Sf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const sr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(L.test(e))e=parseFloat(e);else return e;const n=Sf(e,t.target.x),r=Sf(e,t.target.y);return`${n}% ${r}%`}},Cf="_$css",hx={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,o=e.includes("var("),i=[];o&&(e=e.replace(wm,v=>(i.push(v),Cf)));const s=It.parse(e);if(s.length>5)return r;const l=It.createTransformer(e),a=typeof s[0]!="number"?1:0,u=n.x.scale*t.x,c=n.y.scale*t.y;s[0+a]/=u,s[1+a]/=c;const f=J(u,c,.5);typeof s[2+a]=="number"&&(s[2+a]/=f),typeof s[3+a]=="number"&&(s[3+a]/=f);let d=l(s);if(o){let v=0;d=d.replace(Cf,()=>{const g=i[v];return v++,g})}return d}};class mx extends Le.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:o}=this.props,{projection:i}=t;Hy(yx),i&&(n.group&&n.group.add(i),r&&r.register&&o&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),kr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:o,isPresent:i}=this.props,s=r.projection;return s&&(s.isPresent=i,o||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?s.promote():s.relegate()||_e.postRender(()=>{var l;!((l=s.getStack())===null||l===void 0)&&l.members.length||this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),!t.currentAnimation&&t.isLead()&&this.safeToRemove())}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:o}=t;o&&(o.scheduleCheckAfterUnmount(),n!=null&&n.group&&n.group.remove(o),r!=null&&r.deregister&&r.deregister(o))}safeToRemove(){const{safeToRemove:t}=this.props;t==null||t()}render(){return null}}function vx(e){const[t,n]=jh(),r=S.useContext(Ga);return Le.createElement(mx,{...e,layoutGroup:r,switchLayoutGroup:S.useContext(yh),isPresent:t,safeToRemove:n})}const yx={borderRadius:{...sr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sr,borderTopRightRadius:sr,borderBottomLeftRadius:sr,borderBottomRightRadius:sr,boxShadow:hx},gx={measureLayout:vx};function wx(e,t,n={}){const r=ot(e)?e:Qn(e);return r.start(gu("",r,t,n)),{stop:()=>r.stop(),isAnimating:()=>r.isAnimating()}}const Tm=["TopLeft","TopRight","BottomLeft","BottomRight"],xx=Tm.length,kf=e=>typeof e=="string"?parseFloat(e):e,Ef=e=>typeof e=="number"||L.test(e);function Sx(e,t,n,r,o,i){o?(e.opacity=J(0,n.opacity!==void 0?n.opacity:1,Cx(r)),e.opacityExit=J(t.opacity!==void 0?t.opacity:1,0,kx(r))):i&&(e.opacity=J(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<xx;s++){const l=`border${Tm[s]}Radius`;let a=Pf(t,l),u=Pf(n,l);if(a===void 0&&u===void 0)continue;a||(a=0),u||(u=0),a===0||u===0||Ef(a)===Ef(u)?(e[l]=Math.max(J(kf(a),kf(u),r),0),(rt.test(u)||rt.test(a))&&(e[l]+="%")):e[l]=u}(t.rotate||n.rotate)&&(e.rotate=J(t.rotate||0,n.rotate||0,r))}function Pf(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Cx=_m(0,.5,vu),kx=_m(.5,.95,mu);function _m(e,t,n){return r=>r<e?0:r>t?1:n(ki(e,t,r))}function Tf(e,t){e.min=t.min,e.max=t.max}function He(e,t){Tf(e.x,t.x),Tf(e.y,t.y)}function _f(e,t,n,r,o){return e-=t,e=Ti(e,1/n,r),o!==void 0&&(e=Ti(e,1/o,r)),e}function Ex(e,t=0,n=1,r=.5,o,i=e,s=e){if(rt.test(t)&&(t=parseFloat(t),t=J(s.min,s.max,t/100)-s.min),typeof t!="number")return;let l=J(i.min,i.max,r);e===i&&(l-=t),e.min=_f(e.min,t,n,l,o),e.max=_f(e.max,t,n,l,o)}function Vf(e,t,[n,r,o],i,s){Ex(e,t[n],t[r],t[o],t.scale,i,s)}const Px=["x","scaleX","originX"],Tx=["y","scaleY","originY"];function Af(e,t,n,r){Vf(e.x,t,Px,n==null?void 0:n.x,r==null?void 0:r.x),Vf(e.y,t,Tx,n==null?void 0:n.y,r==null?void 0:r.y)}function Lf(e){return e.translate===0&&e.scale===1}function Vm(e){return Lf(e.x)&&Lf(e.y)}function Am(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function Df(e){return Ne(e.x)/Ne(e.y)}class _x{constructor(){this.members=[]}add(t){ou(this.members,t),t.scheduleRender()}remove(t){if(iu(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(o=>t===o);if(n===0)return!1;let r;for(let o=n;o>=0;o--){const i=this.members[o];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(t,n){var r;const o=this.lead;if(t!==o&&(this.prevLead=o,this.lead=t,t.show(),o)){o.instance&&o.scheduleRender(),t.scheduleRender(),t.resumeFrom=o,n&&(t.resumeFrom.preserveOpacity=!0),o.snapshot&&(t.snapshot=o.snapshot,t.snapshot.latestValues=o.animationValues||o.latestValues),!((r=t.root)===null||r===void 0)&&r.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&o.hide()}}exitAnimationComplete(){this.members.forEach(t=>{var n,r,o,i,s;(r=(n=t.options).onExitComplete)===null||r===void 0||r.call(n),(s=(o=t.resumingFrom)===null||o===void 0?void 0:(i=o.options).onExitComplete)===null||s===void 0||s.call(i)})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Rf(e,t,n){let r="";const o=e.x.translate/t.x,i=e.y.translate/t.y;if((o||i)&&(r=`translate3d(${o}px, ${i}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:a,rotateX:u,rotateY:c}=n;a&&(r+=`rotate(${a}deg) `),u&&(r+=`rotateX(${u}deg) `),c&&(r+=`rotateY(${c}deg) `)}const s=e.x.scale*t.x,l=e.y.scale*t.y;return(s!==1||l!==1)&&(r+=`scale(${s}, ${l})`),r||"none"}const Vx=(e,t)=>e.depth-t.depth;class Ax{constructor(){this.children=[],this.isDirty=!1}add(t){ou(this.children,t),this.isDirty=!0}remove(t){iu(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Vx),this.isDirty=!1,this.children.forEach(t)}}const Mf=["","X","Y","Z"],Nf=1e3;let Lx=0;function Lm({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:o}){return class{constructor(s,l={},a=t==null?void 0:t()){this.id=Lx++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isTransformDirty=!1,this.isProjectionDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.potentialNodes=new Map,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.nodes.forEach(Mx),this.nodes.forEach(Fx),this.nodes.forEach(zx)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.elementId=s,this.latestValues=l,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0,s&&this.root.registerPotentialNode(s,this);for(let u=0;u<this.path.length;u++)this.path[u].shouldResetTransform=!0;this.root===this&&(this.nodes=new Ax)}addEventListener(s,l){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new su),this.eventHandlers.get(s).add(l)}notifyListeners(s,...l){const a=this.eventHandlers.get(s);a==null||a.notify(...l)}hasListeners(s){return this.eventHandlers.has(s)}registerPotentialNode(s,l){this.potentialNodes.set(s,l)}mount(s,l=!1){var a;if(this.instance)return;this.isSVG=s instanceof SVGElement&&s.tagName!=="svg",this.instance=s;const{layoutId:u,layout:c,visualElement:f}=this.options;if(f&&!f.current&&f.mount(s),this.root.nodes.add(this),(a=this.parent)===null||a===void 0||a.children.add(this),this.elementId&&this.root.potentialNodes.delete(this.elementId),l&&(c||u)&&(this.isLayoutDirty=!0),e){let d;const v=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=um(v,250),kr.hasAnimatedSinceResize&&(kr.hasAnimatedSinceResize=!1,this.nodes.forEach(Ff))})}u&&this.root.registerSharedNode(u,this),this.options.animate!==!1&&f&&(u||c)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:v,hasRelativeTargetChanged:g,layout:y})=>{var x,p,h,m,w;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const C=(p=(x=this.options.transition)!==null&&x!==void 0?x:f.getDefaultTransition())!==null&&p!==void 0?p:$x,{onLayoutAnimationStart:E,onLayoutAnimationComplete:P}=f.getProps(),_=!this.targetLayout||!Am(this.targetLayout,y)||g,O=!v&&g;if(!((h=this.resumeFrom)===null||h===void 0)&&h.instance||O||v&&(_||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,O);const A={...cm(C,"layout"),onPlay:E,onComplete:P};f.shouldReduceMotion&&(A.delay=0,A.type=!1),this.startAnimation(A)}else!v&&this.animationProgress===0&&Ff(this),this.isLead()&&((w=(m=this.options).onExitComplete)===null||w===void 0||w.call(m));this.targetLayout=y})}unmount(){var s,l;this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this),(s=this.getStack())===null||s===void 0||s.remove(this),(l=this.parent)===null||l===void 0||l.children.delete(this),this.instance=void 0,zt.preRender(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){var s;return this.isAnimationBlocked||((s=this.parent)===null||s===void 0?void 0:s.isTreeAnimationBlocked())||!1}startUpdate(){var s;this.isUpdateBlocked()||(this.isUpdating=!0,(s=this.nodes)===null||s===void 0||s.forEach(Ix),this.animationId++)}willUpdate(s=!0){var l,a,u;if(this.root.isUpdateBlocked()){(a=(l=this.options).onExitComplete)===null||a===void 0||a.call(l);return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let v=0;v<this.path.length;v++){const g=this.path[v];g.shouldResetTransform=!0,g.updateScroll("snapshot")}const{layoutId:c,layout:f}=this.options;if(c===void 0&&!f)return;const d=(u=this.options.visualElement)===null||u===void 0?void 0:u.getProps().transformTemplate;this.prevTransformTemplateValue=d==null?void 0:d(this.latestValues,""),this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}didUpdate(){if(this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Of);return}this.isUpdating&&(this.isUpdating=!1,this.potentialNodes.size&&(this.potentialNodes.forEach(Wx),this.potentialNodes.clear()),this.nodes.forEach(Ox),this.nodes.forEach(Dx),this.nodes.forEach(Rx),this.clearAllSnapshots(),Ts.update(),Ts.preRender(),Ts.render())}clearAllSnapshots(){this.nodes.forEach(Nx),this.sharedNodes.forEach(jx)}scheduleUpdateProjection(){_e.preRender(this.updateProjection,!1,!0)}scheduleCheckAfterUnmount(){_e.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){var s;if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const l=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ne(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox),(s=this.options.visualElement)===null||s===void 0||s.notify("LayoutMeasure",this.layout.layoutBox,l==null?void 0:l.layoutBox)}updateScroll(s="measure"){let l=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(l=!1),l&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){var s;if(!o)return;const l=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!Vm(this.projectionDelta),u=(s=this.options.visualElement)===null||s===void 0?void 0:s.getProps().transformTemplate,c=u==null?void 0:u(this.latestValues,""),f=c!==this.prevTransformTemplateValue;l&&(a||Xt(this.latestValues)||f)&&(o(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return s&&(a=this.removeTransform(a)),Hx(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return ne();const l=s.measureViewportBox(),{scroll:a}=this.root;return a&&(St(l.x,a.offset.x),St(l.y,a.offset.y)),l}removeElementScroll(s){const l=ne();He(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a],{scroll:c,options:f}=u;if(u!==this.root&&c&&f.layoutScroll){if(c.isRoot){He(l,s);const{scroll:d}=this.root;d&&(St(l.x,-d.offset.x),St(l.y,-d.offset.y))}St(l.x,c.offset.x),St(l.y,c.offset.y)}}return l}applyTransform(s,l=!1){const a=ne();He(a,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];!l&&c.options.layoutScroll&&c.scroll&&c!==c.root&&An(a,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),Xt(c.latestValues)&&An(a,c.latestValues)}return Xt(this.latestValues)&&An(a,this.latestValues),a}removeTransform(s){var l;const a=ne();He(a,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];if(!c.instance||!Xt(c.latestValues))continue;Hl(c.latestValues)&&c.updateSnapshot();const f=ne(),d=c.measurePageBox();He(f,d),Af(a,c.latestValues,(l=c.snapshot)===null||l===void 0?void 0:l.layoutBox,f)}return Xt(this.latestValues)&&Af(a,this.latestValues),a}setTargetDelta(s){this.targetDelta=s,this.isProjectionDirty=!0,this.root.scheduleUpdateProjection()}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}resolveTargetDelta(){var s;const l=this.getLead();if(this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),!this.isProjectionDirty&&!this.attemptToResolveRelativeTarget)return;const{layout:a,layoutId:u}=this.options;if(!(!this.layout||!(a||u))){if(!this.targetDelta&&!this.relativeTarget){const c=this.getClosestProjectingParent();c&&c.layout?(this.relativeParent=c,this.relativeTarget=ne(),this.relativeTargetOrigin=ne(),_r(this.relativeTargetOrigin,this.layout.layoutBox,c.layout.layoutBox),He(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=ne(),this.targetWithTransforms=ne()),this.relativeTarget&&this.relativeTargetOrigin&&(!((s=this.relativeParent)===null||s===void 0)&&s.target)?Aw(this.target,this.relativeTarget,this.relativeParent.target):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):He(this.target,this.layout.layoutBox),ym(this.target,this.targetDelta)):He(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const c=this.getClosestProjectingParent();c&&!!c.resumingFrom==!!this.resumingFrom&&!c.options.layoutScroll&&c.target?(this.relativeParent=c,this.relativeTarget=ne(),this.relativeTargetOrigin=ne(),_r(this.relativeTargetOrigin,this.target,c.target),He(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Hl(this.parent.latestValues)||vm(this.parent.latestValues)))return(this.parent.relativeTarget||this.parent.targetDelta)&&this.parent.layout?this.parent:this.parent.getClosestProjectingParent()}calcProjection(){var s;const{isProjectionDirty:l,isTransformDirty:a}=this;this.isProjectionDirty=this.isTransformDirty=!1;const u=this.getLead(),c=!!this.resumingFrom||this!==u;let f=!0;if(l&&(f=!1),c&&a&&(f=!1),f)return;const{layout:d,layoutId:v}=this.options;if(this.isTreeAnimating=!!(!((s=this.parent)===null||s===void 0)&&s.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||v))return;He(this.layoutCorrected,this.layout.layoutBox),Iw(this.layoutCorrected,this.treeScale,this.path,c);const{target:g}=u;if(!g)return;this.projectionDelta||(this.projectionDelta=Vr(),this.projectionDeltaWithTransform=Vr());const y=this.treeScale.x,x=this.treeScale.y,p=this.projectionTransform;Tr(this.projectionDelta,this.layoutCorrected,g,this.latestValues),this.projectionTransform=Rf(this.projectionDelta,this.treeScale),(this.projectionTransform!==p||this.treeScale.x!==y||this.treeScale.y!==x)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",g))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){var l,a,u;(a=(l=this.options).scheduleRender)===null||a===void 0||a.call(l),s&&((u=this.getStack())===null||u===void 0||u.scheduleRender()),this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,l=!1){var a,u;const c=this.snapshot,f=(c==null?void 0:c.latestValues)||{},d={...this.latestValues},v=Vr();this.relativeTarget=this.relativeTargetOrigin=void 0,this.attemptToResolveRelativeTarget=!l;const g=ne(),y=(c==null?void 0:c.source)!==((a=this.layout)===null||a===void 0?void 0:a.source),x=(((u=this.getStack())===null||u===void 0?void 0:u.members.length)||0)<=1,p=!!(y&&!x&&this.options.crossfade===!0&&!this.path.some(Ux));this.animationProgress=0,this.mixTargetDelta=h=>{var m;const w=h/1e3;zf(v.x,s.x,w),zf(v.y,s.y,w),this.setTargetDelta(v),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&(!((m=this.relativeParent)===null||m===void 0)&&m.layout)&&(_r(g,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Bx(this.relativeTarget,this.relativeTargetOrigin,g,w)),y&&(this.animationValues=d,Sx(d,f,this.latestValues,w,p,x)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=w},this.mixTargetDelta(0)}startAnimation(s){var l,a;this.notifyListeners("animationStart"),(l=this.currentAnimation)===null||l===void 0||l.stop(),this.resumingFrom&&((a=this.resumingFrom.currentAnimation)===null||a===void 0||a.stop()),this.pendingAnimation&&(zt.update(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=_e.update(()=>{kr.hasAnimatedSinceResize=!0,this.currentAnimation=wx(0,Nf,{...s,onUpdate:u=>{var c;this.mixTargetDelta(u),(c=s.onUpdate)===null||c===void 0||c.call(s,u)},onComplete:()=>{var u;(u=s.onComplete)===null||u===void 0||u.call(s),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){var s;this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0),(s=this.getStack())===null||s===void 0||s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){var s;this.currentAnimation&&((s=this.mixTargetDelta)===null||s===void 0||s.call(this,Nf),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:l,target:a,layout:u,latestValues:c}=s;if(!(!l||!a||!u)){if(this!==s&&this.layout&&u&&Dm(this.options.animationType,this.layout.layoutBox,u.layoutBox)){a=this.target||ne();const f=Ne(this.layout.layoutBox.x);a.x.min=s.target.x.min,a.x.max=a.x.min+f;const d=Ne(this.layout.layoutBox.y);a.y.min=s.target.y.min,a.y.max=a.y.min+d}He(l,a),An(l,c),Tr(this.projectionDeltaWithTransform,this.layoutCorrected,l,c)}}registerSharedNode(s,l){var a,u,c;this.sharedNodes.has(s)||this.sharedNodes.set(s,new _x),this.sharedNodes.get(s).add(l),l.promote({transition:(a=l.options.initialPromotionConfig)===null||a===void 0?void 0:a.transition,preserveFollowOpacity:(c=(u=l.options.initialPromotionConfig)===null||u===void 0?void 0:u.shouldPreserveFollowOpacity)===null||c===void 0?void 0:c.call(u,l)})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:l}=this.options;return l?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:l}=this.options;return l?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:l,preserveFollowOpacity:a}={}){const u=this.getStack();u&&u.promote(this,a),s&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let l=!1;const{latestValues:a}=s;if((a.rotate||a.rotateX||a.rotateY||a.rotateZ)&&(l=!0),!l)return;const u={};for(let c=0;c<Mf.length;c++){const f="rotate"+Mf[c];a[f]&&(u[f]=a[f],s.setStaticValue(f,0))}s==null||s.render();for(const c in u)s.setStaticValue(c,u[c]);s.scheduleRender()}getProjectionStyles(s={}){var l,a,u;const c={};if(!this.instance||this.isSVG)return c;if(this.isVisible)c.visibility="";else return{visibility:"hidden"};const f=(l=this.options.visualElement)===null||l===void 0?void 0:l.getProps().transformTemplate;if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=Ho(s.pointerEvents)||"",c.transform=f?f(this.latestValues,""):"none",c;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=Ho(s.pointerEvents)||""),this.hasProjected&&!Xt(this.latestValues)&&(x.transform=f?f({},""):"none",this.hasProjected=!1),x}const v=d.animationValues||d.latestValues;this.applyTransformsToTarget(),c.transform=Rf(this.projectionDeltaWithTransform,this.treeScale,v),f&&(c.transform=f(v,c.transform));const{x:g,y}=this.projectionDelta;c.transformOrigin=`${g.origin*100}% ${y.origin*100}% 0`,d.animationValues?c.opacity=d===this?(u=(a=v.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&u!==void 0?u:1:this.preserveOpacity?this.latestValues.opacity:v.opacityExit:c.opacity=d===this?v.opacity!==void 0?v.opacity:"":v.opacityExit!==void 0?v.opacityExit:0;for(const x in vi){if(v[x]===void 0)continue;const{correct:p,applyTo:h}=vi[x],m=p(v[x],d);if(h){const w=h.length;for(let C=0;C<w;C++)c[h[C]]=m}else c[x]=m}return this.options.layoutId&&(c.pointerEvents=d===this?Ho(s.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var l;return(l=s.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(Of),this.root.sharedNodes.clear()}}}function Dx(e){e.updateLayout()}function Rx(e){var t,n,r;const o=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&o&&e.hasListeners("didUpdate")){const{layoutBox:i,measuredBox:s}=e.layout,{animationType:l}=e.options,a=o.source!==e.layout.source;l==="size"?be(v=>{const g=a?o.measuredBox[v]:o.layoutBox[v],y=Ne(g);g.min=i[v].min,g.max=g.min+y}):Dm(l,o.layoutBox,i)&&be(v=>{const g=a?o.measuredBox[v]:o.layoutBox[v],y=Ne(i[v]);g.max=g.min+y});const u=Vr();Tr(u,i,o.layoutBox);const c=Vr();a?Tr(c,e.applyTransform(s,!0),o.measuredBox):Tr(c,i,o.layoutBox);const f=!Vm(u);let d=!1;if(!e.resumeFrom){const v=e.getClosestProjectingParent();if(v&&!v.resumeFrom){const{snapshot:g,layout:y}=v;if(g&&y){const x=ne();_r(x,o.layoutBox,g.layoutBox);const p=ne();_r(p,i,y.layoutBox),Am(x,p)||(d=!0)}}}e.notifyListeners("didUpdate",{layout:i,snapshot:o,delta:c,layoutDelta:u,hasLayoutChanged:f,hasRelativeTargetChanged:d})}else e.isLead()&&((r=(n=e.options).onExitComplete)===null||r===void 0||r.call(n));e.options.transition=void 0}function Mx(e){e.isProjectionDirty||(e.isProjectionDirty=!!(e.parent&&e.parent.isProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=!!(e.parent&&e.parent.isTransformDirty))}function Nx(e){e.clearSnapshot()}function Of(e){e.clearMeasurements()}function Ox(e){const{visualElement:t}=e.options;t!=null&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Ff(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0}function Fx(e){e.resolveTargetDelta()}function zx(e){e.calcProjection()}function Ix(e){e.resetRotation()}function jx(e){e.removeLeadSnapshot()}function zf(e,t,n){e.translate=J(t.translate,0,n),e.scale=J(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function If(e,t,n,r){e.min=J(t.min,n.min,r),e.max=J(t.max,n.max,r)}function Bx(e,t,n,r){If(e.x,t.x,n.x,r),If(e.y,t.y,n.y,r)}function Ux(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const $x={duration:.45,ease:[.4,0,.1,1]};function Wx(e,t){let n=e.root;for(let i=e.path.length-1;i>=0;i--)if(e.path[i].instance){n=e.path[i];break}const o=(n&&n!==e.root?n.instance:document).querySelector(`[data-projection-id="${t}"]`);o&&e.mount(o,!0)}function jf(e){e.min=Math.round(e.min),e.max=Math.round(e.max)}function Hx(e){jf(e.x),jf(e.y)}function Dm(e,t,n){return e==="position"||e==="preserve-aspect"&&!$l(Df(t),Df(n),.2)}const Kx=Lm({attachResizeListener:(e,t)=>Gi(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Fs={current:void 0},Qx=Lm({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Fs.current){const e=new Kx(0,{});e.mount(window),e.setOptions({layoutScroll:!0}),Fs.current=e}return Fs.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),Gx={...Pw,...Hg,...Gw,...gx},Yx=$y((e,t)=>xg(e,t,Gx,px,Qx));function Rm(){const e=S.useRef(!1);return Cr(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function Xx(){const e=Rm(),[t,n]=S.useState(0),r=S.useCallback(()=>{e.current&&n(t+1)},[t]);return[S.useCallback(()=>_e.postRender(r),[r]),t]}class Zx extends S.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function Jx({children:e,isPresent:t}){const n=S.useId(),r=S.useRef(null),o=S.useRef({width:0,height:0,top:0,left:0});return S.useInsertionEffect(()=>{const{width:i,height:s,top:l,left:a}=o.current;if(t||!r.current||!i||!s)return;r.current.dataset.motionPopId=n;const u=document.createElement("style");return document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${i}px !important;
            height: ${s}px !important;
            top: ${l}px !important;
            left: ${a}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[t]),S.createElement(Zx,{isPresent:t,childRef:r,sizeRef:o},S.cloneElement(e,{ref:r}))}const zs=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:o,presenceAffectsLayout:i,mode:s})=>{const l=Qi(qx),a=S.useId(),u=S.useMemo(()=>({id:a,initial:t,isPresent:n,custom:o,onExitComplete:c=>{l.set(c,!0);for(const f of l.values())if(!f)return;r&&r()},register:c=>(l.set(c,!1),()=>l.delete(c))}),i?void 0:[n]);return S.useMemo(()=>{l.forEach((c,f)=>l.set(f,!1))},[n]),S.useEffect(()=>{!n&&!l.size&&r&&r()},[n]),s==="popLayout"&&(e=S.createElement(Jx,{isPresent:n},e)),S.createElement(no.Provider,{value:u},e)};function qx(){return new Map}const hn=e=>e.key||"";function bx(e,t){e.forEach(n=>{const r=hn(n);t.set(r,n)})}function eS(e){const t=[];return S.Children.forEach(e,n=>{S.isValidElement(n)&&t.push(n)}),t}const tS=({children:e,custom:t,initial:n=!0,onExitComplete:r,exitBeforeEnter:o,presenceAffectsLayout:i=!0,mode:s="sync"})=>{o&&(s="wait",Ih(!1,"Replace exitBeforeEnter with mode='wait'"));let[l]=Xx();const a=S.useContext(Ga).forceRender;a&&(l=a);const u=Rm(),c=eS(e);let f=c;const d=new Set,v=S.useRef(f),g=S.useRef(new Map).current,y=S.useRef(!0);if(Cr(()=>{y.current=!1,bx(c,g),v.current=f}),ru(()=>{y.current=!0,g.clear(),d.clear()}),y.current)return S.createElement(S.Fragment,null,f.map(m=>S.createElement(zs,{key:hn(m),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:i,mode:s},m)));f=[...f];const x=v.current.map(hn),p=c.map(hn),h=x.length;for(let m=0;m<h;m++){const w=x[m];p.indexOf(w)===-1&&d.add(w)}return s==="wait"&&d.size&&(f=[]),d.forEach(m=>{if(p.indexOf(m)!==-1)return;const w=g.get(m);if(!w)return;const C=x.indexOf(m),E=()=>{g.delete(m),d.delete(m);const P=v.current.findIndex(_=>_.key===m);if(v.current.splice(P,1),!d.size){if(v.current=c,u.current===!1)return;l(),r&&r()}};f.splice(C,0,S.createElement(zs,{key:hn(w),isPresent:!1,onExitComplete:E,custom:t,presenceAffectsLayout:i,mode:s},w))}),f=f.map(m=>{const w=m.key;return d.has(w)?m:S.createElement(zs,{key:hn(m),isPresent:!0,presenceAffectsLayout:i,mode:s},m)}),zh!=="production"&&s==="wait"&&f.length>1&&console.warn(`You're attempting to animate multiple children within AnimatePresence, but its mode is set to "wait". This will lead to odd visual behaviour.`),S.createElement(S.Fragment,null,d.size?f:f.map(m=>S.cloneElement(m)))};var nS=e=>{var{children:t,hide:n,mode:r="wait"}=e,{isPlaying:o,safeToRemove:i,isStopped:s}=Vy();return Le.createElement(tS,{mode:r,onExitComplete:s?i:null},!n&&o?t:null)},rS={exports:{}};(function(e){(function(){function t(y,x){document.addEventListener?y.addEventListener("scroll",x,!1):y.attachEvent("scroll",x)}function n(y){document.body?y():document.addEventListener?document.addEventListener("DOMContentLoaded",function x(){document.removeEventListener("DOMContentLoaded",x),y()}):document.attachEvent("onreadystatechange",function x(){(document.readyState=="interactive"||document.readyState=="complete")&&(document.detachEvent("onreadystatechange",x),y())})}function r(y){this.g=document.createElement("div"),this.g.setAttribute("aria-hidden","true"),this.g.appendChild(document.createTextNode(y)),this.h=document.createElement("span"),this.i=document.createElement("span"),this.m=document.createElement("span"),this.j=document.createElement("span"),this.l=-1,this.h.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.i.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.j.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.m.style.cssText="display:inline-block;width:200%;height:200%;font-size:16px;max-width:none;",this.h.appendChild(this.m),this.i.appendChild(this.j),this.g.appendChild(this.h),this.g.appendChild(this.i)}function o(y,x){y.g.style.cssText="max-width:none;min-width:20px;min-height:20px;display:inline-block;overflow:hidden;position:absolute;width:auto;margin:0;padding:0;top:-999px;white-space:nowrap;font-synthesis:none;font:"+x+";"}function i(y){var x=y.g.offsetWidth,p=x+100;return y.j.style.width=p+"px",y.i.scrollLeft=p,y.h.scrollLeft=y.h.scrollWidth+100,y.l!==x?(y.l=x,!0):!1}function s(y,x){function p(){var m=h;i(m)&&m.g.parentNode!==null&&x(m.l)}var h=y;t(y.h,p),t(y.i,p),i(y)}function l(y,x,p){x=x||{},p=p||window,this.family=y,this.style=x.style||"normal",this.weight=x.weight||"normal",this.stretch=x.stretch||"normal",this.context=p}var a=null,u=null,c=null,f=null;function d(y){return u===null&&(v(y)&&/Apple/.test(window.navigator.vendor)?(y=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))(?:\.([0-9]+))/.exec(window.navigator.userAgent),u=!!y&&603>parseInt(y[1],10)):u=!1),u}function v(y){return f===null&&(f=!!y.document.fonts),f}function g(y,x){var p=y.style,h=y.weight;if(c===null){var m=document.createElement("div");try{m.style.font="condensed 100px sans-serif"}catch{}c=m.style.font!==""}return[p,h,c?y.stretch:"","100px",x].join(" ")}l.prototype.load=function(y,x){var p=this,h=y||"BESbswy",m=0,w=x||3e3,C=new Date().getTime();return new Promise(function(E,P){if(v(p.context)&&!d(p.context)){var _=new Promise(function(A,I){function b(){new Date().getTime()-C>=w?I(Error(""+w+"ms timeout exceeded")):p.context.document.fonts.load(g(p,'"'+p.family+'"'),h).then(function(ie){1<=ie.length?A():setTimeout(b,25)},I)}b()}),O=new Promise(function(A,I){m=setTimeout(function(){I(Error(""+w+"ms timeout exceeded"))},w)});Promise.race([O,_]).then(function(){clearTimeout(m),E(p)},P)}else n(function(){function A(){var M;(M=j!=-1&&W!=-1||j!=-1&&ee!=-1||W!=-1&&ee!=-1)&&((M=j!=W&&j!=ee&&W!=ee)||(a===null&&(M=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))/.exec(window.navigator.userAgent),a=!!M&&(536>parseInt(M[1],10)||parseInt(M[1],10)===536&&11>=parseInt(M[2],10))),M=a&&(j==T&&W==T&&ee==T||j==D&&W==D&&ee==D||j==R&&W==R&&ee==R)),M=!M),M&&(F.parentNode!==null&&F.parentNode.removeChild(F),clearTimeout(m),E(p))}function I(){if(new Date().getTime()-C>=w)F.parentNode!==null&&F.parentNode.removeChild(F),P(Error(""+w+"ms timeout exceeded"));else{var M=p.context.document.hidden;(M===!0||M===void 0)&&(j=b.g.offsetWidth,W=ie.g.offsetWidth,ee=ve.g.offsetWidth,A()),m=setTimeout(I,50)}}var b=new r(h),ie=new r(h),ve=new r(h),j=-1,W=-1,ee=-1,T=-1,D=-1,R=-1,F=document.createElement("div");F.dir="ltr",o(b,g(p,"sans-serif")),o(ie,g(p,"serif")),o(ve,g(p,"monospace")),F.appendChild(b.g),F.appendChild(ie.g),F.appendChild(ve.g),p.context.document.body.appendChild(F),T=b.g.offsetWidth,D=ie.g.offsetWidth,R=ve.g.offsetWidth,I(),s(b,function(M){j=M,A()}),o(b,g(p,'"'+p.family+'",sans-serif')),s(ie,function(M){W=M,A()}),o(ie,g(p,'"'+p.family+'",serif')),s(ve,function(M){ee=M,A()}),o(ve,g(p,'"'+p.family+'",monospace'))})})},e.exports=l})()})(rS);const oS=()=>{const{overlayText:e}=hh(),[t,n]=S.useState({timecode:"",date:""});S.useEffect(()=>{const o=()=>{const s=new Date,l=String(s.getHours()).padStart(2,"0"),a=String(s.getMinutes()).padStart(2,"0"),u=String(s.getSeconds()).padStart(2,"0"),c=String(Math.floor(s.getMilliseconds()/40)).padStart(2,"0"),f=`${l}:${a}:${u}:${c}`;n(d=>{const v=new Date(d.date);if(s.getHours()!==v.getHours()){const g=s.getFullYear(),y=String(s.getMonth()+1).padStart(2,"0"),x=String(s.getDate()).padStart(2,"0"),p=`${g}-${y}-${x}`;return{timecode:f,date:p}}return{timecode:f,date:d.date}})};o();const i=setInterval(o,40);return()=>clearInterval(i)},[t.date]);const r=["white","#FFFF00","#00FFFF","#00FF00","#FF00FF","#FF0000","#0000FF","black"];return Ht.jsx(nS,{hide:!1,children:Ht.jsxs("div",{className:"color-bars-container",children:[r.map((o,i)=>Ht.jsx(Yx.div,{className:"color-bar",style:{backgroundColor:o,left:`${i*12.5}%`},initial:{opacity:0},animate:{opacity:1},exit:{opacity:0}},i)),Ht.jsx("div",{className:"overlay",children:e||"UK London SPG2 HD"}),Ht.jsxs("div",{className:"datetimecode",children:[Ht.jsx("div",{children:t.timecode}),Ht.jsx("div",{children:t.date||"Date not available"})]})]})})};xy(oS);

</script>
    <style>
.color-bars-container{position:relative;width:100%;height:100%}.color-bar{position:absolute;top:0;width:12.5%;height:100%}.overlay{position:absolute;top:50px;left:50px;font-size:48px;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;color:#fff;background:#000;padding:10px}.datetimecode{position:absolute;bottom:50px;left:50px;font-size:48px;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;color:#fff;background:#000;padding:10px;text-align:center}

</style>
  </head>
  <body>
  </body>
</html>
