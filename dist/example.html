<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <script type="module" crossorigin>
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();function Bf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Uf={exports:{}},To={},$f={exports:{}},N={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zr=Symbol.for("react.element"),Mm=Symbol.for("react.portal"),Nm=Symbol.for("react.fragment"),Om=Symbol.for("react.strict_mode"),Fm=Symbol.for("react.profiler"),zm=Symbol.for("react.provider"),Im=Symbol.for("react.context"),jm=Symbol.for("react.forward_ref"),Bm=Symbol.for("react.suspense"),Um=Symbol.for("react.memo"),$m=Symbol.for("react.lazy"),wu=Symbol.iterator;function Wm(e){return e===null||typeof e!="object"?null:(e=wu&&e[wu]||e["@@iterator"],typeof e=="function"?e:null)}var Wf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Hf=Object.assign,Kf={};function Qn(e,t,n){this.props=e,this.context=t,this.refs=Kf,this.updater=n||Wf}Qn.prototype.isReactComponent={};Qn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Qn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Qf(){}Qf.prototype=Qn.prototype;function Yl(e,t,n){this.props=e,this.context=t,this.refs=Kf,this.updater=n||Wf}var Xl=Yl.prototype=new Qf;Xl.constructor=Yl;Hf(Xl,Qn.prototype);Xl.isPureReactComponent=!0;var xu=Array.isArray,Gf=Object.prototype.hasOwnProperty,Zl={current:null},Yf={key:!0,ref:!0,__self:!0,__source:!0};function Xf(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)Gf.call(t,r)&&!Yf.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:Zr,type:e,key:o,ref:s,props:i,_owner:Zl.current}}function Hm(e,t){return{$$typeof:Zr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Jl(e){return typeof e=="object"&&e!==null&&e.$$typeof===Zr}function Km(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Su=/\/+/g;function Jo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Km(""+e.key):t.toString(36)}function Ai(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Zr:case Mm:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+Jo(s,0):r,xu(i)?(n="",e!=null&&(n=e.replace(Su,"$&/")+"/"),Ai(i,t,n,"",function(u){return u})):i!=null&&(Jl(i)&&(i=Hm(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(Su,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",xu(e))for(var l=0;l<e.length;l++){o=e[l];var a=r+Jo(o,l);s+=Ai(o,t,n,a,i)}else if(a=Wm(e),typeof a=="function")for(e=a.call(e),l=0;!(o=e.next()).done;)o=o.value,a=r+Jo(o,l++),s+=Ai(o,t,n,a,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function li(e,t,n){if(e==null)return e;var r=[],i=0;return Ai(e,r,"","",function(o){return t.call(n,o,i++)}),r}function Qm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var xe={current:null},Li={transition:null},Gm={ReactCurrentDispatcher:xe,ReactCurrentBatchConfig:Li,ReactCurrentOwner:Zl};function Zf(){throw Error("act(...) is not supported in production builds of React.")}N.Children={map:li,forEach:function(e,t,n){li(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return li(e,function(){t++}),t},toArray:function(e){return li(e,function(t){return t})||[]},only:function(e){if(!Jl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};N.Component=Qn;N.Fragment=Nm;N.Profiler=Fm;N.PureComponent=Yl;N.StrictMode=Om;N.Suspense=Bm;N.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Gm;N.act=Zf;N.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Hf({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=Zl.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)Gf.call(t,a)&&!Yf.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:Zr,type:e.type,key:i,ref:o,props:r,_owner:s}};N.createContext=function(e){return e={$$typeof:Im,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:zm,_context:e},e.Consumer=e};N.createElement=Xf;N.createFactory=function(e){var t=Xf.bind(null,e);return t.type=e,t};N.createRef=function(){return{current:null}};N.forwardRef=function(e){return{$$typeof:jm,render:e}};N.isValidElement=Jl;N.lazy=function(e){return{$$typeof:$m,_payload:{_status:-1,_result:e},_init:Qm}};N.memo=function(e,t){return{$$typeof:Um,type:e,compare:t===void 0?null:t}};N.startTransition=function(e){var t=Li.transition;Li.transition={};try{e()}finally{Li.transition=t}};N.unstable_act=Zf;N.useCallback=function(e,t){return xe.current.useCallback(e,t)};N.useContext=function(e){return xe.current.useContext(e)};N.useDebugValue=function(){};N.useDeferredValue=function(e){return xe.current.useDeferredValue(e)};N.useEffect=function(e,t){return xe.current.useEffect(e,t)};N.useId=function(){return xe.current.useId()};N.useImperativeHandle=function(e,t,n){return xe.current.useImperativeHandle(e,t,n)};N.useInsertionEffect=function(e,t){return xe.current.useInsertionEffect(e,t)};N.useLayoutEffect=function(e,t){return xe.current.useLayoutEffect(e,t)};N.useMemo=function(e,t){return xe.current.useMemo(e,t)};N.useReducer=function(e,t,n){return xe.current.useReducer(e,t,n)};N.useRef=function(e){return xe.current.useRef(e)};N.useState=function(e){return xe.current.useState(e)};N.useSyncExternalStore=function(e,t,n){return xe.current.useSyncExternalStore(e,t,n)};N.useTransition=function(){return xe.current.useTransition()};N.version="18.3.1";$f.exports=N;var S=$f.exports;const Le=Bf(S);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ym=S,Xm=Symbol.for("react.element"),Zm=Symbol.for("react.fragment"),Jm=Object.prototype.hasOwnProperty,qm=Ym.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,bm={key:!0,ref:!0,__self:!0,__source:!0};function Jf(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Jm.call(t,r)&&!bm.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Xm,type:e,key:o,ref:s,props:i,_owner:qm.current}}To.Fragment=Zm;To.jsx=Jf;To.jsxs=Jf;Uf.exports=To;var Cu=Uf.exports,Ve={loading:0,loaded:1,playing:2,paused:3,stopped:4,removed:5},qf={exports:{}},Oe={},bf={exports:{}},ed={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,R){var D=T.length;T.push(R);e:for(;0<D;){var F=D-1>>>1,M=T[F];if(0<i(M,R))T[F]=R,T[D]=M,D=F;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var R=T[0],D=T.pop();if(D!==R){T[0]=D;e:for(var F=0,M=T.length,oi=M>>>1;F<oi;){var $t=2*(F+1)-1,Zo=T[$t],Wt=$t+1,si=T[Wt];if(0>i(Zo,D))Wt<M&&0>i(si,Zo)?(T[F]=si,T[Wt]=D,F=Wt):(T[F]=Zo,T[$t]=D,F=$t);else if(Wt<M&&0>i(si,D))T[F]=si,T[Wt]=D,F=Wt;else break e}}return R}function i(T,R){var D=T.sortIndex-R.sortIndex;return D!==0?D:T.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],c=1,f=null,d=3,y=!1,g=!1,v=!1,x=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(T){for(var R=n(u);R!==null;){if(R.callback===null)r(u);else if(R.startTime<=T)r(u),R.sortIndex=R.expirationTime,t(a,R);else break;R=n(u)}}function w(T){if(v=!1,m(T),!g)if(n(a)!==null)g=!0,W(C);else{var R=n(u);R!==null&&ee(w,R.startTime-T)}}function C(T,R){g=!1,v&&(v=!1,p(_),_=-1),y=!0;var D=d;try{for(m(R),f=n(a);f!==null&&(!(f.expirationTime>R)||T&&!I());){var F=f.callback;if(typeof F=="function"){f.callback=null,d=f.priorityLevel;var M=F(f.expirationTime<=R);R=e.unstable_now(),typeof M=="function"?f.callback=M:f===n(a)&&r(a),m(R)}else r(a);f=n(a)}if(f!==null)var oi=!0;else{var $t=n(u);$t!==null&&ee(w,$t.startTime-R),oi=!1}return oi}finally{f=null,d=D,y=!1}}var E=!1,P=null,_=-1,O=5,A=-1;function I(){return!(e.unstable_now()-A<O)}function b(){if(P!==null){var T=e.unstable_now();A=T;var R=!0;try{R=P(!0,T)}finally{R?oe():(E=!1,P=null)}}else E=!1}var oe;if(typeof h=="function")oe=function(){h(b)};else if(typeof MessageChannel<"u"){var ye=new MessageChannel,j=ye.port2;ye.port1.onmessage=b,oe=function(){j.postMessage(null)}}else oe=function(){x(b,0)};function W(T){P=T,E||(E=!0,oe())}function ee(T,R){_=x(function(){T(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){g||y||(g=!0,W(C))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(T){switch(d){case 1:case 2:case 3:var R=3;break;default:R=d}var D=d;d=R;try{return T()}finally{d=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,R){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var D=d;d=T;try{return R()}finally{d=D}},e.unstable_scheduleCallback=function(T,R,D){var F=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?F+D:F):D=F,T){case 1:var M=-1;break;case 2:M=250;break;case 5:M=**********;break;case 4:M=1e4;break;default:M=5e3}return M=D+M,T={id:c++,callback:R,priorityLevel:T,startTime:D,expirationTime:M,sortIndex:-1},D>F?(T.sortIndex=D,t(u,T),n(a)===null&&T===n(u)&&(v?(p(_),_=-1):v=!0,ee(w,D-F))):(T.sortIndex=M,t(a,T),g||y||(g=!0,W(C))),T},e.unstable_shouldYield=I,e.unstable_wrapCallback=function(T){var R=d;return function(){var D=d;d=R;try{return T.apply(this,arguments)}finally{d=D}}}})(ed);bf.exports=ed;var e0=bf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var t0=S,Me=e0;function k(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var td=new Set,Vr={};function ln(e,t){zn(e,t),zn(e+"Capture",t)}function zn(e,t){for(Vr[e]=t,e=0;e<t.length;e++)td.add(t[e])}var dt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),zs=Object.prototype.hasOwnProperty,n0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ku={},Eu={};function r0(e){return zs.call(Eu,e)?!0:zs.call(ku,e)?!1:n0.test(e)?Eu[e]=!0:(ku[e]=!0,!1)}function i0(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function o0(e,t,n,r){if(t===null||typeof t>"u"||i0(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Se(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var fe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){fe[e]=new Se(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];fe[t]=new Se(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){fe[e]=new Se(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){fe[e]=new Se(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){fe[e]=new Se(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){fe[e]=new Se(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){fe[e]=new Se(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){fe[e]=new Se(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){fe[e]=new Se(e,5,!1,e.toLowerCase(),null,!1,!1)});var ql=/[\-:]([a-z])/g;function bl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ql,bl);fe[t]=new Se(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ql,bl);fe[t]=new Se(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ql,bl);fe[t]=new Se(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){fe[e]=new Se(e,1,!1,e.toLowerCase(),null,!1,!1)});fe.xlinkHref=new Se("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){fe[e]=new Se(e,1,!1,e.toLowerCase(),null,!0,!0)});function ea(e,t,n,r){var i=fe.hasOwnProperty(t)?fe[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(o0(t,n,i,r)&&(n=null),r||i===null?r0(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var yt=t0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ai=Symbol.for("react.element"),hn=Symbol.for("react.portal"),mn=Symbol.for("react.fragment"),ta=Symbol.for("react.strict_mode"),Is=Symbol.for("react.profiler"),nd=Symbol.for("react.provider"),rd=Symbol.for("react.context"),na=Symbol.for("react.forward_ref"),js=Symbol.for("react.suspense"),Bs=Symbol.for("react.suspense_list"),ra=Symbol.for("react.memo"),wt=Symbol.for("react.lazy"),id=Symbol.for("react.offscreen"),Pu=Symbol.iterator;function Zn(e){return e===null||typeof e!="object"?null:(e=Pu&&e[Pu]||e["@@iterator"],typeof e=="function"?e:null)}var X=Object.assign,qo;function sr(e){if(qo===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);qo=t&&t[1]||""}return`
`+qo+e}var bo=!1;function es(e,t){if(!e||bo)return"";bo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,l=o.length-1;1<=s&&0<=l&&i[s]!==o[l];)l--;for(;1<=s&&0<=l;s--,l--)if(i[s]!==o[l]){if(s!==1||l!==1)do if(s--,l--,0>l||i[s]!==o[l]){var a=`
`+i[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{bo=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?sr(e):""}function s0(e){switch(e.tag){case 5:return sr(e.type);case 16:return sr("Lazy");case 13:return sr("Suspense");case 19:return sr("SuspenseList");case 0:case 2:case 15:return e=es(e.type,!1),e;case 11:return e=es(e.type.render,!1),e;case 1:return e=es(e.type,!0),e;default:return""}}function Us(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case mn:return"Fragment";case hn:return"Portal";case Is:return"Profiler";case ta:return"StrictMode";case js:return"Suspense";case Bs:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case rd:return(e.displayName||"Context")+".Consumer";case nd:return(e._context.displayName||"Context")+".Provider";case na:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ra:return t=e.displayName||null,t!==null?t:Us(e.type)||"Memo";case wt:t=e._payload,e=e._init;try{return Us(e(t))}catch{}}return null}function l0(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Us(t);case 8:return t===ta?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Ot(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function od(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function a0(e){var t=od(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ui(e){e._valueTracker||(e._valueTracker=a0(e))}function sd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=od(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Hi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function $s(e,t){var n=t.checked;return X({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n!=null?n:e._wrapperState.initialChecked})}function Tu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Ot(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function ld(e,t){t=t.checked,t!=null&&ea(e,"checked",t,!1)}function Ws(e,t){ld(e,t);var n=Ot(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Hs(e,t.type,n):t.hasOwnProperty("defaultValue")&&Hs(e,t.type,Ot(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function _u(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Hs(e,t,n){(t!=="number"||Hi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var lr=Array.isArray;function An(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Ot(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Ks(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(k(91));return X({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Vu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(k(92));if(lr(n)){if(1<n.length)throw Error(k(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Ot(n)}}function ad(e,t){var n=Ot(t.value),r=Ot(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Au(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ud(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Qs(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ud(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ci,cd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ci=ci||document.createElement("div"),ci.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ci.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Ar(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var dr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},u0=["Webkit","ms","Moz","O"];Object.keys(dr).forEach(function(e){u0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),dr[t]=dr[e]})});function fd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||dr.hasOwnProperty(e)&&dr[e]?(""+t).trim():t+"px"}function dd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=fd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var c0=X({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Gs(e,t){if(t){if(c0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(k(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(k(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(k(61))}if(t.style!=null&&typeof t.style!="object")throw Error(k(62))}}function Ys(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Xs=null;function ia(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Zs=null,Ln=null,Rn=null;function Lu(e){if(e=br(e)){if(typeof Zs!="function")throw Error(k(280));var t=e.stateNode;t&&(t=Ro(t),Zs(e.stateNode,e.type,t))}}function pd(e){Ln?Rn?Rn.push(e):Rn=[e]:Ln=e}function hd(){if(Ln){var e=Ln,t=Rn;if(Rn=Ln=null,Lu(e),t)for(e=0;e<t.length;e++)Lu(t[e])}}function md(e,t){return e(t)}function yd(){}var ts=!1;function vd(e,t,n){if(ts)return e(t,n);ts=!0;try{return md(e,t,n)}finally{ts=!1,(Ln!==null||Rn!==null)&&(yd(),hd())}}function Lr(e,t){var n=e.stateNode;if(n===null)return null;var r=Ro(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(k(231,t,typeof n));return n}var Js=!1;if(dt)try{var Jn={};Object.defineProperty(Jn,"passive",{get:function(){Js=!0}}),window.addEventListener("test",Jn,Jn),window.removeEventListener("test",Jn,Jn)}catch{Js=!1}function f0(e,t,n,r,i,o,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var pr=!1,Ki=null,Qi=!1,qs=null,d0={onError:function(e){pr=!0,Ki=e}};function p0(e,t,n,r,i,o,s,l,a){pr=!1,Ki=null,f0.apply(d0,arguments)}function h0(e,t,n,r,i,o,s,l,a){if(p0.apply(this,arguments),pr){if(pr){var u=Ki;pr=!1,Ki=null}else throw Error(k(198));Qi||(Qi=!0,qs=u)}}function an(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function gd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ru(e){if(an(e)!==e)throw Error(k(188))}function m0(e){var t=e.alternate;if(!t){if(t=an(e),t===null)throw Error(k(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return Ru(i),e;if(o===r)return Ru(i),t;o=o.sibling}throw Error(k(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s)throw Error(k(189))}}if(n.alternate!==r)throw Error(k(190))}if(n.tag!==3)throw Error(k(188));return n.stateNode.current===n?e:t}function wd(e){return e=m0(e),e!==null?xd(e):null}function xd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=xd(e);if(t!==null)return t;e=e.sibling}return null}var Sd=Me.unstable_scheduleCallback,Du=Me.unstable_cancelCallback,y0=Me.unstable_shouldYield,v0=Me.unstable_requestPaint,q=Me.unstable_now,g0=Me.unstable_getCurrentPriorityLevel,oa=Me.unstable_ImmediatePriority,Cd=Me.unstable_UserBlockingPriority,Gi=Me.unstable_NormalPriority,w0=Me.unstable_LowPriority,kd=Me.unstable_IdlePriority,_o=null,tt=null;function x0(e){if(tt&&typeof tt.onCommitFiberRoot=="function")try{tt.onCommitFiberRoot(_o,e,void 0,(e.current.flags&128)===128)}catch{}}var Ye=Math.clz32?Math.clz32:k0,S0=Math.log,C0=Math.LN2;function k0(e){return e>>>=0,e===0?32:31-(S0(e)/C0|0)|0}var fi=64,di=4194304;function ar(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Yi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~i;l!==0?r=ar(l):(o&=s,o!==0&&(r=ar(o)))}else s=n&~i,s!==0?r=ar(s):o!==0&&(r=ar(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ye(t),i=1<<n,r|=e[n],t&=~i;return r}function E0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function P0(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Ye(o),l=1<<s,a=i[s];a===-1?(!(l&n)||l&r)&&(i[s]=E0(l,t)):a<=t&&(e.expiredLanes|=l),o&=~l}}function bs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Ed(){var e=fi;return fi<<=1,!(fi&4194240)&&(fi=64),e}function ns(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Jr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ye(t),e[t]=n}function T0(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Ye(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function sa(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ye(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var B=0;function Pd(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Td,la,_d,Vd,Ad,el=!1,pi=[],_t=null,Vt=null,At=null,Rr=new Map,Dr=new Map,Ct=[],_0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Mu(e,t){switch(e){case"focusin":case"focusout":_t=null;break;case"dragenter":case"dragleave":Vt=null;break;case"mouseover":case"mouseout":At=null;break;case"pointerover":case"pointerout":Rr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Dr.delete(t.pointerId)}}function qn(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=br(t),t!==null&&la(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function V0(e,t,n,r,i){switch(t){case"focusin":return _t=qn(_t,e,t,n,r,i),!0;case"dragenter":return Vt=qn(Vt,e,t,n,r,i),!0;case"mouseover":return At=qn(At,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Rr.set(o,qn(Rr.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Dr.set(o,qn(Dr.get(o)||null,e,t,n,r,i)),!0}return!1}function Ld(e){var t=Xt(e.target);if(t!==null){var n=an(t);if(n!==null){if(t=n.tag,t===13){if(t=gd(n),t!==null){e.blockedOn=t,Ad(e.priority,function(){_d(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ri(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=tl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Xs=r,n.target.dispatchEvent(r),Xs=null}else return t=br(n),t!==null&&la(t),e.blockedOn=n,!1;t.shift()}return!0}function Nu(e,t,n){Ri(e)&&n.delete(t)}function A0(){el=!1,_t!==null&&Ri(_t)&&(_t=null),Vt!==null&&Ri(Vt)&&(Vt=null),At!==null&&Ri(At)&&(At=null),Rr.forEach(Nu),Dr.forEach(Nu)}function bn(e,t){e.blockedOn===t&&(e.blockedOn=null,el||(el=!0,Me.unstable_scheduleCallback(Me.unstable_NormalPriority,A0)))}function Mr(e){function t(i){return bn(i,e)}if(0<pi.length){bn(pi[0],e);for(var n=1;n<pi.length;n++){var r=pi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(_t!==null&&bn(_t,e),Vt!==null&&bn(Vt,e),At!==null&&bn(At,e),Rr.forEach(t),Dr.forEach(t),n=0;n<Ct.length;n++)r=Ct[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Ct.length&&(n=Ct[0],n.blockedOn===null);)Ld(n),n.blockedOn===null&&Ct.shift()}var Dn=yt.ReactCurrentBatchConfig,Xi=!0;function L0(e,t,n,r){var i=B,o=Dn.transition;Dn.transition=null;try{B=1,aa(e,t,n,r)}finally{B=i,Dn.transition=o}}function R0(e,t,n,r){var i=B,o=Dn.transition;Dn.transition=null;try{B=4,aa(e,t,n,r)}finally{B=i,Dn.transition=o}}function aa(e,t,n,r){if(Xi){var i=tl(e,t,n,r);if(i===null)ds(e,t,r,Zi,n),Mu(e,r);else if(V0(i,e,t,n,r))r.stopPropagation();else if(Mu(e,r),t&4&&-1<_0.indexOf(e)){for(;i!==null;){var o=br(i);if(o!==null&&Td(o),o=tl(e,t,n,r),o===null&&ds(e,t,r,Zi,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else ds(e,t,r,null,n)}}var Zi=null;function tl(e,t,n,r){if(Zi=null,e=ia(r),e=Xt(e),e!==null)if(t=an(e),t===null)e=null;else if(n=t.tag,n===13){if(e=gd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Zi=e,null}function Rd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(g0()){case oa:return 1;case Cd:return 4;case Gi:case w0:return 16;case kd:return 536870912;default:return 16}default:return 16}}var Et=null,ua=null,Di=null;function Dd(){if(Di)return Di;var e,t=ua,n=t.length,r,i="value"in Et?Et.value:Et.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return Di=i.slice(e,1<r?1-r:void 0)}function Mi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function hi(){return!0}function Ou(){return!1}function Fe(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(o):o[l]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?hi:Ou,this.isPropagationStopped=Ou,this}return X(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=hi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=hi)},persist:function(){},isPersistent:hi}),t}var Gn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ca=Fe(Gn),qr=X({},Gn,{view:0,detail:0}),D0=Fe(qr),rs,is,er,Vo=X({},qr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:fa,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==er&&(er&&e.type==="mousemove"?(rs=e.screenX-er.screenX,is=e.screenY-er.screenY):is=rs=0,er=e),rs)},movementY:function(e){return"movementY"in e?e.movementY:is}}),Fu=Fe(Vo),M0=X({},Vo,{dataTransfer:0}),N0=Fe(M0),O0=X({},qr,{relatedTarget:0}),os=Fe(O0),F0=X({},Gn,{animationName:0,elapsedTime:0,pseudoElement:0}),z0=Fe(F0),I0=X({},Gn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),j0=Fe(I0),B0=X({},Gn,{data:0}),zu=Fe(B0),U0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},$0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},W0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function H0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=W0[e])?!!t[e]:!1}function fa(){return H0}var K0=X({},qr,{key:function(e){if(e.key){var t=U0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Mi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?$0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:fa,charCode:function(e){return e.type==="keypress"?Mi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Mi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Q0=Fe(K0),G0=X({},Vo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Iu=Fe(G0),Y0=X({},qr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:fa}),X0=Fe(Y0),Z0=X({},Gn,{propertyName:0,elapsedTime:0,pseudoElement:0}),J0=Fe(Z0),q0=X({},Vo,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),b0=Fe(q0),ey=[9,13,27,32],da=dt&&"CompositionEvent"in window,hr=null;dt&&"documentMode"in document&&(hr=document.documentMode);var ty=dt&&"TextEvent"in window&&!hr,Md=dt&&(!da||hr&&8<hr&&11>=hr),ju=" ",Bu=!1;function Nd(e,t){switch(e){case"keyup":return ey.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Od(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var yn=!1;function ny(e,t){switch(e){case"compositionend":return Od(t);case"keypress":return t.which!==32?null:(Bu=!0,ju);case"textInput":return e=t.data,e===ju&&Bu?null:e;default:return null}}function ry(e,t){if(yn)return e==="compositionend"||!da&&Nd(e,t)?(e=Dd(),Di=ua=Et=null,yn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Md&&t.locale!=="ko"?null:t.data;default:return null}}var iy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Uu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!iy[e.type]:t==="textarea"}function Fd(e,t,n,r){pd(r),t=Ji(t,"onChange"),0<t.length&&(n=new ca("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var mr=null,Nr=null;function oy(e){Gd(e,0)}function Ao(e){var t=wn(e);if(sd(t))return e}function sy(e,t){if(e==="change")return t}var zd=!1;if(dt){var ss;if(dt){var ls="oninput"in document;if(!ls){var $u=document.createElement("div");$u.setAttribute("oninput","return;"),ls=typeof $u.oninput=="function"}ss=ls}else ss=!1;zd=ss&&(!document.documentMode||9<document.documentMode)}function Wu(){mr&&(mr.detachEvent("onpropertychange",Id),Nr=mr=null)}function Id(e){if(e.propertyName==="value"&&Ao(Nr)){var t=[];Fd(t,Nr,e,ia(e)),vd(oy,t)}}function ly(e,t,n){e==="focusin"?(Wu(),mr=t,Nr=n,mr.attachEvent("onpropertychange",Id)):e==="focusout"&&Wu()}function ay(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ao(Nr)}function uy(e,t){if(e==="click")return Ao(t)}function cy(e,t){if(e==="input"||e==="change")return Ao(t)}function fy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ze=typeof Object.is=="function"?Object.is:fy;function Or(e,t){if(Ze(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!zs.call(t,i)||!Ze(e[i],t[i]))return!1}return!0}function Hu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ku(e,t){var n=Hu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Hu(n)}}function jd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?jd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Bd(){for(var e=window,t=Hi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Hi(e.document)}return t}function pa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function dy(e){var t=Bd(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&jd(n.ownerDocument.documentElement,n)){if(r!==null&&pa(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=Ku(n,o);var s=Ku(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var py=dt&&"documentMode"in document&&11>=document.documentMode,vn=null,nl=null,yr=null,rl=!1;function Qu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;rl||vn==null||vn!==Hi(r)||(r=vn,"selectionStart"in r&&pa(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),yr&&Or(yr,r)||(yr=r,r=Ji(nl,"onSelect"),0<r.length&&(t=new ca("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=vn)))}function mi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var gn={animationend:mi("Animation","AnimationEnd"),animationiteration:mi("Animation","AnimationIteration"),animationstart:mi("Animation","AnimationStart"),transitionend:mi("Transition","TransitionEnd")},as={},Ud={};dt&&(Ud=document.createElement("div").style,"AnimationEvent"in window||(delete gn.animationend.animation,delete gn.animationiteration.animation,delete gn.animationstart.animation),"TransitionEvent"in window||delete gn.transitionend.transition);function Lo(e){if(as[e])return as[e];if(!gn[e])return e;var t=gn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Ud)return as[e]=t[n];return e}var $d=Lo("animationend"),Wd=Lo("animationiteration"),Hd=Lo("animationstart"),Kd=Lo("transitionend"),Qd=new Map,Gu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function jt(e,t){Qd.set(e,t),ln(t,[e])}for(var us=0;us<Gu.length;us++){var cs=Gu[us],hy=cs.toLowerCase(),my=cs[0].toUpperCase()+cs.slice(1);jt(hy,"on"+my)}jt($d,"onAnimationEnd");jt(Wd,"onAnimationIteration");jt(Hd,"onAnimationStart");jt("dblclick","onDoubleClick");jt("focusin","onFocus");jt("focusout","onBlur");jt(Kd,"onTransitionEnd");zn("onMouseEnter",["mouseout","mouseover"]);zn("onMouseLeave",["mouseout","mouseover"]);zn("onPointerEnter",["pointerout","pointerover"]);zn("onPointerLeave",["pointerout","pointerover"]);ln("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));ln("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));ln("onBeforeInput",["compositionend","keypress","textInput","paste"]);ln("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));ln("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));ln("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ur="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),yy=new Set("cancel close invalid load scroll toggle".split(" ").concat(ur));function Yu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,h0(r,t,void 0,e),e.currentTarget=null}function Gd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==o&&i.isPropagationStopped())break e;Yu(i,l,u),o=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==o&&i.isPropagationStopped())break e;Yu(i,l,u),o=a}}}if(Qi)throw e=qs,Qi=!1,qs=null,e}function H(e,t){var n=t[al];n===void 0&&(n=t[al]=new Set);var r=e+"__bubble";n.has(r)||(Yd(t,e,2,!1),n.add(r))}function fs(e,t,n){var r=0;t&&(r|=4),Yd(n,e,r,t)}var yi="_reactListening"+Math.random().toString(36).slice(2);function Fr(e){if(!e[yi]){e[yi]=!0,td.forEach(function(n){n!=="selectionchange"&&(yy.has(n)||fs(n,!1,e),fs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[yi]||(t[yi]=!0,fs("selectionchange",!1,t))}}function Yd(e,t,n,r){switch(Rd(t)){case 1:var i=L0;break;case 4:i=R0;break;default:i=aa}n=i.bind(null,t,n,e),i=void 0,!Js||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function ds(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;s=s.return}for(;l!==null;){if(s=Xt(l),s===null)return;if(a=s.tag,a===5||a===6){r=o=s;continue e}l=l.parentNode}}r=r.return}vd(function(){var u=o,c=ia(n),f=[];e:{var d=Qd.get(e);if(d!==void 0){var y=ca,g=e;switch(e){case"keypress":if(Mi(n)===0)break e;case"keydown":case"keyup":y=Q0;break;case"focusin":g="focus",y=os;break;case"focusout":g="blur",y=os;break;case"beforeblur":case"afterblur":y=os;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=Fu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=N0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=X0;break;case $d:case Wd:case Hd:y=z0;break;case Kd:y=J0;break;case"scroll":y=D0;break;case"wheel":y=b0;break;case"copy":case"cut":case"paste":y=j0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=Iu}var v=(t&4)!==0,x=!v&&e==="scroll",p=v?d!==null?d+"Capture":null:d;v=[];for(var h=u,m;h!==null;){m=h;var w=m.stateNode;if(m.tag===5&&w!==null&&(m=w,p!==null&&(w=Lr(h,p),w!=null&&v.push(zr(h,w,m)))),x)break;h=h.return}0<v.length&&(d=new y(d,g,null,n,c),f.push({event:d,listeners:v}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",d&&n!==Xs&&(g=n.relatedTarget||n.fromElement)&&(Xt(g)||g[pt]))break e;if((y||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,y?(g=n.relatedTarget||n.toElement,y=u,g=g?Xt(g):null,g!==null&&(x=an(g),g!==x||g.tag!==5&&g.tag!==6)&&(g=null)):(y=null,g=u),y!==g)){if(v=Fu,w="onMouseLeave",p="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(v=Iu,w="onPointerLeave",p="onPointerEnter",h="pointer"),x=y==null?d:wn(y),m=g==null?d:wn(g),d=new v(w,h+"leave",y,n,c),d.target=x,d.relatedTarget=m,w=null,Xt(c)===u&&(v=new v(p,h+"enter",g,n,c),v.target=m,v.relatedTarget=x,w=v),x=w,y&&g)t:{for(v=y,p=g,h=0,m=v;m;m=dn(m))h++;for(m=0,w=p;w;w=dn(w))m++;for(;0<h-m;)v=dn(v),h--;for(;0<m-h;)p=dn(p),m--;for(;h--;){if(v===p||p!==null&&v===p.alternate)break t;v=dn(v),p=dn(p)}v=null}else v=null;y!==null&&Xu(f,d,y,v,!1),g!==null&&x!==null&&Xu(f,x,g,v,!0)}}e:{if(d=u?wn(u):window,y=d.nodeName&&d.nodeName.toLowerCase(),y==="select"||y==="input"&&d.type==="file")var C=sy;else if(Uu(d))if(zd)C=cy;else{C=ay;var E=ly}else(y=d.nodeName)&&y.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(C=uy);if(C&&(C=C(e,u))){Fd(f,C,n,c);break e}E&&E(e,d,u),e==="focusout"&&(E=d._wrapperState)&&E.controlled&&d.type==="number"&&Hs(d,"number",d.value)}switch(E=u?wn(u):window,e){case"focusin":(Uu(E)||E.contentEditable==="true")&&(vn=E,nl=u,yr=null);break;case"focusout":yr=nl=vn=null;break;case"mousedown":rl=!0;break;case"contextmenu":case"mouseup":case"dragend":rl=!1,Qu(f,n,c);break;case"selectionchange":if(py)break;case"keydown":case"keyup":Qu(f,n,c)}var P;if(da)e:{switch(e){case"compositionstart":var _="onCompositionStart";break e;case"compositionend":_="onCompositionEnd";break e;case"compositionupdate":_="onCompositionUpdate";break e}_=void 0}else yn?Nd(e,n)&&(_="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(_="onCompositionStart");_&&(Md&&n.locale!=="ko"&&(yn||_!=="onCompositionStart"?_==="onCompositionEnd"&&yn&&(P=Dd()):(Et=c,ua="value"in Et?Et.value:Et.textContent,yn=!0)),E=Ji(u,_),0<E.length&&(_=new zu(_,e,null,n,c),f.push({event:_,listeners:E}),P?_.data=P:(P=Od(n),P!==null&&(_.data=P)))),(P=ty?ny(e,n):ry(e,n))&&(u=Ji(u,"onBeforeInput"),0<u.length&&(c=new zu("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=P))}Gd(f,t)})}function zr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ji(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=Lr(e,n),o!=null&&r.unshift(zr(e,o,i)),o=Lr(e,t),o!=null&&r.push(zr(e,o,i))),e=e.return}return r}function dn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Xu(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=Lr(n,o),a!=null&&s.unshift(zr(n,a,l))):i||(a=Lr(n,o),a!=null&&s.push(zr(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var vy=/\r\n?/g,gy=/\u0000|\uFFFD/g;function Zu(e){return(typeof e=="string"?e:""+e).replace(vy,`
`).replace(gy,"")}function vi(e,t,n){if(t=Zu(t),Zu(e)!==t&&n)throw Error(k(425))}function qi(){}var il=null,ol=null;function sl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ll=typeof setTimeout=="function"?setTimeout:void 0,wy=typeof clearTimeout=="function"?clearTimeout:void 0,Ju=typeof Promise=="function"?Promise:void 0,xy=typeof queueMicrotask=="function"?queueMicrotask:typeof Ju<"u"?function(e){return Ju.resolve(null).then(e).catch(Sy)}:ll;function Sy(e){setTimeout(function(){throw e})}function ps(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Mr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Mr(t)}function Lt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function qu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Yn=Math.random().toString(36).slice(2),et="__reactFiber$"+Yn,Ir="__reactProps$"+Yn,pt="__reactContainer$"+Yn,al="__reactEvents$"+Yn,Cy="__reactListeners$"+Yn,ky="__reactHandles$"+Yn;function Xt(e){var t=e[et];if(t)return t;for(var n=e.parentNode;n;){if(t=n[pt]||n[et]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=qu(e);e!==null;){if(n=e[et])return n;e=qu(e)}return t}e=n,n=e.parentNode}return null}function br(e){return e=e[et]||e[pt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function wn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(k(33))}function Ro(e){return e[Ir]||null}var ul=[],xn=-1;function Bt(e){return{current:e}}function K(e){0>xn||(e.current=ul[xn],ul[xn]=null,xn--)}function $(e,t){xn++,ul[xn]=e.current,e.current=t}var Ft={},me=Bt(Ft),Ee=Bt(!1),tn=Ft;function In(e,t){var n=e.type.contextTypes;if(!n)return Ft;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Pe(e){return e=e.childContextTypes,e!=null}function bi(){K(Ee),K(me)}function bu(e,t,n){if(me.current!==Ft)throw Error(k(168));$(me,t),$(Ee,n)}function Xd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(k(108,l0(e)||"Unknown",i));return X({},n,r)}function eo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ft,tn=me.current,$(me,e),$(Ee,Ee.current),!0}function ec(e,t,n){var r=e.stateNode;if(!r)throw Error(k(169));n?(e=Xd(e,t,tn),r.__reactInternalMemoizedMergedChildContext=e,K(Ee),K(me),$(me,e)):K(Ee),$(Ee,n)}var lt=null,Do=!1,hs=!1;function Zd(e){lt===null?lt=[e]:lt.push(e)}function Ey(e){Do=!0,Zd(e)}function Ut(){if(!hs&&lt!==null){hs=!0;var e=0,t=B;try{var n=lt;for(B=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}lt=null,Do=!1}catch(i){throw lt!==null&&(lt=lt.slice(e+1)),Sd(oa,Ut),i}finally{B=t,hs=!1}}return null}var Sn=[],Cn=0,to=null,no=0,ze=[],Ie=0,nn=null,at=1,ut="";function Qt(e,t){Sn[Cn++]=no,Sn[Cn++]=to,to=e,no=t}function Jd(e,t,n){ze[Ie++]=at,ze[Ie++]=ut,ze[Ie++]=nn,nn=e;var r=at;e=ut;var i=32-Ye(r)-1;r&=~(1<<i),n+=1;var o=32-Ye(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,at=1<<32-Ye(t)+i|n<<i|r,ut=o+e}else at=1<<o|n<<i|r,ut=e}function ha(e){e.return!==null&&(Qt(e,1),Jd(e,1,0))}function ma(e){for(;e===to;)to=Sn[--Cn],Sn[Cn]=null,no=Sn[--Cn],Sn[Cn]=null;for(;e===nn;)nn=ze[--Ie],ze[Ie]=null,ut=ze[--Ie],ze[Ie]=null,at=ze[--Ie],ze[Ie]=null}var De=null,Re=null,Q=!1,Ge=null;function qd(e,t){var n=je(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function tc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,De=e,Re=Lt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,De=e,Re=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=nn!==null?{id:at,overflow:ut}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=je(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,De=e,Re=null,!0):!1;default:return!1}}function cl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function fl(e){if(Q){var t=Re;if(t){var n=t;if(!tc(e,t)){if(cl(e))throw Error(k(418));t=Lt(n.nextSibling);var r=De;t&&tc(e,t)?qd(r,n):(e.flags=e.flags&-4097|2,Q=!1,De=e)}}else{if(cl(e))throw Error(k(418));e.flags=e.flags&-4097|2,Q=!1,De=e}}}function nc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;De=e}function gi(e){if(e!==De)return!1;if(!Q)return nc(e),Q=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!sl(e.type,e.memoizedProps)),t&&(t=Re)){if(cl(e))throw bd(),Error(k(418));for(;t;)qd(e,t),t=Lt(t.nextSibling)}if(nc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(k(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Re=Lt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Re=null}}else Re=De?Lt(e.stateNode.nextSibling):null;return!0}function bd(){for(var e=Re;e;)e=Lt(e.nextSibling)}function jn(){Re=De=null,Q=!1}function ya(e){Ge===null?Ge=[e]:Ge.push(e)}var Py=yt.ReactCurrentBatchConfig;function tr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(k(309));var r=n.stateNode}if(!r)throw Error(k(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var l=i.refs;s===null?delete l[o]:l[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(k(284));if(!n._owner)throw Error(k(290,e))}return e}function wi(e,t){throw e=Object.prototype.toString.call(t),Error(k(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function rc(e){var t=e._init;return t(e._payload)}function ep(e){function t(p,h){if(e){var m=p.deletions;m===null?(p.deletions=[h],p.flags|=16):m.push(h)}}function n(p,h){if(!e)return null;for(;h!==null;)t(p,h),h=h.sibling;return null}function r(p,h){for(p=new Map;h!==null;)h.key!==null?p.set(h.key,h):p.set(h.index,h),h=h.sibling;return p}function i(p,h){return p=Nt(p,h),p.index=0,p.sibling=null,p}function o(p,h,m){return p.index=m,e?(m=p.alternate,m!==null?(m=m.index,m<h?(p.flags|=2,h):m):(p.flags|=2,h)):(p.flags|=1048576,h)}function s(p){return e&&p.alternate===null&&(p.flags|=2),p}function l(p,h,m,w){return h===null||h.tag!==6?(h=Ss(m,p.mode,w),h.return=p,h):(h=i(h,m),h.return=p,h)}function a(p,h,m,w){var C=m.type;return C===mn?c(p,h,m.props.children,w,m.key):h!==null&&(h.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===wt&&rc(C)===h.type)?(w=i(h,m.props),w.ref=tr(p,h,m),w.return=p,w):(w=Bi(m.type,m.key,m.props,null,p.mode,w),w.ref=tr(p,h,m),w.return=p,w)}function u(p,h,m,w){return h===null||h.tag!==4||h.stateNode.containerInfo!==m.containerInfo||h.stateNode.implementation!==m.implementation?(h=Cs(m,p.mode,w),h.return=p,h):(h=i(h,m.children||[]),h.return=p,h)}function c(p,h,m,w,C){return h===null||h.tag!==7?(h=en(m,p.mode,w,C),h.return=p,h):(h=i(h,m),h.return=p,h)}function f(p,h,m){if(typeof h=="string"&&h!==""||typeof h=="number")return h=Ss(""+h,p.mode,m),h.return=p,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case ai:return m=Bi(h.type,h.key,h.props,null,p.mode,m),m.ref=tr(p,null,h),m.return=p,m;case hn:return h=Cs(h,p.mode,m),h.return=p,h;case wt:var w=h._init;return f(p,w(h._payload),m)}if(lr(h)||Zn(h))return h=en(h,p.mode,m,null),h.return=p,h;wi(p,h)}return null}function d(p,h,m,w){var C=h!==null?h.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return C!==null?null:l(p,h,""+m,w);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case ai:return m.key===C?a(p,h,m,w):null;case hn:return m.key===C?u(p,h,m,w):null;case wt:return C=m._init,d(p,h,C(m._payload),w)}if(lr(m)||Zn(m))return C!==null?null:c(p,h,m,w,null);wi(p,m)}return null}function y(p,h,m,w,C){if(typeof w=="string"&&w!==""||typeof w=="number")return p=p.get(m)||null,l(h,p,""+w,C);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case ai:return p=p.get(w.key===null?m:w.key)||null,a(h,p,w,C);case hn:return p=p.get(w.key===null?m:w.key)||null,u(h,p,w,C);case wt:var E=w._init;return y(p,h,m,E(w._payload),C)}if(lr(w)||Zn(w))return p=p.get(m)||null,c(h,p,w,C,null);wi(h,w)}return null}function g(p,h,m,w){for(var C=null,E=null,P=h,_=h=0,O=null;P!==null&&_<m.length;_++){P.index>_?(O=P,P=null):O=P.sibling;var A=d(p,P,m[_],w);if(A===null){P===null&&(P=O);break}e&&P&&A.alternate===null&&t(p,P),h=o(A,h,_),E===null?C=A:E.sibling=A,E=A,P=O}if(_===m.length)return n(p,P),Q&&Qt(p,_),C;if(P===null){for(;_<m.length;_++)P=f(p,m[_],w),P!==null&&(h=o(P,h,_),E===null?C=P:E.sibling=P,E=P);return Q&&Qt(p,_),C}for(P=r(p,P);_<m.length;_++)O=y(P,p,_,m[_],w),O!==null&&(e&&O.alternate!==null&&P.delete(O.key===null?_:O.key),h=o(O,h,_),E===null?C=O:E.sibling=O,E=O);return e&&P.forEach(function(I){return t(p,I)}),Q&&Qt(p,_),C}function v(p,h,m,w){var C=Zn(m);if(typeof C!="function")throw Error(k(150));if(m=C.call(m),m==null)throw Error(k(151));for(var E=C=null,P=h,_=h=0,O=null,A=m.next();P!==null&&!A.done;_++,A=m.next()){P.index>_?(O=P,P=null):O=P.sibling;var I=d(p,P,A.value,w);if(I===null){P===null&&(P=O);break}e&&P&&I.alternate===null&&t(p,P),h=o(I,h,_),E===null?C=I:E.sibling=I,E=I,P=O}if(A.done)return n(p,P),Q&&Qt(p,_),C;if(P===null){for(;!A.done;_++,A=m.next())A=f(p,A.value,w),A!==null&&(h=o(A,h,_),E===null?C=A:E.sibling=A,E=A);return Q&&Qt(p,_),C}for(P=r(p,P);!A.done;_++,A=m.next())A=y(P,p,_,A.value,w),A!==null&&(e&&A.alternate!==null&&P.delete(A.key===null?_:A.key),h=o(A,h,_),E===null?C=A:E.sibling=A,E=A);return e&&P.forEach(function(b){return t(p,b)}),Q&&Qt(p,_),C}function x(p,h,m,w){if(typeof m=="object"&&m!==null&&m.type===mn&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case ai:e:{for(var C=m.key,E=h;E!==null;){if(E.key===C){if(C=m.type,C===mn){if(E.tag===7){n(p,E.sibling),h=i(E,m.props.children),h.return=p,p=h;break e}}else if(E.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===wt&&rc(C)===E.type){n(p,E.sibling),h=i(E,m.props),h.ref=tr(p,E,m),h.return=p,p=h;break e}n(p,E);break}else t(p,E);E=E.sibling}m.type===mn?(h=en(m.props.children,p.mode,w,m.key),h.return=p,p=h):(w=Bi(m.type,m.key,m.props,null,p.mode,w),w.ref=tr(p,h,m),w.return=p,p=w)}return s(p);case hn:e:{for(E=m.key;h!==null;){if(h.key===E)if(h.tag===4&&h.stateNode.containerInfo===m.containerInfo&&h.stateNode.implementation===m.implementation){n(p,h.sibling),h=i(h,m.children||[]),h.return=p,p=h;break e}else{n(p,h);break}else t(p,h);h=h.sibling}h=Cs(m,p.mode,w),h.return=p,p=h}return s(p);case wt:return E=m._init,x(p,h,E(m._payload),w)}if(lr(m))return g(p,h,m,w);if(Zn(m))return v(p,h,m,w);wi(p,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,h!==null&&h.tag===6?(n(p,h.sibling),h=i(h,m),h.return=p,p=h):(n(p,h),h=Ss(m,p.mode,w),h.return=p,p=h),s(p)):n(p,h)}return x}var Bn=ep(!0),tp=ep(!1),ro=Bt(null),io=null,kn=null,va=null;function ga(){va=kn=io=null}function wa(e){var t=ro.current;K(ro),e._currentValue=t}function dl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Mn(e,t){io=e,va=kn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ke=!0),e.firstContext=null)}function Ue(e){var t=e._currentValue;if(va!==e)if(e={context:e,memoizedValue:t,next:null},kn===null){if(io===null)throw Error(k(308));kn=e,io.dependencies={lanes:0,firstContext:e}}else kn=kn.next=e;return t}var Zt=null;function xa(e){Zt===null?Zt=[e]:Zt.push(e)}function np(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,xa(t)):(n.next=i.next,i.next=n),t.interleaved=n,ht(e,r)}function ht(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var xt=!1;function Sa(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function rp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ct(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Rt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,ht(e,n)}return i=r.interleaved,i===null?(t.next=t,xa(r)):(t.next=i.next,i.next=t),r.interleaved=t,ht(e,n)}function Ni(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,sa(e,n)}}function ic(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function oo(e,t,n,r){var i=e.updateQueue;xt=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?o=u:s.next=u,s=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==s&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(o!==null){var f=i.baseState;s=0,c=u=a=null,l=o;do{var d=l.lane,y=l.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:y,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var g=e,v=l;switch(d=t,y=n,v.tag){case 1:if(g=v.payload,typeof g=="function"){f=g.call(y,f,d);break e}f=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=v.payload,d=typeof g=="function"?g.call(y,f,d):g,d==null)break e;f=X({},f,d);break e;case 2:xt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,d=i.effects,d===null?i.effects=[l]:d.push(l))}else y={eventTime:y,lane:d,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=y,a=f):c=c.next=y,s|=d;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;d=l,l=d.next,d.next=null,i.lastBaseUpdate=d,i.shared.pending=null}}while(!0);if(c===null&&(a=f),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);on|=s,e.lanes=s,e.memoizedState=f}}function oc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(k(191,i));i.call(r)}}}var ei={},nt=Bt(ei),jr=Bt(ei),Br=Bt(ei);function Jt(e){if(e===ei)throw Error(k(174));return e}function Ca(e,t){switch($(Br,t),$(jr,e),$(nt,ei),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Qs(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Qs(t,e)}K(nt),$(nt,t)}function Un(){K(nt),K(jr),K(Br)}function ip(e){Jt(Br.current);var t=Jt(nt.current),n=Qs(t,e.type);t!==n&&($(jr,e),$(nt,n))}function ka(e){jr.current===e&&(K(nt),K(jr))}var G=Bt(0);function so(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ms=[];function Ea(){for(var e=0;e<ms.length;e++)ms[e]._workInProgressVersionPrimary=null;ms.length=0}var Oi=yt.ReactCurrentDispatcher,ys=yt.ReactCurrentBatchConfig,rn=0,Y=null,re=null,se=null,lo=!1,vr=!1,Ur=0,Ty=0;function de(){throw Error(k(321))}function Pa(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ze(e[n],t[n]))return!1;return!0}function Ta(e,t,n,r,i,o){if(rn=o,Y=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Oi.current=e===null||e.memoizedState===null?Ly:Ry,e=n(r,i),vr){o=0;do{if(vr=!1,Ur=0,25<=o)throw Error(k(301));o+=1,se=re=null,t.updateQueue=null,Oi.current=Dy,e=n(r,i)}while(vr)}if(Oi.current=ao,t=re!==null&&re.next!==null,rn=0,se=re=Y=null,lo=!1,t)throw Error(k(300));return e}function _a(){var e=Ur!==0;return Ur=0,e}function qe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return se===null?Y.memoizedState=se=e:se=se.next=e,se}function $e(){if(re===null){var e=Y.alternate;e=e!==null?e.memoizedState:null}else e=re.next;var t=se===null?Y.memoizedState:se.next;if(t!==null)se=t,re=e;else{if(e===null)throw Error(k(310));re=e,e={memoizedState:re.memoizedState,baseState:re.baseState,baseQueue:re.baseQueue,queue:re.queue,next:null},se===null?Y.memoizedState=se=e:se=se.next=e}return se}function $r(e,t){return typeof t=="function"?t(e):t}function vs(e){var t=$e(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=re,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var l=s=null,a=null,u=o;do{var c=u.lane;if((rn&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=f,s=r):a=a.next=f,Y.lanes|=c,on|=c}u=u.next}while(u!==null&&u!==o);a===null?s=r:a.next=l,Ze(r,t.memoizedState)||(ke=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,Y.lanes|=o,on|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function gs(e){var t=$e(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);Ze(o,t.memoizedState)||(ke=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function op(){}function sp(e,t){var n=Y,r=$e(),i=t(),o=!Ze(r.memoizedState,i);if(o&&(r.memoizedState=i,ke=!0),r=r.queue,Va(up.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||se!==null&&se.memoizedState.tag&1){if(n.flags|=2048,Wr(9,ap.bind(null,n,r,i,t),void 0,null),le===null)throw Error(k(349));rn&30||lp(n,t,i)}return i}function lp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function ap(e,t,n,r){t.value=n,t.getSnapshot=r,cp(t)&&fp(e)}function up(e,t,n){return n(function(){cp(t)&&fp(e)})}function cp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ze(e,n)}catch{return!0}}function fp(e){var t=ht(e,1);t!==null&&Xe(t,e,1,-1)}function sc(e){var t=qe();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:$r,lastRenderedState:e},t.queue=e,e=e.dispatch=Ay.bind(null,Y,e),[t.memoizedState,e]}function Wr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function dp(){return $e().memoizedState}function Fi(e,t,n,r){var i=qe();Y.flags|=e,i.memoizedState=Wr(1|t,n,void 0,r===void 0?null:r)}function Mo(e,t,n,r){var i=$e();r=r===void 0?null:r;var o=void 0;if(re!==null){var s=re.memoizedState;if(o=s.destroy,r!==null&&Pa(r,s.deps)){i.memoizedState=Wr(t,n,o,r);return}}Y.flags|=e,i.memoizedState=Wr(1|t,n,o,r)}function lc(e,t){return Fi(8390656,8,e,t)}function Va(e,t){return Mo(2048,8,e,t)}function pp(e,t){return Mo(4,2,e,t)}function hp(e,t){return Mo(4,4,e,t)}function mp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function yp(e,t,n){return n=n!=null?n.concat([e]):null,Mo(4,4,mp.bind(null,t,e),n)}function Aa(){}function vp(e,t){var n=$e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Pa(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function gp(e,t){var n=$e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Pa(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function wp(e,t,n){return rn&21?(Ze(n,t)||(n=Ed(),Y.lanes|=n,on|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ke=!0),e.memoizedState=n)}function _y(e,t){var n=B;B=n!==0&&4>n?n:4,e(!0);var r=ys.transition;ys.transition={};try{e(!1),t()}finally{B=n,ys.transition=r}}function xp(){return $e().memoizedState}function Vy(e,t,n){var r=Mt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Sp(e))Cp(t,n);else if(n=np(e,t,n,r),n!==null){var i=we();Xe(n,e,r,i),kp(n,t,r)}}function Ay(e,t,n){var r=Mt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Sp(e))Cp(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,l=o(s,n);if(i.hasEagerState=!0,i.eagerState=l,Ze(l,s)){var a=t.interleaved;a===null?(i.next=i,xa(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=np(e,t,i,r),n!==null&&(i=we(),Xe(n,e,r,i),kp(n,t,r))}}function Sp(e){var t=e.alternate;return e===Y||t!==null&&t===Y}function Cp(e,t){vr=lo=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function kp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,sa(e,n)}}var ao={readContext:Ue,useCallback:de,useContext:de,useEffect:de,useImperativeHandle:de,useInsertionEffect:de,useLayoutEffect:de,useMemo:de,useReducer:de,useRef:de,useState:de,useDebugValue:de,useDeferredValue:de,useTransition:de,useMutableSource:de,useSyncExternalStore:de,useId:de,unstable_isNewReconciler:!1},Ly={readContext:Ue,useCallback:function(e,t){return qe().memoizedState=[e,t===void 0?null:t],e},useContext:Ue,useEffect:lc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Fi(4194308,4,mp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Fi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Fi(4,2,e,t)},useMemo:function(e,t){var n=qe();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=qe();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Vy.bind(null,Y,e),[r.memoizedState,e]},useRef:function(e){var t=qe();return e={current:e},t.memoizedState=e},useState:sc,useDebugValue:Aa,useDeferredValue:function(e){return qe().memoizedState=e},useTransition:function(){var e=sc(!1),t=e[0];return e=_y.bind(null,e[1]),qe().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Y,i=qe();if(Q){if(n===void 0)throw Error(k(407));n=n()}else{if(n=t(),le===null)throw Error(k(349));rn&30||lp(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,lc(up.bind(null,r,o,e),[e]),r.flags|=2048,Wr(9,ap.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=qe(),t=le.identifierPrefix;if(Q){var n=ut,r=at;n=(r&~(1<<32-Ye(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Ur++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Ty++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Ry={readContext:Ue,useCallback:vp,useContext:Ue,useEffect:Va,useImperativeHandle:yp,useInsertionEffect:pp,useLayoutEffect:hp,useMemo:gp,useReducer:vs,useRef:dp,useState:function(){return vs($r)},useDebugValue:Aa,useDeferredValue:function(e){var t=$e();return wp(t,re.memoizedState,e)},useTransition:function(){var e=vs($r)[0],t=$e().memoizedState;return[e,t]},useMutableSource:op,useSyncExternalStore:sp,useId:xp,unstable_isNewReconciler:!1},Dy={readContext:Ue,useCallback:vp,useContext:Ue,useEffect:Va,useImperativeHandle:yp,useInsertionEffect:pp,useLayoutEffect:hp,useMemo:gp,useReducer:gs,useRef:dp,useState:function(){return gs($r)},useDebugValue:Aa,useDeferredValue:function(e){var t=$e();return re===null?t.memoizedState=e:wp(t,re.memoizedState,e)},useTransition:function(){var e=gs($r)[0],t=$e().memoizedState;return[e,t]},useMutableSource:op,useSyncExternalStore:sp,useId:xp,unstable_isNewReconciler:!1};function Ke(e,t){if(e&&e.defaultProps){t=X({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function pl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:X({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var No={isMounted:function(e){return(e=e._reactInternals)?an(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=we(),i=Mt(e),o=ct(r,i);o.payload=t,n!=null&&(o.callback=n),t=Rt(e,o,i),t!==null&&(Xe(t,e,i,r),Ni(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=we(),i=Mt(e),o=ct(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Rt(e,o,i),t!==null&&(Xe(t,e,i,r),Ni(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=we(),r=Mt(e),i=ct(n,r);i.tag=2,t!=null&&(i.callback=t),t=Rt(e,i,r),t!==null&&(Xe(t,e,r,n),Ni(t,e,r))}};function ac(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!Or(n,r)||!Or(i,o):!0}function Ep(e,t,n){var r=!1,i=Ft,o=t.contextType;return typeof o=="object"&&o!==null?o=Ue(o):(i=Pe(t)?tn:me.current,r=t.contextTypes,o=(r=r!=null)?In(e,i):Ft),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=No,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function uc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&No.enqueueReplaceState(t,t.state,null)}function hl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Sa(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=Ue(o):(o=Pe(t)?tn:me.current,i.context=In(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(pl(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&No.enqueueReplaceState(i,i.state,null),oo(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function $n(e,t){try{var n="",r=t;do n+=s0(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function ws(e,t,n){return{value:e,source:null,stack:n!=null?n:null,digest:t!=null?t:null}}function ml(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var My=typeof WeakMap=="function"?WeakMap:Map;function Pp(e,t,n){n=ct(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){co||(co=!0,Pl=r),ml(e,t)},n}function Tp(e,t,n){n=ct(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){ml(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){ml(e,t),typeof r!="function"&&(Dt===null?Dt=new Set([this]):Dt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function cc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new My;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Gy.bind(null,e,t,n),t.then(e,e))}function fc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function dc(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ct(-1,1),t.tag=2,Rt(n,t,1))),n.lanes|=1),e)}var Ny=yt.ReactCurrentOwner,ke=!1;function ge(e,t,n,r){t.child=e===null?tp(t,null,n,r):Bn(t,e.child,n,r)}function pc(e,t,n,r,i){n=n.render;var o=t.ref;return Mn(t,i),r=Ta(e,t,n,r,o,i),n=_a(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,mt(e,t,i)):(Q&&n&&ha(t),t.flags|=1,ge(e,t,r,i),t.child)}function hc(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!za(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,_p(e,t,o,r,i)):(e=Bi(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:Or,n(s,r)&&e.ref===t.ref)return mt(e,t,i)}return t.flags|=1,e=Nt(o,r),e.ref=t.ref,e.return=t,t.child=e}function _p(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(Or(o,r)&&e.ref===t.ref)if(ke=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(ke=!0);else return t.lanes=e.lanes,mt(e,t,i)}return yl(e,t,n,r,i)}function Vp(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},$(Pn,Ae),Ae|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,$(Pn,Ae),Ae|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,$(Pn,Ae),Ae|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,$(Pn,Ae),Ae|=r;return ge(e,t,i,n),t.child}function Ap(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function yl(e,t,n,r,i){var o=Pe(n)?tn:me.current;return o=In(t,o),Mn(t,i),n=Ta(e,t,n,r,o,i),r=_a(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,mt(e,t,i)):(Q&&r&&ha(t),t.flags|=1,ge(e,t,n,i),t.child)}function mc(e,t,n,r,i){if(Pe(n)){var o=!0;eo(t)}else o=!1;if(Mn(t,i),t.stateNode===null)zi(e,t),Ep(t,n,r),hl(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=Ue(u):(u=Pe(n)?tn:me.current,u=In(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&uc(t,s,r,u),xt=!1;var d=t.memoizedState;s.state=d,oo(t,r,s,i),a=t.memoizedState,l!==r||d!==a||Ee.current||xt?(typeof c=="function"&&(pl(t,n,c,r),a=t.memoizedState),(l=xt||ac(t,n,l,r,d,a,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,rp(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:Ke(t.type,l),s.props=u,f=t.pendingProps,d=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=Ue(a):(a=Pe(n)?tn:me.current,a=In(t,a));var y=n.getDerivedStateFromProps;(c=typeof y=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==f||d!==a)&&uc(t,s,r,a),xt=!1,d=t.memoizedState,s.state=d,oo(t,r,s,i);var g=t.memoizedState;l!==f||d!==g||Ee.current||xt?(typeof y=="function"&&(pl(t,n,y,r),g=t.memoizedState),(u=xt||ac(t,n,u,r,d,g,a)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,g,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,g,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=g),s.props=r,s.state=g,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return vl(e,t,n,r,o,i)}function vl(e,t,n,r,i,o){Ap(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&ec(t,n,!1),mt(e,t,o);r=t.stateNode,Ny.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Bn(t,e.child,null,o),t.child=Bn(t,null,l,o)):ge(e,t,l,o),t.memoizedState=r.state,i&&ec(t,n,!0),t.child}function Lp(e){var t=e.stateNode;t.pendingContext?bu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&bu(e,t.context,!1),Ca(e,t.containerInfo)}function yc(e,t,n,r,i){return jn(),ya(i),t.flags|=256,ge(e,t,n,r),t.child}var gl={dehydrated:null,treeContext:null,retryLane:0};function wl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Rp(e,t,n){var r=t.pendingProps,i=G.current,o=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),$(G,i&1),e===null)return fl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=zo(s,r,0,null),e=en(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=wl(n),t.memoizedState=gl,e):La(t,s));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return Oy(e,t,s,r,l,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Nt(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?o=Nt(l,o):(o=en(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?wl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=gl,r}return o=e.child,e=o.sibling,r=Nt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function La(e,t){return t=zo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function xi(e,t,n,r){return r!==null&&ya(r),Bn(t,e.child,null,n),e=La(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Oy(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=ws(Error(k(422))),xi(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=zo({mode:"visible",children:r.children},i,0,null),o=en(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&Bn(t,e.child,null,s),t.child.memoizedState=wl(s),t.memoizedState=gl,o);if(!(t.mode&1))return xi(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,o=Error(k(419)),r=ws(o,r,void 0),xi(e,t,s,r)}if(l=(s&e.childLanes)!==0,ke||l){if(r=le,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,ht(e,i),Xe(r,e,i,-1))}return Fa(),r=ws(Error(k(421))),xi(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Yy.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,Re=Lt(i.nextSibling),De=t,Q=!0,Ge=null,e!==null&&(ze[Ie++]=at,ze[Ie++]=ut,ze[Ie++]=nn,at=e.id,ut=e.overflow,nn=t),t=La(t,r.children),t.flags|=4096,t)}function vc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),dl(e.return,t,n)}function xs(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function Dp(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(ge(e,t,r.children,n),r=G.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&vc(e,n,t);else if(e.tag===19)vc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if($(G,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&so(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),xs(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&so(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}xs(t,!0,n,null,o);break;case"together":xs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function zi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function mt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),on|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(k(153));if(t.child!==null){for(e=t.child,n=Nt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Nt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Fy(e,t,n){switch(t.tag){case 3:Lp(t),jn();break;case 5:ip(t);break;case 1:Pe(t.type)&&eo(t);break;case 4:Ca(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;$(ro,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?($(G,G.current&1),t.flags|=128,null):n&t.child.childLanes?Rp(e,t,n):($(G,G.current&1),e=mt(e,t,n),e!==null?e.sibling:null);$(G,G.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Dp(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),$(G,G.current),r)break;return null;case 22:case 23:return t.lanes=0,Vp(e,t,n)}return mt(e,t,n)}var Mp,xl,Np,Op;Mp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};xl=function(){};Np=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Jt(nt.current);var o=null;switch(n){case"input":i=$s(e,i),r=$s(e,r),o=[];break;case"select":i=X({},i,{value:void 0}),r=X({},r,{value:void 0}),o=[];break;case"textarea":i=Ks(e,i),r=Ks(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=qi)}Gs(n,r);var s;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Vr.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(o||(o=[]),o.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(o=o||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(o=o||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Vr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&H("scroll",e),o||l===a||(o=[])):(o=o||[]).push(u,a))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};Op=function(e,t,n,r){n!==r&&(t.flags|=4)};function nr(e,t){if(!Q)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function pe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function zy(e,t,n){var r=t.pendingProps;switch(ma(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return pe(t),null;case 1:return Pe(t.type)&&bi(),pe(t),null;case 3:return r=t.stateNode,Un(),K(Ee),K(me),Ea(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(gi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ge!==null&&(Vl(Ge),Ge=null))),xl(e,t),pe(t),null;case 5:ka(t);var i=Jt(Br.current);if(n=t.type,e!==null&&t.stateNode!=null)Np(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(k(166));return pe(t),null}if(e=Jt(nt.current),gi(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[et]=t,r[Ir]=o,e=(t.mode&1)!==0,n){case"dialog":H("cancel",r),H("close",r);break;case"iframe":case"object":case"embed":H("load",r);break;case"video":case"audio":for(i=0;i<ur.length;i++)H(ur[i],r);break;case"source":H("error",r);break;case"img":case"image":case"link":H("error",r),H("load",r);break;case"details":H("toggle",r);break;case"input":Tu(r,o),H("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},H("invalid",r);break;case"textarea":Vu(r,o),H("invalid",r)}Gs(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="children"?typeof l=="string"?r.textContent!==l&&(o.suppressHydrationWarning!==!0&&vi(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(o.suppressHydrationWarning!==!0&&vi(r.textContent,l,e),i=["children",""+l]):Vr.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&H("scroll",r)}switch(n){case"input":ui(r),_u(r,o,!0);break;case"textarea":ui(r),Au(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=qi)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ud(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[et]=t,e[Ir]=r,Mp(e,t,!1,!1),t.stateNode=e;e:{switch(s=Ys(n,r),n){case"dialog":H("cancel",e),H("close",e),i=r;break;case"iframe":case"object":case"embed":H("load",e),i=r;break;case"video":case"audio":for(i=0;i<ur.length;i++)H(ur[i],e);i=r;break;case"source":H("error",e),i=r;break;case"img":case"image":case"link":H("error",e),H("load",e),i=r;break;case"details":H("toggle",e),i=r;break;case"input":Tu(e,r),i=$s(e,r),H("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=X({},r,{value:void 0}),H("invalid",e);break;case"textarea":Vu(e,r),i=Ks(e,r),H("invalid",e);break;default:i=r}Gs(n,i),l=i;for(o in l)if(l.hasOwnProperty(o)){var a=l[o];o==="style"?dd(e,a):o==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&cd(e,a)):o==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Ar(e,a):typeof a=="number"&&Ar(e,""+a):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Vr.hasOwnProperty(o)?a!=null&&o==="onScroll"&&H("scroll",e):a!=null&&ea(e,o,a,s))}switch(n){case"input":ui(e),_u(e,r,!1);break;case"textarea":ui(e),Au(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Ot(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?An(e,!!r.multiple,o,!1):r.defaultValue!=null&&An(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=qi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return pe(t),null;case 6:if(e&&t.stateNode!=null)Op(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(k(166));if(n=Jt(Br.current),Jt(nt.current),gi(t)){if(r=t.stateNode,n=t.memoizedProps,r[et]=t,(o=r.nodeValue!==n)&&(e=De,e!==null))switch(e.tag){case 3:vi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&vi(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[et]=t,t.stateNode=r}return pe(t),null;case 13:if(K(G),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Q&&Re!==null&&t.mode&1&&!(t.flags&128))bd(),jn(),t.flags|=98560,o=!1;else if(o=gi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(k(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(k(317));o[et]=t}else jn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;pe(t),o=!1}else Ge!==null&&(Vl(Ge),Ge=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||G.current&1?ie===0&&(ie=3):Fa())),t.updateQueue!==null&&(t.flags|=4),pe(t),null);case 4:return Un(),xl(e,t),e===null&&Fr(t.stateNode.containerInfo),pe(t),null;case 10:return wa(t.type._context),pe(t),null;case 17:return Pe(t.type)&&bi(),pe(t),null;case 19:if(K(G),o=t.memoizedState,o===null)return pe(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)nr(o,!1);else{if(ie!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=so(e),s!==null){for(t.flags|=128,nr(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return $(G,G.current&1|2),t.child}e=e.sibling}o.tail!==null&&q()>Wn&&(t.flags|=128,r=!0,nr(o,!1),t.lanes=4194304)}else{if(!r)if(e=so(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),nr(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!Q)return pe(t),null}else 2*q()-o.renderingStartTime>Wn&&n!==1073741824&&(t.flags|=128,r=!0,nr(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=q(),t.sibling=null,n=G.current,$(G,r?n&1|2:n&1),t):(pe(t),null);case 22:case 23:return Oa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ae&1073741824&&(pe(t),t.subtreeFlags&6&&(t.flags|=8192)):pe(t),null;case 24:return null;case 25:return null}throw Error(k(156,t.tag))}function Iy(e,t){switch(ma(t),t.tag){case 1:return Pe(t.type)&&bi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Un(),K(Ee),K(me),Ea(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ka(t),null;case 13:if(K(G),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(k(340));jn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return K(G),null;case 4:return Un(),null;case 10:return wa(t.type._context),null;case 22:case 23:return Oa(),null;case 24:return null;default:return null}}var Si=!1,he=!1,jy=typeof WeakSet=="function"?WeakSet:Set,V=null;function En(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Z(e,t,r)}else n.current=null}function Sl(e,t,n){try{n()}catch(r){Z(e,t,r)}}var gc=!1;function By(e,t){if(il=Xi,e=Bd(),pa(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,c=0,f=e,d=null;t:for(;;){for(var y;f!==n||i!==0&&f.nodeType!==3||(l=s+i),f!==o||r!==0&&f.nodeType!==3||(a=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(y=f.firstChild)!==null;)d=f,f=y;for(;;){if(f===e)break t;if(d===n&&++u===i&&(l=s),d===o&&++c===r&&(a=s),(y=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=y}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(ol={focusedElem:e,selectionRange:n},Xi=!1,V=t;V!==null;)if(t=V,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,V=e;else for(;V!==null;){t=V;try{var g=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var v=g.memoizedProps,x=g.memoizedState,p=t.stateNode,h=p.getSnapshotBeforeUpdate(t.elementType===t.type?v:Ke(t.type,v),x);p.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(k(163))}}catch(w){Z(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,V=e;break}V=t.return}return g=gc,gc=!1,g}function gr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&Sl(t,n,o)}i=i.next}while(i!==r)}}function Oo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Cl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Fp(e){var t=e.alternate;t!==null&&(e.alternate=null,Fp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[et],delete t[Ir],delete t[al],delete t[Cy],delete t[ky])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function zp(e){return e.tag===5||e.tag===3||e.tag===4}function wc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||zp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function kl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=qi));else if(r!==4&&(e=e.child,e!==null))for(kl(e,t,n),e=e.sibling;e!==null;)kl(e,t,n),e=e.sibling}function El(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(El(e,t,n),e=e.sibling;e!==null;)El(e,t,n),e=e.sibling}var ae=null,Qe=!1;function vt(e,t,n){for(n=n.child;n!==null;)Ip(e,t,n),n=n.sibling}function Ip(e,t,n){if(tt&&typeof tt.onCommitFiberUnmount=="function")try{tt.onCommitFiberUnmount(_o,n)}catch{}switch(n.tag){case 5:he||En(n,t);case 6:var r=ae,i=Qe;ae=null,vt(e,t,n),ae=r,Qe=i,ae!==null&&(Qe?(e=ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ae.removeChild(n.stateNode));break;case 18:ae!==null&&(Qe?(e=ae,n=n.stateNode,e.nodeType===8?ps(e.parentNode,n):e.nodeType===1&&ps(e,n),Mr(e)):ps(ae,n.stateNode));break;case 4:r=ae,i=Qe,ae=n.stateNode.containerInfo,Qe=!0,vt(e,t,n),ae=r,Qe=i;break;case 0:case 11:case 14:case 15:if(!he&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&Sl(n,t,s),i=i.next}while(i!==r)}vt(e,t,n);break;case 1:if(!he&&(En(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Z(n,t,l)}vt(e,t,n);break;case 21:vt(e,t,n);break;case 22:n.mode&1?(he=(r=he)||n.memoizedState!==null,vt(e,t,n),he=r):vt(e,t,n);break;default:vt(e,t,n)}}function xc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new jy),t.forEach(function(r){var i=Xy.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function We(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:ae=l.stateNode,Qe=!1;break e;case 3:ae=l.stateNode.containerInfo,Qe=!0;break e;case 4:ae=l.stateNode.containerInfo,Qe=!0;break e}l=l.return}if(ae===null)throw Error(k(160));Ip(o,s,i),ae=null,Qe=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){Z(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)jp(t,e),t=t.sibling}function jp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(We(t,e),Je(e),r&4){try{gr(3,e,e.return),Oo(3,e)}catch(v){Z(e,e.return,v)}try{gr(5,e,e.return)}catch(v){Z(e,e.return,v)}}break;case 1:We(t,e),Je(e),r&512&&n!==null&&En(n,n.return);break;case 5:if(We(t,e),Je(e),r&512&&n!==null&&En(n,n.return),e.flags&32){var i=e.stateNode;try{Ar(i,"")}catch(v){Z(e,e.return,v)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&o.type==="radio"&&o.name!=null&&ld(i,o),Ys(l,s);var u=Ys(l,o);for(s=0;s<a.length;s+=2){var c=a[s],f=a[s+1];c==="style"?dd(i,f):c==="dangerouslySetInnerHTML"?cd(i,f):c==="children"?Ar(i,f):ea(i,c,f,u)}switch(l){case"input":Ws(i,o);break;case"textarea":ad(i,o);break;case"select":var d=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var y=o.value;y!=null?An(i,!!o.multiple,y,!1):d!==!!o.multiple&&(o.defaultValue!=null?An(i,!!o.multiple,o.defaultValue,!0):An(i,!!o.multiple,o.multiple?[]:"",!1))}i[Ir]=o}catch(v){Z(e,e.return,v)}}break;case 6:if(We(t,e),Je(e),r&4){if(e.stateNode===null)throw Error(k(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(v){Z(e,e.return,v)}}break;case 3:if(We(t,e),Je(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Mr(t.containerInfo)}catch(v){Z(e,e.return,v)}break;case 4:We(t,e),Je(e);break;case 13:We(t,e),Je(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(Ma=q())),r&4&&xc(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(he=(u=he)||c,We(t,e),he=u):We(t,e),Je(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(V=e,c=e.child;c!==null;){for(f=V=c;V!==null;){switch(d=V,y=d.child,d.tag){case 0:case 11:case 14:case 15:gr(4,d,d.return);break;case 1:En(d,d.return);var g=d.stateNode;if(typeof g.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(v){Z(r,n,v)}}break;case 5:En(d,d.return);break;case 22:if(d.memoizedState!==null){Cc(f);continue}}y!==null?(y.return=d,V=y):Cc(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{i=f.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(l=f.stateNode,a=f.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=fd("display",s))}catch(v){Z(e,e.return,v)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(v){Z(e,e.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:We(t,e),Je(e),r&4&&xc(e);break;case 21:break;default:We(t,e),Je(e)}}function Je(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(zp(n)){var r=n;break e}n=n.return}throw Error(k(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Ar(i,""),r.flags&=-33);var o=wc(e);El(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,l=wc(e);kl(e,l,s);break;default:throw Error(k(161))}}catch(a){Z(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Uy(e,t,n){V=e,Bp(e)}function Bp(e,t,n){for(var r=(e.mode&1)!==0;V!==null;){var i=V,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||Si;if(!s){var l=i.alternate,a=l!==null&&l.memoizedState!==null||he;l=Si;var u=he;if(Si=s,(he=a)&&!u)for(V=i;V!==null;)s=V,a=s.child,s.tag===22&&s.memoizedState!==null?kc(i):a!==null?(a.return=s,V=a):kc(i);for(;o!==null;)V=o,Bp(o),o=o.sibling;V=i,Si=l,he=u}Sc(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,V=o):Sc(e)}}function Sc(e){for(;V!==null;){var t=V;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:he||Oo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!he)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ke(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&oc(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}oc(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&Mr(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(k(163))}he||t.flags&512&&Cl(t)}catch(d){Z(t,t.return,d)}}if(t===e){V=null;break}if(n=t.sibling,n!==null){n.return=t.return,V=n;break}V=t.return}}function Cc(e){for(;V!==null;){var t=V;if(t===e){V=null;break}var n=t.sibling;if(n!==null){n.return=t.return,V=n;break}V=t.return}}function kc(e){for(;V!==null;){var t=V;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Oo(4,t)}catch(a){Z(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){Z(t,i,a)}}var o=t.return;try{Cl(t)}catch(a){Z(t,o,a)}break;case 5:var s=t.return;try{Cl(t)}catch(a){Z(t,s,a)}}}catch(a){Z(t,t.return,a)}if(t===e){V=null;break}var l=t.sibling;if(l!==null){l.return=t.return,V=l;break}V=t.return}}var $y=Math.ceil,uo=yt.ReactCurrentDispatcher,Ra=yt.ReactCurrentOwner,Be=yt.ReactCurrentBatchConfig,z=0,le=null,te=null,ce=0,Ae=0,Pn=Bt(0),ie=0,Hr=null,on=0,Fo=0,Da=0,wr=null,Ce=null,Ma=0,Wn=1/0,st=null,co=!1,Pl=null,Dt=null,Ci=!1,Pt=null,fo=0,xr=0,Tl=null,Ii=-1,ji=0;function we(){return z&6?q():Ii!==-1?Ii:Ii=q()}function Mt(e){return e.mode&1?z&2&&ce!==0?ce&-ce:Py.transition!==null?(ji===0&&(ji=Ed()),ji):(e=B,e!==0||(e=window.event,e=e===void 0?16:Rd(e.type)),e):1}function Xe(e,t,n,r){if(50<xr)throw xr=0,Tl=null,Error(k(185));Jr(e,n,r),(!(z&2)||e!==le)&&(e===le&&(!(z&2)&&(Fo|=n),ie===4&&kt(e,ce)),Te(e,r),n===1&&z===0&&!(t.mode&1)&&(Wn=q()+500,Do&&Ut()))}function Te(e,t){var n=e.callbackNode;P0(e,t);var r=Yi(e,e===le?ce:0);if(r===0)n!==null&&Du(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Du(n),t===1)e.tag===0?Ey(Ec.bind(null,e)):Zd(Ec.bind(null,e)),xy(function(){!(z&6)&&Ut()}),n=null;else{switch(Pd(r)){case 1:n=oa;break;case 4:n=Cd;break;case 16:n=Gi;break;case 536870912:n=kd;break;default:n=Gi}n=Yp(n,Up.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Up(e,t){if(Ii=-1,ji=0,z&6)throw Error(k(327));var n=e.callbackNode;if(Nn()&&e.callbackNode!==n)return null;var r=Yi(e,e===le?ce:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=po(e,r);else{t=r;var i=z;z|=2;var o=Wp();(le!==e||ce!==t)&&(st=null,Wn=q()+500,bt(e,t));do try{Ky();break}catch(l){$p(e,l)}while(!0);ga(),uo.current=o,z=i,te!==null?t=0:(le=null,ce=0,t=ie)}if(t!==0){if(t===2&&(i=bs(e),i!==0&&(r=i,t=_l(e,i))),t===1)throw n=Hr,bt(e,0),kt(e,r),Te(e,q()),n;if(t===6)kt(e,r);else{if(i=e.current.alternate,!(r&30)&&!Wy(i)&&(t=po(e,r),t===2&&(o=bs(e),o!==0&&(r=o,t=_l(e,o))),t===1))throw n=Hr,bt(e,0),kt(e,r),Te(e,q()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(k(345));case 2:Gt(e,Ce,st);break;case 3:if(kt(e,r),(r&130023424)===r&&(t=Ma+500-q(),10<t)){if(Yi(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){we(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=ll(Gt.bind(null,e,Ce,st),t);break}Gt(e,Ce,st);break;case 4:if(kt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Ye(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=q()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*$y(r/1960))-r,10<r){e.timeoutHandle=ll(Gt.bind(null,e,Ce,st),r);break}Gt(e,Ce,st);break;case 5:Gt(e,Ce,st);break;default:throw Error(k(329))}}}return Te(e,q()),e.callbackNode===n?Up.bind(null,e):null}function _l(e,t){var n=wr;return e.current.memoizedState.isDehydrated&&(bt(e,t).flags|=256),e=po(e,t),e!==2&&(t=Ce,Ce=n,t!==null&&Vl(t)),e}function Vl(e){Ce===null?Ce=e:Ce.push.apply(Ce,e)}function Wy(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!Ze(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function kt(e,t){for(t&=~Da,t&=~Fo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ye(t),r=1<<n;e[n]=-1,t&=~r}}function Ec(e){if(z&6)throw Error(k(327));Nn();var t=Yi(e,0);if(!(t&1))return Te(e,q()),null;var n=po(e,t);if(e.tag!==0&&n===2){var r=bs(e);r!==0&&(t=r,n=_l(e,r))}if(n===1)throw n=Hr,bt(e,0),kt(e,t),Te(e,q()),n;if(n===6)throw Error(k(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Gt(e,Ce,st),Te(e,q()),null}function Na(e,t){var n=z;z|=1;try{return e(t)}finally{z=n,z===0&&(Wn=q()+500,Do&&Ut())}}function sn(e){Pt!==null&&Pt.tag===0&&!(z&6)&&Nn();var t=z;z|=1;var n=Be.transition,r=B;try{if(Be.transition=null,B=1,e)return e()}finally{B=r,Be.transition=n,z=t,!(z&6)&&Ut()}}function Oa(){Ae=Pn.current,K(Pn)}function bt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,wy(n)),te!==null)for(n=te.return;n!==null;){var r=n;switch(ma(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&bi();break;case 3:Un(),K(Ee),K(me),Ea();break;case 5:ka(r);break;case 4:Un();break;case 13:K(G);break;case 19:K(G);break;case 10:wa(r.type._context);break;case 22:case 23:Oa()}n=n.return}if(le=e,te=e=Nt(e.current,null),ce=Ae=t,ie=0,Hr=null,Da=Fo=on=0,Ce=wr=null,Zt!==null){for(t=0;t<Zt.length;t++)if(n=Zt[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}Zt=null}return e}function $p(e,t){do{var n=te;try{if(ga(),Oi.current=ao,lo){for(var r=Y.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}lo=!1}if(rn=0,se=re=Y=null,vr=!1,Ur=0,Ra.current=null,n===null||n.return===null){ie=1,Hr=t,te=null;break}e:{var o=e,s=n.return,l=n,a=t;if(t=ce,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var y=fc(s);if(y!==null){y.flags&=-257,dc(y,s,l,o,t),y.mode&1&&cc(o,u,t),t=y,a=u;var g=t.updateQueue;if(g===null){var v=new Set;v.add(a),t.updateQueue=v}else g.add(a);break e}else{if(!(t&1)){cc(o,u,t),Fa();break e}a=Error(k(426))}}else if(Q&&l.mode&1){var x=fc(s);if(x!==null){!(x.flags&65536)&&(x.flags|=256),dc(x,s,l,o,t),ya($n(a,l));break e}}o=a=$n(a,l),ie!==4&&(ie=2),wr===null?wr=[o]:wr.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var p=Pp(o,a,t);ic(o,p);break e;case 1:l=a;var h=o.type,m=o.stateNode;if(!(o.flags&128)&&(typeof h.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(Dt===null||!Dt.has(m)))){o.flags|=65536,t&=-t,o.lanes|=t;var w=Tp(o,l,t);ic(o,w);break e}}o=o.return}while(o!==null)}Kp(n)}catch(C){t=C,te===n&&n!==null&&(te=n=n.return);continue}break}while(!0)}function Wp(){var e=uo.current;return uo.current=ao,e===null?ao:e}function Fa(){(ie===0||ie===3||ie===2)&&(ie=4),le===null||!(on&268435455)&&!(Fo&268435455)||kt(le,ce)}function po(e,t){var n=z;z|=2;var r=Wp();(le!==e||ce!==t)&&(st=null,bt(e,t));do try{Hy();break}catch(i){$p(e,i)}while(!0);if(ga(),z=n,uo.current=r,te!==null)throw Error(k(261));return le=null,ce=0,ie}function Hy(){for(;te!==null;)Hp(te)}function Ky(){for(;te!==null&&!y0();)Hp(te)}function Hp(e){var t=Gp(e.alternate,e,Ae);e.memoizedProps=e.pendingProps,t===null?Kp(e):te=t,Ra.current=null}function Kp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Iy(n,t),n!==null){n.flags&=32767,te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ie=6,te=null;return}}else if(n=zy(n,t,Ae),n!==null){te=n;return}if(t=t.sibling,t!==null){te=t;return}te=t=e}while(t!==null);ie===0&&(ie=5)}function Gt(e,t,n){var r=B,i=Be.transition;try{Be.transition=null,B=1,Qy(e,t,n,r)}finally{Be.transition=i,B=r}return null}function Qy(e,t,n,r){do Nn();while(Pt!==null);if(z&6)throw Error(k(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(k(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(T0(e,o),e===le&&(te=le=null,ce=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ci||(Ci=!0,Yp(Gi,function(){return Nn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Be.transition,Be.transition=null;var s=B;B=1;var l=z;z|=4,Ra.current=null,By(e,n),jp(n,e),dy(ol),Xi=!!il,ol=il=null,e.current=n,Uy(n),v0(),z=l,B=s,Be.transition=o}else e.current=n;if(Ci&&(Ci=!1,Pt=e,fo=i),o=e.pendingLanes,o===0&&(Dt=null),x0(n.stateNode),Te(e,q()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(co)throw co=!1,e=Pl,Pl=null,e;return fo&1&&e.tag!==0&&Nn(),o=e.pendingLanes,o&1?e===Tl?xr++:(xr=0,Tl=e):xr=0,Ut(),null}function Nn(){if(Pt!==null){var e=Pd(fo),t=Be.transition,n=B;try{if(Be.transition=null,B=16>e?16:e,Pt===null)var r=!1;else{if(e=Pt,Pt=null,fo=0,z&6)throw Error(k(331));var i=z;for(z|=4,V=e.current;V!==null;){var o=V,s=o.child;if(V.flags&16){var l=o.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(V=u;V!==null;){var c=V;switch(c.tag){case 0:case 11:case 15:gr(8,c,o)}var f=c.child;if(f!==null)f.return=c,V=f;else for(;V!==null;){c=V;var d=c.sibling,y=c.return;if(Fp(c),c===u){V=null;break}if(d!==null){d.return=y,V=d;break}V=y}}}var g=o.alternate;if(g!==null){var v=g.child;if(v!==null){g.child=null;do{var x=v.sibling;v.sibling=null,v=x}while(v!==null)}}V=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,V=s;else e:for(;V!==null;){if(o=V,o.flags&2048)switch(o.tag){case 0:case 11:case 15:gr(9,o,o.return)}var p=o.sibling;if(p!==null){p.return=o.return,V=p;break e}V=o.return}}var h=e.current;for(V=h;V!==null;){s=V;var m=s.child;if(s.subtreeFlags&2064&&m!==null)m.return=s,V=m;else e:for(s=h;V!==null;){if(l=V,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Oo(9,l)}}catch(C){Z(l,l.return,C)}if(l===s){V=null;break e}var w=l.sibling;if(w!==null){w.return=l.return,V=w;break e}V=l.return}}if(z=i,Ut(),tt&&typeof tt.onPostCommitFiberRoot=="function")try{tt.onPostCommitFiberRoot(_o,e)}catch{}r=!0}return r}finally{B=n,Be.transition=t}}return!1}function Pc(e,t,n){t=$n(n,t),t=Pp(e,t,1),e=Rt(e,t,1),t=we(),e!==null&&(Jr(e,1,t),Te(e,t))}function Z(e,t,n){if(e.tag===3)Pc(e,e,n);else for(;t!==null;){if(t.tag===3){Pc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Dt===null||!Dt.has(r))){e=$n(n,e),e=Tp(t,e,1),t=Rt(t,e,1),e=we(),t!==null&&(Jr(t,1,e),Te(t,e));break}}t=t.return}}function Gy(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=we(),e.pingedLanes|=e.suspendedLanes&n,le===e&&(ce&n)===n&&(ie===4||ie===3&&(ce&130023424)===ce&&500>q()-Ma?bt(e,0):Da|=n),Te(e,t)}function Qp(e,t){t===0&&(e.mode&1?(t=di,di<<=1,!(di&130023424)&&(di=4194304)):t=1);var n=we();e=ht(e,t),e!==null&&(Jr(e,t,n),Te(e,n))}function Yy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Qp(e,n)}function Xy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(k(314))}r!==null&&r.delete(t),Qp(e,n)}var Gp;Gp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ee.current)ke=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ke=!1,Fy(e,t,n);ke=!!(e.flags&131072)}else ke=!1,Q&&t.flags&1048576&&Jd(t,no,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;zi(e,t),e=t.pendingProps;var i=In(t,me.current);Mn(t,n),i=Ta(null,t,r,e,i,n);var o=_a();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Pe(r)?(o=!0,eo(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Sa(t),i.updater=No,t.stateNode=i,i._reactInternals=t,hl(t,r,e,n),t=vl(null,t,r,!0,o,n)):(t.tag=0,Q&&o&&ha(t),ge(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(zi(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=Jy(r),e=Ke(r,e),i){case 0:t=yl(null,t,r,e,n);break e;case 1:t=mc(null,t,r,e,n);break e;case 11:t=pc(null,t,r,e,n);break e;case 14:t=hc(null,t,r,Ke(r.type,e),n);break e}throw Error(k(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),yl(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),mc(e,t,r,i,n);case 3:e:{if(Lp(t),e===null)throw Error(k(387));r=t.pendingProps,o=t.memoizedState,i=o.element,rp(e,t),oo(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=$n(Error(k(423)),t),t=yc(e,t,r,n,i);break e}else if(r!==i){i=$n(Error(k(424)),t),t=yc(e,t,r,n,i);break e}else for(Re=Lt(t.stateNode.containerInfo.firstChild),De=t,Q=!0,Ge=null,n=tp(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(jn(),r===i){t=mt(e,t,n);break e}ge(e,t,r,n)}t=t.child}return t;case 5:return ip(t),e===null&&fl(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,sl(r,i)?s=null:o!==null&&sl(r,o)&&(t.flags|=32),Ap(e,t),ge(e,t,s,n),t.child;case 6:return e===null&&fl(t),null;case 13:return Rp(e,t,n);case 4:return Ca(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Bn(t,null,r,n):ge(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),pc(e,t,r,i,n);case 7:return ge(e,t,t.pendingProps,n),t.child;case 8:return ge(e,t,t.pendingProps.children,n),t.child;case 12:return ge(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,$(ro,r._currentValue),r._currentValue=s,o!==null)if(Ze(o.value,s)){if(o.children===i.children&&!Ee.current){t=mt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var l=o.dependencies;if(l!==null){s=o.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(o.tag===1){a=ct(-1,n&-n),a.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),dl(o.return,n,t),l.lanes|=n;break}a=a.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(k(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),dl(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}ge(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Mn(t,n),i=Ue(i),r=r(i),t.flags|=1,ge(e,t,r,n),t.child;case 14:return r=t.type,i=Ke(r,t.pendingProps),i=Ke(r.type,i),hc(e,t,r,i,n);case 15:return _p(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),zi(e,t),t.tag=1,Pe(r)?(e=!0,eo(t)):e=!1,Mn(t,n),Ep(t,r,i),hl(t,r,i,n),vl(null,t,r,!0,e,n);case 19:return Dp(e,t,n);case 22:return Vp(e,t,n)}throw Error(k(156,t.tag))};function Yp(e,t){return Sd(e,t)}function Zy(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function je(e,t,n,r){return new Zy(e,t,n,r)}function za(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Jy(e){if(typeof e=="function")return za(e)?1:0;if(e!=null){if(e=e.$$typeof,e===na)return 11;if(e===ra)return 14}return 2}function Nt(e,t){var n=e.alternate;return n===null?(n=je(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Bi(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")za(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case mn:return en(n.children,i,o,t);case ta:s=8,i|=8;break;case Is:return e=je(12,n,t,i|2),e.elementType=Is,e.lanes=o,e;case js:return e=je(13,n,t,i),e.elementType=js,e.lanes=o,e;case Bs:return e=je(19,n,t,i),e.elementType=Bs,e.lanes=o,e;case id:return zo(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case nd:s=10;break e;case rd:s=9;break e;case na:s=11;break e;case ra:s=14;break e;case wt:s=16,r=null;break e}throw Error(k(130,e==null?e:typeof e,""))}return t=je(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function en(e,t,n,r){return e=je(7,e,r,t),e.lanes=n,e}function zo(e,t,n,r){return e=je(22,e,r,t),e.elementType=id,e.lanes=n,e.stateNode={isHidden:!1},e}function Ss(e,t,n){return e=je(6,e,null,t),e.lanes=n,e}function Cs(e,t,n){return t=je(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function qy(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ns(0),this.expirationTimes=ns(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ns(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Ia(e,t,n,r,i,o,s,l,a){return e=new qy(e,t,n,l,a),t===1?(t=1,o===!0&&(t|=8)):t=0,o=je(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Sa(o),e}function by(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:hn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Xp(e){if(!e)return Ft;e=e._reactInternals;e:{if(an(e)!==e||e.tag!==1)throw Error(k(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Pe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(k(171))}if(e.tag===1){var n=e.type;if(Pe(n))return Xd(e,n,t)}return t}function Zp(e,t,n,r,i,o,s,l,a){return e=Ia(n,r,!0,e,i,o,s,l,a),e.context=Xp(null),n=e.current,r=we(),i=Mt(n),o=ct(r,i),o.callback=t!=null?t:null,Rt(n,o,i),e.current.lanes=i,Jr(e,i,r),Te(e,r),e}function Io(e,t,n,r){var i=t.current,o=we(),s=Mt(i);return n=Xp(n),t.context===null?t.context=n:t.pendingContext=n,t=ct(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Rt(i,t,s),e!==null&&(Xe(e,i,s,o),Ni(e,i,s)),s}function ho(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Tc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ja(e,t){Tc(e,t),(e=e.alternate)&&Tc(e,t)}function ev(){return null}var Jp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ba(e){this._internalRoot=e}jo.prototype.render=Ba.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(k(409));Io(e,t,null,null)};jo.prototype.unmount=Ba.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;sn(function(){Io(null,e,null,null)}),t[pt]=null}};function jo(e){this._internalRoot=e}jo.prototype.unstable_scheduleHydration=function(e){if(e){var t=Vd();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ct.length&&t!==0&&t<Ct[n].priority;n++);Ct.splice(n,0,e),n===0&&Ld(e)}};function Ua(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Bo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function _c(){}function tv(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=ho(s);o.call(u)}}var s=Zp(t,r,e,0,null,!1,!1,"",_c);return e._reactRootContainer=s,e[pt]=s.current,Fr(e.nodeType===8?e.parentNode:e),sn(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=ho(a);l.call(u)}}var a=Ia(e,0,!1,null,null,!1,!1,"",_c);return e._reactRootContainer=a,e[pt]=a.current,Fr(e.nodeType===8?e.parentNode:e),sn(function(){Io(t,a,n,r)}),a}function Uo(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var l=i;i=function(){var a=ho(s);l.call(a)}}Io(t,s,e,i)}else s=tv(n,t,e,i,r);return ho(s)}Td=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ar(t.pendingLanes);n!==0&&(sa(t,n|1),Te(t,q()),!(z&6)&&(Wn=q()+500,Ut()))}break;case 13:sn(function(){var r=ht(e,1);if(r!==null){var i=we();Xe(r,e,1,i)}}),ja(e,1)}};la=function(e){if(e.tag===13){var t=ht(e,134217728);if(t!==null){var n=we();Xe(t,e,134217728,n)}ja(e,134217728)}};_d=function(e){if(e.tag===13){var t=Mt(e),n=ht(e,t);if(n!==null){var r=we();Xe(n,e,t,r)}ja(e,t)}};Vd=function(){return B};Ad=function(e,t){var n=B;try{return B=e,t()}finally{B=n}};Zs=function(e,t,n){switch(t){case"input":if(Ws(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Ro(r);if(!i)throw Error(k(90));sd(r),Ws(r,i)}}}break;case"textarea":ad(e,n);break;case"select":t=n.value,t!=null&&An(e,!!n.multiple,t,!1)}};md=Na;yd=sn;var nv={usingClientEntryPoint:!1,Events:[br,wn,Ro,pd,hd,Na]},rr={findFiberByHostInstance:Xt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},rv={bundleType:rr.bundleType,version:rr.version,rendererPackageName:rr.rendererPackageName,rendererConfig:rr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:yt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=wd(e),e===null?null:e.stateNode},findFiberByHostInstance:rr.findFiberByHostInstance||ev,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ki=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ki.isDisabled&&ki.supportsFiber)try{_o=ki.inject(rv),tt=ki}catch{}}Oe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=nv;Oe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ua(t))throw Error(k(200));return by(e,t,null,n)};Oe.createRoot=function(e,t){if(!Ua(e))throw Error(k(299));var n=!1,r="",i=Jp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Ia(e,1,!1,null,null,n,!1,r,i),e[pt]=t.current,Fr(e.nodeType===8?e.parentNode:e),new Ba(t)};Oe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(k(188)):(e=Object.keys(e).join(","),Error(k(268,e)));return e=wd(t),e=e===null?null:e.stateNode,e};Oe.flushSync=function(e){return sn(e)};Oe.hydrate=function(e,t,n){if(!Bo(t))throw Error(k(200));return Uo(null,e,t,!0,n)};Oe.hydrateRoot=function(e,t,n){if(!Ua(e))throw Error(k(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=Jp;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Zp(t,null,e,1,n!=null?n:null,i,!1,o,s),e[pt]=t.current,Fr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new jo(t)};Oe.render=function(e,t,n){if(!Bo(t))throw Error(k(200));return Uo(null,e,t,!1,n)};Oe.unmountComponentAtNode=function(e){if(!Bo(e))throw Error(k(40));return e._reactRootContainer?(sn(function(){Uo(null,null,e,!1,function(){e._reactRootContainer=null,e[pt]=null})}),!0):!1};Oe.unstable_batchedUpdates=Na;Oe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Bo(n))throw Error(k(200));if(e==null||e._reactInternals===void 0)throw Error(k(38));return Uo(e,t,n,!1,r)};Oe.version="18.3.1-next-f1338f8080-20240426";function qp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(qp)}catch(e){console.error(e)}}qp(),qf.exports=Oe;var bp=qf.exports;const iv=Bf(bp);var eh,Vc=iv;eh=Vc.createRoot,Vc.hydrateRoot;const th="3.7.7",ov=th,Xn=typeof Buffer=="function",Ac=typeof TextDecoder=="function"?new TextDecoder:void 0,Lc=typeof TextEncoder=="function"?new TextEncoder:void 0,sv="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",cr=Array.prototype.slice.call(sv),Ei=(e=>{let t={};return e.forEach((n,r)=>t[n]=r),t})(cr),lv=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,ue=String.fromCharCode.bind(String),Rc=typeof Uint8Array.from=="function"?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),nh=e=>e.replace(/=/g,"").replace(/[+\/]/g,t=>t=="+"?"-":"_"),rh=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),ih=e=>{let t,n,r,i,o="";const s=e.length%3;for(let l=0;l<e.length;){if((n=e.charCodeAt(l++))>255||(r=e.charCodeAt(l++))>255||(i=e.charCodeAt(l++))>255)throw new TypeError("invalid character found");t=n<<16|r<<8|i,o+=cr[t>>18&63]+cr[t>>12&63]+cr[t>>6&63]+cr[t&63]}return s?o.slice(0,s-3)+"===".substring(s):o},$a=typeof btoa=="function"?e=>btoa(e):Xn?e=>Buffer.from(e,"binary").toString("base64"):ih,Al=Xn?e=>Buffer.from(e).toString("base64"):e=>{let n=[];for(let r=0,i=e.length;r<i;r+=4096)n.push(ue.apply(null,e.subarray(r,r+4096)));return $a(n.join(""))},Ui=(e,t=!1)=>t?nh(Al(e)):Al(e),av=e=>{if(e.length<2){var t=e.charCodeAt(0);return t<128?e:t<2048?ue(192|t>>>6)+ue(128|t&63):ue(224|t>>>12&15)+ue(128|t>>>6&63)+ue(128|t&63)}else{var t=65536+(e.charCodeAt(0)-55296)*1024+(e.charCodeAt(1)-56320);return ue(240|t>>>18&7)+ue(128|t>>>12&63)+ue(128|t>>>6&63)+ue(128|t&63)}},uv=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,oh=e=>e.replace(uv,av),Dc=Xn?e=>Buffer.from(e,"utf8").toString("base64"):Lc?e=>Al(Lc.encode(e)):e=>$a(oh(e)),On=(e,t=!1)=>t?nh(Dc(e)):Dc(e),Mc=e=>On(e,!0),cv=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,fv=e=>{switch(e.length){case 4:var t=(7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3),n=t-65536;return ue((n>>>10)+55296)+ue((n&1023)+56320);case 3:return ue((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return ue((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},sh=e=>e.replace(cv,fv),lh=e=>{if(e=e.replace(/\s+/g,""),!lv.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(e.length&3));let t,n="",r,i;for(let o=0;o<e.length;)t=Ei[e.charAt(o++)]<<18|Ei[e.charAt(o++)]<<12|(r=Ei[e.charAt(o++)])<<6|(i=Ei[e.charAt(o++)]),n+=r===64?ue(t>>16&255):i===64?ue(t>>16&255,t>>8&255):ue(t>>16&255,t>>8&255,t&255);return n},Wa=typeof atob=="function"?e=>atob(rh(e)):Xn?e=>Buffer.from(e,"base64").toString("binary"):lh,ah=Xn?e=>Rc(Buffer.from(e,"base64")):e=>Rc(Wa(e).split("").map(t=>t.charCodeAt(0))),uh=e=>ah(ch(e)),dv=Xn?e=>Buffer.from(e,"base64").toString("utf8"):Ac?e=>Ac.decode(ah(e)):e=>sh(Wa(e)),ch=e=>rh(e.replace(/[-_]/g,t=>t=="-"?"+":"/")),Ll=e=>dv(ch(e)),pv=e=>{if(typeof e!="string")return!1;const t=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},fh=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),dh=function(){const e=(t,n)=>Object.defineProperty(String.prototype,t,fh(n));e("fromBase64",function(){return Ll(this)}),e("toBase64",function(t){return On(this,t)}),e("toBase64URI",function(){return On(this,!0)}),e("toBase64URL",function(){return On(this,!0)}),e("toUint8Array",function(){return uh(this)})},ph=function(){const e=(t,n)=>Object.defineProperty(Uint8Array.prototype,t,fh(n));e("toBase64",function(t){return Ui(this,t)}),e("toBase64URI",function(){return Ui(this,!0)}),e("toBase64URL",function(){return Ui(this,!0)})},hv=()=>{dh(),ph()},mv={version:th,VERSION:ov,atob:Wa,atobPolyfill:lh,btoa:$a,btoaPolyfill:ih,fromBase64:Ll,toBase64:On,encode:On,encodeURI:Mc,encodeURL:Mc,utob:oh,btou:sh,decode:Ll,isValid:pv,fromUint8Array:Ui,toUint8Array:uh,extendString:dh,extendUint8Array:ph,extendBuiltins:hv};function yv(e){if(typeof e=="object")return e;if(typeof e!="string")return null;try{return e.startsWith("{")?JSON.parse(e):vv(e)}catch(t){return console.log("parse failed:"+t.message),null}}function vv(e){var t=new window.DOMParser().parseFromString(e,"text/xml"),n=t.getElementsByTagName("componentData");if(!n.length){var r,i=(r=e.match(/<templateData>(.*)<\/templateData>/))===null||r===void 0?void 0:r[1];return JSON.parse(mv.decode(i))}var o={};for(var s of n)for(var l of s.getElementsByTagName("data")||[])if(l.getAttribute("value")!=null){o[s.getAttribute("id")]=l.getAttribute("value");break}return o}var Ha=Le.createContext(),gv=e=>{var{children:t,name:n}=e,[r,i]=S.useState(Ve.loading),[o,s]=S.useState({}),[l,a]=S.useState([]),[u,c]=S.useState(),[f,d]=S.useState(!1),y=x=>{console.log("".concat(n||"").concat(x))},g=S.useCallback(x=>(a(p=>[...p,x]),()=>{a(p=>p.filter(h=>h!==x))}),[]);S.useLayoutEffect(()=>{var x=!1;return window.load=()=>{i(Ve.loaded),y(".load()")},window.play=()=>{x=!0,d(!0),y(".play()")},window.pause=()=>{i(Ve.paused),y(".pause()")},window.stop=()=>{x?(i(Ve.stopped),y(".stop()")):(i(Ve.removed),y(".stop() without play"))},window.update=p=>{var h=yv(p);if(h&&(y(".update(".concat(h?JSON.stringify(h||{},null,2):"null",")")),s(h),!x)){var m=g("__initialData");c(()=>m)}},()=>{delete window.load,delete window.play,delete window.pause,delete window.stop,delete window.update}},[]),S.useEffect(()=>{u==null||u()},[u]),S.useEffect(()=>{r<Ve.playing&&f&&!l.length&&i(Ve.playing)},[r,o,f,l]),S.useEffect(()=>{if(r===Ve.removed){var x,p;y(".remove()"),(x=(p=window).remove)===null||x===void 0||x.call(p)}},[r]);var v=S.useCallback(()=>{i(Ve.removed)},[]);return Le.createElement(Ha.Provider,{value:{data:o,state:r,name:n,safeToRemove:v,delayPlay:g}},r!==Ve.removed?Le.createElement(wv,null,t):null)},wv=S.memo(e=>{var{children:t}=e;return t}),ks=null,xv=(e,t)=>{var{container:n=document.getElementById("root"),cssReset:r=!0,name:i=e.name}={};if(n||(n=document.createElement("div"),n.id="root",document.body.appendChild(n)),r){var o=` 
      width: 100vw;
      height: 100vh;
      overflow: hidden;
      margin: 0;
    `;document.body.style.cssText=o,n.style.cssText=o}ks||(ks=eh(n)),bp.flushSync(()=>{ks.render(S.createElement(gv,{name:i},S.createElement(e)))})};function Nc(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function Oc(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Nc(Object(n),!0).forEach(function(r){kv(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Nc(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function Sv(e,t){if(typeof e!="object"||!e)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Cv(e){var t=Sv(e,"string");return typeof t=="symbol"?t:String(t)}function kv(e,t,n){return t=Cv(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ev(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,o;for(o=0;o<r.length;o++)i=r[o],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function Pv(e,t){if(e==null)return{};var n=Ev(e,t),r,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)r=o[i],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}var Tv=(e,t)=>{var n=Le.useRef(null),r=Le.useRef(e);return Le.useEffect(()=>{r.current=e},[e]),Le.useEffect(()=>{var i=()=>r.current();if(typeof t=="number")return n.current=window.setTimeout(i,t),()=>window.clearTimeout(n.current)},[t]),n},_v=["state","safeToRemove"],Vv=e=>{var t=Le.useContext(Ha),{state:n,safeToRemove:r}=t,i=Pv(t,_v),o=hh();return Tv(r,n===Ve.stopped&&Number.isFinite(void 0)?e.removeDelay*1e3:null),Oc(Oc({},i),{},{data:o,state:n,safeToRemove:r,isPlaying:n===Ve.playing,isStopped:n===Ve.stopped})},hh=e=>{var{data:t}=Le.useContext(Ha),{trim:n=!0}={};return S.useMemo(()=>{if(!n)return t;var r={};for(var[i,o]of Object.entries(t))r[i]=typeof o=="string"?o.trim():o;return r},[t,n])};const Ka=S.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),$o=S.createContext({});function Av(){return S.useContext($o).visualElement}const ti=S.createContext(null),un=typeof document<"u",Sr=un?S.useLayoutEffect:S.useEffect,mh=S.createContext({strict:!1});function Lv(e,t,n,r){const i=Av(),o=S.useContext(mh),s=S.useContext(ti),l=S.useContext(Ka).reducedMotion,a=S.useRef();r=r||o.renderer,!a.current&&r&&(a.current=r(e,{visualState:t,parent:i,props:n,presenceId:s?s.id:void 0,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:l}));const u=a.current;return Sr(()=>{u&&u.render()}),Sr(()=>{u&&u.animationState&&u.animationState.animateChanges()}),Sr(()=>()=>u&&u.notify("Unmount"),[]),u}function Tn(e){return typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function Rv(e,t,n){return S.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Tn(n)&&(n.current=r))},[t])}function Kr(e){return typeof e=="string"||Array.isArray(e)}function Wo(e){return typeof e=="object"&&typeof e.start=="function"}const Dv=["initial","animate","exit","whileHover","whileDrag","whileTap","whileFocus","whileInView"];function Ho(e){return Wo(e.animate)||Dv.some(t=>Kr(e[t]))}function yh(e){return!!(Ho(e)||e.variants)}function Mv(e,t){if(Ho(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Kr(n)?n:void 0,animate:Kr(r)?r:void 0}}return e.inherit!==!1?t:{}}function Nv(e){const{initial:t,animate:n}=Mv(e,S.useContext($o));return S.useMemo(()=>({initial:t,animate:n}),[Fc(t),Fc(n)])}function Fc(e){return Array.isArray(e)?e.join(" "):e}const ot=e=>({isEnabled:t=>e.some(n=>!!t[n])}),Qr={measureLayout:ot(["layout","layoutId","drag"]),animation:ot(["animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView"]),exit:ot(["exit"]),drag:ot(["drag","dragControls"]),focus:ot(["whileFocus"]),hover:ot(["whileHover","onHoverStart","onHoverEnd"]),tap:ot(["whileTap","onTap","onTapStart","onTapCancel"]),pan:ot(["onPan","onPanStart","onPanSessionStart","onPanEnd"]),inView:ot(["whileInView","onViewportEnter","onViewportLeave"])};function Ov(e){for(const t in e)t==="projectionNodeConstructor"?Qr.projectionNodeConstructor=e[t]:Qr[t].Component=e[t]}function Ko(e){const t=S.useRef(null);return t.current===null&&(t.current=e()),t.current}const Cr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};let Fv=1;function zv(){return Ko(()=>{if(Cr.hasEverUpdated)return Fv++})}const Qa=S.createContext({});class Iv extends Le.Component{getSnapshotBeforeUpdate(){const{visualElement:t,props:n}=this.props;return t&&t.setProps(n),null}componentDidUpdate(){}render(){return this.props.children}}const vh=S.createContext({}),jv=Symbol.for("motionComponentSymbol");function Bv({preloadedFeatures:e,createVisualElement:t,projectionNodeConstructor:n,useRender:r,useVisualState:i,Component:o}){e&&Ov(e);function s(a,u){const c={...S.useContext(Ka),...a,layoutId:Uv(a)},{isStatic:f}=c;let d=null;const y=Nv(a),g=f?void 0:zv(),v=i(a,f);if(!f&&un){y.visualElement=Lv(o,v,c,t);const x=S.useContext(mh).strict,p=S.useContext(vh);y.visualElement&&(d=y.visualElement.loadFeatures(c,x,e,g,n||Qr.projectionNodeConstructor,p))}return S.createElement(Iv,{visualElement:y.visualElement,props:c},d,S.createElement($o.Provider,{value:y},r(o,a,g,Rv(v,y.visualElement,u),v,f,y.visualElement)))}const l=S.forwardRef(s);return l[jv]=o,l}function Uv({layoutId:e}){const t=S.useContext(Qa).id;return t&&e!==void 0?t+"-"+e:e}function $v(e){function t(r,i={}){return Bv(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const Wv=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Ga(e){return typeof e!="string"||e.includes("-")?!1:!!(Wv.indexOf(e)>-1||/[A-Z]/.test(e))}const mo={};function Hv(e){Object.assign(mo,e)}const yo=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],cn=new Set(yo);function gh(e,{layout:t,layoutId:n}){return cn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!mo[e]||e==="opacity")}const it=e=>!!(e!=null&&e.getVelocity),Kv={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Qv=(e,t)=>yo.indexOf(e)-yo.indexOf(t);function Gv({transform:e,transformKeys:t},{enableHardwareAcceleration:n=!0,allowTransformNone:r=!0},i,o){let s="";t.sort(Qv);for(const l of t)s+=`${Kv[l]||l}(${e[l]}) `;return n&&!e.z&&(s+="translateZ(0)"),s=s.trim(),o?s=o(e,i?"":s):r&&i&&(s="none"),s}function wh(e){return e.startsWith("--")}const Yv=(e,t)=>t&&typeof e=="number"?t.transform(e):e,Hn=(e,t,n)=>Math.min(Math.max(n,e),t),fn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},kr={...fn,transform:e=>Hn(0,1,e)},Pi={...fn,default:1},Er=e=>Math.round(e*1e5)/1e5,Gr=/(-)?([\d]*\.?[\d])+/g,Rl=/(#[0-9a-f]{6}|#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Xv=/^(#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function ni(e){return typeof e=="string"}const ri=e=>({test:t=>ni(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),gt=ri("deg"),rt=ri("%"),L=ri("px"),Zv=ri("vh"),Jv=ri("vw"),zc={...rt,parse:e=>rt.parse(e)/100,transform:e=>rt.transform(e*100)},Ic={...fn,transform:Math.round},xh={borderWidth:L,borderTopWidth:L,borderRightWidth:L,borderBottomWidth:L,borderLeftWidth:L,borderRadius:L,radius:L,borderTopLeftRadius:L,borderTopRightRadius:L,borderBottomRightRadius:L,borderBottomLeftRadius:L,width:L,maxWidth:L,height:L,maxHeight:L,size:L,top:L,right:L,bottom:L,left:L,padding:L,paddingTop:L,paddingRight:L,paddingBottom:L,paddingLeft:L,margin:L,marginTop:L,marginRight:L,marginBottom:L,marginLeft:L,rotate:gt,rotateX:gt,rotateY:gt,rotateZ:gt,scale:Pi,scaleX:Pi,scaleY:Pi,scaleZ:Pi,skew:gt,skewX:gt,skewY:gt,distance:L,translateX:L,translateY:L,translateZ:L,x:L,y:L,z:L,perspective:L,transformPerspective:L,opacity:kr,originX:zc,originY:zc,originZ:L,zIndex:Ic,fillOpacity:kr,strokeOpacity:kr,numOctaves:Ic};function Ya(e,t,n,r){const{style:i,vars:o,transform:s,transformKeys:l,transformOrigin:a}=e;l.length=0;let u=!1,c=!1,f=!0;for(const d in t){const y=t[d];if(wh(d)){o[d]=y;continue}const g=xh[d],v=Yv(y,g);if(cn.has(d)){if(u=!0,s[d]=v,l.push(d),!f)continue;y!==(g.default||0)&&(f=!1)}else d.startsWith("origin")?(c=!0,a[d]=v):i[d]=v}if(t.transform||(u||r?i.transform=Gv(e,n,f,r):i.transform&&(i.transform="none")),c){const{originX:d="50%",originY:y="50%",originZ:g=0}=a;i.transformOrigin=`${d} ${y} ${g}`}}const Xa=()=>({style:{},transform:{},transformKeys:[],transformOrigin:{},vars:{}});function Sh(e,t,n){for(const r in t)!it(t[r])&&!gh(r,n)&&(e[r]=t[r])}function qv({transformTemplate:e},t,n){return S.useMemo(()=>{const r=Xa();return Ya(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function bv(e,t,n){const r=e.style||{},i={};return Sh(i,r,e),Object.assign(i,qv(e,t,n)),e.transformValues?e.transformValues(i):i}function eg(e,t,n){const r={},i=bv(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),r.style=i,r}const tg=["animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView"],ng=["whileTap","onTap","onTapStart","onTapCancel"],rg=["onPan","onPanStart","onPanSessionStart","onPanEnd"],ig=["whileInView","onViewportEnter","onViewportLeave","viewport"],og=new Set(["initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","layout","layoutId","layoutDependency","onLayoutAnimationStart","onLayoutAnimationComplete","onLayoutMeasure","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","drag","dragControls","dragListener","dragConstraints","dragDirectionLock","dragSnapToOrigin","_dragX","_dragY","dragElastic","dragMomentum","dragPropagation","dragTransition","onHoverStart","onHoverEnd","layoutScroll",...ig,...ng,...tg,...rg]);function vo(e){return og.has(e)}let Ch=e=>!vo(e);function sg(e){e&&(Ch=t=>t.startsWith("on")?!vo(t):e(t))}try{sg(require("@emotion/is-prop-valid").default)}catch{}function lg(e,t,n){const r={};for(const i in e)(Ch(i)||n===!0&&vo(i)||!t&&!vo(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function jc(e,t,n){return typeof e=="string"?e:L.transform(t+n*e)}function ag(e,t,n){const r=jc(t,e.x,e.width),i=jc(n,e.y,e.height);return`${r} ${i}`}const ug={offset:"stroke-dashoffset",array:"stroke-dasharray"},cg={offset:"strokeDashoffset",array:"strokeDasharray"};function fg(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?ug:cg;e[o.offset]=L.transform(-r);const s=L.transform(t),l=L.transform(n);e[o.array]=`${s} ${l}`}function Za(e,{attrX:t,attrY:n,originX:r,originY:i,pathLength:o,pathSpacing:s=1,pathOffset:l=0,...a},u,c,f){if(Ya(e,a,u,f),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:d,style:y,dimensions:g}=e;d.transform&&(g&&(y.transform=d.transform),delete d.transform),g&&(r!==void 0||i!==void 0||y.transform)&&(y.transformOrigin=ag(g,r!==void 0?r:.5,i!==void 0?i:.5)),t!==void 0&&(d.x=t),n!==void 0&&(d.y=n),o!==void 0&&fg(d,o,s,l,!1)}const kh=()=>({...Xa(),attrs:{}}),Ja=e=>typeof e=="string"&&e.toLowerCase()==="svg";function dg(e,t,n,r){const i=S.useMemo(()=>{const o=kh();return Za(o,t,{enableHardwareAcceleration:!1},Ja(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};Sh(o,e.style,e),i.style={...o,...i.style}}return i}function pg(e=!1){return(n,r,i,o,{latestValues:s},l)=>{const u=(Ga(n)?dg:eg)(r,s,l,n),f={...lg(r,typeof n=="string",e),...u,ref:o};return i&&(f["data-projection-id"]=i),S.createElement(n,f)}}const qa=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();function Eh(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const Ph=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Th(e,t,n,r){Eh(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(Ph.has(i)?i:qa(i),t.attrs[i])}function ba(e){const{style:t}=e,n={};for(const r in t)(it(t[r])||gh(r,e))&&(n[r]=t[r]);return n}function _h(e){const t=ba(e);for(const n in e)if(it(e[n])){const r=n==="x"||n==="y"?"attr"+n.toUpperCase():n;t[r]=e[n]}return t}function eu(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}const go=e=>Array.isArray(e),hg=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),mg=e=>go(e)?e[e.length-1]||0:e;function $i(e){const t=it(e)?e.get():e;return hg(t)?t.toValue():t}function yg({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,o){const s={latestValues:vg(r,i,o,e),renderState:t()};return n&&(s.mount=l=>n(r,l,s)),s}const Vh=e=>(t,n)=>{const r=S.useContext($o),i=S.useContext(ti),o=()=>yg(e,t,r,i);return n?o():Ko(o)};function vg(e,t,n,r){const i={},o=r(e);for(const d in o)i[d]=$i(o[d]);let{initial:s,animate:l}=e;const a=Ho(e),u=yh(e);t&&u&&!a&&e.inherit!==!1&&(s===void 0&&(s=t.initial),l===void 0&&(l=t.animate));let c=n?n.initial===!1:!1;c=c||s===!1;const f=c?l:s;return f&&typeof f!="boolean"&&!Wo(f)&&(Array.isArray(f)?f:[f]).forEach(y=>{const g=eu(e,y);if(!g)return;const{transitionEnd:v,transition:x,...p}=g;for(const h in p){let m=p[h];if(Array.isArray(m)){const w=c?m.length-1:0;m=m[w]}m!==null&&(i[h]=m)}for(const h in v)i[h]=v[h]}),i}const gg={useVisualState:Vh({scrapeMotionValuesFromProps:_h,createRenderState:kh,onMount:(e,t,{renderState:n,latestValues:r})=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}Za(n,r,{enableHardwareAcceleration:!1},Ja(t.tagName),e.transformTemplate),Th(t,n)}})},wg={useVisualState:Vh({scrapeMotionValuesFromProps:ba,createRenderState:Xa})};function xg(e,{forwardMotionProps:t=!1},n,r,i){return{...Ga(e)?gg:wg,preloadedFeatures:n,useRender:pg(t),createVisualElement:r,projectionNodeConstructor:i,Component:e}}var U;(function(e){e.Animate="animate",e.Hover="whileHover",e.Tap="whileTap",e.Drag="whileDrag",e.Focus="whileFocus",e.InView="whileInView",e.Exit="exit"})(U||(U={}));function Qo(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function Dl(e,t,n,r){S.useEffect(()=>{const i=e.current;if(n&&i)return Qo(i,t,n,r)},[e,t,n,r])}function Sg({whileFocus:e,visualElement:t}){const{animationState:n}=t,r=()=>{n&&n.setActive(U.Focus,!0)},i=()=>{n&&n.setActive(U.Focus,!1)};Dl(t,"focus",e?r:void 0),Dl(t,"blur",e?i:void 0)}function Ah(e){return typeof PointerEvent<"u"&&e instanceof PointerEvent?e.pointerType==="mouse":e instanceof MouseEvent}function Lh(e){return!!e.touches}function Cg(e){return t=>{const n=t instanceof MouseEvent;(!n||n&&t.button===0)&&e(t)}}const kg={pageX:0,pageY:0};function Eg(e,t="page"){const r=e.touches[0]||e.changedTouches[0]||kg;return{x:r[t+"X"],y:r[t+"Y"]}}function Pg(e,t="page"){return{x:e[t+"X"],y:e[t+"Y"]}}function tu(e,t="page"){return{point:Lh(e)?Eg(e,t):Pg(e,t)}}const Rh=(e,t=!1)=>{const n=r=>e(r,tu(r));return t?Cg(n):n},Tg=()=>un&&window.onpointerdown===null,_g=()=>un&&window.ontouchstart===null,Vg=()=>un&&window.onmousedown===null,Ag={pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointercancel:"mousecancel",pointerover:"mouseover",pointerout:"mouseout",pointerenter:"mouseenter",pointerleave:"mouseleave"},Lg={pointerdown:"touchstart",pointermove:"touchmove",pointerup:"touchend",pointercancel:"touchcancel"};function Dh(e){return Tg()?e:_g()?Lg[e]:Vg()?Ag[e]:e}function Fn(e,t,n,r){return Qo(e,Dh(t),Rh(n,t==="pointerdown"),r)}function wo(e,t,n,r){return Dl(e,Dh(t),n&&Rh(n,t==="pointerdown"),r)}function Mh(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const Bc=Mh("dragHorizontal"),Uc=Mh("dragVertical");function Nh(e){let t=!1;if(e==="y")t=Uc();else if(e==="x")t=Bc();else{const n=Bc(),r=Uc();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function Oh(){const e=Nh(!0);return e?(e(),!1):!0}function $c(e,t,n){return(r,i)=>{!Ah(r)||Oh()||(e.animationState&&e.animationState.setActive(U.Hover,t),n&&n(r,i))}}function Rg({onHoverStart:e,onHoverEnd:t,whileHover:n,visualElement:r}){wo(r,"pointerenter",e||n?$c(r,!0,e):void 0,{passive:!e}),wo(r,"pointerleave",t||n?$c(r,!1,t):void 0,{passive:!t})}const Fh=(e,t)=>t?e===t?!0:Fh(e,t.parentElement):!1;function nu(e){return S.useEffect(()=>()=>e(),[])}const Dg=(e,t)=>n=>t(e(n)),Go=(...e)=>e.reduce(Dg);function Mg({onTap:e,onTapStart:t,onTapCancel:n,whileTap:r,visualElement:i}){const o=e||t||n||r,s=S.useRef(!1),l=S.useRef(null),a={passive:!(t||e||n||y)};function u(){l.current&&l.current(),l.current=null}function c(){return u(),s.current=!1,i.animationState&&i.animationState.setActive(U.Tap,!1),!Oh()}function f(g,v){c()&&(Fh(i.current,g.target)?e&&e(g,v):n&&n(g,v))}function d(g,v){c()&&n&&n(g,v)}function y(g,v){u(),!s.current&&(s.current=!0,l.current=Go(Fn(window,"pointerup",f,a),Fn(window,"pointercancel",d,a)),i.animationState&&i.animationState.setActive(U.Tap,!0),t&&t(g,v))}wo(i,"pointerdown",o?y:void 0,a),nu(u)}var Ng={};const Og="production",zh=typeof process>"u"||Ng===void 0?Og:"production",Wc=new Set;function Ih(e,t,n){Wc.has(t)||(console.warn(t),Wc.add(t))}const Ml=new WeakMap,Es=new WeakMap,Fg=e=>{const t=Ml.get(e.target);t&&t(e)},zg=e=>{e.forEach(Fg)};function Ig({root:e,...t}){const n=e||document;Es.has(n)||Es.set(n,{});const r=Es.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(zg,{root:e,...t})),r[i]}function jg(e,t,n){const r=Ig(t);return Ml.set(e,n),r.observe(e),()=>{Ml.delete(e),r.unobserve(e)}}function Bg({visualElement:e,whileInView:t,onViewportEnter:n,onViewportLeave:r,viewport:i={}}){const o=S.useRef({hasEnteredView:!1,isInView:!1});let s=!!(t||n||r);i.once&&o.current.hasEnteredView&&(s=!1),(typeof IntersectionObserver>"u"?Wg:$g)(s,o.current,e,i)}const Ug={some:0,all:1};function $g(e,t,n,{root:r,margin:i,amount:o="some",once:s}){S.useEffect(()=>{if(!e||!n.current)return;const l={root:r==null?void 0:r.current,rootMargin:i,threshold:typeof o=="number"?o:Ug[o]},a=u=>{const{isIntersecting:c}=u;if(t.isInView===c||(t.isInView=c,s&&!c&&t.hasEnteredView))return;c&&(t.hasEnteredView=!0),n.animationState&&n.animationState.setActive(U.InView,c);const f=n.getProps(),d=c?f.onViewportEnter:f.onViewportLeave;d&&d(u)};return jg(n.current,l,a)},[e,r,i,o])}function Wg(e,t,n,{fallback:r=!0}){S.useEffect(()=>{!e||!r||(zh!=="production"&&Ih(!1,"IntersectionObserver not available on this device. whileInView animations will trigger on mount."),requestAnimationFrame(()=>{t.hasEnteredView=!0;const{onViewportEnter:i}=n.getProps();i&&i(null),n.animationState&&n.animationState.setActive(U.InView,!0)}))},[e])}const Tt=e=>t=>(e(t),null),Hg={inView:Tt(Bg),tap:Tt(Mg),focus:Tt(Sg),hover:Tt(Rg)};function jh(){const e=S.useContext(ti);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=S.useId();return S.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}function Bh(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}const Kg=e=>/^\-?\d*\.?\d+$/.test(e),Qg=e=>/^0[^.\s]+$/.test(e),ft={delta:0,timestamp:0},Uh=1/60*1e3,Gg=typeof performance<"u"?()=>performance.now():()=>Date.now(),$h=typeof window<"u"?e=>window.requestAnimationFrame(e):e=>setTimeout(()=>e(Gg()),Uh);function Yg(e){let t=[],n=[],r=0,i=!1,o=!1;const s=new WeakSet,l={schedule:(a,u=!1,c=!1)=>{const f=c&&i,d=f?t:n;return u&&s.add(a),d.indexOf(a)===-1&&(d.push(a),f&&i&&(r=t.length)),a},cancel:a=>{const u=n.indexOf(a);u!==-1&&n.splice(u,1),s.delete(a)},process:a=>{if(i){o=!0;return}if(i=!0,[t,n]=[n,t],n.length=0,r=t.length,r)for(let u=0;u<r;u++){const c=t[u];c(a),s.has(c)&&(l.schedule(c),e())}i=!1,o&&(o=!1,l.process(a))}};return l}const Xg=40;let Nl=!0,Yr=!1,Ol=!1;const ii=["read","update","preRender","render","postRender"],Yo=ii.reduce((e,t)=>(e[t]=Yg(()=>Yr=!0),e),{}),_e=ii.reduce((e,t)=>{const n=Yo[t];return e[t]=(r,i=!1,o=!1)=>(Yr||Jg(),n.schedule(r,i,o)),e},{}),zt=ii.reduce((e,t)=>(e[t]=Yo[t].cancel,e),{}),Ps=ii.reduce((e,t)=>(e[t]=()=>Yo[t].process(ft),e),{}),Zg=e=>Yo[e].process(ft),Wh=e=>{Yr=!1,ft.delta=Nl?Uh:Math.max(Math.min(e-ft.timestamp,Xg),1),ft.timestamp=e,Ol=!0,ii.forEach(Zg),Ol=!1,Yr&&(Nl=!1,$h(Wh))},Jg=()=>{Yr=!0,Nl=!0,Ol||$h(Wh)};function ru(e,t){e.indexOf(t)===-1&&e.push(t)}function iu(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class ou{constructor(){this.subscriptions=[]}add(t){return ru(this.subscriptions,t),()=>iu(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function su(e,t){return t?e*(1e3/t):0}const qg=e=>!isNaN(parseFloat(e));class bg{constructor(t,n={}){this.version="7.10.3",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:o,timestamp:s}=ft;this.lastUpdated!==s&&(this.timeDelta=o,this.lastUpdated=s,_e.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>_e.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=qg(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){return this.events[t]||(this.events[t]=new ou),this.events[t].add(n)}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t){this.passiveEffect=t}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?su(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.stopAnimation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.stopAnimation&&(this.stopAnimation(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.stopAnimation}clearAnimation(){this.stopAnimation=null}destroy(){this.clearListeners(),this.stop()}}function Kn(e,t){return new bg(e,t)}const lu=(e,t)=>n=>!!(ni(n)&&Xv.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),Hh=(e,t,n)=>r=>{if(!ni(r))return r;const[i,o,s,l]=r.match(Gr);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:l!==void 0?parseFloat(l):1}},e1=e=>Hn(0,255,e),Ts={...fn,transform:e=>Math.round(e1(e))},qt={test:lu("rgb","red"),parse:Hh("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+Ts.transform(e)+", "+Ts.transform(t)+", "+Ts.transform(n)+", "+Er(kr.transform(r))+")"};function t1(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Fl={test:lu("#"),parse:t1,transform:qt.transform},_n={test:lu("hsl","hue"),parse:Hh("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+rt.transform(Er(t))+", "+rt.transform(Er(n))+", "+Er(kr.transform(r))+")"},ve={test:e=>qt.test(e)||Fl.test(e)||_n.test(e),parse:e=>qt.test(e)?qt.parse(e):_n.test(e)?_n.parse(e):Fl.parse(e),transform:e=>ni(e)?e:e.hasOwnProperty("red")?qt.transform(e):_n.transform(e)},Kh="${c}",Qh="${n}";function n1(e){var t,n;return isNaN(e)&&ni(e)&&(((t=e.match(Gr))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Rl))===null||n===void 0?void 0:n.length)||0)>0}function xo(e){typeof e=="number"&&(e=`${e}`);const t=[];let n=0,r=0;const i=e.match(Rl);i&&(n=i.length,e=e.replace(Rl,Kh),t.push(...i.map(ve.parse)));const o=e.match(Gr);return o&&(r=o.length,e=e.replace(Gr,Qh),t.push(...o.map(fn.parse))),{values:t,numColors:n,numNumbers:r,tokenised:e}}function Gh(e){return xo(e).values}function Yh(e){const{values:t,numColors:n,tokenised:r}=xo(e),i=t.length;return o=>{let s=r;for(let l=0;l<i;l++)s=s.replace(l<n?Kh:Qh,l<n?ve.transform(o[l]):Er(o[l]));return s}}const r1=e=>typeof e=="number"?0:e;function i1(e){const t=Gh(e);return Yh(e)(t.map(r1))}const It={test:n1,parse:Gh,createTransformer:Yh,getAnimatableNone:i1},o1=new Set(["brightness","contrast","saturate","opacity"]);function s1(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Gr)||[];if(!r)return e;const i=n.replace(r,"");let o=o1.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const l1=/([a-z-]*)\(.*?\)/g,zl={...It,getAnimatableNone:e=>{const t=e.match(l1);return t?t.map(s1).join(" "):e}},a1={...xh,color:ve,backgroundColor:ve,outlineColor:ve,fill:ve,stroke:ve,borderColor:ve,borderTopColor:ve,borderRightColor:ve,borderBottomColor:ve,borderLeftColor:ve,filter:zl,WebkitFilter:zl},au=e=>a1[e];function uu(e,t){var n;let r=au(e);return r!==zl&&(r=It),(n=r.getAnimatableNone)===null||n===void 0?void 0:n.call(r,t)}const Xh=e=>t=>t.test(e),u1={test:e=>e==="auto",parse:e=>e},Zh=[fn,L,rt,gt,Jv,Zv,u1],ir=e=>Zh.find(Xh(e)),c1=[...Zh,ve,It],f1=e=>c1.find(Xh(e));function d1(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function p1(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function Xo(e,t,n){const r=e.getProps();return eu(r,t,n!==void 0?n:r.custom,d1(e),p1(e))}function h1(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Kn(n))}function m1(e,t){const n=Xo(e,t);let{transitionEnd:r={},transition:i={},...o}=n?e.makeTargetAnimatable(n,!1):{};o={...o,...r};for(const s in o){const l=mg(o[s]);h1(e,s,l)}}function y1(e,t,n){var r,i;const o=Object.keys(t).filter(l=>!e.hasValue(l)),s=o.length;if(s)for(let l=0;l<s;l++){const a=o[l],u=t[a];let c=null;Array.isArray(u)&&(c=u[0]),c===null&&(c=(i=(r=n[a])!==null&&r!==void 0?r:e.readValue(a))!==null&&i!==void 0?i:t[a]),c!=null&&(typeof c=="string"&&(Kg(c)||Qg(c))?c=parseFloat(c):!f1(c)&&It.test(u)&&(c=uu(a,u)),e.addValue(a,Kn(c,{owner:e})),n[a]===void 0&&(n[a]=c),c!==null&&e.setBaseTarget(a,c))}}function v1(e,t){return t?(t[e]||t.default||t).from:void 0}function g1(e,t,n){var r;const i={};for(const o in e){const s=v1(o,t);i[o]=s!==void 0?s:(r=n.getValue(o))===null||r===void 0?void 0:r.get()}return i}function So(e){return!!(it(e)&&e.add)}const w1=(e,t)=>`${e}: ${t}`;function x1(e,t){const{MotionAppearAnimations:n}=window,r=w1(e,cn.has(t)?"transform":t),i=n&&n.get(r);return i?(_e.render(()=>{try{i.cancel(),n.delete(r)}catch{}}),i.currentTime||0):0}const S1="framerAppearId",C1="data-"+qa(S1);var Xr=function(){};const Wi=e=>e*1e3,k1={current:!1},cu=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,fu=e=>t=>1-e(1-t),du=e=>e*e,E1=fu(du),pu=cu(du),J=(e,t,n)=>-n*e+n*t+e;function _s(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function P1({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;i=_s(a,l,e+1/3),o=_s(a,l,e),s=_s(a,l,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}const Vs=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},T1=[Fl,qt,_n],_1=e=>T1.find(t=>t.test(e));function Hc(e){const t=_1(e);let n=t.parse(e);return t===_n&&(n=P1(n)),n}const Jh=(e,t)=>{const n=Hc(e),r=Hc(t),i={...n};return o=>(i.red=Vs(n.red,r.red,o),i.green=Vs(n.green,r.green,o),i.blue=Vs(n.blue,r.blue,o),i.alpha=J(n.alpha,r.alpha,o),qt.transform(i))};function qh(e,t){return typeof e=="number"?n=>J(e,t,n):ve.test(e)?Jh(e,t):em(e,t)}const bh=(e,t)=>{const n=[...e],r=n.length,i=e.map((o,s)=>qh(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}},V1=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=qh(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}},em=(e,t)=>{const n=It.createTransformer(t),r=xo(e),i=xo(t);return r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?Go(bh(r.values,i.values),n):s=>`${s>0?t:e}`},Co=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Kc=(e,t)=>n=>J(e,t,n);function A1(e){return typeof e=="number"?Kc:typeof e=="string"?ve.test(e)?Jh:em:Array.isArray(e)?bh:typeof e=="object"?V1:Kc}function L1(e,t,n){const r=[],i=n||A1(e[0]),o=e.length-1;for(let s=0;s<o;s++){let l=i(e[s],e[s+1]);if(t){const a=Array.isArray(t)?t[s]:t;l=Go(a,l)}r.push(l)}return r}function tm(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;Xr(o===t.length),Xr(!r||!Array.isArray(r)||r.length===o-1),e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=L1(t,r,i),l=s.length,a=u=>{let c=0;if(l>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const f=Co(e[c],e[c+1],u);return s[c](f)};return n?u=>a(Hn(e[0],e[o-1],u)):a}const hu=e=>e,nm=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,R1=1e-7,D1=12;function M1(e,t,n,r,i){let o,s,l=0;do s=t+(n-t)/2,o=nm(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>R1&&++l<D1);return s}function rm(e,t,n,r){if(e===t&&n===r)return hu;const i=o=>M1(o,0,1,e,n);return o=>o===0||o===1?o:nm(i(o),t,r)}const im=e=>1-Math.sin(Math.acos(e)),mu=fu(im),N1=cu(mu),om=rm(.33,1.53,.69,.99),yu=fu(om),O1=cu(yu),F1=e=>(e*=2)<1?.5*yu(e):.5*(2-Math.pow(2,-10*(e-1))),Qc={linear:hu,easeIn:du,easeInOut:pu,easeOut:E1,circIn:im,circInOut:N1,circOut:mu,backIn:yu,backInOut:O1,backOut:om,anticipate:F1},Gc=e=>{if(Array.isArray(e)){Xr(e.length===4);const[t,n,r,i]=e;return rm(t,n,r,i)}else if(typeof e=="string")return Xr(Qc[e]!==void 0),Qc[e];return e},z1=e=>Array.isArray(e)&&typeof e[0]!="number";function I1(e,t){return e.map(()=>t||pu).splice(0,e.length-1)}function j1(e){const t=e.length;return e.map((n,r)=>r!==0?r/(t-1):0)}function B1(e,t){return e.map(n=>n*t)}function ko({keyframes:e,ease:t=pu,times:n,duration:r=300}){e=[...e];const i=ko[0],o=z1(t)?t.map(Gc):Gc(t),s={done:!1,value:i},l=B1(n&&n.length===ko.length?n:j1(e),r);function a(){return tm(l,e,{ease:Array.isArray(o)?o:I1(e,o)})}let u=a();return{next:c=>(s.value=u(c),s.done=c>=r,s),flipTarget:()=>{e.reverse(),u=a()}}}const As=.001,U1=.01,$1=10,W1=.05,H1=1;function K1({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,o,s=1-t;s=Hn(W1,H1,s),e=Hn(U1,$1,e/1e3),s<1?(i=u=>{const c=u*s,f=c*e,d=c-n,y=Il(u,s),g=Math.exp(-f);return As-d/y*g},o=u=>{const f=u*s*e,d=f*n+n,y=Math.pow(s,2)*Math.pow(u,2)*e,g=Math.exp(-f),v=Il(Math.pow(u,2),s);return(-i(u)+As>0?-1:1)*((d-y)*g)/v}):(i=u=>{const c=Math.exp(-u*e),f=(u-n)*e+1;return-As+c*f},o=u=>{const c=Math.exp(-u*e),f=(n-u)*(e*e);return c*f});const l=5/e,a=G1(i,o,l);if(e=e*1e3,isNaN(a))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(a,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const Q1=12;function G1(e,t,n){let r=n;for(let i=1;i<Q1;i++)r=r-e(r)/t(r);return r}function Il(e,t){return e*Math.sqrt(1-t*t)}const Y1=["duration","bounce"],X1=["stiffness","damping","mass"];function Yc(e,t){return t.some(n=>e[n]!==void 0)}function Z1(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Yc(e,X1)&&Yc(e,Y1)){const n=K1(e);t={...t,...n,velocity:0,mass:1},t.isResolvedFromDuration=!0}return t}const J1=5;function sm({keyframes:e,restSpeed:t=2,restDelta:n=.01,...r}){let i=e[0],o=e[e.length-1];const s={done:!1,value:i},{stiffness:l,damping:a,mass:u,velocity:c,duration:f,isResolvedFromDuration:d}=Z1(r);let y=q1,g=c?-(c/1e3):0;const v=a/(2*Math.sqrt(l*u));function x(){const p=o-i,h=Math.sqrt(l/u)/1e3;if(n===void 0&&(n=Math.min(Math.abs(o-i)/100,.4)),v<1){const m=Il(h,v);y=w=>{const C=Math.exp(-v*h*w);return o-C*((g+v*h*p)/m*Math.sin(m*w)+p*Math.cos(m*w))}}else if(v===1)y=m=>o-Math.exp(-h*m)*(p+(g+h*p)*m);else{const m=h*Math.sqrt(v*v-1);y=w=>{const C=Math.exp(-v*h*w),E=Math.min(m*w,300);return o-C*((g+v*h*p)*Math.sinh(E)+m*p*Math.cosh(E))/m}}}return x(),{next:p=>{const h=y(p);if(d)s.done=p>=f;else{let m=g;if(p!==0)if(v<1){const E=Math.max(0,p-J1);m=su(h-y(E),p-E)}else m=0;const w=Math.abs(m)<=t,C=Math.abs(o-h)<=n;s.done=w&&C}return s.value=s.done?o:h,s},flipTarget:()=>{g=-g,[i,o]=[o,i],x()}}}sm.needsInterpolation=(e,t)=>typeof e=="string"||typeof t=="string";const q1=e=>0;function b1({keyframes:e=[0],velocity:t=0,power:n=.8,timeConstant:r=350,restDelta:i=.5,modifyTarget:o}){const s=e[0],l={done:!1,value:s};let a=n*t;const u=s+a,c=o===void 0?u:o(u);return c!==u&&(a=c-s),{next:f=>{const d=-a*Math.exp(-f/r);return l.done=!(d>i||d<-i),l.value=l.done?c:c+d,l},flipTarget:()=>{}}}const ew={decay:b1,keyframes:ko,tween:ko,spring:sm};function lm(e,t,n=0){return e-t-n}function tw(e,t=0,n=0,r=!0){return r?lm(t+-e,t,n):t-(e-t)+n}function nw(e,t,n,r){return r?e>=t+n:e<=-n}const rw=e=>{const t=({delta:n})=>e(n);return{start:()=>_e.update(t,!0),stop:()=>zt.update(t)}};function Eo({duration:e,driver:t=rw,elapsed:n=0,repeat:r=0,repeatType:i="loop",repeatDelay:o=0,keyframes:s,autoplay:l=!0,onPlay:a,onStop:u,onComplete:c,onRepeat:f,onUpdate:d,type:y="keyframes",...g}){var v,x;let p,h=0,m=e,w,C=!1,E=!0,P;const _=ew[s.length>2?"keyframes":y],O=s[0],A=s[s.length-1];!((x=(v=_).needsInterpolation)===null||x===void 0)&&x.call(v,O,A)&&(P=tm([0,100],[O,A],{clamp:!1}),s=[0,100]);const I=_({...g,duration:e,keyframes:s});function b(){h++,i==="reverse"?(E=h%2===0,n=tw(n,m,o,E)):(n=lm(n,m,o),i==="mirror"&&I.flipTarget()),C=!1,f&&f()}function oe(){p.stop(),c&&c()}function ye(W){if(E||(W=-W),n+=W,!C){const ee=I.next(Math.max(0,n));w=ee.value,P&&(w=P(w)),C=E?ee.done:n<=0}d&&d(w),C&&(h===0&&(m=m!==void 0?m:n),h<r?nw(n,m,o,E)&&b():oe())}function j(){a&&a(),p=t(ye),p.start()}return l&&j(),{stop:()=>{u&&u(),p.stop()},sample:W=>I.next(Math.max(0,W))}}function iw(e){return!e||Array.isArray(e)||typeof e=="string"&&am[e]}const fr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,am={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:fr([0,.65,.55,1]),circOut:fr([.55,0,1,.45]),backIn:fr([.31,.01,.66,-.59]),backOut:fr([.33,1.53,.69,.99])};function ow(e){if(e)return Array.isArray(e)?fr(e):am[e]}function sw(e,t,n,{delay:r=0,duration:i,repeat:o=0,repeatType:s="loop",ease:l,times:a}={}){return e.animate({[t]:n,offset:a},{delay:r,duration:i,easing:ow(l),fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}const Ti=10;function lw(e,t,{onUpdate:n,onComplete:r,...i}){let{keyframes:o,duration:s=.3,elapsed:l=0,ease:a}=i;if(i.type==="spring"||!iw(i.ease)){const c=Eo(i);let f={done:!1,value:o[0]};const d=[];let y=0;for(;!f.done;)f=c.sample(y),d.push(f.value),y+=Ti;o=d,s=y-Ti,a="linear"}const u=sw(e.owner.current,t,o,{...i,delay:-l,duration:s,ease:a});return u.onfinish=()=>{e.set(o[o.length-1]),r&&r()},()=>{const{currentTime:c}=u;if(c){const f=Eo(i);e.setWithVelocity(f.sample(c-Ti).value,f.sample(c).value,Ti)}_e.update(()=>u.cancel())}}function um(e,t){const n=performance.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(zt.read(r),e(o-t))};return _e.read(r,!0),()=>zt.read(r)}function aw({keyframes:e,elapsed:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),()=>{});return t?um(i,-t):i()}function uw({keyframes:e,velocity:t=0,min:n,max:r,power:i=.8,timeConstant:o=750,bounceStiffness:s=500,bounceDamping:l=10,restDelta:a=1,modifyTarget:u,driver:c,onUpdate:f,onComplete:d,onStop:y}){const g=e[0];let v;function x(w){return n!==void 0&&w<n||r!==void 0&&w>r}function p(w){return n===void 0?r:r===void 0||Math.abs(n-w)<Math.abs(r-w)?n:r}function h(w){v==null||v.stop(),v=Eo({keyframes:[0,1],velocity:0,...w,driver:c,onUpdate:C=>{var E;f==null||f(C),(E=w.onUpdate)===null||E===void 0||E.call(w,C)},onComplete:d,onStop:y})}function m(w){h({type:"spring",stiffness:s,damping:l,restDelta:a,...w})}if(x(g))m({velocity:t,keyframes:[g,p(g)]});else{let w=i*t+g;typeof u<"u"&&(w=u(w));const C=p(w),E=C===n?-1:1;let P,_;const O=A=>{P=_,_=A,t=su(A-P,ft.delta),(E===1&&A>C||E===-1&&A<C)&&m({keyframes:[A,C],velocity:t})};h({type:"decay",keyframes:[g,0],velocity:t,timeConstant:o,power:i,restDelta:a,modifyTarget:u,onUpdate:x(w)?O:void 0})}return{stop:()=>v==null?void 0:v.stop()}}const Ht=()=>({type:"spring",stiffness:500,damping:25,restSpeed:10}),_i=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),Ls=()=>({type:"keyframes",ease:"linear",duration:.3}),cw={type:"keyframes",duration:.8},Xc={x:Ht,y:Ht,z:Ht,rotate:Ht,rotateX:Ht,rotateY:Ht,rotateZ:Ht,scaleX:_i,scaleY:_i,scale:_i,opacity:Ls,backgroundColor:Ls,color:Ls,default:_i},fw=(e,{keyframes:t})=>t.length>2?cw:(Xc[e]||Xc.default)(t[1]),jl=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&It.test(t)&&!t.startsWith("url("));function dw({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:l,from:a,...u}){return!!Object.keys(u).length}function Zc(e){return e===0||typeof e=="string"&&parseFloat(e)===0&&e.indexOf(" ")===-1}function Jc(e){return typeof e=="number"?0:uu("",e)}function cm(e,t){return e[t]||e.default||e}function pw(e,t,n,r){const i=jl(t,n);let o=r.from!==void 0?r.from:e.get();return o==="none"&&i&&typeof n=="string"?o=uu(t,n):Zc(o)&&typeof n=="string"?o=Jc(n):!Array.isArray(n)&&Zc(n)&&typeof o=="string"&&(n=Jc(o)),Array.isArray(n)?(n[0]===null&&(n[0]=o),n):[o,n]}const qc={waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate")},Rs={},fm={};for(const e in qc)fm[e]=()=>(Rs[e]===void 0&&(Rs[e]=qc[e]()),Rs[e]);const hw=new Set(["opacity"]),vu=(e,t,n,r={})=>i=>{const o=cm(r,e)||{},s=o.delay||r.delay||0;let{elapsed:l=0}=r;l=l-Wi(s);const a=pw(t,e,n,o),u=a[0],c=a[a.length-1],f=jl(e,u),d=jl(e,c);let y={keyframes:a,velocity:t.getVelocity(),...o,elapsed:l,onUpdate:p=>{t.set(p),o.onUpdate&&o.onUpdate(p)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(!f||!d||k1.current||o.type===!1)return aw(y);if(o.type==="inertia"){const p=uw(y);return()=>p.stop()}dw(o)||(y={...y,...fw(e,y)}),y.duration&&(y.duration=Wi(y.duration)),y.repeatDelay&&(y.repeatDelay=Wi(y.repeatDelay));const g=t.owner,v=g&&g.current;if(fm.waapi()&&hw.has(e)&&!y.repeatDelay&&y.repeatType!=="mirror"&&y.damping!==0&&g&&v instanceof HTMLElement&&!g.getProps().onUpdate)return lw(t,e,y);{const p=Eo(y);return()=>p.stop()}};function mw(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>Bl(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=Bl(e,t,n);else{const i=typeof t=="function"?Xo(e,t,n.custom):t;r=dm(e,i,n)}return r.then(()=>e.notify("AnimationComplete",t))}function Bl(e,t,n={}){var r;const i=Xo(e,t,n.custom);let{transition:o=e.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(o=n.transitionOverride);const s=i?()=>dm(e,i,n):()=>Promise.resolve(),l=!((r=e.variantChildren)===null||r===void 0)&&r.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:f,staggerDirection:d}=o;return yw(e,t,c+u,f,d,n)}:()=>Promise.resolve(),{when:a}=o;if(a){const[u,c]=a==="beforeChildren"?[s,l]:[l,s];return u().then(c)}else return Promise.all([s(),l(n.delay)])}function dm(e,t,{delay:n=0,transitionOverride:r,type:i}={}){var o;let{transition:s=e.getDefaultTransition(),transitionEnd:l,...a}=e.makeTargetAnimatable(t);const u=e.getValue("willChange");r&&(s=r);const c=[],f=i&&((o=e.animationState)===null||o===void 0?void 0:o.getState()[i]);for(const d in a){const y=e.getValue(d),g=a[d];if(!y||g===void 0||f&&gw(f,d))continue;let v={delay:n,elapsed:0,...s};if(e.shouldReduceMotion&&cn.has(d)&&(v={...v,type:!1,delay:0}),!y.hasAnimated){const p=e.getProps()[C1];p&&(v.elapsed=x1(p,d))}let x=y.start(vu(d,y,g,v));So(u)&&(u.add(d),x=x.then(()=>u.remove(d))),c.push(x)}return Promise.all(c).then(()=>{l&&m1(e,l)})}function yw(e,t,n=0,r=0,i=1,o){const s=[],l=(e.variantChildren.size-1)*r,a=i===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(e.variantChildren).sort(vw).forEach((u,c)=>{s.push(Bl(u,t,{...o,delay:n+a(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function vw(e,t){return e.sortNodePosition(t)}function gw({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}const gu=[U.Animate,U.InView,U.Focus,U.Hover,U.Tap,U.Drag,U.Exit],ww=[...gu].reverse(),xw=gu.length;function Sw(e){return t=>Promise.all(t.map(({animation:n,options:r})=>mw(e,n,r)))}function Cw(e){let t=Sw(e);const n=Ew();let r=!0;const i=(a,u)=>{const c=Xo(e,u);if(c){const{transition:f,transitionEnd:d,...y}=c;a={...a,...y,...d}}return a};function o(a){t=a(e)}function s(a,u){const c=e.getProps(),f=e.getVariantContext(!0)||{},d=[],y=new Set;let g={},v=1/0;for(let p=0;p<xw;p++){const h=ww[p],m=n[h],w=c[h]!==void 0?c[h]:f[h],C=Kr(w),E=h===u?m.isActive:null;E===!1&&(v=p);let P=w===f[h]&&w!==c[h]&&C;if(P&&r&&e.manuallyAnimateOnMount&&(P=!1),m.protectedKeys={...g},!m.isActive&&E===null||!w&&!m.prevProp||Wo(w)||typeof w=="boolean")continue;const _=kw(m.prevProp,w);let O=_||h===u&&m.isActive&&!P&&C||p>v&&C;const A=Array.isArray(w)?w:[w];let I=A.reduce(i,{});E===!1&&(I={});const{prevResolvedValues:b={}}=m,oe={...b,...I},ye=j=>{O=!0,y.delete(j),m.needsAnimating[j]=!0};for(const j in oe){const W=I[j],ee=b[j];g.hasOwnProperty(j)||(W!==ee?go(W)&&go(ee)?!Bh(W,ee)||_?ye(j):m.protectedKeys[j]=!0:W!==void 0?ye(j):y.add(j):W!==void 0&&y.has(j)?ye(j):m.protectedKeys[j]=!0)}m.prevProp=w,m.prevResolvedValues=I,m.isActive&&(g={...g,...I}),r&&e.blockInitialAnimation&&(O=!1),O&&!P&&d.push(...A.map(j=>({animation:j,options:{type:h,...a}})))}if(y.size){const p={};y.forEach(h=>{const m=e.getBaseTarget(h);m!==void 0&&(p[h]=m)}),d.push({animation:p})}let x=!!d.length;return r&&c.initial===!1&&!e.manuallyAnimateOnMount&&(x=!1),r=!1,x?t(d):Promise.resolve()}function l(a,u,c){var f;if(n[a].isActive===u)return Promise.resolve();(f=e.variantChildren)===null||f===void 0||f.forEach(y=>{var g;return(g=y.animationState)===null||g===void 0?void 0:g.setActive(a,u)}),n[a].isActive=u;const d=s(c,a);for(const y in n)n[y].protectedKeys={};return d}return{animateChanges:s,setActive:l,setAnimateFunction:o,getState:()=>n}}function kw(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Bh(t,e):!1}function Kt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Ew(){return{[U.Animate]:Kt(!0),[U.InView]:Kt(),[U.Hover]:Kt(),[U.Tap]:Kt(),[U.Drag]:Kt(),[U.Focus]:Kt(),[U.Exit]:Kt()}}const Pw={animation:Tt(({visualElement:e,animate:t})=>{e.animationState||(e.animationState=Cw(e)),Wo(t)&&S.useEffect(()=>t.subscribe(e),[t])}),exit:Tt(e=>{const{custom:t,visualElement:n}=e,[r,i]=jh(),o=S.useContext(ti);S.useEffect(()=>{n.isPresent=r;const s=n.animationState&&n.animationState.setActive(U.Exit,!r,{custom:o&&o.custom||t});s&&!r&&s.then(i)},[r])})},bc=(e,t)=>Math.abs(e-t);function Tw(e,t){const n=bc(e.x,t.x),r=bc(e.y,t.y);return Math.sqrt(n**2+r**2)}class pm{constructor(t,n,{transformPagePoint:r}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const u=Ms(this.lastMoveEventInfo,this.history),c=this.startEvent!==null,f=Tw(u.offset,{x:0,y:0})>=3;if(!c&&!f)return;const{point:d}=u,{timestamp:y}=ft;this.history.push({...d,timestamp:y});const{onStart:g,onMove:v}=this.handlers;c||(g&&g(this.lastMoveEvent,u),this.startEvent=this.lastMoveEvent),v&&v(this.lastMoveEvent,u)},this.handlePointerMove=(u,c)=>{if(this.lastMoveEvent=u,this.lastMoveEventInfo=Ds(c,this.transformPagePoint),Ah(u)&&u.buttons===0){this.handlePointerUp(u,c);return}_e.update(this.updatePoint,!0)},this.handlePointerUp=(u,c)=>{this.end();const{onEnd:f,onSessionEnd:d}=this.handlers,y=Ms(Ds(c,this.transformPagePoint),this.history);this.startEvent&&f&&f(u,y),d&&d(u,y)},Lh(t)&&t.touches.length>1)return;this.handlers=n,this.transformPagePoint=r;const i=tu(t),o=Ds(i,this.transformPagePoint),{point:s}=o,{timestamp:l}=ft;this.history=[{...s,timestamp:l}];const{onSessionStart:a}=n;a&&a(t,Ms(o,this.history)),this.removeListeners=Go(Fn(window,"pointermove",this.handlePointerMove),Fn(window,"pointerup",this.handlePointerUp),Fn(window,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),zt.update(this.updatePoint)}}function Ds(e,t){return t?{point:t(e.point)}:e}function ef(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Ms({point:e},t){return{point:e,delta:ef(e,hm(t)),offset:ef(e,_w(t)),velocity:Vw(t,.1)}}function _w(e){return e[0]}function hm(e){return e[e.length-1]}function Vw(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=hm(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>Wi(t)));)n--;if(!r)return{x:0,y:0};const o=(i.timestamp-r.timestamp)/1e3;if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function Ne(e){return e.max-e.min}function Ul(e,t=0,n=.01){return Math.abs(e-t)<=n}function tf(e,t,n,r=.5){e.origin=r,e.originPoint=J(t.min,t.max,e.origin),e.scale=Ne(n)/Ne(t),(Ul(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=J(n.min,n.max,e.origin)-e.originPoint,(Ul(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Pr(e,t,n,r){tf(e.x,t.x,n.x,r==null?void 0:r.originX),tf(e.y,t.y,n.y,r==null?void 0:r.originY)}function nf(e,t,n){e.min=n.min+t.min,e.max=e.min+Ne(t)}function Aw(e,t,n){nf(e.x,t.x,n.x),nf(e.y,t.y,n.y)}function rf(e,t,n){e.min=t.min-n.min,e.max=e.min+Ne(t)}function Tr(e,t,n){rf(e.x,t.x,n.x),rf(e.y,t.y,n.y)}function Lw(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?J(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?J(n,e,r.max):Math.min(e,n)),e}function of(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function Rw(e,{top:t,left:n,bottom:r,right:i}){return{x:of(e.x,n,i),y:of(e.y,t,r)}}function sf(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function Dw(e,t){return{x:sf(e.x,t.x),y:sf(e.y,t.y)}}function Mw(e,t){let n=.5;const r=Ne(e),i=Ne(t);return i>r?n=Co(t.min,t.max-r,e.min):r>i&&(n=Co(e.min,e.max-i,t.min)),Hn(0,1,n)}function Nw(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const $l=.35;function Ow(e=$l){return e===!1?e=0:e===!0&&(e=$l),{x:lf(e,"left","right"),y:lf(e,"top","bottom")}}function lf(e,t,n){return{min:af(e,t),max:af(e,n)}}function af(e,t){return typeof e=="number"?e:e[t]||0}const uf=()=>({translate:0,scale:1,origin:0,originPoint:0}),_r=()=>({x:uf(),y:uf()}),cf=()=>({min:0,max:0}),ne=()=>({x:cf(),y:cf()});function be(e){return[e("x"),e("y")]}function mm({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function Fw({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function zw(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Ns(e){return e===void 0||e===1}function Wl({scale:e,scaleX:t,scaleY:n}){return!Ns(e)||!Ns(t)||!Ns(n)}function Yt(e){return Wl(e)||ym(e)||e.z||e.rotate||e.rotateX||e.rotateY}function ym(e){return ff(e.x)||ff(e.y)}function ff(e){return e&&e!=="0%"}function Po(e,t,n){const r=e-n,i=t*r;return n+i}function df(e,t,n,r,i){return i!==void 0&&(e=Po(e,i,r)),Po(e,n,r)+t}function Hl(e,t=0,n=1,r,i){e.min=df(e.min,t,n,r,i),e.max=df(e.max,t,n,r,i)}function vm(e,{x:t,y:n}){Hl(e.x,t.translate,t.scale,t.originPoint),Hl(e.y,n.translate,n.scale,n.originPoint)}function Iw(e,t,n,r=!1){var i,o;const s=n.length;if(!s)return;t.x=t.y=1;let l,a;for(let u=0;u<s;u++)l=n[u],a=l.projectionDelta,((o=(i=l.instance)===null||i===void 0?void 0:i.style)===null||o===void 0?void 0:o.display)!=="contents"&&(r&&l.options.layoutScroll&&l.scroll&&l!==l.root&&Vn(e,{x:-l.scroll.offset.x,y:-l.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,vm(e,a)),r&&Yt(l.latestValues)&&Vn(e,l.latestValues));t.x=pf(t.x),t.y=pf(t.y)}function pf(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function St(e,t){e.min=e.min+t,e.max=e.max+t}function hf(e,t,[n,r,i]){const o=t[i]!==void 0?t[i]:.5,s=J(e.min,e.max,o);Hl(e,t[n],t[r],s,t.scale)}const jw=["x","scaleX","originX"],Bw=["y","scaleY","originY"];function Vn(e,t){hf(e.x,t,jw),hf(e.y,t,Bw)}function gm(e,t){return mm(zw(e.getBoundingClientRect(),t))}function Uw(e,t,n){const r=gm(e,n),{scroll:i}=t;return i&&(St(r.x,i.offset.x),St(r.y,i.offset.y)),r}const $w=new WeakMap;class Ww{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ne(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){if(this.visualElement.isPresent===!1)return;const r=l=>{this.stopAnimation(),n&&this.snapToCursor(tu(l,"page").point)},i=(l,a)=>{var u;const{drag:c,dragPropagation:f,onDragStart:d}=this.getProps();c&&!f&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Nh(c),!this.openGlobalLock)||(this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),be(y=>{var g,v;let x=this.getAxisMotionValue(y).get()||0;if(rt.test(x)){const p=(v=(g=this.visualElement.projection)===null||g===void 0?void 0:g.layout)===null||v===void 0?void 0:v.layoutBox[y];p&&(x=Ne(p)*(parseFloat(x)/100))}this.originPoint[y]=x}),d==null||d(l,a),(u=this.visualElement.animationState)===null||u===void 0||u.setActive(U.Drag,!0))},o=(l,a)=>{const{dragPropagation:u,dragDirectionLock:c,onDirectionLock:f,onDrag:d}=this.getProps();if(!u&&!this.openGlobalLock)return;const{offset:y}=a;if(c&&this.currentDirection===null){this.currentDirection=Hw(y),this.currentDirection!==null&&(f==null||f(this.currentDirection));return}this.updateAxis("x",a.point,y),this.updateAxis("y",a.point,y),this.visualElement.render(),d==null||d(l,a)},s=(l,a)=>this.stop(l,a);this.panSession=new pm(t,{onSessionStart:r,onStart:i,onMove:o,onSessionEnd:s},{transformPagePoint:this.visualElement.getTransformPagePoint()})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o==null||o(t,n)}cancel(){var t,n;this.isDragging=!1,this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!1),(t=this.panSession)===null||t===void 0||t.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),(n=this.visualElement.animationState)===null||n===void 0||n.setActive(U.Drag,!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Vi(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=Lw(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){const{dragConstraints:t,dragElastic:n}=this.getProps(),{layout:r}=this.visualElement.projection||{},i=this.constraints;t&&Tn(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=Rw(r.layoutBox,t):this.constraints=!1,this.elastic=Ow(n),i!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&be(o=>{this.getAxisMotionValue(o)&&(this.constraints[o]=Nw(r.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Tn(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=Uw(r,i.root,this.visualElement.getTransformPagePoint());let s=Dw(i.layout.layoutBox,o);if(n){const l=n(Fw(s));this.hasMutatedConstraints=!!l,l&&(s=mm(l))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},u=be(c=>{if(!Vi(c,n,this.currentDirection))return;let f=(a==null?void 0:a[c])||{};s&&(f={min:0,max:0});const d=i?200:1e6,y=i?40:1e7,g={type:"inertia",velocity:r?t[c]:0,bounceStiffness:d,bounceDamping:y,timeConstant:750,restDelta:1,restSpeed:10,...o,...f};return this.startAxisValueAnimation(c,g)});return Promise.all(u).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(vu(t,r,0,n))}stopAnimation(){be(t=>this.getAxisMotionValue(t).stop())}getAxisMotionValue(t){var n;const r="_drag"+t.toUpperCase(),i=this.visualElement.getProps()[r];return i||this.visualElement.getValue(t,((n=this.visualElement.getProps().initial)===null||n===void 0?void 0:n[t])||0)}snapToCursor(t){be(n=>{const{drag:r}=this.getProps();if(!Vi(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:l}=i.layout.layoutBox[n];o.set(t[n]-J(s,l,.5))}})}scalePositionWithinConstraints(){var t;if(!this.visualElement.current)return;const{drag:n,dragConstraints:r}=this.getProps(),{projection:i}=this.visualElement;if(!Tn(r)||!i||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};be(l=>{const a=this.getAxisMotionValue(l);if(a){const u=a.get();o[l]=Mw({min:u,max:u},this.constraints[l])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",(t=i.root)===null||t===void 0||t.updateScroll(),i.updateLayout(),this.resolveConstraints(),be(l=>{if(!Vi(l,n,null))return;const a=this.getAxisMotionValue(l),{min:u,max:c}=this.constraints[l];a.set(J(u,c,o[l]))})}addListeners(){var t;if(!this.visualElement.current)return;$w.set(this.visualElement,this);const n=this.visualElement.current,r=Fn(n,"pointerdown",u=>{const{drag:c,dragListener:f=!0}=this.getProps();c&&f&&this.start(u)}),i=()=>{const{dragConstraints:u}=this.getProps();Tn(u)&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,s=o.addEventListener("measure",i);o&&!o.layout&&((t=o.root)===null||t===void 0||t.updateScroll(),o.updateLayout()),i();const l=Qo(window,"resize",()=>this.scalePositionWithinConstraints()),a=o.addEventListener("didUpdate",({delta:u,hasLayoutChanged:c})=>{this.isDragging&&c&&(be(f=>{const d=this.getAxisMotionValue(f);d&&(this.originPoint[f]+=u[f].translate,d.set(d.get()+u[f].translate))}),this.visualElement.render())});return()=>{l(),r(),s(),a==null||a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=$l,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:l}}}function Vi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function Hw(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}function Kw(e){const{dragControls:t,visualElement:n}=e,r=Ko(()=>new Ww(n));S.useEffect(()=>t&&t.subscribe(r),[r,t]),S.useEffect(()=>r.addListeners(),[r])}function Qw({onPan:e,onPanStart:t,onPanEnd:n,onPanSessionStart:r,visualElement:i}){const o=e||t||n||r,s=S.useRef(null),{transformPagePoint:l}=S.useContext(Ka),a={onSessionStart:r,onStart:t,onMove:e,onEnd:(c,f)=>{s.current=null,n&&n(c,f)}};S.useEffect(()=>{s.current!==null&&s.current.updateHandlers(a)});function u(c){s.current=new pm(c,a,{transformPagePoint:l})}wo(i,"pointerdown",o&&u),nu(()=>s.current&&s.current.end())}const Gw={pan:Tt(Qw),drag:Tt(Kw)};function Kl(e){return typeof e=="string"&&e.startsWith("var(--")}const wm=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Yw(e){const t=wm.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function Ql(e,t,n=1){const[r,i]=Yw(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);return o?o.trim():Kl(i)?Ql(i,t,n+1):i}function Xw(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const o=i.get();if(!Kl(o))return;const s=Ql(o,r);s&&i.set(s)});for(const i in t){const o=t[i];if(!Kl(o))continue;const s=Ql(o,r);s&&(t[i]=s,n&&n[i]===void 0&&(n[i]=o))}return{target:t,transitionEnd:n}}const Zw=new Set(["width","height","top","left","right","bottom","x","y"]),xm=e=>Zw.has(e),Jw=e=>Object.keys(e).some(xm),Sm=(e,t)=>{e.set(t,!1),e.set(t)},mf=e=>e===fn||e===L;var yf;(function(e){e.width="width",e.height="height",e.left="left",e.right="right",e.top="top",e.bottom="bottom"})(yf||(yf={}));const vf=(e,t)=>parseFloat(e.split(", ")[t]),gf=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return vf(i[1],t);{const o=r.match(/^matrix\((.+)\)$/);return o?vf(o[1],e):0}},qw=new Set(["x","y","z"]),bw=yo.filter(e=>!qw.has(e));function ex(e){const t=[];return bw.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const wf={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:gf(4,13),y:gf(5,14)},tx=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:s}=o,l={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{l[u]=wf[u](r,o)}),t.render();const a=t.measureViewportBox();return n.forEach(u=>{const c=t.getValue(u);Sm(c,l[u]),e[u]=wf[u](a,o)}),e},nx=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(xm);let o=[],s=!1;const l=[];if(i.forEach(a=>{const u=e.getValue(a);if(!e.hasValue(a))return;let c=n[a],f=ir(c);const d=t[a];let y;if(go(d)){const g=d.length,v=d[0]===null?1:0;c=d[v],f=ir(c);for(let x=v;x<g;x++)y?Xr(ir(d[x])===y):y=ir(d[x])}else y=ir(d);if(f!==y)if(mf(f)&&mf(y)){const g=u.get();typeof g=="string"&&u.set(parseFloat(g)),typeof d=="string"?t[a]=parseFloat(d):Array.isArray(d)&&y===L&&(t[a]=d.map(parseFloat))}else f!=null&&f.transform&&(y!=null&&y.transform)&&(c===0||d===0)?c===0?u.set(y.transform(c)):t[a]=f.transform(d):(s||(o=ex(e),s=!0),l.push(a),r[a]=r[a]!==void 0?r[a]:t[a],Sm(u,d))}),l.length){const a=l.indexOf("height")>=0?window.pageYOffset:null,u=tx(t,e,l);return o.length&&o.forEach(([c,f])=>{e.getValue(c).set(f)}),e.render(),un&&a!==null&&window.scrollTo({top:a}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function rx(e,t,n,r){return Jw(t)?nx(e,t,n,r):{target:t,transitionEnd:r}}const ix=(e,t,n,r)=>{const i=Xw(e,t,r);return t=i.target,r=i.transitionEnd,rx(e,t,n,r)},Gl={current:null},Cm={current:!1};function ox(){if(Cm.current=!0,!!un)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Gl.current=e.matches;e.addListener(t),t()}else Gl.current=!1}function sx(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],s=n[i];if(it(o))e.addValue(i,o),So(r)&&r.add(i);else if(it(s))e.addValue(i,Kn(o,{owner:e})),So(r)&&r.remove(i);else if(s!==o)if(e.hasValue(i)){const l=e.getValue(i);!l.hasAnimated&&l.set(o)}else{const l=e.getStaticValue(i);e.addValue(i,Kn(l!==void 0?l:o))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const km=Object.keys(Qr),lx=km.length,xf=["AnimationStart","AnimationComplete","Update","Unmount","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ax{constructor({parent:t,props:n,reducedMotionConfig:r,visualState:i},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.isPresent=!0,this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>_e.render(this.render,!1,!0);const{latestValues:s,renderState:l}=i;this.latestValues=s,this.baseTarget={...s},this.initialValues=n.initial?{...s}:{},this.renderState=l,this.parent=t,this.props=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.isControllingVariants=Ho(n),this.isVariantNode=yh(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:a,...u}=this.scrapeMotionValuesFromProps(n);for(const c in u){const f=u[c];s[c]!==void 0&&it(f)&&(f.set(s[c],!1),So(a)&&a.add(c))}}scrapeMotionValuesFromProps(t){return{}}mount(t){var n;this.current=t,this.projection&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=(n=this.parent)===null||n===void 0?void 0:n.addVariantChild(this)),this.values.forEach((r,i)=>this.bindToMotionValue(i,r)),Cm.current||ox(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Gl.current,this.parent&&this.parent.children.add(this),this.setProps(this.props)}unmount(){var t,n,r;(t=this.projection)===null||t===void 0||t.unmount(),zt.update(this.notifyUpdate),zt.render(this.render),this.valueSubscriptions.forEach(i=>i()),(n=this.removeFromVariantTree)===null||n===void 0||n.call(this),(r=this.parent)===null||r===void 0||r.children.delete(this);for(const i in this.events)this.events[i].clear();this.current=null}bindToMotionValue(t,n){const r=cn.has(t),i=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&_e.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),o()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures(t,n,r,i,o,s){const l=[];for(let a=0;a<lx;a++){const u=km[a],{isEnabled:c,Component:f}=Qr[u];c(t)&&f&&l.push(S.createElement(f,{key:u,...t,visualElement:this}))}if(!this.projection&&o){this.projection=new o(i,this.latestValues,this.parent&&this.parent.projection);const{layoutId:a,layout:u,drag:c,dragConstraints:f,layoutScroll:d}=t;this.projection.setOptions({layoutId:a,layout:u,alwaysMeasureLayout:!!c||f&&Tn(f),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:s,layoutScroll:d})}return l}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ne()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}setProps(t){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.props=t;for(let n=0;n<xf.length;n++){const r=xf[n];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);const i=t["on"+r];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=sx(this,this.scrapeMotionValuesFromProps(t),this.prevMotionValues)}getProps(){return this.props}getVariant(t){var n;return(n=this.props.variants)===null||n===void 0?void 0:n[t]}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){var t;return this.isVariantNode?this:(t=this.parent)===null||t===void 0?void 0:t.getClosestVariantNode()}getVariantContext(t=!1){var n,r;if(t)return(n=this.parent)===null||n===void 0?void 0:n.getVariantContext();if(!this.isControllingVariants){const o=((r=this.parent)===null||r===void 0?void 0:r.getVariantContext())||{};return this.props.initial!==void 0&&(o.initial=this.props.initial),o}const i={};for(let o=0;o<ux;o++){const s=Em[o],l=this.props[s];(Kr(l)||l===!1)&&(i[s]=l)}return i}addVariantChild(t){var n;const r=this.getClosestVariantNode();if(r)return(n=r.variantChildren)===null||n===void 0||n.add(t),()=>r.variantChildren.delete(t)}addValue(t,n){this.hasValue(t)&&this.removeValue(t),this.values.set(t,n),this.latestValues[t]=n.get(),this.bindToMotionValue(t,n)}removeValue(t){var n;this.values.delete(t),(n=this.valueSubscriptions.get(t))===null||n===void 0||n(),this.valueSubscriptions.delete(t),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Kn(n,{owner:this}),this.addValue(t,r)),r}readValue(t){return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=eu(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!it(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new ou),this.events[t].add(n)}notify(t,...n){var r;(r=this.events[t])===null||r===void 0||r.notify(...n)}}const Em=["initial",...gu],ux=Em.length;class Pm extends ax{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){var r;return(r=t.style)===null||r===void 0?void 0:r[n]}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},o){let s=g1(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),s&&(s=i(s))),o){y1(this,r,s);const l=ix(this,r,s,n);n=l.transitionEnd,r=l.target}return{transition:t,transitionEnd:n,...r}}}function cx(e){return window.getComputedStyle(e)}class fx extends Pm{readValueFromInstance(t,n){if(cn.has(n)){const r=au(n);return r&&r.default||0}else{const r=cx(t),i=(wh(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return gm(t,n)}build(t,n,r,i){Ya(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t){return ba(t)}renderInstance(t,n,r,i){Eh(t,n,r,i)}}class dx extends Pm{constructor(){super(...arguments),this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){var r;return cn.has(n)?((r=au(n))===null||r===void 0?void 0:r.default)||0:(n=Ph.has(n)?n:qa(n),t.getAttribute(n))}measureInstanceViewportBox(){return ne()}scrapeMotionValuesFromProps(t){return _h(t)}build(t,n,r,i){Za(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){Th(t,n,r,i)}mount(t){this.isSVGTag=Ja(t.tagName),super.mount(t)}}const px=(e,t)=>Ga(e)?new dx(t,{enableHardwareAcceleration:!1}):new fx(t,{enableHardwareAcceleration:!0});function Sf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const or={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(L.test(e))e=parseFloat(e);else return e;const n=Sf(e,t.target.x),r=Sf(e,t.target.y);return`${n}% ${r}%`}},Cf="_$css",hx={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=e.includes("var("),o=[];i&&(e=e.replace(wm,y=>(o.push(y),Cf)));const s=It.parse(e);if(s.length>5)return r;const l=It.createTransformer(e),a=typeof s[0]!="number"?1:0,u=n.x.scale*t.x,c=n.y.scale*t.y;s[0+a]/=u,s[1+a]/=c;const f=J(u,c,.5);typeof s[2+a]=="number"&&(s[2+a]/=f),typeof s[3+a]=="number"&&(s[3+a]/=f);let d=l(s);if(i){let y=0;d=d.replace(Cf,()=>{const g=o[y];return y++,g})}return d}};class mx extends Le.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;Hv(vx),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Cr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||_e.postRender(()=>{var l;!((l=s.getStack())===null||l===void 0)&&l.members.length||this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),!t.currentAnimation&&t.isLead()&&this.safeToRemove())}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n!=null&&n.group&&n.group.remove(i),r!=null&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t==null||t()}render(){return null}}function yx(e){const[t,n]=jh(),r=S.useContext(Qa);return Le.createElement(mx,{...e,layoutGroup:r,switchLayoutGroup:S.useContext(vh),isPresent:t,safeToRemove:n})}const vx={borderRadius:{...or,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:or,borderTopRightRadius:or,borderBottomLeftRadius:or,borderBottomRightRadius:or,boxShadow:hx},gx={measureLayout:yx};function wx(e,t,n={}){const r=it(e)?e:Kn(e);return r.start(vu("",r,t,n)),{stop:()=>r.stop(),isAnimating:()=>r.isAnimating()}}const Tm=["TopLeft","TopRight","BottomLeft","BottomRight"],xx=Tm.length,kf=e=>typeof e=="string"?parseFloat(e):e,Ef=e=>typeof e=="number"||L.test(e);function Sx(e,t,n,r,i,o){i?(e.opacity=J(0,n.opacity!==void 0?n.opacity:1,Cx(r)),e.opacityExit=J(t.opacity!==void 0?t.opacity:1,0,kx(r))):o&&(e.opacity=J(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<xx;s++){const l=`border${Tm[s]}Radius`;let a=Pf(t,l),u=Pf(n,l);if(a===void 0&&u===void 0)continue;a||(a=0),u||(u=0),a===0||u===0||Ef(a)===Ef(u)?(e[l]=Math.max(J(kf(a),kf(u),r),0),(rt.test(u)||rt.test(a))&&(e[l]+="%")):e[l]=u}(t.rotate||n.rotate)&&(e.rotate=J(t.rotate||0,n.rotate||0,r))}function Pf(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Cx=_m(0,.5,mu),kx=_m(.5,.95,hu);function _m(e,t,n){return r=>r<e?0:r>t?1:n(Co(e,t,r))}function Tf(e,t){e.min=t.min,e.max=t.max}function He(e,t){Tf(e.x,t.x),Tf(e.y,t.y)}function _f(e,t,n,r,i){return e-=t,e=Po(e,1/n,r),i!==void 0&&(e=Po(e,1/i,r)),e}function Ex(e,t=0,n=1,r=.5,i,o=e,s=e){if(rt.test(t)&&(t=parseFloat(t),t=J(s.min,s.max,t/100)-s.min),typeof t!="number")return;let l=J(o.min,o.max,r);e===o&&(l-=t),e.min=_f(e.min,t,n,l,i),e.max=_f(e.max,t,n,l,i)}function Vf(e,t,[n,r,i],o,s){Ex(e,t[n],t[r],t[i],t.scale,o,s)}const Px=["x","scaleX","originX"],Tx=["y","scaleY","originY"];function Af(e,t,n,r){Vf(e.x,t,Px,n==null?void 0:n.x,r==null?void 0:r.x),Vf(e.y,t,Tx,n==null?void 0:n.y,r==null?void 0:r.y)}function Lf(e){return e.translate===0&&e.scale===1}function Vm(e){return Lf(e.x)&&Lf(e.y)}function Am(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function Rf(e){return Ne(e.x)/Ne(e.y)}class _x{constructor(){this.members=[]}add(t){ru(this.members,t),t.scheduleRender()}remove(t){if(iu(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){var r;const i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,n&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),!((r=t.root)===null||r===void 0)&&r.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:o}=t.options;o===!1&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{var n,r,i,o,s;(r=(n=t.options).onExitComplete)===null||r===void 0||r.call(n),(s=(i=t.resumingFrom)===null||i===void 0?void 0:(o=i.options).onExitComplete)===null||s===void 0||s.call(o)})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Df(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r=`translate3d(${i}px, ${o}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:a,rotateX:u,rotateY:c}=n;a&&(r+=`rotate(${a}deg) `),u&&(r+=`rotateX(${u}deg) `),c&&(r+=`rotateY(${c}deg) `)}const s=e.x.scale*t.x,l=e.y.scale*t.y;return(s!==1||l!==1)&&(r+=`scale(${s}, ${l})`),r||"none"}const Vx=(e,t)=>e.depth-t.depth;class Ax{constructor(){this.children=[],this.isDirty=!1}add(t){ru(this.children,t),this.isDirty=!0}remove(t){iu(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Vx),this.isDirty=!1,this.children.forEach(t)}}const Mf=["","X","Y","Z"],Nf=1e3;let Lx=0;function Lm({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s,l={},a=t==null?void 0:t()){this.id=Lx++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isTransformDirty=!1,this.isProjectionDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.potentialNodes=new Map,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.nodes.forEach(Mx),this.nodes.forEach(Fx),this.nodes.forEach(zx)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.elementId=s,this.latestValues=l,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0,s&&this.root.registerPotentialNode(s,this);for(let u=0;u<this.path.length;u++)this.path[u].shouldResetTransform=!0;this.root===this&&(this.nodes=new Ax)}addEventListener(s,l){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new ou),this.eventHandlers.get(s).add(l)}notifyListeners(s,...l){const a=this.eventHandlers.get(s);a==null||a.notify(...l)}hasListeners(s){return this.eventHandlers.has(s)}registerPotentialNode(s,l){this.potentialNodes.set(s,l)}mount(s,l=!1){var a;if(this.instance)return;this.isSVG=s instanceof SVGElement&&s.tagName!=="svg",this.instance=s;const{layoutId:u,layout:c,visualElement:f}=this.options;if(f&&!f.current&&f.mount(s),this.root.nodes.add(this),(a=this.parent)===null||a===void 0||a.children.add(this),this.elementId&&this.root.potentialNodes.delete(this.elementId),l&&(c||u)&&(this.isLayoutDirty=!0),e){let d;const y=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=um(y,250),Cr.hasAnimatedSinceResize&&(Cr.hasAnimatedSinceResize=!1,this.nodes.forEach(Ff))})}u&&this.root.registerSharedNode(u,this),this.options.animate!==!1&&f&&(u||c)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:y,hasRelativeTargetChanged:g,layout:v})=>{var x,p,h,m,w;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const C=(p=(x=this.options.transition)!==null&&x!==void 0?x:f.getDefaultTransition())!==null&&p!==void 0?p:$x,{onLayoutAnimationStart:E,onLayoutAnimationComplete:P}=f.getProps(),_=!this.targetLayout||!Am(this.targetLayout,v)||g,O=!y&&g;if(!((h=this.resumeFrom)===null||h===void 0)&&h.instance||O||y&&(_||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,O);const A={...cm(C,"layout"),onPlay:E,onComplete:P};f.shouldReduceMotion&&(A.delay=0,A.type=!1),this.startAnimation(A)}else!y&&this.animationProgress===0&&Ff(this),this.isLead()&&((w=(m=this.options).onExitComplete)===null||w===void 0||w.call(m));this.targetLayout=v})}unmount(){var s,l;this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this),(s=this.getStack())===null||s===void 0||s.remove(this),(l=this.parent)===null||l===void 0||l.children.delete(this),this.instance=void 0,zt.preRender(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){var s;return this.isAnimationBlocked||((s=this.parent)===null||s===void 0?void 0:s.isTreeAnimationBlocked())||!1}startUpdate(){var s;this.isUpdateBlocked()||(this.isUpdating=!0,(s=this.nodes)===null||s===void 0||s.forEach(Ix),this.animationId++)}willUpdate(s=!0){var l,a,u;if(this.root.isUpdateBlocked()){(a=(l=this.options).onExitComplete)===null||a===void 0||a.call(l);return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let y=0;y<this.path.length;y++){const g=this.path[y];g.shouldResetTransform=!0,g.updateScroll("snapshot")}const{layoutId:c,layout:f}=this.options;if(c===void 0&&!f)return;const d=(u=this.options.visualElement)===null||u===void 0?void 0:u.getProps().transformTemplate;this.prevTransformTemplateValue=d==null?void 0:d(this.latestValues,""),this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}didUpdate(){if(this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Of);return}this.isUpdating&&(this.isUpdating=!1,this.potentialNodes.size&&(this.potentialNodes.forEach(Wx),this.potentialNodes.clear()),this.nodes.forEach(Ox),this.nodes.forEach(Rx),this.nodes.forEach(Dx),this.clearAllSnapshots(),Ps.update(),Ps.preRender(),Ps.render())}clearAllSnapshots(){this.nodes.forEach(Nx),this.sharedNodes.forEach(jx)}scheduleUpdateProjection(){_e.preRender(this.updateProjection,!1,!0)}scheduleCheckAfterUnmount(){_e.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){var s;if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const l=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ne(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox),(s=this.options.visualElement)===null||s===void 0||s.notify("LayoutMeasure",this.layout.layoutBox,l==null?void 0:l.layoutBox)}updateScroll(s="measure"){let l=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(l=!1),l&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){var s;if(!i)return;const l=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!Vm(this.projectionDelta),u=(s=this.options.visualElement)===null||s===void 0?void 0:s.getProps().transformTemplate,c=u==null?void 0:u(this.latestValues,""),f=c!==this.prevTransformTemplateValue;l&&(a||Yt(this.latestValues)||f)&&(i(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return s&&(a=this.removeTransform(a)),Hx(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return ne();const l=s.measureViewportBox(),{scroll:a}=this.root;return a&&(St(l.x,a.offset.x),St(l.y,a.offset.y)),l}removeElementScroll(s){const l=ne();He(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a],{scroll:c,options:f}=u;if(u!==this.root&&c&&f.layoutScroll){if(c.isRoot){He(l,s);const{scroll:d}=this.root;d&&(St(l.x,-d.offset.x),St(l.y,-d.offset.y))}St(l.x,c.offset.x),St(l.y,c.offset.y)}}return l}applyTransform(s,l=!1){const a=ne();He(a,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];!l&&c.options.layoutScroll&&c.scroll&&c!==c.root&&Vn(a,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),Yt(c.latestValues)&&Vn(a,c.latestValues)}return Yt(this.latestValues)&&Vn(a,this.latestValues),a}removeTransform(s){var l;const a=ne();He(a,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];if(!c.instance||!Yt(c.latestValues))continue;Wl(c.latestValues)&&c.updateSnapshot();const f=ne(),d=c.measurePageBox();He(f,d),Af(a,c.latestValues,(l=c.snapshot)===null||l===void 0?void 0:l.layoutBox,f)}return Yt(this.latestValues)&&Af(a,this.latestValues),a}setTargetDelta(s){this.targetDelta=s,this.isProjectionDirty=!0,this.root.scheduleUpdateProjection()}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}resolveTargetDelta(){var s;const l=this.getLead();if(this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),!this.isProjectionDirty&&!this.attemptToResolveRelativeTarget)return;const{layout:a,layoutId:u}=this.options;if(!(!this.layout||!(a||u))){if(!this.targetDelta&&!this.relativeTarget){const c=this.getClosestProjectingParent();c&&c.layout?(this.relativeParent=c,this.relativeTarget=ne(),this.relativeTargetOrigin=ne(),Tr(this.relativeTargetOrigin,this.layout.layoutBox,c.layout.layoutBox),He(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=ne(),this.targetWithTransforms=ne()),this.relativeTarget&&this.relativeTargetOrigin&&(!((s=this.relativeParent)===null||s===void 0)&&s.target)?Aw(this.target,this.relativeTarget,this.relativeParent.target):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):He(this.target,this.layout.layoutBox),vm(this.target,this.targetDelta)):He(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const c=this.getClosestProjectingParent();c&&!!c.resumingFrom==!!this.resumingFrom&&!c.options.layoutScroll&&c.target?(this.relativeParent=c,this.relativeTarget=ne(),this.relativeTargetOrigin=ne(),Tr(this.relativeTargetOrigin,this.target,c.target),He(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Wl(this.parent.latestValues)||ym(this.parent.latestValues)))return(this.parent.relativeTarget||this.parent.targetDelta)&&this.parent.layout?this.parent:this.parent.getClosestProjectingParent()}calcProjection(){var s;const{isProjectionDirty:l,isTransformDirty:a}=this;this.isProjectionDirty=this.isTransformDirty=!1;const u=this.getLead(),c=!!this.resumingFrom||this!==u;let f=!0;if(l&&(f=!1),c&&a&&(f=!1),f)return;const{layout:d,layoutId:y}=this.options;if(this.isTreeAnimating=!!(!((s=this.parent)===null||s===void 0)&&s.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||y))return;He(this.layoutCorrected,this.layout.layoutBox),Iw(this.layoutCorrected,this.treeScale,this.path,c);const{target:g}=u;if(!g)return;this.projectionDelta||(this.projectionDelta=_r(),this.projectionDeltaWithTransform=_r());const v=this.treeScale.x,x=this.treeScale.y,p=this.projectionTransform;Pr(this.projectionDelta,this.layoutCorrected,g,this.latestValues),this.projectionTransform=Df(this.projectionDelta,this.treeScale),(this.projectionTransform!==p||this.treeScale.x!==v||this.treeScale.y!==x)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",g))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){var l,a,u;(a=(l=this.options).scheduleRender)===null||a===void 0||a.call(l),s&&((u=this.getStack())===null||u===void 0||u.scheduleRender()),this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,l=!1){var a,u;const c=this.snapshot,f=(c==null?void 0:c.latestValues)||{},d={...this.latestValues},y=_r();this.relativeTarget=this.relativeTargetOrigin=void 0,this.attemptToResolveRelativeTarget=!l;const g=ne(),v=(c==null?void 0:c.source)!==((a=this.layout)===null||a===void 0?void 0:a.source),x=(((u=this.getStack())===null||u===void 0?void 0:u.members.length)||0)<=1,p=!!(v&&!x&&this.options.crossfade===!0&&!this.path.some(Ux));this.animationProgress=0,this.mixTargetDelta=h=>{var m;const w=h/1e3;zf(y.x,s.x,w),zf(y.y,s.y,w),this.setTargetDelta(y),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&(!((m=this.relativeParent)===null||m===void 0)&&m.layout)&&(Tr(g,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Bx(this.relativeTarget,this.relativeTargetOrigin,g,w)),v&&(this.animationValues=d,Sx(d,f,this.latestValues,w,p,x)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=w},this.mixTargetDelta(0)}startAnimation(s){var l,a;this.notifyListeners("animationStart"),(l=this.currentAnimation)===null||l===void 0||l.stop(),this.resumingFrom&&((a=this.resumingFrom.currentAnimation)===null||a===void 0||a.stop()),this.pendingAnimation&&(zt.update(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=_e.update(()=>{Cr.hasAnimatedSinceResize=!0,this.currentAnimation=wx(0,Nf,{...s,onUpdate:u=>{var c;this.mixTargetDelta(u),(c=s.onUpdate)===null||c===void 0||c.call(s,u)},onComplete:()=>{var u;(u=s.onComplete)===null||u===void 0||u.call(s),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){var s;this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0),(s=this.getStack())===null||s===void 0||s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){var s;this.currentAnimation&&((s=this.mixTargetDelta)===null||s===void 0||s.call(this,Nf),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:l,target:a,layout:u,latestValues:c}=s;if(!(!l||!a||!u)){if(this!==s&&this.layout&&u&&Rm(this.options.animationType,this.layout.layoutBox,u.layoutBox)){a=this.target||ne();const f=Ne(this.layout.layoutBox.x);a.x.min=s.target.x.min,a.x.max=a.x.min+f;const d=Ne(this.layout.layoutBox.y);a.y.min=s.target.y.min,a.y.max=a.y.min+d}He(l,a),Vn(l,c),Pr(this.projectionDeltaWithTransform,this.layoutCorrected,l,c)}}registerSharedNode(s,l){var a,u,c;this.sharedNodes.has(s)||this.sharedNodes.set(s,new _x),this.sharedNodes.get(s).add(l),l.promote({transition:(a=l.options.initialPromotionConfig)===null||a===void 0?void 0:a.transition,preserveFollowOpacity:(c=(u=l.options.initialPromotionConfig)===null||u===void 0?void 0:u.shouldPreserveFollowOpacity)===null||c===void 0?void 0:c.call(u,l)})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:l}=this.options;return l?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:l}=this.options;return l?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:l,preserveFollowOpacity:a}={}){const u=this.getStack();u&&u.promote(this,a),s&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let l=!1;const{latestValues:a}=s;if((a.rotate||a.rotateX||a.rotateY||a.rotateZ)&&(l=!0),!l)return;const u={};for(let c=0;c<Mf.length;c++){const f="rotate"+Mf[c];a[f]&&(u[f]=a[f],s.setStaticValue(f,0))}s==null||s.render();for(const c in u)s.setStaticValue(c,u[c]);s.scheduleRender()}getProjectionStyles(s={}){var l,a,u;const c={};if(!this.instance||this.isSVG)return c;if(this.isVisible)c.visibility="";else return{visibility:"hidden"};const f=(l=this.options.visualElement)===null||l===void 0?void 0:l.getProps().transformTemplate;if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=$i(s.pointerEvents)||"",c.transform=f?f(this.latestValues,""):"none",c;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=$i(s.pointerEvents)||""),this.hasProjected&&!Yt(this.latestValues)&&(x.transform=f?f({},""):"none",this.hasProjected=!1),x}const y=d.animationValues||d.latestValues;this.applyTransformsToTarget(),c.transform=Df(this.projectionDeltaWithTransform,this.treeScale,y),f&&(c.transform=f(y,c.transform));const{x:g,y:v}=this.projectionDelta;c.transformOrigin=`${g.origin*100}% ${v.origin*100}% 0`,d.animationValues?c.opacity=d===this?(u=(a=y.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&u!==void 0?u:1:this.preserveOpacity?this.latestValues.opacity:y.opacityExit:c.opacity=d===this?y.opacity!==void 0?y.opacity:"":y.opacityExit!==void 0?y.opacityExit:0;for(const x in mo){if(y[x]===void 0)continue;const{correct:p,applyTo:h}=mo[x],m=p(y[x],d);if(h){const w=h.length;for(let C=0;C<w;C++)c[h[C]]=m}else c[x]=m}return this.options.layoutId&&(c.pointerEvents=d===this?$i(s.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var l;return(l=s.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(Of),this.root.sharedNodes.clear()}}}function Rx(e){e.updateLayout()}function Dx(e){var t,n,r;const i=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&i&&e.hasListeners("didUpdate")){const{layoutBox:o,measuredBox:s}=e.layout,{animationType:l}=e.options,a=i.source!==e.layout.source;l==="size"?be(y=>{const g=a?i.measuredBox[y]:i.layoutBox[y],v=Ne(g);g.min=o[y].min,g.max=g.min+v}):Rm(l,i.layoutBox,o)&&be(y=>{const g=a?i.measuredBox[y]:i.layoutBox[y],v=Ne(o[y]);g.max=g.min+v});const u=_r();Pr(u,o,i.layoutBox);const c=_r();a?Pr(c,e.applyTransform(s,!0),i.measuredBox):Pr(c,o,i.layoutBox);const f=!Vm(u);let d=!1;if(!e.resumeFrom){const y=e.getClosestProjectingParent();if(y&&!y.resumeFrom){const{snapshot:g,layout:v}=y;if(g&&v){const x=ne();Tr(x,i.layoutBox,g.layoutBox);const p=ne();Tr(p,o,v.layoutBox),Am(x,p)||(d=!0)}}}e.notifyListeners("didUpdate",{layout:o,snapshot:i,delta:c,layoutDelta:u,hasLayoutChanged:f,hasRelativeTargetChanged:d})}else e.isLead()&&((r=(n=e.options).onExitComplete)===null||r===void 0||r.call(n));e.options.transition=void 0}function Mx(e){e.isProjectionDirty||(e.isProjectionDirty=!!(e.parent&&e.parent.isProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=!!(e.parent&&e.parent.isTransformDirty))}function Nx(e){e.clearSnapshot()}function Of(e){e.clearMeasurements()}function Ox(e){const{visualElement:t}=e.options;t!=null&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Ff(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0}function Fx(e){e.resolveTargetDelta()}function zx(e){e.calcProjection()}function Ix(e){e.resetRotation()}function jx(e){e.removeLeadSnapshot()}function zf(e,t,n){e.translate=J(t.translate,0,n),e.scale=J(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function If(e,t,n,r){e.min=J(t.min,n.min,r),e.max=J(t.max,n.max,r)}function Bx(e,t,n,r){If(e.x,t.x,n.x,r),If(e.y,t.y,n.y,r)}function Ux(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const $x={duration:.45,ease:[.4,0,.1,1]};function Wx(e,t){let n=e.root;for(let o=e.path.length-1;o>=0;o--)if(e.path[o].instance){n=e.path[o];break}const i=(n&&n!==e.root?n.instance:document).querySelector(`[data-projection-id="${t}"]`);i&&e.mount(i,!0)}function jf(e){e.min=Math.round(e.min),e.max=Math.round(e.max)}function Hx(e){jf(e.x),jf(e.y)}function Rm(e,t,n){return e==="position"||e==="preserve-aspect"&&!Ul(Rf(t),Rf(n),.2)}const Kx=Lm({attachResizeListener:(e,t)=>Qo(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Os={current:void 0},Qx=Lm({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Os.current){const e=new Kx(0,{});e.mount(window),e.setOptions({layoutScroll:!0}),Os.current=e}return Os.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),Gx={...Pw,...Hg,...Gw,...gx},Yx=$v((e,t)=>xg(e,t,Gx,px,Qx));function Dm(){const e=S.useRef(!1);return Sr(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function Xx(){const e=Dm(),[t,n]=S.useState(0),r=S.useCallback(()=>{e.current&&n(t+1)},[t]);return[S.useCallback(()=>_e.postRender(r),[r]),t]}class Zx extends S.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function Jx({children:e,isPresent:t}){const n=S.useId(),r=S.useRef(null),i=S.useRef({width:0,height:0,top:0,left:0});return S.useInsertionEffect(()=>{const{width:o,height:s,top:l,left:a}=i.current;if(t||!r.current||!o||!s)return;r.current.dataset.motionPopId=n;const u=document.createElement("style");return document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${s}px !important;
            top: ${l}px !important;
            left: ${a}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[t]),S.createElement(Zx,{isPresent:t,childRef:r,sizeRef:i},S.cloneElement(e,{ref:r}))}const Fs=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:o,mode:s})=>{const l=Ko(qx),a=S.useId(),u=S.useMemo(()=>({id:a,initial:t,isPresent:n,custom:i,onExitComplete:c=>{l.set(c,!0);for(const f of l.values())if(!f)return;r&&r()},register:c=>(l.set(c,!1),()=>l.delete(c))}),o?void 0:[n]);return S.useMemo(()=>{l.forEach((c,f)=>l.set(f,!1))},[n]),S.useEffect(()=>{!n&&!l.size&&r&&r()},[n]),s==="popLayout"&&(e=S.createElement(Jx,{isPresent:n},e)),S.createElement(ti.Provider,{value:u},e)};function qx(){return new Map}const pn=e=>e.key||"";function bx(e,t){e.forEach(n=>{const r=pn(n);t.set(r,n)})}function eS(e){const t=[];return S.Children.forEach(e,n=>{S.isValidElement(n)&&t.push(n)}),t}const tS=({children:e,custom:t,initial:n=!0,onExitComplete:r,exitBeforeEnter:i,presenceAffectsLayout:o=!0,mode:s="sync"})=>{i&&(s="wait",Ih(!1,"Replace exitBeforeEnter with mode='wait'"));let[l]=Xx();const a=S.useContext(Qa).forceRender;a&&(l=a);const u=Dm(),c=eS(e);let f=c;const d=new Set,y=S.useRef(f),g=S.useRef(new Map).current,v=S.useRef(!0);if(Sr(()=>{v.current=!1,bx(c,g),y.current=f}),nu(()=>{v.current=!0,g.clear(),d.clear()}),v.current)return S.createElement(S.Fragment,null,f.map(m=>S.createElement(Fs,{key:pn(m),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:o,mode:s},m)));f=[...f];const x=y.current.map(pn),p=c.map(pn),h=x.length;for(let m=0;m<h;m++){const w=x[m];p.indexOf(w)===-1&&d.add(w)}return s==="wait"&&d.size&&(f=[]),d.forEach(m=>{if(p.indexOf(m)!==-1)return;const w=g.get(m);if(!w)return;const C=x.indexOf(m),E=()=>{g.delete(m),d.delete(m);const P=y.current.findIndex(_=>_.key===m);if(y.current.splice(P,1),!d.size){if(y.current=c,u.current===!1)return;l(),r&&r()}};f.splice(C,0,S.createElement(Fs,{key:pn(w),isPresent:!1,onExitComplete:E,custom:t,presenceAffectsLayout:o,mode:s},w))}),f=f.map(m=>{const w=m.key;return d.has(w)?m:S.createElement(Fs,{key:pn(m),isPresent:!0,presenceAffectsLayout:o,mode:s},m)}),zh!=="production"&&s==="wait"&&f.length>1&&console.warn(`You're attempting to animate multiple children within AnimatePresence, but its mode is set to "wait". This will lead to odd visual behaviour.`),S.createElement(S.Fragment,null,d.size?f:f.map(m=>S.cloneElement(m)))};var nS=e=>{var{children:t,hide:n,mode:r="wait"}=e,{isPlaying:i,safeToRemove:o,isStopped:s}=Vv();return Le.createElement(tS,{mode:r,onExitComplete:s?o:null},!n&&i?t:null)},rS={exports:{}};(function(e){(function(){function t(v,x){document.addEventListener?v.addEventListener("scroll",x,!1):v.attachEvent("scroll",x)}function n(v){document.body?v():document.addEventListener?document.addEventListener("DOMContentLoaded",function x(){document.removeEventListener("DOMContentLoaded",x),v()}):document.attachEvent("onreadystatechange",function x(){(document.readyState=="interactive"||document.readyState=="complete")&&(document.detachEvent("onreadystatechange",x),v())})}function r(v){this.g=document.createElement("div"),this.g.setAttribute("aria-hidden","true"),this.g.appendChild(document.createTextNode(v)),this.h=document.createElement("span"),this.i=document.createElement("span"),this.m=document.createElement("span"),this.j=document.createElement("span"),this.l=-1,this.h.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.i.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.j.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.m.style.cssText="display:inline-block;width:200%;height:200%;font-size:16px;max-width:none;",this.h.appendChild(this.m),this.i.appendChild(this.j),this.g.appendChild(this.h),this.g.appendChild(this.i)}function i(v,x){v.g.style.cssText="max-width:none;min-width:20px;min-height:20px;display:inline-block;overflow:hidden;position:absolute;width:auto;margin:0;padding:0;top:-999px;white-space:nowrap;font-synthesis:none;font:"+x+";"}function o(v){var x=v.g.offsetWidth,p=x+100;return v.j.style.width=p+"px",v.i.scrollLeft=p,v.h.scrollLeft=v.h.scrollWidth+100,v.l!==x?(v.l=x,!0):!1}function s(v,x){function p(){var m=h;o(m)&&m.g.parentNode!==null&&x(m.l)}var h=v;t(v.h,p),t(v.i,p),o(v)}function l(v,x,p){x=x||{},p=p||window,this.family=v,this.style=x.style||"normal",this.weight=x.weight||"normal",this.stretch=x.stretch||"normal",this.context=p}var a=null,u=null,c=null,f=null;function d(v){return u===null&&(y(v)&&/Apple/.test(window.navigator.vendor)?(v=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))(?:\.([0-9]+))/.exec(window.navigator.userAgent),u=!!v&&603>parseInt(v[1],10)):u=!1),u}function y(v){return f===null&&(f=!!v.document.fonts),f}function g(v,x){var p=v.style,h=v.weight;if(c===null){var m=document.createElement("div");try{m.style.font="condensed 100px sans-serif"}catch{}c=m.style.font!==""}return[p,h,c?v.stretch:"","100px",x].join(" ")}l.prototype.load=function(v,x){var p=this,h=v||"BESbswy",m=0,w=x||3e3,C=new Date().getTime();return new Promise(function(E,P){if(y(p.context)&&!d(p.context)){var _=new Promise(function(A,I){function b(){new Date().getTime()-C>=w?I(Error(""+w+"ms timeout exceeded")):p.context.document.fonts.load(g(p,'"'+p.family+'"'),h).then(function(oe){1<=oe.length?A():setTimeout(b,25)},I)}b()}),O=new Promise(function(A,I){m=setTimeout(function(){I(Error(""+w+"ms timeout exceeded"))},w)});Promise.race([O,_]).then(function(){clearTimeout(m),E(p)},P)}else n(function(){function A(){var M;(M=j!=-1&&W!=-1||j!=-1&&ee!=-1||W!=-1&&ee!=-1)&&((M=j!=W&&j!=ee&&W!=ee)||(a===null&&(M=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))/.exec(window.navigator.userAgent),a=!!M&&(536>parseInt(M[1],10)||parseInt(M[1],10)===536&&11>=parseInt(M[2],10))),M=a&&(j==T&&W==T&&ee==T||j==R&&W==R&&ee==R||j==D&&W==D&&ee==D)),M=!M),M&&(F.parentNode!==null&&F.parentNode.removeChild(F),clearTimeout(m),E(p))}function I(){if(new Date().getTime()-C>=w)F.parentNode!==null&&F.parentNode.removeChild(F),P(Error(""+w+"ms timeout exceeded"));else{var M=p.context.document.hidden;(M===!0||M===void 0)&&(j=b.g.offsetWidth,W=oe.g.offsetWidth,ee=ye.g.offsetWidth,A()),m=setTimeout(I,50)}}var b=new r(h),oe=new r(h),ye=new r(h),j=-1,W=-1,ee=-1,T=-1,R=-1,D=-1,F=document.createElement("div");F.dir="ltr",i(b,g(p,"sans-serif")),i(oe,g(p,"serif")),i(ye,g(p,"monospace")),F.appendChild(b.g),F.appendChild(oe.g),F.appendChild(ye.g),p.context.document.body.appendChild(F),T=b.g.offsetWidth,R=oe.g.offsetWidth,D=ye.g.offsetWidth,I(),s(b,function(M){j=M,A()}),i(b,g(p,'"'+p.family+'",sans-serif')),s(oe,function(M){W=M,A()}),i(oe,g(p,'"'+p.family+'",serif')),s(ye,function(M){ee=M,A()}),i(ye,g(p,'"'+p.family+'",monospace'))})})},e.exports=l})()})(rS);const iS=()=>{const{name:e}=hh();return Cu.jsx(nS,{hide:!e,children:Cu.jsx(Yx.div,{className:"container",initial:{opacity:0,y:50},animate:{opacity:1,y:0},exit:{opacity:0,y:50},children:e},e)})};xv(iS);

</script>
    <style>
.container{position:absolute;bottom:80px;left:266px;width:1388px;padding:20px;background-color:#fff;border-radius:6px;font-size:70px;font-family:sans-serif;font-weight:600;white-space:nowrap;display:flex;align-items:center}

</style>
  </head>
  <body>
  </body>
</html>
