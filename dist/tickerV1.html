<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <script type="module" crossorigin>
(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();var L=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function r8(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var o8={exports:{}},as={},s8={exports:{}},I={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cr=Symbol.for("react.element"),I5=Symbol.for("react.portal"),q5=Symbol.for("react.fragment"),j5=Symbol.for("react.strict_mode"),F5=Symbol.for("react.profiler"),B5=Symbol.for("react.provider"),U5=Symbol.for("react.context"),z5=Symbol.for("react.forward_ref"),$5=Symbol.for("react.suspense"),H5=Symbol.for("react.memo"),G5=Symbol.for("react.lazy"),bc=Symbol.iterator;function W5(t){return t===null||typeof t!="object"?null:(t=bc&&t[bc]||t["@@iterator"],typeof t=="function"?t:null)}var i8={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},a8=Object.assign,l8={};function v1(t,e,n){this.props=t,this.context=e,this.refs=l8,this.updater=n||i8}v1.prototype.isReactComponent={};v1.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};v1.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function c8(){}c8.prototype=v1.prototype;function Ua(t,e,n){this.props=t,this.context=e,this.refs=l8,this.updater=n||i8}var za=Ua.prototype=new c8;za.constructor=Ua;a8(za,v1.prototype);za.isPureReactComponent=!0;var _c=Array.isArray,u8=Object.prototype.hasOwnProperty,$a={current:null},f8={key:!0,ref:!0,__self:!0,__source:!0};function d8(t,e,n){var r,o={},s=null,i=null;if(e!=null)for(r in e.ref!==void 0&&(i=e.ref),e.key!==void 0&&(s=""+e.key),e)u8.call(e,r)&&!f8.hasOwnProperty(r)&&(o[r]=e[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var l=Array(a),c=0;c<a;c++)l[c]=arguments[c+2];o.children=l}if(t&&t.defaultProps)for(r in a=t.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:Cr,type:t,key:s,ref:i,props:o,_owner:$a.current}}function Q5(t,e){return{$$typeof:Cr,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}function Ha(t){return typeof t=="object"&&t!==null&&t.$$typeof===Cr}function K5(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var kc=/\/+/g;function Fs(t,e){return typeof t=="object"&&t!==null&&t.key!=null?K5(""+t.key):e.toString(36)}function ao(t,e,n,r,o){var s=typeof t;(s==="undefined"||s==="boolean")&&(t=null);var i=!1;if(t===null)i=!0;else switch(s){case"string":case"number":i=!0;break;case"object":switch(t.$$typeof){case Cr:case I5:i=!0}}if(i)return i=t,o=o(i),t=r===""?"."+Fs(i,0):r,_c(o)?(n="",t!=null&&(n=t.replace(kc,"$&/")+"/"),ao(o,e,n,"",function(c){return c})):o!=null&&(Ha(o)&&(o=Q5(o,n+(!o.key||i&&i.key===o.key?"":(""+o.key).replace(kc,"$&/")+"/")+t)),e.push(o)),1;if(i=0,r=r===""?".":r+":",_c(t))for(var a=0;a<t.length;a++){s=t[a];var l=r+Fs(s,a);i+=ao(s,e,n,l,o)}else if(l=W5(t),typeof l=="function")for(t=l.call(t),a=0;!(s=t.next()).done;)s=s.value,l=r+Fs(s,a++),i+=ao(s,e,n,l,o);else if(s==="object")throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");return i}function qr(t,e,n){if(t==null)return t;var r=[],o=0;return ao(t,r,"","",function(s){return e.call(n,s,o++)}),r}function Y5(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var kt={current:null},lo={transition:null},X5={ReactCurrentDispatcher:kt,ReactCurrentBatchConfig:lo,ReactCurrentOwner:$a};function p8(){throw Error("act(...) is not supported in production builds of React.")}I.Children={map:qr,forEach:function(t,e,n){qr(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return qr(t,function(){e++}),e},toArray:function(t){return qr(t,function(e){return e})||[]},only:function(t){if(!Ha(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};I.Component=v1;I.Fragment=q5;I.Profiler=F5;I.PureComponent=Ua;I.StrictMode=j5;I.Suspense=$5;I.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=X5;I.act=p8;I.cloneElement=function(t,e,n){if(t==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var r=a8({},t.props),o=t.key,s=t.ref,i=t._owner;if(e!=null){if(e.ref!==void 0&&(s=e.ref,i=$a.current),e.key!==void 0&&(o=""+e.key),t.type&&t.type.defaultProps)var a=t.type.defaultProps;for(l in e)u8.call(e,l)&&!f8.hasOwnProperty(l)&&(r[l]=e[l]===void 0&&a!==void 0?a[l]:e[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var c=0;c<l;c++)a[c]=arguments[c+2];r.children=a}return{$$typeof:Cr,type:t.type,key:o,ref:s,props:r,_owner:i}};I.createContext=function(t){return t={$$typeof:U5,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},t.Provider={$$typeof:B5,_context:t},t.Consumer=t};I.createElement=d8;I.createFactory=function(t){var e=d8.bind(null,t);return e.type=t,e};I.createRef=function(){return{current:null}};I.forwardRef=function(t){return{$$typeof:z5,render:t}};I.isValidElement=Ha;I.lazy=function(t){return{$$typeof:G5,_payload:{_status:-1,_result:t},_init:Y5}};I.memo=function(t,e){return{$$typeof:H5,type:t,compare:e===void 0?null:e}};I.startTransition=function(t){var e=lo.transition;lo.transition={};try{t()}finally{lo.transition=e}};I.unstable_act=p8;I.useCallback=function(t,e){return kt.current.useCallback(t,e)};I.useContext=function(t){return kt.current.useContext(t)};I.useDebugValue=function(){};I.useDeferredValue=function(t){return kt.current.useDeferredValue(t)};I.useEffect=function(t,e){return kt.current.useEffect(t,e)};I.useId=function(){return kt.current.useId()};I.useImperativeHandle=function(t,e,n){return kt.current.useImperativeHandle(t,e,n)};I.useInsertionEffect=function(t,e){return kt.current.useInsertionEffect(t,e)};I.useLayoutEffect=function(t,e){return kt.current.useLayoutEffect(t,e)};I.useMemo=function(t,e){return kt.current.useMemo(t,e)};I.useReducer=function(t,e,n){return kt.current.useReducer(t,e,n)};I.useRef=function(t){return kt.current.useRef(t)};I.useState=function(t){return kt.current.useState(t)};I.useSyncExternalStore=function(t,e,n){return kt.current.useSyncExternalStore(t,e,n)};I.useTransition=function(){return kt.current.useTransition()};I.version="18.3.1";s8.exports=I;var T=s8.exports;const $=r8(T);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Z5=T,J5=Symbol.for("react.element"),td=Symbol.for("react.fragment"),ed=Object.prototype.hasOwnProperty,nd=Z5.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,rd={key:!0,ref:!0,__self:!0,__source:!0};function h8(t,e,n){var r,o={},s=null,i=null;n!==void 0&&(s=""+n),e.key!==void 0&&(s=""+e.key),e.ref!==void 0&&(i=e.ref);for(r in e)ed.call(e,r)&&!rd.hasOwnProperty(r)&&(o[r]=e[r]);if(t&&t.defaultProps)for(r in e=t.defaultProps,e)o[r]===void 0&&(o[r]=e[r]);return{$$typeof:J5,type:t,key:s,ref:i,props:o,_owner:nd.current}}as.Fragment=td;as.jsx=h8;as.jsxs=h8;o8.exports=as;var Rn=o8.exports,qt={loading:0,loaded:1,playing:2,paused:3,stopped:4,removed:5},m8={exports:{}},Wt={},g8={exports:{}},v8={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(t){function e(k,N){var R=k.length;k.push(N);t:for(;0<R;){var j=R-1>>>1,V=k[j];if(0<o(V,N))k[j]=N,k[R]=V,R=j;else break t}}function n(k){return k.length===0?null:k[0]}function r(k){if(k.length===0)return null;var N=k[0],R=k.pop();if(R!==N){k[0]=R;t:for(var j=0,V=k.length,Vr=V>>>1;j<Vr;){var an=2*(j+1)-1,js=k[an],ln=an+1,Ir=k[ln];if(0>o(js,R))ln<V&&0>o(Ir,js)?(k[j]=Ir,k[ln]=R,j=ln):(k[j]=js,k[an]=R,j=an);else if(ln<V&&0>o(Ir,R))k[j]=Ir,k[ln]=R,j=ln;else break t}}return N}function o(k,N){var R=k.sortIndex-N.sortIndex;return R!==0?R:k.id-N.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;t.unstable_now=function(){return s.now()}}else{var i=Date,a=i.now();t.unstable_now=function(){return i.now()-a}}var l=[],c=[],u=1,f=null,d=3,m=!1,y=!1,v=!1,x=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,g=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function h(k){for(var N=n(c);N!==null;){if(N.callback===null)r(c);else if(N.startTime<=k)r(c),N.sortIndex=N.expirationTime,e(l,N);else break;N=n(c)}}function w(k){if(v=!1,h(k),!y)if(n(l)!==null)y=!0,Q(S);else{var N=n(c);N!==null&&st(w,N.startTime-k)}}function S(k,N){y=!1,v&&(v=!1,p(_),_=-1),m=!0;var R=d;try{for(h(N),f=n(l);f!==null&&(!(f.expirationTime>N)||k&&!O());){var j=f.callback;if(typeof j=="function"){f.callback=null,d=f.priorityLevel;var V=j(f.expirationTime<=N);N=t.unstable_now(),typeof V=="function"?f.callback=V:f===n(l)&&r(l),h(N)}else r(l);f=n(l)}if(f!==null)var Vr=!0;else{var an=n(c);an!==null&&st(w,an.startTime-N),Vr=!1}return Vr}finally{f=null,d=R,m=!1}}var E=!1,b=null,_=-1,M=5,A=-1;function O(){return!(t.unstable_now()-A<M)}function W(){if(b!==null){var k=t.unstable_now();A=k;var N=!0;try{N=b(!0,k)}finally{N?ut():(E=!1,b=null)}}else E=!1}var ut;if(typeof g=="function")ut=function(){g(W)};else if(typeof MessageChannel<"u"){var Tt=new MessageChannel,B=Tt.port2;Tt.port1.onmessage=W,ut=function(){B.postMessage(null)}}else ut=function(){x(W,0)};function Q(k){b=k,E||(E=!0,ut())}function st(k,N){_=x(function(){k(t.unstable_now())},N)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(k){k.callback=null},t.unstable_continueExecution=function(){y||m||(y=!0,Q(S))},t.unstable_forceFrameRate=function(k){0>k||125<k?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):M=0<k?Math.floor(1e3/k):5},t.unstable_getCurrentPriorityLevel=function(){return d},t.unstable_getFirstCallbackNode=function(){return n(l)},t.unstable_next=function(k){switch(d){case 1:case 2:case 3:var N=3;break;default:N=d}var R=d;d=N;try{return k()}finally{d=R}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(k,N){switch(k){case 1:case 2:case 3:case 4:case 5:break;default:k=3}var R=d;d=k;try{return N()}finally{d=R}},t.unstable_scheduleCallback=function(k,N,R){var j=t.unstable_now();switch(typeof R=="object"&&R!==null?(R=R.delay,R=typeof R=="number"&&0<R?j+R:j):R=j,k){case 1:var V=-1;break;case 2:V=250;break;case 5:V=**********;break;case 4:V=1e4;break;default:V=5e3}return V=R+V,k={id:u++,callback:N,priorityLevel:k,startTime:R,expirationTime:V,sortIndex:-1},R>j?(k.sortIndex=R,e(c,k),n(l)===null&&k===n(c)&&(v?(p(_),_=-1):v=!0,st(w,R-j))):(k.sortIndex=V,e(l,k),y||m||(y=!0,Q(S))),k},t.unstable_shouldYield=O,t.unstable_wrapCallback=function(k){var N=d;return function(){var R=d;d=N;try{return k.apply(this,arguments)}finally{d=R}}}})(v8);g8.exports=v8;var od=g8.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sd=T,Ht=od;function C(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var y8=new Set,er={};function kn(t,e){a1(t,e),a1(t+"Capture",e)}function a1(t,e){for(er[t]=e,t=0;t<e.length;t++)y8.add(e[t])}var De=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ki=Object.prototype.hasOwnProperty,id=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Dc={},Ac={};function ad(t){return ki.call(Ac,t)?!0:ki.call(Dc,t)?!1:id.test(t)?Ac[t]=!0:(Dc[t]=!0,!1)}function ld(t,e,n,r){if(n!==null&&n.type===0)return!1;switch(typeof e){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function cd(t,e,n,r){if(e===null||typeof e>"u"||ld(t,e,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!e;case 4:return e===!1;case 5:return isNaN(e);case 6:return isNaN(e)||1>e}return!1}function Dt(t,e,n,r,o,s,i){this.acceptsBooleans=e===2||e===3||e===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=t,this.type=e,this.sanitizeURL=s,this.removeEmptyString=i}var vt={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){vt[t]=new Dt(t,0,!1,t,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var e=t[0];vt[e]=new Dt(e,1,!1,t[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(t){vt[t]=new Dt(t,2,!1,t.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){vt[t]=new Dt(t,2,!1,t,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){vt[t]=new Dt(t,3,!1,t.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(t){vt[t]=new Dt(t,3,!0,t,null,!1,!1)});["capture","download"].forEach(function(t){vt[t]=new Dt(t,4,!1,t,null,!1,!1)});["cols","rows","size","span"].forEach(function(t){vt[t]=new Dt(t,6,!1,t,null,!1,!1)});["rowSpan","start"].forEach(function(t){vt[t]=new Dt(t,5,!1,t.toLowerCase(),null,!1,!1)});var Ga=/[\-:]([a-z])/g;function Wa(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var e=t.replace(Ga,Wa);vt[e]=new Dt(e,1,!1,t,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var e=t.replace(Ga,Wa);vt[e]=new Dt(e,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(t){var e=t.replace(Ga,Wa);vt[e]=new Dt(e,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(t){vt[t]=new Dt(t,1,!1,t.toLowerCase(),null,!1,!1)});vt.xlinkHref=new Dt("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(t){vt[t]=new Dt(t,1,!1,t.toLowerCase(),null,!0,!0)});function Qa(t,e,n,r){var o=vt.hasOwnProperty(e)?vt[e]:null;(o!==null?o.type!==0:r||!(2<e.length)||e[0]!=="o"&&e[0]!=="O"||e[1]!=="n"&&e[1]!=="N")&&(cd(e,n,o,r)&&(n=null),r||o===null?ad(e)&&(n===null?t.removeAttribute(e):t.setAttribute(e,""+n)):o.mustUseProperty?t[o.propertyName]=n===null?o.type===3?!1:"":n:(e=o.attributeName,r=o.attributeNamespace,n===null?t.removeAttribute(e):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?t.setAttributeNS(r,e,n):t.setAttribute(e,n))))}var Ne=sd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,jr=Symbol.for("react.element"),In=Symbol.for("react.portal"),qn=Symbol.for("react.fragment"),Ka=Symbol.for("react.strict_mode"),Di=Symbol.for("react.profiler"),w8=Symbol.for("react.provider"),x8=Symbol.for("react.context"),Ya=Symbol.for("react.forward_ref"),Ai=Symbol.for("react.suspense"),Pi=Symbol.for("react.suspense_list"),Xa=Symbol.for("react.memo"),Ve=Symbol.for("react.lazy"),S8=Symbol.for("react.offscreen"),Pc=Symbol.iterator;function S1(t){return t===null||typeof t!="object"?null:(t=Pc&&t[Pc]||t["@@iterator"],typeof t=="function"?t:null)}var tt=Object.assign,Bs;function N1(t){if(Bs===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);Bs=e&&e[1]||""}return`
`+Bs+t}var Us=!1;function zs(t,e){if(!t||Us)return"";Us=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(e)if(e=function(){throw Error()},Object.defineProperty(e.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(e,[])}catch(c){var r=c}Reflect.construct(t,[],e)}else{try{e.call()}catch(c){r=c}t.call(e.prototype)}else{try{throw Error()}catch(c){r=c}t()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var o=c.stack.split(`
`),s=r.stack.split(`
`),i=o.length-1,a=s.length-1;1<=i&&0<=a&&o[i]!==s[a];)a--;for(;1<=i&&0<=a;i--,a--)if(o[i]!==s[a]){if(i!==1||a!==1)do if(i--,a--,0>a||o[i]!==s[a]){var l=`
`+o[i].replace(" at new "," at ");return t.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",t.displayName)),l}while(1<=i&&0<=a);break}}}finally{Us=!1,Error.prepareStackTrace=n}return(t=t?t.displayName||t.name:"")?N1(t):""}function ud(t){switch(t.tag){case 5:return N1(t.type);case 16:return N1("Lazy");case 13:return N1("Suspense");case 19:return N1("SuspenseList");case 0:case 2:case 15:return t=zs(t.type,!1),t;case 11:return t=zs(t.type.render,!1),t;case 1:return t=zs(t.type,!0),t;default:return""}}function Li(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case qn:return"Fragment";case In:return"Portal";case Di:return"Profiler";case Ka:return"StrictMode";case Ai:return"Suspense";case Pi:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case x8:return(t.displayName||"Context")+".Consumer";case w8:return(t._context.displayName||"Context")+".Provider";case Ya:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Xa:return e=t.displayName||null,e!==null?e:Li(t.type)||"Memo";case Ve:e=t._payload,t=t._init;try{return Li(t(e))}catch{}}return null}function fd(t){var e=t.type;switch(t.tag){case 24:return"Cache";case 9:return(e.displayName||"Context")+".Consumer";case 10:return(e._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=e.render,t=t.displayName||t.name||"",e.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return e;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Li(e);case 8:return e===Ka?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e}return null}function Je(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function T8(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function dd(t){var e=T8(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,s=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return o.call(this)},set:function(i){r=""+i,s.call(this,i)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Fr(t){t._valueTracker||(t._valueTracker=dd(t))}function E8(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),r="";return t&&(r=T8(t)?t.checked?"true":"false":t.value),t=r,t!==n?(e.setValue(t),!0):!1}function Co(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}function Ni(t,e){var n=e.checked;return tt({},e,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n!=null?n:t._wrapperState.initialChecked})}function Lc(t,e){var n=e.defaultValue==null?"":e.defaultValue,r=e.checked!=null?e.checked:e.defaultChecked;n=Je(e.value!=null?e.value:n),t._wrapperState={initialChecked:r,initialValue:n,controlled:e.type==="checkbox"||e.type==="radio"?e.checked!=null:e.value!=null}}function C8(t,e){e=e.checked,e!=null&&Qa(t,"checked",e,!1)}function Ri(t,e){C8(t,e);var n=Je(e.value),r=e.type;if(n!=null)r==="number"?(n===0&&t.value===""||t.value!=n)&&(t.value=""+n):t.value!==""+n&&(t.value=""+n);else if(r==="submit"||r==="reset"){t.removeAttribute("value");return}e.hasOwnProperty("value")?Mi(t,e.type,n):e.hasOwnProperty("defaultValue")&&Mi(t,e.type,Je(e.defaultValue)),e.checked==null&&e.defaultChecked!=null&&(t.defaultChecked=!!e.defaultChecked)}function Nc(t,e,n){if(e.hasOwnProperty("value")||e.hasOwnProperty("defaultValue")){var r=e.type;if(!(r!=="submit"&&r!=="reset"||e.value!==void 0&&e.value!==null))return;e=""+t._wrapperState.initialValue,n||e===t.value||(t.value=e),t.defaultValue=e}n=t.name,n!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,n!==""&&(t.name=n)}function Mi(t,e,n){(e!=="number"||Co(t.ownerDocument)!==t)&&(n==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+n&&(t.defaultValue=""+n))}var R1=Array.isArray;function Jn(t,e,n,r){if(t=t.options,e){e={};for(var o=0;o<n.length;o++)e["$"+n[o]]=!0;for(n=0;n<t.length;n++)o=e.hasOwnProperty("$"+t[n].value),t[n].selected!==o&&(t[n].selected=o),o&&r&&(t[n].defaultSelected=!0)}else{for(n=""+Je(n),e=null,o=0;o<t.length;o++){if(t[o].value===n){t[o].selected=!0,r&&(t[o].defaultSelected=!0);return}e!==null||t[o].disabled||(e=t[o])}e!==null&&(e.selected=!0)}}function Oi(t,e){if(e.dangerouslySetInnerHTML!=null)throw Error(C(91));return tt({},e,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function Rc(t,e){var n=e.value;if(n==null){if(n=e.children,e=e.defaultValue,n!=null){if(e!=null)throw Error(C(92));if(R1(n)){if(1<n.length)throw Error(C(93));n=n[0]}e=n}e==null&&(e=""),n=e}t._wrapperState={initialValue:Je(n)}}function b8(t,e){var n=Je(e.value),r=Je(e.defaultValue);n!=null&&(n=""+n,n!==t.value&&(t.value=n),e.defaultValue==null&&t.defaultValue!==n&&(t.defaultValue=n)),r!=null&&(t.defaultValue=""+r)}function Mc(t){var e=t.textContent;e===t._wrapperState.initialValue&&e!==""&&e!==null&&(t.value=e)}function _8(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Vi(t,e){return t==null||t==="http://www.w3.org/1999/xhtml"?_8(e):t==="http://www.w3.org/2000/svg"&&e==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var Br,k8=function(t){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(e,n,r,o){MSApp.execUnsafeLocalFunction(function(){return t(e,n,r,o)})}:t}(function(t,e){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=e;else{for(Br=Br||document.createElement("div"),Br.innerHTML="<svg>"+e.valueOf().toString()+"</svg>",e=Br.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;e.firstChild;)t.appendChild(e.firstChild)}});function nr(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var q1={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pd=["Webkit","ms","Moz","O"];Object.keys(q1).forEach(function(t){pd.forEach(function(e){e=e+t.charAt(0).toUpperCase()+t.substring(1),q1[e]=q1[t]})});function D8(t,e,n){return e==null||typeof e=="boolean"||e===""?"":n||typeof e!="number"||e===0||q1.hasOwnProperty(t)&&q1[t]?(""+e).trim():e+"px"}function A8(t,e){t=t.style;for(var n in e)if(e.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=D8(n,e[n],r);n==="float"&&(n="cssFloat"),r?t.setProperty(n,o):t[n]=o}}var hd=tt({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ii(t,e){if(e){if(hd[t]&&(e.children!=null||e.dangerouslySetInnerHTML!=null))throw Error(C(137,t));if(e.dangerouslySetInnerHTML!=null){if(e.children!=null)throw Error(C(60));if(typeof e.dangerouslySetInnerHTML!="object"||!("__html"in e.dangerouslySetInnerHTML))throw Error(C(61))}if(e.style!=null&&typeof e.style!="object")throw Error(C(62))}}function qi(t,e){if(t.indexOf("-")===-1)return typeof e.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ji=null;function Za(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Fi=null,t1=null,e1=null;function Oc(t){if(t=kr(t)){if(typeof Fi!="function")throw Error(C(280));var e=t.stateNode;e&&(e=ds(e),Fi(t.stateNode,t.type,e))}}function P8(t){t1?e1?e1.push(t):e1=[t]:t1=t}function L8(){if(t1){var t=t1,e=e1;if(e1=t1=null,Oc(t),e)for(t=0;t<e.length;t++)Oc(e[t])}}function N8(t,e){return t(e)}function R8(){}var $s=!1;function M8(t,e,n){if($s)return t(e,n);$s=!0;try{return N8(t,e,n)}finally{$s=!1,(t1!==null||e1!==null)&&(R8(),L8())}}function rr(t,e){var n=t.stateNode;if(n===null)return null;var r=ds(n);if(r===null)return null;n=r[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(C(231,e,typeof n));return n}var Bi=!1;if(De)try{var T1={};Object.defineProperty(T1,"passive",{get:function(){Bi=!0}}),window.addEventListener("test",T1,T1),window.removeEventListener("test",T1,T1)}catch{Bi=!1}function md(t,e,n,r,o,s,i,a,l){var c=Array.prototype.slice.call(arguments,3);try{e.apply(n,c)}catch(u){this.onError(u)}}var j1=!1,bo=null,_o=!1,Ui=null,gd={onError:function(t){j1=!0,bo=t}};function vd(t,e,n,r,o,s,i,a,l){j1=!1,bo=null,md.apply(gd,arguments)}function yd(t,e,n,r,o,s,i,a,l){if(vd.apply(this,arguments),j1){if(j1){var c=bo;j1=!1,bo=null}else throw Error(C(198));_o||(_o=!0,Ui=c)}}function Dn(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function O8(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function Vc(t){if(Dn(t)!==t)throw Error(C(188))}function wd(t){var e=t.alternate;if(!e){if(e=Dn(t),e===null)throw Error(C(188));return e!==t?null:t}for(var n=t,r=e;;){var o=n.return;if(o===null)break;var s=o.alternate;if(s===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===s.child){for(s=o.child;s;){if(s===n)return Vc(o),t;if(s===r)return Vc(o),e;s=s.sibling}throw Error(C(188))}if(n.return!==r.return)n=o,r=s;else{for(var i=!1,a=o.child;a;){if(a===n){i=!0,n=o,r=s;break}if(a===r){i=!0,r=o,n=s;break}a=a.sibling}if(!i){for(a=s.child;a;){if(a===n){i=!0,n=s,r=o;break}if(a===r){i=!0,r=s,n=o;break}a=a.sibling}if(!i)throw Error(C(189))}}if(n.alternate!==r)throw Error(C(190))}if(n.tag!==3)throw Error(C(188));return n.stateNode.current===n?t:e}function V8(t){return t=wd(t),t!==null?I8(t):null}function I8(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var e=I8(t);if(e!==null)return e;t=t.sibling}return null}var q8=Ht.unstable_scheduleCallback,Ic=Ht.unstable_cancelCallback,xd=Ht.unstable_shouldYield,Sd=Ht.unstable_requestPaint,ot=Ht.unstable_now,Td=Ht.unstable_getCurrentPriorityLevel,Ja=Ht.unstable_ImmediatePriority,j8=Ht.unstable_UserBlockingPriority,ko=Ht.unstable_NormalPriority,Ed=Ht.unstable_LowPriority,F8=Ht.unstable_IdlePriority,ls=null,pe=null;function Cd(t){if(pe&&typeof pe.onCommitFiberRoot=="function")try{pe.onCommitFiberRoot(ls,t,void 0,(t.current.flags&128)===128)}catch{}}var ie=Math.clz32?Math.clz32:kd,bd=Math.log,_d=Math.LN2;function kd(t){return t>>>=0,t===0?32:31-(bd(t)/_d|0)|0}var Ur=64,zr=4194304;function M1(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function Do(t,e){var n=t.pendingLanes;if(n===0)return 0;var r=0,o=t.suspendedLanes,s=t.pingedLanes,i=n&268435455;if(i!==0){var a=i&~o;a!==0?r=M1(a):(s&=i,s!==0&&(r=M1(s)))}else i=n&~o,i!==0?r=M1(i):s!==0&&(r=M1(s));if(r===0)return 0;if(e!==0&&e!==r&&!(e&o)&&(o=r&-r,s=e&-e,o>=s||o===16&&(s&4194240)!==0))return e;if(r&4&&(r|=n&16),e=t.entangledLanes,e!==0)for(t=t.entanglements,e&=r;0<e;)n=31-ie(e),o=1<<n,r|=t[n],e&=~o;return r}function Dd(t,e){switch(t){case 1:case 2:case 4:return e+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ad(t,e){for(var n=t.suspendedLanes,r=t.pingedLanes,o=t.expirationTimes,s=t.pendingLanes;0<s;){var i=31-ie(s),a=1<<i,l=o[i];l===-1?(!(a&n)||a&r)&&(o[i]=Dd(a,e)):l<=e&&(t.expiredLanes|=a),s&=~a}}function zi(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function B8(){var t=Ur;return Ur<<=1,!(Ur&4194240)&&(Ur=64),t}function Hs(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function br(t,e,n){t.pendingLanes|=e,e!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,e=31-ie(e),t[e]=n}function Pd(t,e){var n=t.pendingLanes&~e;t.pendingLanes=e,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=e,t.mutableReadLanes&=e,t.entangledLanes&=e,e=t.entanglements;var r=t.eventTimes;for(t=t.expirationTimes;0<n;){var o=31-ie(n),s=1<<o;e[o]=0,r[o]=-1,t[o]=-1,n&=~s}}function tl(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var r=31-ie(n),o=1<<r;o&e|t[r]&e&&(t[r]|=e),n&=~o}}var U=0;function U8(t){return t&=-t,1<t?4<t?t&268435455?16:536870912:4:1}var z8,el,$8,H8,G8,$i=!1,$r=[],$e=null,He=null,Ge=null,or=new Map,sr=new Map,je=[],Ld="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function qc(t,e){switch(t){case"focusin":case"focusout":$e=null;break;case"dragenter":case"dragleave":He=null;break;case"mouseover":case"mouseout":Ge=null;break;case"pointerover":case"pointerout":or.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":sr.delete(e.pointerId)}}function E1(t,e,n,r,o,s){return t===null||t.nativeEvent!==s?(t={blockedOn:e,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[o]},e!==null&&(e=kr(e),e!==null&&el(e)),t):(t.eventSystemFlags|=r,e=t.targetContainers,o!==null&&e.indexOf(o)===-1&&e.push(o),t)}function Nd(t,e,n,r,o){switch(e){case"focusin":return $e=E1($e,t,e,n,r,o),!0;case"dragenter":return He=E1(He,t,e,n,r,o),!0;case"mouseover":return Ge=E1(Ge,t,e,n,r,o),!0;case"pointerover":var s=o.pointerId;return or.set(s,E1(or.get(s)||null,t,e,n,r,o)),!0;case"gotpointercapture":return s=o.pointerId,sr.set(s,E1(sr.get(s)||null,t,e,n,r,o)),!0}return!1}function W8(t){var e=mn(t.target);if(e!==null){var n=Dn(e);if(n!==null){if(e=n.tag,e===13){if(e=O8(n),e!==null){t.blockedOn=e,G8(t.priority,function(){$8(n)});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function co(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=Hi(t.domEventName,t.eventSystemFlags,e[0],t.nativeEvent);if(n===null){n=t.nativeEvent;var r=new n.constructor(n.type,n);ji=r,n.target.dispatchEvent(r),ji=null}else return e=kr(n),e!==null&&el(e),t.blockedOn=n,!1;e.shift()}return!0}function jc(t,e,n){co(t)&&n.delete(e)}function Rd(){$i=!1,$e!==null&&co($e)&&($e=null),He!==null&&co(He)&&(He=null),Ge!==null&&co(Ge)&&(Ge=null),or.forEach(jc),sr.forEach(jc)}function C1(t,e){t.blockedOn===e&&(t.blockedOn=null,$i||($i=!0,Ht.unstable_scheduleCallback(Ht.unstable_NormalPriority,Rd)))}function ir(t){function e(o){return C1(o,t)}if(0<$r.length){C1($r[0],t);for(var n=1;n<$r.length;n++){var r=$r[n];r.blockedOn===t&&(r.blockedOn=null)}}for($e!==null&&C1($e,t),He!==null&&C1(He,t),Ge!==null&&C1(Ge,t),or.forEach(e),sr.forEach(e),n=0;n<je.length;n++)r=je[n],r.blockedOn===t&&(r.blockedOn=null);for(;0<je.length&&(n=je[0],n.blockedOn===null);)W8(n),n.blockedOn===null&&je.shift()}var n1=Ne.ReactCurrentBatchConfig,Ao=!0;function Md(t,e,n,r){var o=U,s=n1.transition;n1.transition=null;try{U=1,nl(t,e,n,r)}finally{U=o,n1.transition=s}}function Od(t,e,n,r){var o=U,s=n1.transition;n1.transition=null;try{U=4,nl(t,e,n,r)}finally{U=o,n1.transition=s}}function nl(t,e,n,r){if(Ao){var o=Hi(t,e,n,r);if(o===null)ei(t,e,r,Po,n),qc(t,r);else if(Nd(o,t,e,n,r))r.stopPropagation();else if(qc(t,r),e&4&&-1<Ld.indexOf(t)){for(;o!==null;){var s=kr(o);if(s!==null&&z8(s),s=Hi(t,e,n,r),s===null&&ei(t,e,r,Po,n),s===o)break;o=s}o!==null&&r.stopPropagation()}else ei(t,e,r,null,n)}}var Po=null;function Hi(t,e,n,r){if(Po=null,t=Za(r),t=mn(t),t!==null)if(e=Dn(t),e===null)t=null;else if(n=e.tag,n===13){if(t=O8(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null);return Po=t,null}function Q8(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Td()){case Ja:return 1;case j8:return 4;case ko:case Ed:return 16;case F8:return 536870912;default:return 16}default:return 16}}var Be=null,rl=null,uo=null;function K8(){if(uo)return uo;var t,e=rl,n=e.length,r,o="value"in Be?Be.value:Be.textContent,s=o.length;for(t=0;t<n&&e[t]===o[t];t++);var i=n-t;for(r=1;r<=i&&e[n-r]===o[s-r];r++);return uo=o.slice(t,1<r?1-r:void 0)}function fo(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Hr(){return!0}function Fc(){return!1}function Qt(t){function e(n,r,o,s,i){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=s,this.target=i,this.currentTarget=null;for(var a in t)t.hasOwnProperty(a)&&(n=t[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Hr:Fc,this.isPropagationStopped=Fc,this}return tt(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Hr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Hr)},persist:function(){},isPersistent:Hr}),e}var y1={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ol=Qt(y1),_r=tt({},y1,{view:0,detail:0}),Vd=Qt(_r),Gs,Ws,b1,cs=tt({},_r,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:sl,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==b1&&(b1&&t.type==="mousemove"?(Gs=t.screenX-b1.screenX,Ws=t.screenY-b1.screenY):Ws=Gs=0,b1=t),Gs)},movementY:function(t){return"movementY"in t?t.movementY:Ws}}),Bc=Qt(cs),Id=tt({},cs,{dataTransfer:0}),qd=Qt(Id),jd=tt({},_r,{relatedTarget:0}),Qs=Qt(jd),Fd=tt({},y1,{animationName:0,elapsedTime:0,pseudoElement:0}),Bd=Qt(Fd),Ud=tt({},y1,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),zd=Qt(Ud),$d=tt({},y1,{data:0}),Uc=Qt($d),Hd={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Gd={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Wd={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Qd(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Wd[t])?!!e[t]:!1}function sl(){return Qd}var Kd=tt({},_r,{key:function(t){if(t.key){var e=Hd[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=fo(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Gd[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:sl,charCode:function(t){return t.type==="keypress"?fo(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?fo(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Yd=Qt(Kd),Xd=tt({},cs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),zc=Qt(Xd),Zd=tt({},_r,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:sl}),Jd=Qt(Zd),t6=tt({},y1,{propertyName:0,elapsedTime:0,pseudoElement:0}),e6=Qt(t6),n6=tt({},cs,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),r6=Qt(n6),o6=[9,13,27,32],il=De&&"CompositionEvent"in window,F1=null;De&&"documentMode"in document&&(F1=document.documentMode);var s6=De&&"TextEvent"in window&&!F1,Y8=De&&(!il||F1&&8<F1&&11>=F1),$c=" ",Hc=!1;function X8(t,e){switch(t){case"keyup":return o6.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Z8(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var jn=!1;function i6(t,e){switch(t){case"compositionend":return Z8(e);case"keypress":return e.which!==32?null:(Hc=!0,$c);case"textInput":return t=e.data,t===$c&&Hc?null:t;default:return null}}function a6(t,e){if(jn)return t==="compositionend"||!il&&X8(t,e)?(t=K8(),uo=rl=Be=null,jn=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Y8&&e.locale!=="ko"?null:e.data;default:return null}}var l6={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Gc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!l6[t.type]:e==="textarea"}function J8(t,e,n,r){P8(r),e=Lo(e,"onChange"),0<e.length&&(n=new ol("onChange","change",null,n,r),t.push({event:n,listeners:e}))}var B1=null,ar=null;function c6(t){u9(t,0)}function us(t){var e=Un(t);if(E8(e))return t}function u6(t,e){if(t==="change")return e}var t9=!1;if(De){var Ks;if(De){var Ys="oninput"in document;if(!Ys){var Wc=document.createElement("div");Wc.setAttribute("oninput","return;"),Ys=typeof Wc.oninput=="function"}Ks=Ys}else Ks=!1;t9=Ks&&(!document.documentMode||9<document.documentMode)}function Qc(){B1&&(B1.detachEvent("onpropertychange",e9),ar=B1=null)}function e9(t){if(t.propertyName==="value"&&us(ar)){var e=[];J8(e,ar,t,Za(t)),M8(c6,e)}}function f6(t,e,n){t==="focusin"?(Qc(),B1=e,ar=n,B1.attachEvent("onpropertychange",e9)):t==="focusout"&&Qc()}function d6(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return us(ar)}function p6(t,e){if(t==="click")return us(e)}function h6(t,e){if(t==="input"||t==="change")return us(e)}function m6(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var le=typeof Object.is=="function"?Object.is:m6;function lr(t,e){if(le(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!ki.call(e,o)||!le(t[o],e[o]))return!1}return!0}function Kc(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Yc(t,e){var n=Kc(t);t=0;for(var r;n;){if(n.nodeType===3){if(r=t+n.textContent.length,t<=e&&r>=e)return{node:n,offset:e-t};t=r}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=Kc(n)}}function n9(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?n9(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function r9(){for(var t=window,e=Co();e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=Co(t.document)}return e}function al(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}function g6(t){var e=r9(),n=t.focusedElem,r=t.selectionRange;if(e!==n&&n&&n.ownerDocument&&n9(n.ownerDocument.documentElement,n)){if(r!==null&&al(n)){if(e=r.start,t=r.end,t===void 0&&(t=e),"selectionStart"in n)n.selectionStart=e,n.selectionEnd=Math.min(t,n.value.length);else if(t=(e=n.ownerDocument||document)&&e.defaultView||window,t.getSelection){t=t.getSelection();var o=n.textContent.length,s=Math.min(r.start,o);r=r.end===void 0?s:Math.min(r.end,o),!t.extend&&s>r&&(o=r,r=s,s=o),o=Yc(n,s);var i=Yc(n,r);o&&i&&(t.rangeCount!==1||t.anchorNode!==o.node||t.anchorOffset!==o.offset||t.focusNode!==i.node||t.focusOffset!==i.offset)&&(e=e.createRange(),e.setStart(o.node,o.offset),t.removeAllRanges(),s>r?(t.addRange(e),t.extend(i.node,i.offset)):(e.setEnd(i.node,i.offset),t.addRange(e)))}}for(e=[],t=n;t=t.parentNode;)t.nodeType===1&&e.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<e.length;n++)t=e[n],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var v6=De&&"documentMode"in document&&11>=document.documentMode,Fn=null,Gi=null,U1=null,Wi=!1;function Xc(t,e,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Wi||Fn==null||Fn!==Co(r)||(r=Fn,"selectionStart"in r&&al(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),U1&&lr(U1,r)||(U1=r,r=Lo(Gi,"onSelect"),0<r.length&&(e=new ol("onSelect","select",null,e,n),t.push({event:e,listeners:r}),e.target=Fn)))}function Gr(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Bn={animationend:Gr("Animation","AnimationEnd"),animationiteration:Gr("Animation","AnimationIteration"),animationstart:Gr("Animation","AnimationStart"),transitionend:Gr("Transition","TransitionEnd")},Xs={},o9={};De&&(o9=document.createElement("div").style,"AnimationEvent"in window||(delete Bn.animationend.animation,delete Bn.animationiteration.animation,delete Bn.animationstart.animation),"TransitionEvent"in window||delete Bn.transitionend.transition);function fs(t){if(Xs[t])return Xs[t];if(!Bn[t])return t;var e=Bn[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in o9)return Xs[t]=e[n];return t}var s9=fs("animationend"),i9=fs("animationiteration"),a9=fs("animationstart"),l9=fs("transitionend"),c9=new Map,Zc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function rn(t,e){c9.set(t,e),kn(e,[t])}for(var Zs=0;Zs<Zc.length;Zs++){var Js=Zc[Zs],y6=Js.toLowerCase(),w6=Js[0].toUpperCase()+Js.slice(1);rn(y6,"on"+w6)}rn(s9,"onAnimationEnd");rn(i9,"onAnimationIteration");rn(a9,"onAnimationStart");rn("dblclick","onDoubleClick");rn("focusin","onFocus");rn("focusout","onBlur");rn(l9,"onTransitionEnd");a1("onMouseEnter",["mouseout","mouseover"]);a1("onMouseLeave",["mouseout","mouseover"]);a1("onPointerEnter",["pointerout","pointerover"]);a1("onPointerLeave",["pointerout","pointerover"]);kn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));kn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));kn("onBeforeInput",["compositionend","keypress","textInput","paste"]);kn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));kn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));kn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var O1="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),x6=new Set("cancel close invalid load scroll toggle".split(" ").concat(O1));function Jc(t,e,n){var r=t.type||"unknown-event";t.currentTarget=n,yd(r,e,void 0,t),t.currentTarget=null}function u9(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var r=t[n],o=r.event;r=r.listeners;t:{var s=void 0;if(e)for(var i=r.length-1;0<=i;i--){var a=r[i],l=a.instance,c=a.currentTarget;if(a=a.listener,l!==s&&o.isPropagationStopped())break t;Jc(o,a,c),s=l}else for(i=0;i<r.length;i++){if(a=r[i],l=a.instance,c=a.currentTarget,a=a.listener,l!==s&&o.isPropagationStopped())break t;Jc(o,a,c),s=l}}}if(_o)throw t=Ui,_o=!1,Ui=null,t}function K(t,e){var n=e[Zi];n===void 0&&(n=e[Zi]=new Set);var r=t+"__bubble";n.has(r)||(f9(e,t,2,!1),n.add(r))}function ti(t,e,n){var r=0;e&&(r|=4),f9(n,t,r,e)}var Wr="_reactListening"+Math.random().toString(36).slice(2);function cr(t){if(!t[Wr]){t[Wr]=!0,y8.forEach(function(n){n!=="selectionchange"&&(x6.has(n)||ti(n,!1,t),ti(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Wr]||(e[Wr]=!0,ti("selectionchange",!1,e))}}function f9(t,e,n,r){switch(Q8(e)){case 1:var o=Md;break;case 4:o=Od;break;default:o=nl}n=o.bind(null,e,n,t),o=void 0,!Bi||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(o=!0),r?o!==void 0?t.addEventListener(e,n,{capture:!0,passive:o}):t.addEventListener(e,n,!0):o!==void 0?t.addEventListener(e,n,{passive:o}):t.addEventListener(e,n,!1)}function ei(t,e,n,r,o){var s=r;if(!(e&1)&&!(e&2)&&r!==null)t:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(i===4)for(i=r.return;i!==null;){var l=i.tag;if((l===3||l===4)&&(l=i.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;i=i.return}for(;a!==null;){if(i=mn(a),i===null)return;if(l=i.tag,l===5||l===6){r=s=i;continue t}a=a.parentNode}}r=r.return}M8(function(){var c=s,u=Za(n),f=[];t:{var d=c9.get(t);if(d!==void 0){var m=ol,y=t;switch(t){case"keypress":if(fo(n)===0)break t;case"keydown":case"keyup":m=Yd;break;case"focusin":y="focus",m=Qs;break;case"focusout":y="blur",m=Qs;break;case"beforeblur":case"afterblur":m=Qs;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=Bc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=qd;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=Jd;break;case s9:case i9:case a9:m=Bd;break;case l9:m=e6;break;case"scroll":m=Vd;break;case"wheel":m=r6;break;case"copy":case"cut":case"paste":m=zd;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=zc}var v=(e&4)!==0,x=!v&&t==="scroll",p=v?d!==null?d+"Capture":null:d;v=[];for(var g=c,h;g!==null;){h=g;var w=h.stateNode;if(h.tag===5&&w!==null&&(h=w,p!==null&&(w=rr(g,p),w!=null&&v.push(ur(g,w,h)))),x)break;g=g.return}0<v.length&&(d=new m(d,y,null,n,u),f.push({event:d,listeners:v}))}}if(!(e&7)){t:{if(d=t==="mouseover"||t==="pointerover",m=t==="mouseout"||t==="pointerout",d&&n!==ji&&(y=n.relatedTarget||n.fromElement)&&(mn(y)||y[Ae]))break t;if((m||d)&&(d=u.window===u?u:(d=u.ownerDocument)?d.defaultView||d.parentWindow:window,m?(y=n.relatedTarget||n.toElement,m=c,y=y?mn(y):null,y!==null&&(x=Dn(y),y!==x||y.tag!==5&&y.tag!==6)&&(y=null)):(m=null,y=c),m!==y)){if(v=Bc,w="onMouseLeave",p="onMouseEnter",g="mouse",(t==="pointerout"||t==="pointerover")&&(v=zc,w="onPointerLeave",p="onPointerEnter",g="pointer"),x=m==null?d:Un(m),h=y==null?d:Un(y),d=new v(w,g+"leave",m,n,u),d.target=x,d.relatedTarget=h,w=null,mn(u)===c&&(v=new v(p,g+"enter",y,n,u),v.target=h,v.relatedTarget=x,w=v),x=w,m&&y)e:{for(v=m,p=y,g=0,h=v;h;h=Mn(h))g++;for(h=0,w=p;w;w=Mn(w))h++;for(;0<g-h;)v=Mn(v),g--;for(;0<h-g;)p=Mn(p),h--;for(;g--;){if(v===p||p!==null&&v===p.alternate)break e;v=Mn(v),p=Mn(p)}v=null}else v=null;m!==null&&tu(f,d,m,v,!1),y!==null&&x!==null&&tu(f,x,y,v,!0)}}t:{if(d=c?Un(c):window,m=d.nodeName&&d.nodeName.toLowerCase(),m==="select"||m==="input"&&d.type==="file")var S=u6;else if(Gc(d))if(t9)S=h6;else{S=d6;var E=f6}else(m=d.nodeName)&&m.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(S=p6);if(S&&(S=S(t,c))){J8(f,S,n,u);break t}E&&E(t,d,c),t==="focusout"&&(E=d._wrapperState)&&E.controlled&&d.type==="number"&&Mi(d,"number",d.value)}switch(E=c?Un(c):window,t){case"focusin":(Gc(E)||E.contentEditable==="true")&&(Fn=E,Gi=c,U1=null);break;case"focusout":U1=Gi=Fn=null;break;case"mousedown":Wi=!0;break;case"contextmenu":case"mouseup":case"dragend":Wi=!1,Xc(f,n,u);break;case"selectionchange":if(v6)break;case"keydown":case"keyup":Xc(f,n,u)}var b;if(il)t:{switch(t){case"compositionstart":var _="onCompositionStart";break t;case"compositionend":_="onCompositionEnd";break t;case"compositionupdate":_="onCompositionUpdate";break t}_=void 0}else jn?X8(t,n)&&(_="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(_="onCompositionStart");_&&(Y8&&n.locale!=="ko"&&(jn||_!=="onCompositionStart"?_==="onCompositionEnd"&&jn&&(b=K8()):(Be=u,rl="value"in Be?Be.value:Be.textContent,jn=!0)),E=Lo(c,_),0<E.length&&(_=new Uc(_,t,null,n,u),f.push({event:_,listeners:E}),b?_.data=b:(b=Z8(n),b!==null&&(_.data=b)))),(b=s6?i6(t,n):a6(t,n))&&(c=Lo(c,"onBeforeInput"),0<c.length&&(u=new Uc("onBeforeInput","beforeinput",null,n,u),f.push({event:u,listeners:c}),u.data=b))}u9(f,e)})}function ur(t,e,n){return{instance:t,listener:e,currentTarget:n}}function Lo(t,e){for(var n=e+"Capture",r=[];t!==null;){var o=t,s=o.stateNode;o.tag===5&&s!==null&&(o=s,s=rr(t,n),s!=null&&r.unshift(ur(t,s,o)),s=rr(t,e),s!=null&&r.push(ur(t,s,o))),t=t.return}return r}function Mn(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function tu(t,e,n,r,o){for(var s=e._reactName,i=[];n!==null&&n!==r;){var a=n,l=a.alternate,c=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&c!==null&&(a=c,o?(l=rr(n,s),l!=null&&i.unshift(ur(n,l,a))):o||(l=rr(n,s),l!=null&&i.push(ur(n,l,a)))),n=n.return}i.length!==0&&t.push({event:e,listeners:i})}var S6=/\r\n?/g,T6=/\u0000|\uFFFD/g;function eu(t){return(typeof t=="string"?t:""+t).replace(S6,`
`).replace(T6,"")}function Qr(t,e,n){if(e=eu(e),eu(t)!==e&&n)throw Error(C(425))}function No(){}var Qi=null,Ki=null;function Yi(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Xi=typeof setTimeout=="function"?setTimeout:void 0,E6=typeof clearTimeout=="function"?clearTimeout:void 0,nu=typeof Promise=="function"?Promise:void 0,C6=typeof queueMicrotask=="function"?queueMicrotask:typeof nu<"u"?function(t){return nu.resolve(null).then(t).catch(b6)}:Xi;function b6(t){setTimeout(function(){throw t})}function ni(t,e){var n=e,r=0;do{var o=n.nextSibling;if(t.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){t.removeChild(o),ir(e);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);ir(e)}function We(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?")break;if(e==="/$")return null}}return t}function ru(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}var w1=Math.random().toString(36).slice(2),de="__reactFiber$"+w1,fr="__reactProps$"+w1,Ae="__reactContainer$"+w1,Zi="__reactEvents$"+w1,_6="__reactListeners$"+w1,k6="__reactHandles$"+w1;function mn(t){var e=t[de];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Ae]||n[de]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=ru(t);t!==null;){if(n=t[de])return n;t=ru(t)}return e}t=n,n=t.parentNode}return null}function kr(t){return t=t[de]||t[Ae],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function Un(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(C(33))}function ds(t){return t[fr]||null}var Ji=[],zn=-1;function on(t){return{current:t}}function Y(t){0>zn||(t.current=Ji[zn],Ji[zn]=null,zn--)}function G(t,e){zn++,Ji[zn]=t.current,t.current=e}var tn={},St=on(tn),Mt=on(!1),Tn=tn;function l1(t,e){var n=t.type.contextTypes;if(!n)return tn;var r=t.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===e)return r.__reactInternalMemoizedMaskedChildContext;var o={},s;for(s in n)o[s]=e[s];return r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=e,t.__reactInternalMemoizedMaskedChildContext=o),o}function Ot(t){return t=t.childContextTypes,t!=null}function Ro(){Y(Mt),Y(St)}function ou(t,e,n){if(St.current!==tn)throw Error(C(168));G(St,e),G(Mt,n)}function d9(t,e,n){var r=t.stateNode;if(e=e.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in e))throw Error(C(108,fd(t)||"Unknown",o));return tt({},n,r)}function Mo(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||tn,Tn=St.current,G(St,t),G(Mt,Mt.current),!0}function su(t,e,n){var r=t.stateNode;if(!r)throw Error(C(169));n?(t=d9(t,e,Tn),r.__reactInternalMemoizedMergedChildContext=t,Y(Mt),Y(St),G(St,t)):Y(Mt),G(Mt,n)}var Te=null,ps=!1,ri=!1;function p9(t){Te===null?Te=[t]:Te.push(t)}function D6(t){ps=!0,p9(t)}function sn(){if(!ri&&Te!==null){ri=!0;var t=0,e=U;try{var n=Te;for(U=1;t<n.length;t++){var r=n[t];do r=r(!0);while(r!==null)}Te=null,ps=!1}catch(o){throw Te!==null&&(Te=Te.slice(t+1)),q8(Ja,sn),o}finally{U=e,ri=!1}}return null}var $n=[],Hn=0,Oo=null,Vo=0,Kt=[],Yt=0,En=null,Ee=1,Ce="";function fn(t,e){$n[Hn++]=Vo,$n[Hn++]=Oo,Oo=t,Vo=e}function h9(t,e,n){Kt[Yt++]=Ee,Kt[Yt++]=Ce,Kt[Yt++]=En,En=t;var r=Ee;t=Ce;var o=32-ie(r)-1;r&=~(1<<o),n+=1;var s=32-ie(e)+o;if(30<s){var i=o-o%5;s=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Ee=1<<32-ie(e)+o|n<<o|r,Ce=s+t}else Ee=1<<s|n<<o|r,Ce=t}function ll(t){t.return!==null&&(fn(t,1),h9(t,1,0))}function cl(t){for(;t===Oo;)Oo=$n[--Hn],$n[Hn]=null,Vo=$n[--Hn],$n[Hn]=null;for(;t===En;)En=Kt[--Yt],Kt[Yt]=null,Ce=Kt[--Yt],Kt[Yt]=null,Ee=Kt[--Yt],Kt[Yt]=null}var $t=null,zt=null,X=!1,se=null;function m9(t,e){var n=Xt(5,null,null,0);n.elementType="DELETED",n.stateNode=e,n.return=t,e=t.deletions,e===null?(t.deletions=[n],t.flags|=16):e.push(n)}function iu(t,e){switch(t.tag){case 5:var n=t.type;return e=e.nodeType!==1||n.toLowerCase()!==e.nodeName.toLowerCase()?null:e,e!==null?(t.stateNode=e,$t=t,zt=We(e.firstChild),!0):!1;case 6:return e=t.pendingProps===""||e.nodeType!==3?null:e,e!==null?(t.stateNode=e,$t=t,zt=null,!0):!1;case 13:return e=e.nodeType!==8?null:e,e!==null?(n=En!==null?{id:Ee,overflow:Ce}:null,t.memoizedState={dehydrated:e,treeContext:n,retryLane:1073741824},n=Xt(18,null,null,0),n.stateNode=e,n.return=t,t.child=n,$t=t,zt=null,!0):!1;default:return!1}}function ta(t){return(t.mode&1)!==0&&(t.flags&128)===0}function ea(t){if(X){var e=zt;if(e){var n=e;if(!iu(t,e)){if(ta(t))throw Error(C(418));e=We(n.nextSibling);var r=$t;e&&iu(t,e)?m9(r,n):(t.flags=t.flags&-4097|2,X=!1,$t=t)}}else{if(ta(t))throw Error(C(418));t.flags=t.flags&-4097|2,X=!1,$t=t}}}function au(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;$t=t}function Kr(t){if(t!==$t)return!1;if(!X)return au(t),X=!0,!1;var e;if((e=t.tag!==3)&&!(e=t.tag!==5)&&(e=t.type,e=e!=="head"&&e!=="body"&&!Yi(t.type,t.memoizedProps)),e&&(e=zt)){if(ta(t))throw g9(),Error(C(418));for(;e;)m9(t,e),e=We(e.nextSibling)}if(au(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(C(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="/$"){if(e===0){zt=We(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++}t=t.nextSibling}zt=null}}else zt=$t?We(t.stateNode.nextSibling):null;return!0}function g9(){for(var t=zt;t;)t=We(t.nextSibling)}function c1(){zt=$t=null,X=!1}function ul(t){se===null?se=[t]:se.push(t)}var A6=Ne.ReactCurrentBatchConfig;function _1(t,e,n){if(t=n.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(C(309));var r=n.stateNode}if(!r)throw Error(C(147,t));var o=r,s=""+t;return e!==null&&e.ref!==null&&typeof e.ref=="function"&&e.ref._stringRef===s?e.ref:(e=function(i){var a=o.refs;i===null?delete a[s]:a[s]=i},e._stringRef=s,e)}if(typeof t!="string")throw Error(C(284));if(!n._owner)throw Error(C(290,t))}return t}function Yr(t,e){throw t=Object.prototype.toString.call(e),Error(C(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t))}function lu(t){var e=t._init;return e(t._payload)}function v9(t){function e(p,g){if(t){var h=p.deletions;h===null?(p.deletions=[g],p.flags|=16):h.push(g)}}function n(p,g){if(!t)return null;for(;g!==null;)e(p,g),g=g.sibling;return null}function r(p,g){for(p=new Map;g!==null;)g.key!==null?p.set(g.key,g):p.set(g.index,g),g=g.sibling;return p}function o(p,g){return p=Xe(p,g),p.index=0,p.sibling=null,p}function s(p,g,h){return p.index=h,t?(h=p.alternate,h!==null?(h=h.index,h<g?(p.flags|=2,g):h):(p.flags|=2,g)):(p.flags|=1048576,g)}function i(p){return t&&p.alternate===null&&(p.flags|=2),p}function a(p,g,h,w){return g===null||g.tag!==6?(g=ui(h,p.mode,w),g.return=p,g):(g=o(g,h),g.return=p,g)}function l(p,g,h,w){var S=h.type;return S===qn?u(p,g,h.props.children,w,h.key):g!==null&&(g.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Ve&&lu(S)===g.type)?(w=o(g,h.props),w.ref=_1(p,g,h),w.return=p,w):(w=wo(h.type,h.key,h.props,null,p.mode,w),w.ref=_1(p,g,h),w.return=p,w)}function c(p,g,h,w){return g===null||g.tag!==4||g.stateNode.containerInfo!==h.containerInfo||g.stateNode.implementation!==h.implementation?(g=fi(h,p.mode,w),g.return=p,g):(g=o(g,h.children||[]),g.return=p,g)}function u(p,g,h,w,S){return g===null||g.tag!==7?(g=Sn(h,p.mode,w,S),g.return=p,g):(g=o(g,h),g.return=p,g)}function f(p,g,h){if(typeof g=="string"&&g!==""||typeof g=="number")return g=ui(""+g,p.mode,h),g.return=p,g;if(typeof g=="object"&&g!==null){switch(g.$$typeof){case jr:return h=wo(g.type,g.key,g.props,null,p.mode,h),h.ref=_1(p,null,g),h.return=p,h;case In:return g=fi(g,p.mode,h),g.return=p,g;case Ve:var w=g._init;return f(p,w(g._payload),h)}if(R1(g)||S1(g))return g=Sn(g,p.mode,h,null),g.return=p,g;Yr(p,g)}return null}function d(p,g,h,w){var S=g!==null?g.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return S!==null?null:a(p,g,""+h,w);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case jr:return h.key===S?l(p,g,h,w):null;case In:return h.key===S?c(p,g,h,w):null;case Ve:return S=h._init,d(p,g,S(h._payload),w)}if(R1(h)||S1(h))return S!==null?null:u(p,g,h,w,null);Yr(p,h)}return null}function m(p,g,h,w,S){if(typeof w=="string"&&w!==""||typeof w=="number")return p=p.get(h)||null,a(g,p,""+w,S);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case jr:return p=p.get(w.key===null?h:w.key)||null,l(g,p,w,S);case In:return p=p.get(w.key===null?h:w.key)||null,c(g,p,w,S);case Ve:var E=w._init;return m(p,g,h,E(w._payload),S)}if(R1(w)||S1(w))return p=p.get(h)||null,u(g,p,w,S,null);Yr(g,w)}return null}function y(p,g,h,w){for(var S=null,E=null,b=g,_=g=0,M=null;b!==null&&_<h.length;_++){b.index>_?(M=b,b=null):M=b.sibling;var A=d(p,b,h[_],w);if(A===null){b===null&&(b=M);break}t&&b&&A.alternate===null&&e(p,b),g=s(A,g,_),E===null?S=A:E.sibling=A,E=A,b=M}if(_===h.length)return n(p,b),X&&fn(p,_),S;if(b===null){for(;_<h.length;_++)b=f(p,h[_],w),b!==null&&(g=s(b,g,_),E===null?S=b:E.sibling=b,E=b);return X&&fn(p,_),S}for(b=r(p,b);_<h.length;_++)M=m(b,p,_,h[_],w),M!==null&&(t&&M.alternate!==null&&b.delete(M.key===null?_:M.key),g=s(M,g,_),E===null?S=M:E.sibling=M,E=M);return t&&b.forEach(function(O){return e(p,O)}),X&&fn(p,_),S}function v(p,g,h,w){var S=S1(h);if(typeof S!="function")throw Error(C(150));if(h=S.call(h),h==null)throw Error(C(151));for(var E=S=null,b=g,_=g=0,M=null,A=h.next();b!==null&&!A.done;_++,A=h.next()){b.index>_?(M=b,b=null):M=b.sibling;var O=d(p,b,A.value,w);if(O===null){b===null&&(b=M);break}t&&b&&O.alternate===null&&e(p,b),g=s(O,g,_),E===null?S=O:E.sibling=O,E=O,b=M}if(A.done)return n(p,b),X&&fn(p,_),S;if(b===null){for(;!A.done;_++,A=h.next())A=f(p,A.value,w),A!==null&&(g=s(A,g,_),E===null?S=A:E.sibling=A,E=A);return X&&fn(p,_),S}for(b=r(p,b);!A.done;_++,A=h.next())A=m(b,p,_,A.value,w),A!==null&&(t&&A.alternate!==null&&b.delete(A.key===null?_:A.key),g=s(A,g,_),E===null?S=A:E.sibling=A,E=A);return t&&b.forEach(function(W){return e(p,W)}),X&&fn(p,_),S}function x(p,g,h,w){if(typeof h=="object"&&h!==null&&h.type===qn&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case jr:t:{for(var S=h.key,E=g;E!==null;){if(E.key===S){if(S=h.type,S===qn){if(E.tag===7){n(p,E.sibling),g=o(E,h.props.children),g.return=p,p=g;break t}}else if(E.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Ve&&lu(S)===E.type){n(p,E.sibling),g=o(E,h.props),g.ref=_1(p,E,h),g.return=p,p=g;break t}n(p,E);break}else e(p,E);E=E.sibling}h.type===qn?(g=Sn(h.props.children,p.mode,w,h.key),g.return=p,p=g):(w=wo(h.type,h.key,h.props,null,p.mode,w),w.ref=_1(p,g,h),w.return=p,p=w)}return i(p);case In:t:{for(E=h.key;g!==null;){if(g.key===E)if(g.tag===4&&g.stateNode.containerInfo===h.containerInfo&&g.stateNode.implementation===h.implementation){n(p,g.sibling),g=o(g,h.children||[]),g.return=p,p=g;break t}else{n(p,g);break}else e(p,g);g=g.sibling}g=fi(h,p.mode,w),g.return=p,p=g}return i(p);case Ve:return E=h._init,x(p,g,E(h._payload),w)}if(R1(h))return y(p,g,h,w);if(S1(h))return v(p,g,h,w);Yr(p,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,g!==null&&g.tag===6?(n(p,g.sibling),g=o(g,h),g.return=p,p=g):(n(p,g),g=ui(h,p.mode,w),g.return=p,p=g),i(p)):n(p,g)}return x}var u1=v9(!0),y9=v9(!1),Io=on(null),qo=null,Gn=null,fl=null;function dl(){fl=Gn=qo=null}function pl(t){var e=Io.current;Y(Io),t._currentValue=e}function na(t,e,n){for(;t!==null;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,r!==null&&(r.childLanes|=e)):r!==null&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===n)break;t=t.return}}function r1(t,e){qo=t,fl=Gn=null,t=t.dependencies,t!==null&&t.firstContext!==null&&(t.lanes&e&&(Rt=!0),t.firstContext=null)}function Jt(t){var e=t._currentValue;if(fl!==t)if(t={context:t,memoizedValue:e,next:null},Gn===null){if(qo===null)throw Error(C(308));Gn=t,qo.dependencies={lanes:0,firstContext:t}}else Gn=Gn.next=t;return e}var gn=null;function hl(t){gn===null?gn=[t]:gn.push(t)}function w9(t,e,n,r){var o=e.interleaved;return o===null?(n.next=n,hl(e)):(n.next=o.next,o.next=n),e.interleaved=n,Pe(t,r)}function Pe(t,e){t.lanes|=e;var n=t.alternate;for(n!==null&&(n.lanes|=e),n=t,t=t.return;t!==null;)t.childLanes|=e,n=t.alternate,n!==null&&(n.childLanes|=e),n=t,t=t.return;return n.tag===3?n.stateNode:null}var Ie=!1;function ml(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function x9(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function be(t,e){return{eventTime:t,lane:e,tag:0,payload:null,callback:null,next:null}}function Qe(t,e,n){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,F&2){var o=r.pending;return o===null?e.next=e:(e.next=o.next,o.next=e),r.pending=e,Pe(t,n)}return o=r.interleaved,o===null?(e.next=e,hl(r)):(e.next=o.next,o.next=e),r.interleaved=e,Pe(t,n)}function po(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194240)!==0)){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,tl(t,n)}}function cu(t,e){var n=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?o=s=i:s=s.next=i,n=n.next}while(n!==null);s===null?o=s=e:s=s.next=e}else o=s=e;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:s,shared:r.shared,effects:r.effects},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}function jo(t,e,n,r){var o=t.updateQueue;Ie=!1;var s=o.firstBaseUpdate,i=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var l=a,c=l.next;l.next=null,i===null?s=c:i.next=c,i=l;var u=t.alternate;u!==null&&(u=u.updateQueue,a=u.lastBaseUpdate,a!==i&&(a===null?u.firstBaseUpdate=c:a.next=c,u.lastBaseUpdate=l))}if(s!==null){var f=o.baseState;i=0,u=c=l=null,a=s;do{var d=a.lane,m=a.eventTime;if((r&d)===d){u!==null&&(u=u.next={eventTime:m,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});t:{var y=t,v=a;switch(d=e,m=n,v.tag){case 1:if(y=v.payload,typeof y=="function"){f=y.call(m,f,d);break t}f=y;break t;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,d=typeof y=="function"?y.call(m,f,d):y,d==null)break t;f=tt({},f,d);break t;case 2:Ie=!0}}a.callback!==null&&a.lane!==0&&(t.flags|=64,d=o.effects,d===null?o.effects=[a]:d.push(a))}else m={eventTime:m,lane:d,tag:a.tag,payload:a.payload,callback:a.callback,next:null},u===null?(c=u=m,l=f):u=u.next=m,i|=d;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;d=a,a=d.next,d.next=null,o.lastBaseUpdate=d,o.shared.pending=null}}while(!0);if(u===null&&(l=f),o.baseState=l,o.firstBaseUpdate=c,o.lastBaseUpdate=u,e=o.shared.interleaved,e!==null){o=e;do i|=o.lane,o=o.next;while(o!==e)}else s===null&&(o.shared.lanes=0);bn|=i,t.lanes=i,t.memoizedState=f}}function uu(t,e,n){if(t=e.effects,e.effects=null,t!==null)for(e=0;e<t.length;e++){var r=t[e],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(C(191,o));o.call(r)}}}var Dr={},he=on(Dr),dr=on(Dr),pr=on(Dr);function vn(t){if(t===Dr)throw Error(C(174));return t}function gl(t,e){switch(G(pr,e),G(dr,t),G(he,Dr),t=e.nodeType,t){case 9:case 11:e=(e=e.documentElement)?e.namespaceURI:Vi(null,"");break;default:t=t===8?e.parentNode:e,e=t.namespaceURI||null,t=t.tagName,e=Vi(e,t)}Y(he),G(he,e)}function f1(){Y(he),Y(dr),Y(pr)}function S9(t){vn(pr.current);var e=vn(he.current),n=Vi(e,t.type);e!==n&&(G(dr,t),G(he,n))}function vl(t){dr.current===t&&(Y(he),Y(dr))}var Z=on(0);function Fo(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var oi=[];function yl(){for(var t=0;t<oi.length;t++)oi[t]._workInProgressVersionPrimary=null;oi.length=0}var ho=Ne.ReactCurrentDispatcher,si=Ne.ReactCurrentBatchConfig,Cn=0,J=null,lt=null,ft=null,Bo=!1,z1=!1,hr=0,P6=0;function yt(){throw Error(C(321))}function wl(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!le(t[n],e[n]))return!1;return!0}function xl(t,e,n,r,o,s){if(Cn=s,J=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,ho.current=t===null||t.memoizedState===null?M6:O6,t=n(r,o),z1){s=0;do{if(z1=!1,hr=0,25<=s)throw Error(C(301));s+=1,ft=lt=null,e.updateQueue=null,ho.current=V6,t=n(r,o)}while(z1)}if(ho.current=Uo,e=lt!==null&&lt.next!==null,Cn=0,ft=lt=J=null,Bo=!1,e)throw Error(C(300));return t}function Sl(){var t=hr!==0;return hr=0,t}function ue(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ft===null?J.memoizedState=ft=t:ft=ft.next=t,ft}function te(){if(lt===null){var t=J.alternate;t=t!==null?t.memoizedState:null}else t=lt.next;var e=ft===null?J.memoizedState:ft.next;if(e!==null)ft=e,lt=t;else{if(t===null)throw Error(C(310));lt=t,t={memoizedState:lt.memoizedState,baseState:lt.baseState,baseQueue:lt.baseQueue,queue:lt.queue,next:null},ft===null?J.memoizedState=ft=t:ft=ft.next=t}return ft}function mr(t,e){return typeof e=="function"?e(t):e}function ii(t){var e=te(),n=e.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=t;var r=lt,o=r.baseQueue,s=n.pending;if(s!==null){if(o!==null){var i=o.next;o.next=s.next,s.next=i}r.baseQueue=o=s,n.pending=null}if(o!==null){s=o.next,r=r.baseState;var a=i=null,l=null,c=s;do{var u=c.lane;if((Cn&u)===u)l!==null&&(l=l.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:t(r,c.action);else{var f={lane:u,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};l===null?(a=l=f,i=r):l=l.next=f,J.lanes|=u,bn|=u}c=c.next}while(c!==null&&c!==s);l===null?i=r:l.next=a,le(r,e.memoizedState)||(Rt=!0),e.memoizedState=r,e.baseState=i,e.baseQueue=l,n.lastRenderedState=r}if(t=n.interleaved,t!==null){o=t;do s=o.lane,J.lanes|=s,bn|=s,o=o.next;while(o!==t)}else o===null&&(n.lanes=0);return[e.memoizedState,n.dispatch]}function ai(t){var e=te(),n=e.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=t;var r=n.dispatch,o=n.pending,s=e.memoizedState;if(o!==null){n.pending=null;var i=o=o.next;do s=t(s,i.action),i=i.next;while(i!==o);le(s,e.memoizedState)||(Rt=!0),e.memoizedState=s,e.baseQueue===null&&(e.baseState=s),n.lastRenderedState=s}return[s,r]}function T9(){}function E9(t,e){var n=J,r=te(),o=e(),s=!le(r.memoizedState,o);if(s&&(r.memoizedState=o,Rt=!0),r=r.queue,Tl(_9.bind(null,n,r,t),[t]),r.getSnapshot!==e||s||ft!==null&&ft.memoizedState.tag&1){if(n.flags|=2048,gr(9,b9.bind(null,n,r,o,e),void 0,null),pt===null)throw Error(C(349));Cn&30||C9(n,e,o)}return o}function C9(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=J.updateQueue,e===null?(e={lastEffect:null,stores:null},J.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function b9(t,e,n,r){e.value=n,e.getSnapshot=r,k9(e)&&D9(t)}function _9(t,e,n){return n(function(){k9(e)&&D9(t)})}function k9(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!le(t,n)}catch{return!0}}function D9(t){var e=Pe(t,1);e!==null&&ae(e,t,1,-1)}function fu(t){var e=ue();return typeof t=="function"&&(t=t()),e.memoizedState=e.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:mr,lastRenderedState:t},e.queue=t,t=t.dispatch=R6.bind(null,J,t),[e.memoizedState,t]}function gr(t,e,n,r){return t={tag:t,create:e,destroy:n,deps:r,next:null},e=J.updateQueue,e===null?(e={lastEffect:null,stores:null},J.updateQueue=e,e.lastEffect=t.next=t):(n=e.lastEffect,n===null?e.lastEffect=t.next=t:(r=n.next,n.next=t,t.next=r,e.lastEffect=t)),t}function A9(){return te().memoizedState}function mo(t,e,n,r){var o=ue();J.flags|=t,o.memoizedState=gr(1|e,n,void 0,r===void 0?null:r)}function hs(t,e,n,r){var o=te();r=r===void 0?null:r;var s=void 0;if(lt!==null){var i=lt.memoizedState;if(s=i.destroy,r!==null&&wl(r,i.deps)){o.memoizedState=gr(e,n,s,r);return}}J.flags|=t,o.memoizedState=gr(1|e,n,s,r)}function du(t,e){return mo(8390656,8,t,e)}function Tl(t,e){return hs(2048,8,t,e)}function P9(t,e){return hs(4,2,t,e)}function L9(t,e){return hs(4,4,t,e)}function N9(t,e){if(typeof e=="function")return t=t(),e(t),function(){e(null)};if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function R9(t,e,n){return n=n!=null?n.concat([t]):null,hs(4,4,N9.bind(null,e,t),n)}function El(){}function M9(t,e){var n=te();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&wl(e,r[1])?r[0]:(n.memoizedState=[t,e],t)}function O9(t,e){var n=te();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&wl(e,r[1])?r[0]:(t=t(),n.memoizedState=[t,e],t)}function V9(t,e,n){return Cn&21?(le(n,e)||(n=B8(),J.lanes|=n,bn|=n,t.baseState=!0),e):(t.baseState&&(t.baseState=!1,Rt=!0),t.memoizedState=n)}function L6(t,e){var n=U;U=n!==0&&4>n?n:4,t(!0);var r=si.transition;si.transition={};try{t(!1),e()}finally{U=n,si.transition=r}}function I9(){return te().memoizedState}function N6(t,e,n){var r=Ye(t);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},q9(t))j9(e,n);else if(n=w9(t,e,n,r),n!==null){var o=_t();ae(n,t,r,o),F9(n,e,r)}}function R6(t,e,n){var r=Ye(t),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(q9(t))j9(e,o);else{var s=t.alternate;if(t.lanes===0&&(s===null||s.lanes===0)&&(s=e.lastRenderedReducer,s!==null))try{var i=e.lastRenderedState,a=s(i,n);if(o.hasEagerState=!0,o.eagerState=a,le(a,i)){var l=e.interleaved;l===null?(o.next=o,hl(e)):(o.next=l.next,l.next=o),e.interleaved=o;return}}catch{}finally{}n=w9(t,e,o,r),n!==null&&(o=_t(),ae(n,t,r,o),F9(n,e,r))}}function q9(t){var e=t.alternate;return t===J||e!==null&&e===J}function j9(t,e){z1=Bo=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function F9(t,e,n){if(n&4194240){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,tl(t,n)}}var Uo={readContext:Jt,useCallback:yt,useContext:yt,useEffect:yt,useImperativeHandle:yt,useInsertionEffect:yt,useLayoutEffect:yt,useMemo:yt,useReducer:yt,useRef:yt,useState:yt,useDebugValue:yt,useDeferredValue:yt,useTransition:yt,useMutableSource:yt,useSyncExternalStore:yt,useId:yt,unstable_isNewReconciler:!1},M6={readContext:Jt,useCallback:function(t,e){return ue().memoizedState=[t,e===void 0?null:e],t},useContext:Jt,useEffect:du,useImperativeHandle:function(t,e,n){return n=n!=null?n.concat([t]):null,mo(4194308,4,N9.bind(null,e,t),n)},useLayoutEffect:function(t,e){return mo(4194308,4,t,e)},useInsertionEffect:function(t,e){return mo(4,2,t,e)},useMemo:function(t,e){var n=ue();return e=e===void 0?null:e,t=t(),n.memoizedState=[t,e],t},useReducer:function(t,e,n){var r=ue();return e=n!==void 0?n(e):e,r.memoizedState=r.baseState=e,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:e},r.queue=t,t=t.dispatch=N6.bind(null,J,t),[r.memoizedState,t]},useRef:function(t){var e=ue();return t={current:t},e.memoizedState=t},useState:fu,useDebugValue:El,useDeferredValue:function(t){return ue().memoizedState=t},useTransition:function(){var t=fu(!1),e=t[0];return t=L6.bind(null,t[1]),ue().memoizedState=t,[e,t]},useMutableSource:function(){},useSyncExternalStore:function(t,e,n){var r=J,o=ue();if(X){if(n===void 0)throw Error(C(407));n=n()}else{if(n=e(),pt===null)throw Error(C(349));Cn&30||C9(r,e,n)}o.memoizedState=n;var s={value:n,getSnapshot:e};return o.queue=s,du(_9.bind(null,r,s,t),[t]),r.flags|=2048,gr(9,b9.bind(null,r,s,n,e),void 0,null),n},useId:function(){var t=ue(),e=pt.identifierPrefix;if(X){var n=Ce,r=Ee;n=(r&~(1<<32-ie(r)-1)).toString(32)+n,e=":"+e+"R"+n,n=hr++,0<n&&(e+="H"+n.toString(32)),e+=":"}else n=P6++,e=":"+e+"r"+n.toString(32)+":";return t.memoizedState=e},unstable_isNewReconciler:!1},O6={readContext:Jt,useCallback:M9,useContext:Jt,useEffect:Tl,useImperativeHandle:R9,useInsertionEffect:P9,useLayoutEffect:L9,useMemo:O9,useReducer:ii,useRef:A9,useState:function(){return ii(mr)},useDebugValue:El,useDeferredValue:function(t){var e=te();return V9(e,lt.memoizedState,t)},useTransition:function(){var t=ii(mr)[0],e=te().memoizedState;return[t,e]},useMutableSource:T9,useSyncExternalStore:E9,useId:I9,unstable_isNewReconciler:!1},V6={readContext:Jt,useCallback:M9,useContext:Jt,useEffect:Tl,useImperativeHandle:R9,useInsertionEffect:P9,useLayoutEffect:L9,useMemo:O9,useReducer:ai,useRef:A9,useState:function(){return ai(mr)},useDebugValue:El,useDeferredValue:function(t){var e=te();return lt===null?e.memoizedState=t:V9(e,lt.memoizedState,t)},useTransition:function(){var t=ai(mr)[0],e=te().memoizedState;return[t,e]},useMutableSource:T9,useSyncExternalStore:E9,useId:I9,unstable_isNewReconciler:!1};function re(t,e){if(t&&t.defaultProps){e=tt({},e),t=t.defaultProps;for(var n in t)e[n]===void 0&&(e[n]=t[n]);return e}return e}function ra(t,e,n,r){e=t.memoizedState,n=n(r,e),n=n==null?e:tt({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var ms={isMounted:function(t){return(t=t._reactInternals)?Dn(t)===t:!1},enqueueSetState:function(t,e,n){t=t._reactInternals;var r=_t(),o=Ye(t),s=be(r,o);s.payload=e,n!=null&&(s.callback=n),e=Qe(t,s,o),e!==null&&(ae(e,t,o,r),po(e,t,o))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var r=_t(),o=Ye(t),s=be(r,o);s.tag=1,s.payload=e,n!=null&&(s.callback=n),e=Qe(t,s,o),e!==null&&(ae(e,t,o,r),po(e,t,o))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=_t(),r=Ye(t),o=be(n,r);o.tag=2,e!=null&&(o.callback=e),e=Qe(t,o,r),e!==null&&(ae(e,t,r,n),po(e,t,r))}};function pu(t,e,n,r,o,s,i){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,s,i):e.prototype&&e.prototype.isPureReactComponent?!lr(n,r)||!lr(o,s):!0}function B9(t,e,n){var r=!1,o=tn,s=e.contextType;return typeof s=="object"&&s!==null?s=Jt(s):(o=Ot(e)?Tn:St.current,r=e.contextTypes,s=(r=r!=null)?l1(t,o):tn),e=new e(n,s),t.memoizedState=e.state!==null&&e.state!==void 0?e.state:null,e.updater=ms,t.stateNode=e,e._reactInternals=t,r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=o,t.__reactInternalMemoizedMaskedChildContext=s),e}function hu(t,e,n,r){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,r),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,r),e.state!==t&&ms.enqueueReplaceState(e,e.state,null)}function oa(t,e,n,r){var o=t.stateNode;o.props=n,o.state=t.memoizedState,o.refs={},ml(t);var s=e.contextType;typeof s=="object"&&s!==null?o.context=Jt(s):(s=Ot(e)?Tn:St.current,o.context=l1(t,s)),o.state=t.memoizedState,s=e.getDerivedStateFromProps,typeof s=="function"&&(ra(t,e,s,n),o.state=t.memoizedState),typeof e.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(e=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),e!==o.state&&ms.enqueueReplaceState(o,o.state,null),jo(t,n,o,r),o.state=t.memoizedState),typeof o.componentDidMount=="function"&&(t.flags|=4194308)}function d1(t,e){try{var n="",r=e;do n+=ud(r),r=r.return;while(r);var o=n}catch(s){o=`
Error generating stack: `+s.message+`
`+s.stack}return{value:t,source:e,stack:o,digest:null}}function li(t,e,n){return{value:t,source:null,stack:n!=null?n:null,digest:e!=null?e:null}}function sa(t,e){try{console.error(e.value)}catch(n){setTimeout(function(){throw n})}}var I6=typeof WeakMap=="function"?WeakMap:Map;function U9(t,e,n){n=be(-1,n),n.tag=3,n.payload={element:null};var r=e.value;return n.callback=function(){$o||($o=!0,ma=r),sa(t,e)},n}function z9(t,e,n){n=be(-1,n),n.tag=3;var r=t.type.getDerivedStateFromError;if(typeof r=="function"){var o=e.value;n.payload=function(){return r(o)},n.callback=function(){sa(t,e)}}var s=t.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){sa(t,e),typeof r!="function"&&(Ke===null?Ke=new Set([this]):Ke.add(this));var i=e.stack;this.componentDidCatch(e.value,{componentStack:i!==null?i:""})}),n}function mu(t,e,n){var r=t.pingCache;if(r===null){r=t.pingCache=new I6;var o=new Set;r.set(e,o)}else o=r.get(e),o===void 0&&(o=new Set,r.set(e,o));o.has(n)||(o.add(n),t=X6.bind(null,t,e,n),e.then(t,t))}function gu(t){do{var e;if((e=t.tag===13)&&(e=t.memoizedState,e=e!==null?e.dehydrated!==null:!0),e)return t;t=t.return}while(t!==null);return null}function vu(t,e,n,r,o){return t.mode&1?(t.flags|=65536,t.lanes=o,t):(t===e?t.flags|=65536:(t.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(e=be(-1,1),e.tag=2,Qe(n,e,1))),n.lanes|=1),t)}var q6=Ne.ReactCurrentOwner,Rt=!1;function Ct(t,e,n,r){e.child=t===null?y9(e,null,n,r):u1(e,t.child,n,r)}function yu(t,e,n,r,o){n=n.render;var s=e.ref;return r1(e,o),r=xl(t,e,n,r,s,o),n=Sl(),t!==null&&!Rt?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~o,Le(t,e,o)):(X&&n&&ll(e),e.flags|=1,Ct(t,e,r,o),e.child)}function wu(t,e,n,r,o){if(t===null){var s=n.type;return typeof s=="function"&&!Ll(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(e.tag=15,e.type=s,$9(t,e,s,r,o)):(t=wo(n.type,null,r,e,e.mode,o),t.ref=e.ref,t.return=e,e.child=t)}if(s=t.child,!(t.lanes&o)){var i=s.memoizedProps;if(n=n.compare,n=n!==null?n:lr,n(i,r)&&t.ref===e.ref)return Le(t,e,o)}return e.flags|=1,t=Xe(s,r),t.ref=e.ref,t.return=e,e.child=t}function $9(t,e,n,r,o){if(t!==null){var s=t.memoizedProps;if(lr(s,r)&&t.ref===e.ref)if(Rt=!1,e.pendingProps=r=s,(t.lanes&o)!==0)t.flags&131072&&(Rt=!0);else return e.lanes=t.lanes,Le(t,e,o)}return ia(t,e,n,r,o)}function H9(t,e,n){var r=e.pendingProps,o=r.children,s=t!==null?t.memoizedState:null;if(r.mode==="hidden")if(!(e.mode&1))e.memoizedState={baseLanes:0,cachePool:null,transitions:null},G(Qn,jt),jt|=n;else{if(!(n&1073741824))return t=s!==null?s.baseLanes|n:n,e.lanes=e.childLanes=1073741824,e.memoizedState={baseLanes:t,cachePool:null,transitions:null},e.updateQueue=null,G(Qn,jt),jt|=t,null;e.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,G(Qn,jt),jt|=r}else s!==null?(r=s.baseLanes|n,e.memoizedState=null):r=n,G(Qn,jt),jt|=r;return Ct(t,e,o,n),e.child}function G9(t,e){var n=e.ref;(t===null&&n!==null||t!==null&&t.ref!==n)&&(e.flags|=512,e.flags|=2097152)}function ia(t,e,n,r,o){var s=Ot(n)?Tn:St.current;return s=l1(e,s),r1(e,o),n=xl(t,e,n,r,s,o),r=Sl(),t!==null&&!Rt?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~o,Le(t,e,o)):(X&&r&&ll(e),e.flags|=1,Ct(t,e,n,o),e.child)}function xu(t,e,n,r,o){if(Ot(n)){var s=!0;Mo(e)}else s=!1;if(r1(e,o),e.stateNode===null)go(t,e),B9(e,n,r),oa(e,n,r,o),r=!0;else if(t===null){var i=e.stateNode,a=e.memoizedProps;i.props=a;var l=i.context,c=n.contextType;typeof c=="object"&&c!==null?c=Jt(c):(c=Ot(n)?Tn:St.current,c=l1(e,c));var u=n.getDerivedStateFromProps,f=typeof u=="function"||typeof i.getSnapshotBeforeUpdate=="function";f||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==r||l!==c)&&hu(e,i,r,c),Ie=!1;var d=e.memoizedState;i.state=d,jo(e,r,i,o),l=e.memoizedState,a!==r||d!==l||Mt.current||Ie?(typeof u=="function"&&(ra(e,n,u,r),l=e.memoizedState),(a=Ie||pu(e,n,a,r,d,l,c))?(f||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(e.flags|=4194308)):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=l),i.props=r,i.state=l,i.context=c,r=a):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),r=!1)}else{i=e.stateNode,x9(t,e),a=e.memoizedProps,c=e.type===e.elementType?a:re(e.type,a),i.props=c,f=e.pendingProps,d=i.context,l=n.contextType,typeof l=="object"&&l!==null?l=Jt(l):(l=Ot(n)?Tn:St.current,l=l1(e,l));var m=n.getDerivedStateFromProps;(u=typeof m=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==f||d!==l)&&hu(e,i,r,l),Ie=!1,d=e.memoizedState,i.state=d,jo(e,r,i,o);var y=e.memoizedState;a!==f||d!==y||Mt.current||Ie?(typeof m=="function"&&(ra(e,n,m,r),y=e.memoizedState),(c=Ie||pu(e,n,c,r,d,y,l)||!1)?(u||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,y,l),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,y,l)),typeof i.componentDidUpdate=="function"&&(e.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===t.memoizedProps&&d===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&d===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=y),i.props=r,i.state=y,i.context=l,r=c):(typeof i.componentDidUpdate!="function"||a===t.memoizedProps&&d===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&d===t.memoizedState||(e.flags|=1024),r=!1)}return aa(t,e,n,r,s,o)}function aa(t,e,n,r,o,s){G9(t,e);var i=(e.flags&128)!==0;if(!r&&!i)return o&&su(e,n,!1),Le(t,e,s);r=e.stateNode,q6.current=e;var a=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return e.flags|=1,t!==null&&i?(e.child=u1(e,t.child,null,s),e.child=u1(e,null,a,s)):Ct(t,e,a,s),e.memoizedState=r.state,o&&su(e,n,!0),e.child}function W9(t){var e=t.stateNode;e.pendingContext?ou(t,e.pendingContext,e.pendingContext!==e.context):e.context&&ou(t,e.context,!1),gl(t,e.containerInfo)}function Su(t,e,n,r,o){return c1(),ul(o),e.flags|=256,Ct(t,e,n,r),e.child}var la={dehydrated:null,treeContext:null,retryLane:0};function ca(t){return{baseLanes:t,cachePool:null,transitions:null}}function Q9(t,e,n){var r=e.pendingProps,o=Z.current,s=!1,i=(e.flags&128)!==0,a;if((a=i)||(a=t!==null&&t.memoizedState===null?!1:(o&2)!==0),a?(s=!0,e.flags&=-129):(t===null||t.memoizedState!==null)&&(o|=1),G(Z,o&1),t===null)return ea(e),t=e.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?(e.mode&1?t.data==="$!"?e.lanes=8:e.lanes=1073741824:e.lanes=1,null):(i=r.children,t=r.fallback,s?(r=e.mode,s=e.child,i={mode:"hidden",children:i},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=i):s=ys(i,r,0,null),t=Sn(t,r,n,null),s.return=e,t.return=e,s.sibling=t,e.child=s,e.child.memoizedState=ca(n),e.memoizedState=la,t):Cl(e,i));if(o=t.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return j6(t,e,i,r,a,o,n);if(s){s=r.fallback,i=e.mode,o=t.child,a=o.sibling;var l={mode:"hidden",children:r.children};return!(i&1)&&e.child!==o?(r=e.child,r.childLanes=0,r.pendingProps=l,e.deletions=null):(r=Xe(o,l),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?s=Xe(a,s):(s=Sn(s,i,n,null),s.flags|=2),s.return=e,r.return=e,r.sibling=s,e.child=r,r=s,s=e.child,i=t.child.memoizedState,i=i===null?ca(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},s.memoizedState=i,s.childLanes=t.childLanes&~n,e.memoizedState=la,r}return s=t.child,t=s.sibling,r=Xe(s,{mode:"visible",children:r.children}),!(e.mode&1)&&(r.lanes=n),r.return=e,r.sibling=null,t!==null&&(n=e.deletions,n===null?(e.deletions=[t],e.flags|=16):n.push(t)),e.child=r,e.memoizedState=null,r}function Cl(t,e){return e=ys({mode:"visible",children:e},t.mode,0,null),e.return=t,t.child=e}function Xr(t,e,n,r){return r!==null&&ul(r),u1(e,t.child,null,n),t=Cl(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function j6(t,e,n,r,o,s,i){if(n)return e.flags&256?(e.flags&=-257,r=li(Error(C(422))),Xr(t,e,i,r)):e.memoizedState!==null?(e.child=t.child,e.flags|=128,null):(s=r.fallback,o=e.mode,r=ys({mode:"visible",children:r.children},o,0,null),s=Sn(s,o,i,null),s.flags|=2,r.return=e,s.return=e,r.sibling=s,e.child=r,e.mode&1&&u1(e,t.child,null,i),e.child.memoizedState=ca(i),e.memoizedState=la,s);if(!(e.mode&1))return Xr(t,e,i,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(C(419)),r=li(s,r,void 0),Xr(t,e,i,r)}if(a=(i&t.childLanes)!==0,Rt||a){if(r=pt,r!==null){switch(i&-i){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|i)?0:o,o!==0&&o!==s.retryLane&&(s.retryLane=o,Pe(t,o),ae(r,t,o,-1))}return Pl(),r=li(Error(C(421))),Xr(t,e,i,r)}return o.data==="$?"?(e.flags|=128,e.child=t.child,e=Z6.bind(null,t),o._reactRetry=e,null):(t=s.treeContext,zt=We(o.nextSibling),$t=e,X=!0,se=null,t!==null&&(Kt[Yt++]=Ee,Kt[Yt++]=Ce,Kt[Yt++]=En,Ee=t.id,Ce=t.overflow,En=e),e=Cl(e,r.children),e.flags|=4096,e)}function Tu(t,e,n){t.lanes|=e;var r=t.alternate;r!==null&&(r.lanes|=e),na(t.return,e,n)}function ci(t,e,n,r,o){var s=t.memoizedState;s===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(s.isBackwards=e,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=o)}function K9(t,e,n){var r=e.pendingProps,o=r.revealOrder,s=r.tail;if(Ct(t,e,r.children,n),r=Z.current,r&2)r=r&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Tu(t,n,e);else if(t.tag===19)Tu(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}if(G(Z,r),!(e.mode&1))e.memoizedState=null;else switch(o){case"forwards":for(n=e.child,o=null;n!==null;)t=n.alternate,t!==null&&Fo(t)===null&&(o=n),n=n.sibling;n=o,n===null?(o=e.child,e.child=null):(o=n.sibling,n.sibling=null),ci(e,!1,o,n,s);break;case"backwards":for(n=null,o=e.child,e.child=null;o!==null;){if(t=o.alternate,t!==null&&Fo(t)===null){e.child=o;break}t=o.sibling,o.sibling=n,n=o,o=t}ci(e,!0,n,null,s);break;case"together":ci(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function go(t,e){!(e.mode&1)&&t!==null&&(t.alternate=null,e.alternate=null,e.flags|=2)}function Le(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),bn|=e.lanes,!(n&e.childLanes))return null;if(t!==null&&e.child!==t.child)throw Error(C(153));if(e.child!==null){for(t=e.child,n=Xe(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=Xe(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function F6(t,e,n){switch(e.tag){case 3:W9(e),c1();break;case 5:S9(e);break;case 1:Ot(e.type)&&Mo(e);break;case 4:gl(e,e.stateNode.containerInfo);break;case 10:var r=e.type._context,o=e.memoizedProps.value;G(Io,r._currentValue),r._currentValue=o;break;case 13:if(r=e.memoizedState,r!==null)return r.dehydrated!==null?(G(Z,Z.current&1),e.flags|=128,null):n&e.child.childLanes?Q9(t,e,n):(G(Z,Z.current&1),t=Le(t,e,n),t!==null?t.sibling:null);G(Z,Z.current&1);break;case 19:if(r=(n&e.childLanes)!==0,t.flags&128){if(r)return K9(t,e,n);e.flags|=128}if(o=e.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),G(Z,Z.current),r)break;return null;case 22:case 23:return e.lanes=0,H9(t,e,n)}return Le(t,e,n)}var Y9,ua,X9,Z9;Y9=function(t,e){for(var n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ua=function(){};X9=function(t,e,n,r){var o=t.memoizedProps;if(o!==r){t=e.stateNode,vn(he.current);var s=null;switch(n){case"input":o=Ni(t,o),r=Ni(t,r),s=[];break;case"select":o=tt({},o,{value:void 0}),r=tt({},r,{value:void 0}),s=[];break;case"textarea":o=Oi(t,o),r=Oi(t,r),s=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(t.onclick=No)}Ii(n,r);var i;n=null;for(c in o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&o[c]!=null)if(c==="style"){var a=o[c];for(i in a)a.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(er.hasOwnProperty(c)?s||(s=[]):(s=s||[]).push(c,null));for(c in r){var l=r[c];if(a=o!=null?o[c]:void 0,r.hasOwnProperty(c)&&l!==a&&(l!=null||a!=null))if(c==="style")if(a){for(i in a)!a.hasOwnProperty(i)||l&&l.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in l)l.hasOwnProperty(i)&&a[i]!==l[i]&&(n||(n={}),n[i]=l[i])}else n||(s||(s=[]),s.push(c,n)),n=l;else c==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(s=s||[]).push(c,l)):c==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(c,""+l):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(er.hasOwnProperty(c)?(l!=null&&c==="onScroll"&&K("scroll",t),s||a===l||(s=[])):(s=s||[]).push(c,l))}n&&(s=s||[]).push("style",n);var c=s;(e.updateQueue=c)&&(e.flags|=4)}};Z9=function(t,e,n,r){n!==r&&(e.flags|=4)};function k1(t,e){if(!X)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function wt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,r=0;if(e)for(var o=t.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=t,o=o.sibling;else for(o=t.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=t,o=o.sibling;return t.subtreeFlags|=r,t.childLanes=n,e}function B6(t,e,n){var r=e.pendingProps;switch(cl(e),e.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return wt(e),null;case 1:return Ot(e.type)&&Ro(),wt(e),null;case 3:return r=e.stateNode,f1(),Y(Mt),Y(St),yl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(t===null||t.child===null)&&(Kr(e)?e.flags|=4:t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,se!==null&&(ya(se),se=null))),ua(t,e),wt(e),null;case 5:vl(e);var o=vn(pr.current);if(n=e.type,t!==null&&e.stateNode!=null)X9(t,e,n,r,o),t.ref!==e.ref&&(e.flags|=512,e.flags|=2097152);else{if(!r){if(e.stateNode===null)throw Error(C(166));return wt(e),null}if(t=vn(he.current),Kr(e)){r=e.stateNode,n=e.type;var s=e.memoizedProps;switch(r[de]=e,r[fr]=s,t=(e.mode&1)!==0,n){case"dialog":K("cancel",r),K("close",r);break;case"iframe":case"object":case"embed":K("load",r);break;case"video":case"audio":for(o=0;o<O1.length;o++)K(O1[o],r);break;case"source":K("error",r);break;case"img":case"image":case"link":K("error",r),K("load",r);break;case"details":K("toggle",r);break;case"input":Lc(r,s),K("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},K("invalid",r);break;case"textarea":Rc(r,s),K("invalid",r)}Ii(n,s),o=null;for(var i in s)if(s.hasOwnProperty(i)){var a=s[i];i==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&Qr(r.textContent,a,t),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&Qr(r.textContent,a,t),o=["children",""+a]):er.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&K("scroll",r)}switch(n){case"input":Fr(r),Nc(r,s,!0);break;case"textarea":Fr(r),Mc(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=No)}r=o,e.updateQueue=r,r!==null&&(e.flags|=4)}else{i=o.nodeType===9?o:o.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=_8(n)),t==="http://www.w3.org/1999/xhtml"?n==="script"?(t=i.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof r.is=="string"?t=i.createElement(n,{is:r.is}):(t=i.createElement(n),n==="select"&&(i=t,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):t=i.createElementNS(t,n),t[de]=e,t[fr]=r,Y9(t,e,!1,!1),e.stateNode=t;t:{switch(i=qi(n,r),n){case"dialog":K("cancel",t),K("close",t),o=r;break;case"iframe":case"object":case"embed":K("load",t),o=r;break;case"video":case"audio":for(o=0;o<O1.length;o++)K(O1[o],t);o=r;break;case"source":K("error",t),o=r;break;case"img":case"image":case"link":K("error",t),K("load",t),o=r;break;case"details":K("toggle",t),o=r;break;case"input":Lc(t,r),o=Ni(t,r),K("invalid",t);break;case"option":o=r;break;case"select":t._wrapperState={wasMultiple:!!r.multiple},o=tt({},r,{value:void 0}),K("invalid",t);break;case"textarea":Rc(t,r),o=Oi(t,r),K("invalid",t);break;default:o=r}Ii(n,o),a=o;for(s in a)if(a.hasOwnProperty(s)){var l=a[s];s==="style"?A8(t,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&k8(t,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&nr(t,l):typeof l=="number"&&nr(t,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(er.hasOwnProperty(s)?l!=null&&s==="onScroll"&&K("scroll",t):l!=null&&Qa(t,s,l,i))}switch(n){case"input":Fr(t),Nc(t,r,!1);break;case"textarea":Fr(t),Mc(t);break;case"option":r.value!=null&&t.setAttribute("value",""+Je(r.value));break;case"select":t.multiple=!!r.multiple,s=r.value,s!=null?Jn(t,!!r.multiple,s,!1):r.defaultValue!=null&&Jn(t,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(t.onclick=No)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break t;case"img":r=!0;break t;default:r=!1}}r&&(e.flags|=4)}e.ref!==null&&(e.flags|=512,e.flags|=2097152)}return wt(e),null;case 6:if(t&&e.stateNode!=null)Z9(t,e,t.memoizedProps,r);else{if(typeof r!="string"&&e.stateNode===null)throw Error(C(166));if(n=vn(pr.current),vn(he.current),Kr(e)){if(r=e.stateNode,n=e.memoizedProps,r[de]=e,(s=r.nodeValue!==n)&&(t=$t,t!==null))switch(t.tag){case 3:Qr(r.nodeValue,n,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&Qr(r.nodeValue,n,(t.mode&1)!==0)}s&&(e.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[de]=e,e.stateNode=r}return wt(e),null;case 13:if(Y(Z),r=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(X&&zt!==null&&e.mode&1&&!(e.flags&128))g9(),c1(),e.flags|=98560,s=!1;else if(s=Kr(e),r!==null&&r.dehydrated!==null){if(t===null){if(!s)throw Error(C(318));if(s=e.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(C(317));s[de]=e}else c1(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;wt(e),s=!1}else se!==null&&(ya(se),se=null),s=!0;if(!s)return e.flags&65536?e:null}return e.flags&128?(e.lanes=n,e):(r=r!==null,r!==(t!==null&&t.memoizedState!==null)&&r&&(e.child.flags|=8192,e.mode&1&&(t===null||Z.current&1?ct===0&&(ct=3):Pl())),e.updateQueue!==null&&(e.flags|=4),wt(e),null);case 4:return f1(),ua(t,e),t===null&&cr(e.stateNode.containerInfo),wt(e),null;case 10:return pl(e.type._context),wt(e),null;case 17:return Ot(e.type)&&Ro(),wt(e),null;case 19:if(Y(Z),s=e.memoizedState,s===null)return wt(e),null;if(r=(e.flags&128)!==0,i=s.rendering,i===null)if(r)k1(s,!1);else{if(ct!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(i=Fo(t),i!==null){for(e.flags|=128,k1(s,!1),r=i.updateQueue,r!==null&&(e.updateQueue=r,e.flags|=4),e.subtreeFlags=0,r=n,n=e.child;n!==null;)s=n,t=r,s.flags&=14680066,i=s.alternate,i===null?(s.childLanes=0,s.lanes=t,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=i.childLanes,s.lanes=i.lanes,s.child=i.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=i.memoizedProps,s.memoizedState=i.memoizedState,s.updateQueue=i.updateQueue,s.type=i.type,t=i.dependencies,s.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),n=n.sibling;return G(Z,Z.current&1|2),e.child}t=t.sibling}s.tail!==null&&ot()>p1&&(e.flags|=128,r=!0,k1(s,!1),e.lanes=4194304)}else{if(!r)if(t=Fo(i),t!==null){if(e.flags|=128,r=!0,n=t.updateQueue,n!==null&&(e.updateQueue=n,e.flags|=4),k1(s,!0),s.tail===null&&s.tailMode==="hidden"&&!i.alternate&&!X)return wt(e),null}else 2*ot()-s.renderingStartTime>p1&&n!==1073741824&&(e.flags|=128,r=!0,k1(s,!1),e.lanes=4194304);s.isBackwards?(i.sibling=e.child,e.child=i):(n=s.last,n!==null?n.sibling=i:e.child=i,s.last=i)}return s.tail!==null?(e=s.tail,s.rendering=e,s.tail=e.sibling,s.renderingStartTime=ot(),e.sibling=null,n=Z.current,G(Z,r?n&1|2:n&1),e):(wt(e),null);case 22:case 23:return Al(),r=e.memoizedState!==null,t!==null&&t.memoizedState!==null!==r&&(e.flags|=8192),r&&e.mode&1?jt&1073741824&&(wt(e),e.subtreeFlags&6&&(e.flags|=8192)):wt(e),null;case 24:return null;case 25:return null}throw Error(C(156,e.tag))}function U6(t,e){switch(cl(e),e.tag){case 1:return Ot(e.type)&&Ro(),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return f1(),Y(Mt),Y(St),yl(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 5:return vl(e),null;case 13:if(Y(Z),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(C(340));c1()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Y(Z),null;case 4:return f1(),null;case 10:return pl(e.type._context),null;case 22:case 23:return Al(),null;case 24:return null;default:return null}}var Zr=!1,xt=!1,z6=typeof WeakSet=="function"?WeakSet:Set,D=null;function Wn(t,e){var n=t.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){et(t,e,r)}else n.current=null}function fa(t,e,n){try{n()}catch(r){et(t,e,r)}}var Eu=!1;function $6(t,e){if(Qi=Ao,t=r9(),al(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break t}var i=0,a=-1,l=-1,c=0,u=0,f=t,d=null;e:for(;;){for(var m;f!==n||o!==0&&f.nodeType!==3||(a=i+o),f!==s||r!==0&&f.nodeType!==3||(l=i+r),f.nodeType===3&&(i+=f.nodeValue.length),(m=f.firstChild)!==null;)d=f,f=m;for(;;){if(f===t)break e;if(d===n&&++c===o&&(a=i),d===s&&++u===r&&(l=i),(m=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=m}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ki={focusedElem:t,selectionRange:n},Ao=!1,D=e;D!==null;)if(e=D,t=e.child,(e.subtreeFlags&1028)!==0&&t!==null)t.return=e,D=t;else for(;D!==null;){e=D;try{var y=e.alternate;if(e.flags&1024)switch(e.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var v=y.memoizedProps,x=y.memoizedState,p=e.stateNode,g=p.getSnapshotBeforeUpdate(e.elementType===e.type?v:re(e.type,v),x);p.__reactInternalSnapshotBeforeUpdate=g}break;case 3:var h=e.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(C(163))}}catch(w){et(e,e.return,w)}if(t=e.sibling,t!==null){t.return=e.return,D=t;break}D=e.return}return y=Eu,Eu=!1,y}function $1(t,e,n){var r=e.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&t)===t){var s=o.destroy;o.destroy=void 0,s!==void 0&&fa(e,n,s)}o=o.next}while(o!==r)}}function gs(t,e){if(e=e.updateQueue,e=e!==null?e.lastEffect:null,e!==null){var n=e=e.next;do{if((n.tag&t)===t){var r=n.create;n.destroy=r()}n=n.next}while(n!==e)}}function da(t){var e=t.ref;if(e!==null){var n=t.stateNode;switch(t.tag){case 5:t=n;break;default:t=n}typeof e=="function"?e(t):e.current=t}}function J9(t){var e=t.alternate;e!==null&&(t.alternate=null,J9(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&(delete e[de],delete e[fr],delete e[Zi],delete e[_6],delete e[k6])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function tf(t){return t.tag===5||t.tag===3||t.tag===4}function Cu(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||tf(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function pa(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.nodeType===8?n.parentNode.insertBefore(t,e):n.insertBefore(t,e):(n.nodeType===8?(e=n.parentNode,e.insertBefore(t,n)):(e=n,e.appendChild(t)),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=No));else if(r!==4&&(t=t.child,t!==null))for(pa(t,e,n),t=t.sibling;t!==null;)pa(t,e,n),t=t.sibling}function ha(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(r!==4&&(t=t.child,t!==null))for(ha(t,e,n),t=t.sibling;t!==null;)ha(t,e,n),t=t.sibling}var ht=null,oe=!1;function Me(t,e,n){for(n=n.child;n!==null;)ef(t,e,n),n=n.sibling}function ef(t,e,n){if(pe&&typeof pe.onCommitFiberUnmount=="function")try{pe.onCommitFiberUnmount(ls,n)}catch{}switch(n.tag){case 5:xt||Wn(n,e);case 6:var r=ht,o=oe;ht=null,Me(t,e,n),ht=r,oe=o,ht!==null&&(oe?(t=ht,n=n.stateNode,t.nodeType===8?t.parentNode.removeChild(n):t.removeChild(n)):ht.removeChild(n.stateNode));break;case 18:ht!==null&&(oe?(t=ht,n=n.stateNode,t.nodeType===8?ni(t.parentNode,n):t.nodeType===1&&ni(t,n),ir(t)):ni(ht,n.stateNode));break;case 4:r=ht,o=oe,ht=n.stateNode.containerInfo,oe=!0,Me(t,e,n),ht=r,oe=o;break;case 0:case 11:case 14:case 15:if(!xt&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var s=o,i=s.destroy;s=s.tag,i!==void 0&&(s&2||s&4)&&fa(n,e,i),o=o.next}while(o!==r)}Me(t,e,n);break;case 1:if(!xt&&(Wn(n,e),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){et(n,e,a)}Me(t,e,n);break;case 21:Me(t,e,n);break;case 22:n.mode&1?(xt=(r=xt)||n.memoizedState!==null,Me(t,e,n),xt=r):Me(t,e,n);break;default:Me(t,e,n)}}function bu(t){var e=t.updateQueue;if(e!==null){t.updateQueue=null;var n=t.stateNode;n===null&&(n=t.stateNode=new z6),e.forEach(function(r){var o=J6.bind(null,t,r);n.has(r)||(n.add(r),r.then(o,o))})}}function ee(t,e){var n=e.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var s=t,i=e,a=i;t:for(;a!==null;){switch(a.tag){case 5:ht=a.stateNode,oe=!1;break t;case 3:ht=a.stateNode.containerInfo,oe=!0;break t;case 4:ht=a.stateNode.containerInfo,oe=!0;break t}a=a.return}if(ht===null)throw Error(C(160));ef(s,i,o),ht=null,oe=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(c){et(o,e,c)}}if(e.subtreeFlags&12854)for(e=e.child;e!==null;)nf(e,t),e=e.sibling}function nf(t,e){var n=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(ee(e,t),ce(t),r&4){try{$1(3,t,t.return),gs(3,t)}catch(v){et(t,t.return,v)}try{$1(5,t,t.return)}catch(v){et(t,t.return,v)}}break;case 1:ee(e,t),ce(t),r&512&&n!==null&&Wn(n,n.return);break;case 5:if(ee(e,t),ce(t),r&512&&n!==null&&Wn(n,n.return),t.flags&32){var o=t.stateNode;try{nr(o,"")}catch(v){et(t,t.return,v)}}if(r&4&&(o=t.stateNode,o!=null)){var s=t.memoizedProps,i=n!==null?n.memoizedProps:s,a=t.type,l=t.updateQueue;if(t.updateQueue=null,l!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&C8(o,s),qi(a,i);var c=qi(a,s);for(i=0;i<l.length;i+=2){var u=l[i],f=l[i+1];u==="style"?A8(o,f):u==="dangerouslySetInnerHTML"?k8(o,f):u==="children"?nr(o,f):Qa(o,u,f,c)}switch(a){case"input":Ri(o,s);break;case"textarea":b8(o,s);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!s.multiple;var m=s.value;m!=null?Jn(o,!!s.multiple,m,!1):d!==!!s.multiple&&(s.defaultValue!=null?Jn(o,!!s.multiple,s.defaultValue,!0):Jn(o,!!s.multiple,s.multiple?[]:"",!1))}o[fr]=s}catch(v){et(t,t.return,v)}}break;case 6:if(ee(e,t),ce(t),r&4){if(t.stateNode===null)throw Error(C(162));o=t.stateNode,s=t.memoizedProps;try{o.nodeValue=s}catch(v){et(t,t.return,v)}}break;case 3:if(ee(e,t),ce(t),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ir(e.containerInfo)}catch(v){et(t,t.return,v)}break;case 4:ee(e,t),ce(t);break;case 13:ee(e,t),ce(t),o=t.child,o.flags&8192&&(s=o.memoizedState!==null,o.stateNode.isHidden=s,!s||o.alternate!==null&&o.alternate.memoizedState!==null||(kl=ot())),r&4&&bu(t);break;case 22:if(u=n!==null&&n.memoizedState!==null,t.mode&1?(xt=(c=xt)||u,ee(e,t),xt=c):ee(e,t),ce(t),r&8192){if(c=t.memoizedState!==null,(t.stateNode.isHidden=c)&&!u&&t.mode&1)for(D=t,u=t.child;u!==null;){for(f=D=u;D!==null;){switch(d=D,m=d.child,d.tag){case 0:case 11:case 14:case 15:$1(4,d,d.return);break;case 1:Wn(d,d.return);var y=d.stateNode;if(typeof y.componentWillUnmount=="function"){r=d,n=d.return;try{e=r,y.props=e.memoizedProps,y.state=e.memoizedState,y.componentWillUnmount()}catch(v){et(r,n,v)}}break;case 5:Wn(d,d.return);break;case 22:if(d.memoizedState!==null){ku(f);continue}}m!==null?(m.return=d,D=m):ku(f)}u=u.sibling}t:for(u=null,f=t;;){if(f.tag===5){if(u===null){u=f;try{o=f.stateNode,c?(s=o.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=f.stateNode,l=f.memoizedProps.style,i=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=D8("display",i))}catch(v){et(t,t.return,v)}}}else if(f.tag===6){if(u===null)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(v){et(t,t.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===t)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===t)break t;for(;f.sibling===null;){if(f.return===null||f.return===t)break t;u===f&&(u=null),f=f.return}u===f&&(u=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ee(e,t),ce(t),r&4&&bu(t);break;case 21:break;default:ee(e,t),ce(t)}}function ce(t){var e=t.flags;if(e&2){try{t:{for(var n=t.return;n!==null;){if(tf(n)){var r=n;break t}n=n.return}throw Error(C(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(nr(o,""),r.flags&=-33);var s=Cu(t);ha(t,s,o);break;case 3:case 4:var i=r.stateNode.containerInfo,a=Cu(t);pa(t,a,i);break;default:throw Error(C(161))}}catch(l){et(t,t.return,l)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function H6(t,e,n){D=t,rf(t)}function rf(t,e,n){for(var r=(t.mode&1)!==0;D!==null;){var o=D,s=o.child;if(o.tag===22&&r){var i=o.memoizedState!==null||Zr;if(!i){var a=o.alternate,l=a!==null&&a.memoizedState!==null||xt;a=Zr;var c=xt;if(Zr=i,(xt=l)&&!c)for(D=o;D!==null;)i=D,l=i.child,i.tag===22&&i.memoizedState!==null?Du(o):l!==null?(l.return=i,D=l):Du(o);for(;s!==null;)D=s,rf(s),s=s.sibling;D=o,Zr=a,xt=c}_u(t)}else o.subtreeFlags&8772&&s!==null?(s.return=o,D=s):_u(t)}}function _u(t){for(;D!==null;){var e=D;if(e.flags&8772){var n=e.alternate;try{if(e.flags&8772)switch(e.tag){case 0:case 11:case 15:xt||gs(5,e);break;case 1:var r=e.stateNode;if(e.flags&4&&!xt)if(n===null)r.componentDidMount();else{var o=e.elementType===e.type?n.memoizedProps:re(e.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=e.updateQueue;s!==null&&uu(e,s,r);break;case 3:var i=e.updateQueue;if(i!==null){if(n=null,e.child!==null)switch(e.child.tag){case 5:n=e.child.stateNode;break;case 1:n=e.child.stateNode}uu(e,i,n)}break;case 5:var a=e.stateNode;if(n===null&&e.flags&4){n=a;var l=e.memoizedProps;switch(e.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(e.memoizedState===null){var c=e.alternate;if(c!==null){var u=c.memoizedState;if(u!==null){var f=u.dehydrated;f!==null&&ir(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(C(163))}xt||e.flags&512&&da(e)}catch(d){et(e,e.return,d)}}if(e===t){D=null;break}if(n=e.sibling,n!==null){n.return=e.return,D=n;break}D=e.return}}function ku(t){for(;D!==null;){var e=D;if(e===t){D=null;break}var n=e.sibling;if(n!==null){n.return=e.return,D=n;break}D=e.return}}function Du(t){for(;D!==null;){var e=D;try{switch(e.tag){case 0:case 11:case 15:var n=e.return;try{gs(4,e)}catch(l){et(e,n,l)}break;case 1:var r=e.stateNode;if(typeof r.componentDidMount=="function"){var o=e.return;try{r.componentDidMount()}catch(l){et(e,o,l)}}var s=e.return;try{da(e)}catch(l){et(e,s,l)}break;case 5:var i=e.return;try{da(e)}catch(l){et(e,i,l)}}}catch(l){et(e,e.return,l)}if(e===t){D=null;break}var a=e.sibling;if(a!==null){a.return=e.return,D=a;break}D=e.return}}var G6=Math.ceil,zo=Ne.ReactCurrentDispatcher,bl=Ne.ReactCurrentOwner,Zt=Ne.ReactCurrentBatchConfig,F=0,pt=null,it=null,gt=0,jt=0,Qn=on(0),ct=0,vr=null,bn=0,vs=0,_l=0,H1=null,Lt=null,kl=0,p1=1/0,Se=null,$o=!1,ma=null,Ke=null,Jr=!1,Ue=null,Ho=0,G1=0,ga=null,vo=-1,yo=0;function _t(){return F&6?ot():vo!==-1?vo:vo=ot()}function Ye(t){return t.mode&1?F&2&&gt!==0?gt&-gt:A6.transition!==null?(yo===0&&(yo=B8()),yo):(t=U,t!==0||(t=window.event,t=t===void 0?16:Q8(t.type)),t):1}function ae(t,e,n,r){if(50<G1)throw G1=0,ga=null,Error(C(185));br(t,n,r),(!(F&2)||t!==pt)&&(t===pt&&(!(F&2)&&(vs|=n),ct===4&&Fe(t,gt)),Vt(t,r),n===1&&F===0&&!(e.mode&1)&&(p1=ot()+500,ps&&sn()))}function Vt(t,e){var n=t.callbackNode;Ad(t,e);var r=Do(t,t===pt?gt:0);if(r===0)n!==null&&Ic(n),t.callbackNode=null,t.callbackPriority=0;else if(e=r&-r,t.callbackPriority!==e){if(n!=null&&Ic(n),e===1)t.tag===0?D6(Au.bind(null,t)):p9(Au.bind(null,t)),C6(function(){!(F&6)&&sn()}),n=null;else{switch(U8(r)){case 1:n=Ja;break;case 4:n=j8;break;case 16:n=ko;break;case 536870912:n=F8;break;default:n=ko}n=df(n,of.bind(null,t))}t.callbackPriority=e,t.callbackNode=n}}function of(t,e){if(vo=-1,yo=0,F&6)throw Error(C(327));var n=t.callbackNode;if(o1()&&t.callbackNode!==n)return null;var r=Do(t,t===pt?gt:0);if(r===0)return null;if(r&30||r&t.expiredLanes||e)e=Go(t,r);else{e=r;var o=F;F|=2;var s=af();(pt!==t||gt!==e)&&(Se=null,p1=ot()+500,xn(t,e));do try{K6();break}catch(a){sf(t,a)}while(!0);dl(),zo.current=s,F=o,it!==null?e=0:(pt=null,gt=0,e=ct)}if(e!==0){if(e===2&&(o=zi(t),o!==0&&(r=o,e=va(t,o))),e===1)throw n=vr,xn(t,0),Fe(t,r),Vt(t,ot()),n;if(e===6)Fe(t,r);else{if(o=t.current.alternate,!(r&30)&&!W6(o)&&(e=Go(t,r),e===2&&(s=zi(t),s!==0&&(r=s,e=va(t,s))),e===1))throw n=vr,xn(t,0),Fe(t,r),Vt(t,ot()),n;switch(t.finishedWork=o,t.finishedLanes=r,e){case 0:case 1:throw Error(C(345));case 2:dn(t,Lt,Se);break;case 3:if(Fe(t,r),(r&130023424)===r&&(e=kl+500-ot(),10<e)){if(Do(t,0)!==0)break;if(o=t.suspendedLanes,(o&r)!==r){_t(),t.pingedLanes|=t.suspendedLanes&o;break}t.timeoutHandle=Xi(dn.bind(null,t,Lt,Se),e);break}dn(t,Lt,Se);break;case 4:if(Fe(t,r),(r&4194240)===r)break;for(e=t.eventTimes,o=-1;0<r;){var i=31-ie(r);s=1<<i,i=e[i],i>o&&(o=i),r&=~s}if(r=o,r=ot()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*G6(r/1960))-r,10<r){t.timeoutHandle=Xi(dn.bind(null,t,Lt,Se),r);break}dn(t,Lt,Se);break;case 5:dn(t,Lt,Se);break;default:throw Error(C(329))}}}return Vt(t,ot()),t.callbackNode===n?of.bind(null,t):null}function va(t,e){var n=H1;return t.current.memoizedState.isDehydrated&&(xn(t,e).flags|=256),t=Go(t,e),t!==2&&(e=Lt,Lt=n,e!==null&&ya(e)),t}function ya(t){Lt===null?Lt=t:Lt.push.apply(Lt,t)}function W6(t){for(var e=t;;){if(e.flags&16384){var n=e.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],s=o.getSnapshot;o=o.value;try{if(!le(s(),o))return!1}catch{return!1}}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Fe(t,e){for(e&=~_l,e&=~vs,t.suspendedLanes|=e,t.pingedLanes&=~e,t=t.expirationTimes;0<e;){var n=31-ie(e),r=1<<n;t[n]=-1,e&=~r}}function Au(t){if(F&6)throw Error(C(327));o1();var e=Do(t,0);if(!(e&1))return Vt(t,ot()),null;var n=Go(t,e);if(t.tag!==0&&n===2){var r=zi(t);r!==0&&(e=r,n=va(t,r))}if(n===1)throw n=vr,xn(t,0),Fe(t,e),Vt(t,ot()),n;if(n===6)throw Error(C(345));return t.finishedWork=t.current.alternate,t.finishedLanes=e,dn(t,Lt,Se),Vt(t,ot()),null}function Dl(t,e){var n=F;F|=1;try{return t(e)}finally{F=n,F===0&&(p1=ot()+500,ps&&sn())}}function _n(t){Ue!==null&&Ue.tag===0&&!(F&6)&&o1();var e=F;F|=1;var n=Zt.transition,r=U;try{if(Zt.transition=null,U=1,t)return t()}finally{U=r,Zt.transition=n,F=e,!(F&6)&&sn()}}function Al(){jt=Qn.current,Y(Qn)}function xn(t,e){t.finishedWork=null,t.finishedLanes=0;var n=t.timeoutHandle;if(n!==-1&&(t.timeoutHandle=-1,E6(n)),it!==null)for(n=it.return;n!==null;){var r=n;switch(cl(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ro();break;case 3:f1(),Y(Mt),Y(St),yl();break;case 5:vl(r);break;case 4:f1();break;case 13:Y(Z);break;case 19:Y(Z);break;case 10:pl(r.type._context);break;case 22:case 23:Al()}n=n.return}if(pt=t,it=t=Xe(t.current,null),gt=jt=e,ct=0,vr=null,_l=vs=bn=0,Lt=H1=null,gn!==null){for(e=0;e<gn.length;e++)if(n=gn[e],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,s=n.pending;if(s!==null){var i=s.next;s.next=o,r.next=i}n.pending=r}gn=null}return t}function sf(t,e){do{var n=it;try{if(dl(),ho.current=Uo,Bo){for(var r=J.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Bo=!1}if(Cn=0,ft=lt=J=null,z1=!1,hr=0,bl.current=null,n===null||n.return===null){ct=1,vr=e,it=null;break}t:{var s=t,i=n.return,a=n,l=e;if(e=gt,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var c=l,u=a,f=u.tag;if(!(u.mode&1)&&(f===0||f===11||f===15)){var d=u.alternate;d?(u.updateQueue=d.updateQueue,u.memoizedState=d.memoizedState,u.lanes=d.lanes):(u.updateQueue=null,u.memoizedState=null)}var m=gu(i);if(m!==null){m.flags&=-257,vu(m,i,a,s,e),m.mode&1&&mu(s,c,e),e=m,l=c;var y=e.updateQueue;if(y===null){var v=new Set;v.add(l),e.updateQueue=v}else y.add(l);break t}else{if(!(e&1)){mu(s,c,e),Pl();break t}l=Error(C(426))}}else if(X&&a.mode&1){var x=gu(i);if(x!==null){!(x.flags&65536)&&(x.flags|=256),vu(x,i,a,s,e),ul(d1(l,a));break t}}s=l=d1(l,a),ct!==4&&(ct=2),H1===null?H1=[s]:H1.push(s),s=i;do{switch(s.tag){case 3:s.flags|=65536,e&=-e,s.lanes|=e;var p=U9(s,l,e);cu(s,p);break t;case 1:a=l;var g=s.type,h=s.stateNode;if(!(s.flags&128)&&(typeof g.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(Ke===null||!Ke.has(h)))){s.flags|=65536,e&=-e,s.lanes|=e;var w=z9(s,a,e);cu(s,w);break t}}s=s.return}while(s!==null)}cf(n)}catch(S){e=S,it===n&&n!==null&&(it=n=n.return);continue}break}while(!0)}function af(){var t=zo.current;return zo.current=Uo,t===null?Uo:t}function Pl(){(ct===0||ct===3||ct===2)&&(ct=4),pt===null||!(bn&268435455)&&!(vs&268435455)||Fe(pt,gt)}function Go(t,e){var n=F;F|=2;var r=af();(pt!==t||gt!==e)&&(Se=null,xn(t,e));do try{Q6();break}catch(o){sf(t,o)}while(!0);if(dl(),F=n,zo.current=r,it!==null)throw Error(C(261));return pt=null,gt=0,ct}function Q6(){for(;it!==null;)lf(it)}function K6(){for(;it!==null&&!xd();)lf(it)}function lf(t){var e=ff(t.alternate,t,jt);t.memoizedProps=t.pendingProps,e===null?cf(t):it=e,bl.current=null}function cf(t){var e=t;do{var n=e.alternate;if(t=e.return,e.flags&32768){if(n=U6(n,e),n!==null){n.flags&=32767,it=n;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{ct=6,it=null;return}}else if(n=B6(n,e,jt),n!==null){it=n;return}if(e=e.sibling,e!==null){it=e;return}it=e=t}while(e!==null);ct===0&&(ct=5)}function dn(t,e,n){var r=U,o=Zt.transition;try{Zt.transition=null,U=1,Y6(t,e,n,r)}finally{Zt.transition=o,U=r}return null}function Y6(t,e,n,r){do o1();while(Ue!==null);if(F&6)throw Error(C(327));n=t.finishedWork;var o=t.finishedLanes;if(n===null)return null;if(t.finishedWork=null,t.finishedLanes=0,n===t.current)throw Error(C(177));t.callbackNode=null,t.callbackPriority=0;var s=n.lanes|n.childLanes;if(Pd(t,s),t===pt&&(it=pt=null,gt=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Jr||(Jr=!0,df(ko,function(){return o1(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=Zt.transition,Zt.transition=null;var i=U;U=1;var a=F;F|=4,bl.current=null,$6(t,n),nf(n,t),g6(Ki),Ao=!!Qi,Ki=Qi=null,t.current=n,H6(n),Sd(),F=a,U=i,Zt.transition=s}else t.current=n;if(Jr&&(Jr=!1,Ue=t,Ho=o),s=t.pendingLanes,s===0&&(Ke=null),Cd(n.stateNode),Vt(t,ot()),e!==null)for(r=t.onRecoverableError,n=0;n<e.length;n++)o=e[n],r(o.value,{componentStack:o.stack,digest:o.digest});if($o)throw $o=!1,t=ma,ma=null,t;return Ho&1&&t.tag!==0&&o1(),s=t.pendingLanes,s&1?t===ga?G1++:(G1=0,ga=t):G1=0,sn(),null}function o1(){if(Ue!==null){var t=U8(Ho),e=Zt.transition,n=U;try{if(Zt.transition=null,U=16>t?16:t,Ue===null)var r=!1;else{if(t=Ue,Ue=null,Ho=0,F&6)throw Error(C(331));var o=F;for(F|=4,D=t.current;D!==null;){var s=D,i=s.child;if(D.flags&16){var a=s.deletions;if(a!==null){for(var l=0;l<a.length;l++){var c=a[l];for(D=c;D!==null;){var u=D;switch(u.tag){case 0:case 11:case 15:$1(8,u,s)}var f=u.child;if(f!==null)f.return=u,D=f;else for(;D!==null;){u=D;var d=u.sibling,m=u.return;if(J9(u),u===c){D=null;break}if(d!==null){d.return=m,D=d;break}D=m}}}var y=s.alternate;if(y!==null){var v=y.child;if(v!==null){y.child=null;do{var x=v.sibling;v.sibling=null,v=x}while(v!==null)}}D=s}}if(s.subtreeFlags&2064&&i!==null)i.return=s,D=i;else t:for(;D!==null;){if(s=D,s.flags&2048)switch(s.tag){case 0:case 11:case 15:$1(9,s,s.return)}var p=s.sibling;if(p!==null){p.return=s.return,D=p;break t}D=s.return}}var g=t.current;for(D=g;D!==null;){i=D;var h=i.child;if(i.subtreeFlags&2064&&h!==null)h.return=i,D=h;else t:for(i=g;D!==null;){if(a=D,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:gs(9,a)}}catch(S){et(a,a.return,S)}if(a===i){D=null;break t}var w=a.sibling;if(w!==null){w.return=a.return,D=w;break t}D=a.return}}if(F=o,sn(),pe&&typeof pe.onPostCommitFiberRoot=="function")try{pe.onPostCommitFiberRoot(ls,t)}catch{}r=!0}return r}finally{U=n,Zt.transition=e}}return!1}function Pu(t,e,n){e=d1(n,e),e=U9(t,e,1),t=Qe(t,e,1),e=_t(),t!==null&&(br(t,1,e),Vt(t,e))}function et(t,e,n){if(t.tag===3)Pu(t,t,n);else for(;e!==null;){if(e.tag===3){Pu(e,t,n);break}else if(e.tag===1){var r=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Ke===null||!Ke.has(r))){t=d1(n,t),t=z9(e,t,1),e=Qe(e,t,1),t=_t(),e!==null&&(br(e,1,t),Vt(e,t));break}}e=e.return}}function X6(t,e,n){var r=t.pingCache;r!==null&&r.delete(e),e=_t(),t.pingedLanes|=t.suspendedLanes&n,pt===t&&(gt&n)===n&&(ct===4||ct===3&&(gt&130023424)===gt&&500>ot()-kl?xn(t,0):_l|=n),Vt(t,e)}function uf(t,e){e===0&&(t.mode&1?(e=zr,zr<<=1,!(zr&130023424)&&(zr=4194304)):e=1);var n=_t();t=Pe(t,e),t!==null&&(br(t,e,n),Vt(t,n))}function Z6(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),uf(t,n)}function J6(t,e){var n=0;switch(t.tag){case 13:var r=t.stateNode,o=t.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=t.stateNode;break;default:throw Error(C(314))}r!==null&&r.delete(e),uf(t,n)}var ff;ff=function(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps||Mt.current)Rt=!0;else{if(!(t.lanes&n)&&!(e.flags&128))return Rt=!1,F6(t,e,n);Rt=!!(t.flags&131072)}else Rt=!1,X&&e.flags&1048576&&h9(e,Vo,e.index);switch(e.lanes=0,e.tag){case 2:var r=e.type;go(t,e),t=e.pendingProps;var o=l1(e,St.current);r1(e,n),o=xl(null,e,r,t,o,n);var s=Sl();return e.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(e.tag=1,e.memoizedState=null,e.updateQueue=null,Ot(r)?(s=!0,Mo(e)):s=!1,e.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,ml(e),o.updater=ms,e.stateNode=o,o._reactInternals=e,oa(e,r,t,n),e=aa(null,e,r,!0,s,n)):(e.tag=0,X&&s&&ll(e),Ct(null,e,o,n),e=e.child),e;case 16:r=e.elementType;t:{switch(go(t,e),t=e.pendingProps,o=r._init,r=o(r._payload),e.type=r,o=e.tag=e7(r),t=re(r,t),o){case 0:e=ia(null,e,r,t,n);break t;case 1:e=xu(null,e,r,t,n);break t;case 11:e=yu(null,e,r,t,n);break t;case 14:e=wu(null,e,r,re(r.type,t),n);break t}throw Error(C(306,r,""))}return e;case 0:return r=e.type,o=e.pendingProps,o=e.elementType===r?o:re(r,o),ia(t,e,r,o,n);case 1:return r=e.type,o=e.pendingProps,o=e.elementType===r?o:re(r,o),xu(t,e,r,o,n);case 3:t:{if(W9(e),t===null)throw Error(C(387));r=e.pendingProps,s=e.memoizedState,o=s.element,x9(t,e),jo(e,r,null,n);var i=e.memoizedState;if(r=i.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},e.updateQueue.baseState=s,e.memoizedState=s,e.flags&256){o=d1(Error(C(423)),e),e=Su(t,e,r,n,o);break t}else if(r!==o){o=d1(Error(C(424)),e),e=Su(t,e,r,n,o);break t}else for(zt=We(e.stateNode.containerInfo.firstChild),$t=e,X=!0,se=null,n=y9(e,null,r,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(c1(),r===o){e=Le(t,e,n);break t}Ct(t,e,r,n)}e=e.child}return e;case 5:return S9(e),t===null&&ea(e),r=e.type,o=e.pendingProps,s=t!==null?t.memoizedProps:null,i=o.children,Yi(r,o)?i=null:s!==null&&Yi(r,s)&&(e.flags|=32),G9(t,e),Ct(t,e,i,n),e.child;case 6:return t===null&&ea(e),null;case 13:return Q9(t,e,n);case 4:return gl(e,e.stateNode.containerInfo),r=e.pendingProps,t===null?e.child=u1(e,null,r,n):Ct(t,e,r,n),e.child;case 11:return r=e.type,o=e.pendingProps,o=e.elementType===r?o:re(r,o),yu(t,e,r,o,n);case 7:return Ct(t,e,e.pendingProps,n),e.child;case 8:return Ct(t,e,e.pendingProps.children,n),e.child;case 12:return Ct(t,e,e.pendingProps.children,n),e.child;case 10:t:{if(r=e.type._context,o=e.pendingProps,s=e.memoizedProps,i=o.value,G(Io,r._currentValue),r._currentValue=i,s!==null)if(le(s.value,i)){if(s.children===o.children&&!Mt.current){e=Le(t,e,n);break t}}else for(s=e.child,s!==null&&(s.return=e);s!==null;){var a=s.dependencies;if(a!==null){i=s.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=be(-1,n&-n),l.tag=2;var c=s.updateQueue;if(c!==null){c=c.shared;var u=c.pending;u===null?l.next=l:(l.next=u.next,u.next=l),c.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),na(s.return,n,e),a.lanes|=n;break}l=l.next}}else if(s.tag===10)i=s.type===e.type?null:s.child;else if(s.tag===18){if(i=s.return,i===null)throw Error(C(341));i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),na(i,n,e),i=s.sibling}else i=s.child;if(i!==null)i.return=s;else for(i=s;i!==null;){if(i===e){i=null;break}if(s=i.sibling,s!==null){s.return=i.return,i=s;break}i=i.return}s=i}Ct(t,e,o.children,n),e=e.child}return e;case 9:return o=e.type,r=e.pendingProps.children,r1(e,n),o=Jt(o),r=r(o),e.flags|=1,Ct(t,e,r,n),e.child;case 14:return r=e.type,o=re(r,e.pendingProps),o=re(r.type,o),wu(t,e,r,o,n);case 15:return $9(t,e,e.type,e.pendingProps,n);case 17:return r=e.type,o=e.pendingProps,o=e.elementType===r?o:re(r,o),go(t,e),e.tag=1,Ot(r)?(t=!0,Mo(e)):t=!1,r1(e,n),B9(e,r,o),oa(e,r,o,n),aa(null,e,r,!0,t,n);case 19:return K9(t,e,n);case 22:return H9(t,e,n)}throw Error(C(156,e.tag))};function df(t,e){return q8(t,e)}function t7(t,e,n,r){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Xt(t,e,n,r){return new t7(t,e,n,r)}function Ll(t){return t=t.prototype,!(!t||!t.isReactComponent)}function e7(t){if(typeof t=="function")return Ll(t)?1:0;if(t!=null){if(t=t.$$typeof,t===Ya)return 11;if(t===Xa)return 14}return 2}function Xe(t,e){var n=t.alternate;return n===null?(n=Xt(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&14680064,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n}function wo(t,e,n,r,o,s){var i=2;if(r=t,typeof t=="function")Ll(t)&&(i=1);else if(typeof t=="string")i=5;else t:switch(t){case qn:return Sn(n.children,o,s,e);case Ka:i=8,o|=8;break;case Di:return t=Xt(12,n,e,o|2),t.elementType=Di,t.lanes=s,t;case Ai:return t=Xt(13,n,e,o),t.elementType=Ai,t.lanes=s,t;case Pi:return t=Xt(19,n,e,o),t.elementType=Pi,t.lanes=s,t;case S8:return ys(n,o,s,e);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case w8:i=10;break t;case x8:i=9;break t;case Ya:i=11;break t;case Xa:i=14;break t;case Ve:i=16,r=null;break t}throw Error(C(130,t==null?t:typeof t,""))}return e=Xt(i,n,e,o),e.elementType=t,e.type=r,e.lanes=s,e}function Sn(t,e,n,r){return t=Xt(7,t,r,e),t.lanes=n,t}function ys(t,e,n,r){return t=Xt(22,t,r,e),t.elementType=S8,t.lanes=n,t.stateNode={isHidden:!1},t}function ui(t,e,n){return t=Xt(6,t,null,e),t.lanes=n,t}function fi(t,e,n){return e=Xt(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function n7(t,e,n,r,o){this.tag=e,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Hs(0),this.expirationTimes=Hs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Hs(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Nl(t,e,n,r,o,s,i,a,l){return t=new n7(t,e,n,a,l),e===1?(e=1,s===!0&&(e|=8)):e=0,s=Xt(3,null,null,e),t.current=s,s.stateNode=t,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},ml(s),t}function r7(t,e,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:In,key:r==null?null:""+r,children:t,containerInfo:e,implementation:n}}function pf(t){if(!t)return tn;t=t._reactInternals;t:{if(Dn(t)!==t||t.tag!==1)throw Error(C(170));var e=t;do{switch(e.tag){case 3:e=e.stateNode.context;break t;case 1:if(Ot(e.type)){e=e.stateNode.__reactInternalMemoizedMergedChildContext;break t}}e=e.return}while(e!==null);throw Error(C(171))}if(t.tag===1){var n=t.type;if(Ot(n))return d9(t,n,e)}return e}function hf(t,e,n,r,o,s,i,a,l){return t=Nl(n,r,!0,t,o,s,i,a,l),t.context=pf(null),n=t.current,r=_t(),o=Ye(n),s=be(r,o),s.callback=e!=null?e:null,Qe(n,s,o),t.current.lanes=o,br(t,o,r),Vt(t,r),t}function ws(t,e,n,r){var o=e.current,s=_t(),i=Ye(o);return n=pf(n),e.context===null?e.context=n:e.pendingContext=n,e=be(s,i),e.payload={element:t},r=r===void 0?null:r,r!==null&&(e.callback=r),t=Qe(o,e,i),t!==null&&(ae(t,o,i,s),po(t,o,i)),i}function Wo(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function Lu(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function Rl(t,e){Lu(t,e),(t=t.alternate)&&Lu(t,e)}function o7(){return null}var mf=typeof reportError=="function"?reportError:function(t){console.error(t)};function Ml(t){this._internalRoot=t}xs.prototype.render=Ml.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(C(409));ws(t,e,null,null)};xs.prototype.unmount=Ml.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;_n(function(){ws(null,t,null,null)}),e[Ae]=null}};function xs(t){this._internalRoot=t}xs.prototype.unstable_scheduleHydration=function(t){if(t){var e=H8();t={blockedOn:null,target:t,priority:e};for(var n=0;n<je.length&&e!==0&&e<je[n].priority;n++);je.splice(n,0,t),n===0&&W8(t)}};function Ol(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function Ss(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function Nu(){}function s7(t,e,n,r,o){if(o){if(typeof r=="function"){var s=r;r=function(){var c=Wo(i);s.call(c)}}var i=hf(e,r,t,0,null,!1,!1,"",Nu);return t._reactRootContainer=i,t[Ae]=i.current,cr(t.nodeType===8?t.parentNode:t),_n(),i}for(;o=t.lastChild;)t.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var c=Wo(l);a.call(c)}}var l=Nl(t,0,!1,null,null,!1,!1,"",Nu);return t._reactRootContainer=l,t[Ae]=l.current,cr(t.nodeType===8?t.parentNode:t),_n(function(){ws(e,l,n,r)}),l}function Ts(t,e,n,r,o){var s=n._reactRootContainer;if(s){var i=s;if(typeof o=="function"){var a=o;o=function(){var l=Wo(i);a.call(l)}}ws(e,i,t,o)}else i=s7(n,e,t,o,r);return Wo(i)}z8=function(t){switch(t.tag){case 3:var e=t.stateNode;if(e.current.memoizedState.isDehydrated){var n=M1(e.pendingLanes);n!==0&&(tl(e,n|1),Vt(e,ot()),!(F&6)&&(p1=ot()+500,sn()))}break;case 13:_n(function(){var r=Pe(t,1);if(r!==null){var o=_t();ae(r,t,1,o)}}),Rl(t,1)}};el=function(t){if(t.tag===13){var e=Pe(t,134217728);if(e!==null){var n=_t();ae(e,t,134217728,n)}Rl(t,134217728)}};$8=function(t){if(t.tag===13){var e=Ye(t),n=Pe(t,e);if(n!==null){var r=_t();ae(n,t,e,r)}Rl(t,e)}};H8=function(){return U};G8=function(t,e){var n=U;try{return U=t,e()}finally{U=n}};Fi=function(t,e,n){switch(e){case"input":if(Ri(t,n),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+e)+'][type="radio"]'),e=0;e<n.length;e++){var r=n[e];if(r!==t&&r.form===t.form){var o=ds(r);if(!o)throw Error(C(90));E8(r),Ri(r,o)}}}break;case"textarea":b8(t,n);break;case"select":e=n.value,e!=null&&Jn(t,!!n.multiple,e,!1)}};N8=Dl;R8=_n;var i7={usingClientEntryPoint:!1,Events:[kr,Un,ds,P8,L8,Dl]},D1={findFiberByHostInstance:mn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},a7={bundleType:D1.bundleType,version:D1.version,rendererPackageName:D1.rendererPackageName,rendererConfig:D1.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Ne.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=V8(t),t===null?null:t.stateNode},findFiberByHostInstance:D1.findFiberByHostInstance||o7,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var to=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!to.isDisabled&&to.supportsFiber)try{ls=to.inject(a7),pe=to}catch{}}Wt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=i7;Wt.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ol(e))throw Error(C(200));return r7(t,e,null,n)};Wt.createRoot=function(t,e){if(!Ol(t))throw Error(C(299));var n=!1,r="",o=mf;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(r=e.identifierPrefix),e.onRecoverableError!==void 0&&(o=e.onRecoverableError)),e=Nl(t,1,!1,null,null,n,!1,r,o),t[Ae]=e.current,cr(t.nodeType===8?t.parentNode:t),new Ml(e)};Wt.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(C(188)):(t=Object.keys(t).join(","),Error(C(268,t)));return t=V8(e),t=t===null?null:t.stateNode,t};Wt.flushSync=function(t){return _n(t)};Wt.hydrate=function(t,e,n){if(!Ss(e))throw Error(C(200));return Ts(null,t,e,!0,n)};Wt.hydrateRoot=function(t,e,n){if(!Ol(t))throw Error(C(405));var r=n!=null&&n.hydratedSources||null,o=!1,s="",i=mf;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),e=hf(e,null,t,1,n!=null?n:null,o,!1,s,i),t[Ae]=e.current,cr(t),r)for(t=0;t<r.length;t++)n=r[t],o=n._getVersion,o=o(n._source),e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[n,o]:e.mutableSourceEagerHydrationData.push(n,o);return new xs(e)};Wt.render=function(t,e,n){if(!Ss(e))throw Error(C(200));return Ts(null,t,e,!1,n)};Wt.unmountComponentAtNode=function(t){if(!Ss(t))throw Error(C(40));return t._reactRootContainer?(_n(function(){Ts(null,null,t,!1,function(){t._reactRootContainer=null,t[Ae]=null})}),!0):!1};Wt.unstable_batchedUpdates=Dl;Wt.unstable_renderSubtreeIntoContainer=function(t,e,n,r){if(!Ss(n))throw Error(C(200));if(t==null||t._reactInternals===void 0)throw Error(C(38));return Ts(t,e,n,!1,r)};Wt.version="18.3.1-next-f1338f8080-20240426";function gf(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(gf)}catch(t){console.error(t)}}gf(),m8.exports=Wt;var vf=m8.exports;const l7=r8(vf);var yf,Ru=l7;yf=Ru.createRoot,Ru.hydrateRoot;const wf="3.7.7",c7=wf,x1=typeof Buffer=="function",Mu=typeof TextDecoder=="function"?new TextDecoder:void 0,Ou=typeof TextEncoder=="function"?new TextEncoder:void 0,u7="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",V1=Array.prototype.slice.call(u7),eo=(t=>{let e={};return t.forEach((n,r)=>e[n]=r),e})(V1),f7=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,mt=String.fromCharCode.bind(String),Vu=typeof Uint8Array.from=="function"?Uint8Array.from.bind(Uint8Array):t=>new Uint8Array(Array.prototype.slice.call(t,0)),xf=t=>t.replace(/=/g,"").replace(/[+\/]/g,e=>e=="+"?"-":"_"),Sf=t=>t.replace(/[^A-Za-z0-9\+\/]/g,""),Tf=t=>{let e,n,r,o,s="";const i=t.length%3;for(let a=0;a<t.length;){if((n=t.charCodeAt(a++))>255||(r=t.charCodeAt(a++))>255||(o=t.charCodeAt(a++))>255)throw new TypeError("invalid character found");e=n<<16|r<<8|o,s+=V1[e>>18&63]+V1[e>>12&63]+V1[e>>6&63]+V1[e&63]}return i?s.slice(0,i-3)+"===".substring(i):s},Vl=typeof btoa=="function"?t=>btoa(t):x1?t=>Buffer.from(t,"binary").toString("base64"):Tf,wa=x1?t=>Buffer.from(t).toString("base64"):t=>{let n=[];for(let r=0,o=t.length;r<o;r+=4096)n.push(mt.apply(null,t.subarray(r,r+4096)));return Vl(n.join(""))},xo=(t,e=!1)=>e?xf(wa(t)):wa(t),d7=t=>{if(t.length<2){var e=t.charCodeAt(0);return e<128?t:e<2048?mt(192|e>>>6)+mt(128|e&63):mt(224|e>>>12&15)+mt(128|e>>>6&63)+mt(128|e&63)}else{var e=65536+(t.charCodeAt(0)-55296)*1024+(t.charCodeAt(1)-56320);return mt(240|e>>>18&7)+mt(128|e>>>12&63)+mt(128|e>>>6&63)+mt(128|e&63)}},p7=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,Ef=t=>t.replace(p7,d7),Iu=x1?t=>Buffer.from(t,"utf8").toString("base64"):Ou?t=>wa(Ou.encode(t)):t=>Vl(Ef(t)),s1=(t,e=!1)=>e?xf(Iu(t)):Iu(t),qu=t=>s1(t,!0),h7=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,m7=t=>{switch(t.length){case 4:var e=(7&t.charCodeAt(0))<<18|(63&t.charCodeAt(1))<<12|(63&t.charCodeAt(2))<<6|63&t.charCodeAt(3),n=e-65536;return mt((n>>>10)+55296)+mt((n&1023)+56320);case 3:return mt((15&t.charCodeAt(0))<<12|(63&t.charCodeAt(1))<<6|63&t.charCodeAt(2));default:return mt((31&t.charCodeAt(0))<<6|63&t.charCodeAt(1))}},Cf=t=>t.replace(h7,m7),bf=t=>{if(t=t.replace(/\s+/g,""),!f7.test(t))throw new TypeError("malformed base64.");t+="==".slice(2-(t.length&3));let e,n="",r,o;for(let s=0;s<t.length;)e=eo[t.charAt(s++)]<<18|eo[t.charAt(s++)]<<12|(r=eo[t.charAt(s++)])<<6|(o=eo[t.charAt(s++)]),n+=r===64?mt(e>>16&255):o===64?mt(e>>16&255,e>>8&255):mt(e>>16&255,e>>8&255,e&255);return n},Il=typeof atob=="function"?t=>atob(Sf(t)):x1?t=>Buffer.from(t,"base64").toString("binary"):bf,_f=x1?t=>Vu(Buffer.from(t,"base64")):t=>Vu(Il(t).split("").map(e=>e.charCodeAt(0))),kf=t=>_f(Df(t)),g7=x1?t=>Buffer.from(t,"base64").toString("utf8"):Mu?t=>Mu.decode(_f(t)):t=>Cf(Il(t)),Df=t=>Sf(t.replace(/[-_]/g,e=>e=="-"?"+":"/")),xa=t=>g7(Df(t)),v7=t=>{if(typeof t!="string")return!1;const e=t.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(e)||!/[^\s0-9a-zA-Z\-_]/.test(e)},Af=t=>({value:t,enumerable:!1,writable:!0,configurable:!0}),Pf=function(){const t=(e,n)=>Object.defineProperty(String.prototype,e,Af(n));t("fromBase64",function(){return xa(this)}),t("toBase64",function(e){return s1(this,e)}),t("toBase64URI",function(){return s1(this,!0)}),t("toBase64URL",function(){return s1(this,!0)}),t("toUint8Array",function(){return kf(this)})},Lf=function(){const t=(e,n)=>Object.defineProperty(Uint8Array.prototype,e,Af(n));t("toBase64",function(e){return xo(this,e)}),t("toBase64URI",function(){return xo(this,!0)}),t("toBase64URL",function(){return xo(this,!0)})},y7=()=>{Pf(),Lf()},w7={version:wf,VERSION:c7,atob:Il,atobPolyfill:bf,btoa:Vl,btoaPolyfill:Tf,fromBase64:xa,toBase64:s1,encode:s1,encodeURI:qu,encodeURL:qu,utob:Ef,btou:Cf,decode:xa,isValid:v7,fromUint8Array:xo,toUint8Array:kf,extendString:Pf,extendUint8Array:Lf,extendBuiltins:y7};function x7(t){if(typeof t=="object")return t;if(typeof t!="string")return null;try{return t.startsWith("{")?JSON.parse(t):S7(t)}catch(e){return console.log("parse failed:"+e.message),null}}function S7(t){var e=new window.DOMParser().parseFromString(t,"text/xml"),n=e.getElementsByTagName("componentData");if(!n.length){var r,o=(r=t.match(/<templateData>(.*)<\/templateData>/))===null||r===void 0?void 0:r[1];return JSON.parse(w7.decode(o))}var s={};for(var i of n)for(var a of i.getElementsByTagName("data")||[])if(a.getAttribute("value")!=null){s[i.getAttribute("id")]=a.getAttribute("value");break}return s}var ql=$.createContext(),T7=t=>{var{children:e,name:n}=t,[r,o]=T.useState(qt.loading),[s,i]=T.useState({}),[a,l]=T.useState([]),[c,u]=T.useState(),[f,d]=T.useState(!1),m=x=>{console.log("".concat(n||"").concat(x))},y=T.useCallback(x=>(l(p=>[...p,x]),()=>{l(p=>p.filter(g=>g!==x))}),[]);T.useLayoutEffect(()=>{var x=!1;return window.load=()=>{o(qt.loaded),m(".load()")},window.play=()=>{x=!0,d(!0),m(".play()")},window.pause=()=>{o(qt.paused),m(".pause()")},window.stop=()=>{x?(o(qt.stopped),m(".stop()")):(o(qt.removed),m(".stop() without play"))},window.update=p=>{var g=x7(p);if(g&&(m(".update(".concat(g?JSON.stringify(g||{},null,2):"null",")")),i(g),!x)){var h=y("__initialData");u(()=>h)}},()=>{delete window.load,delete window.play,delete window.pause,delete window.stop,delete window.update}},[]),T.useEffect(()=>{c==null||c()},[c]),T.useEffect(()=>{r<qt.playing&&f&&!a.length&&o(qt.playing)},[r,s,f,a]),T.useEffect(()=>{if(r===qt.removed){var x,p;m(".remove()"),(x=(p=window).remove)===null||x===void 0||x.call(p)}},[r]);var v=T.useCallback(()=>{o(qt.removed)},[]);return $.createElement(ql.Provider,{value:{data:s,state:r,name:n,safeToRemove:v,delayPlay:y}},r!==qt.removed?$.createElement(E7,null,e):null)},E7=T.memo(t=>{var{children:e}=t;return e}),di=null,C7=(t,e)=>{var{container:n=document.getElementById("root"),cssReset:r=!0,name:o=t.name}={};if(n||(n=document.createElement("div"),n.id="root",document.body.appendChild(n)),r){var s=` 
      width: 100vw;
      height: 100vh;
      overflow: hidden;
      margin: 0;
    `;document.body.style.cssText=s,n.style.cssText=s}di||(di=yf(n)),vf.flushSync(()=>{di.render(T.createElement(T7,{name:o},T.createElement(t)))})};function ju(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(t,o).enumerable})),n.push.apply(n,r)}return n}function Fu(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?ju(Object(n),!0).forEach(function(r){D7(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ju(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function b7(t,e){if(typeof t!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function _7(t){var e=b7(t,"string");return typeof e=="symbol"?e:String(e)}function Bu(t,e,n,r,o,s,i){try{var a=t[s](i),l=a.value}catch(c){n(c);return}a.done?e(l):Promise.resolve(l).then(r,o)}function k7(t){return function(){var e=this,n=arguments;return new Promise(function(r,o){var s=t.apply(e,n);function i(l){Bu(s,r,o,i,a,"next",l)}function a(l){Bu(s,r,o,i,a,"throw",l)}i(void 0)})}}function D7(t,e,n){return e=_7(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function A7(t,e){if(t==null)return{};var n={},r=Object.keys(t),o,s;for(s=0;s<r.length;s++)o=r[s],!(e.indexOf(o)>=0)&&(n[o]=t[o]);return n}function P7(t,e){if(t==null)return{};var n=A7(t,e),r,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);for(o=0;o<s.length;o++)r=s[o],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}var L7=(t,e)=>{var n=$.useRef(null),r=$.useRef(t);return $.useEffect(()=>{r.current=t},[t]),$.useEffect(()=>{var o=()=>r.current();if(typeof e=="number")return n.current=window.setTimeout(o,e),()=>window.clearTimeout(n.current)},[e]),n},N7=["state","safeToRemove"],Nf=t=>{var e=$.useContext(ql),{state:n,safeToRemove:r}=e,o=P7(e,N7),s=R7();return L7(r,n===qt.stopped&&Number.isFinite(void 0)?t.removeDelay*1e3:null),Fu(Fu({},o),{},{data:s,state:n,safeToRemove:r,isPlaying:n===qt.playing,isStopped:n===qt.stopped})},R7=t=>{var{data:e}=$.useContext(ql),{trim:n=!0}={};return T.useMemo(()=>{if(!n)return e;var r={};for(var[o,s]of Object.entries(e))r[o]=typeof s=="string"?s.trim():s;return r},[e,n])};const jl=T.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),Es=T.createContext({});function M7(){return T.useContext(Es).visualElement}const Ar=T.createContext(null),An=typeof document<"u",W1=An?T.useLayoutEffect:T.useEffect,Rf=T.createContext({strict:!1});function O7(t,e,n,r){const o=M7(),s=T.useContext(Rf),i=T.useContext(Ar),a=T.useContext(jl).reducedMotion,l=T.useRef();r=r||s.renderer,!l.current&&r&&(l.current=r(t,{visualState:e,parent:o,props:n,presenceId:i?i.id:void 0,blockInitialAnimation:i?i.initial===!1:!1,reducedMotionConfig:a}));const c=l.current;return W1(()=>{c&&c.render()}),W1(()=>{c&&c.animationState&&c.animationState.animateChanges()}),W1(()=>()=>c&&c.notify("Unmount"),[]),c}function Kn(t){return typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function V7(t,e,n){return T.useCallback(r=>{r&&t.mount&&t.mount(r),e&&(r?e.mount(r):e.unmount()),n&&(typeof n=="function"?n(r):Kn(n)&&(n.current=r))},[e])}function yr(t){return typeof t=="string"||Array.isArray(t)}function Cs(t){return typeof t=="object"&&typeof t.start=="function"}const I7=["initial","animate","exit","whileHover","whileDrag","whileTap","whileFocus","whileInView"];function bs(t){return Cs(t.animate)||I7.some(e=>yr(t[e]))}function Mf(t){return!!(bs(t)||t.variants)}function q7(t,e){if(bs(t)){const{initial:n,animate:r}=t;return{initial:n===!1||yr(n)?n:void 0,animate:yr(r)?r:void 0}}return t.inherit!==!1?e:{}}function j7(t){const{initial:e,animate:n}=q7(t,T.useContext(Es));return T.useMemo(()=>({initial:e,animate:n}),[Uu(e),Uu(n)])}function Uu(t){return Array.isArray(t)?t.join(" "):t}const ye=t=>({isEnabled:e=>t.some(n=>!!e[n])}),wr={measureLayout:ye(["layout","layoutId","drag"]),animation:ye(["animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView"]),exit:ye(["exit"]),drag:ye(["drag","dragControls"]),focus:ye(["whileFocus"]),hover:ye(["whileHover","onHoverStart","onHoverEnd"]),tap:ye(["whileTap","onTap","onTapStart","onTapCancel"]),pan:ye(["onPan","onPanStart","onPanSessionStart","onPanEnd"]),inView:ye(["whileInView","onViewportEnter","onViewportLeave"])};function F7(t){for(const e in t)e==="projectionNodeConstructor"?wr.projectionNodeConstructor=t[e]:wr[e].Component=t[e]}function _s(t){const e=T.useRef(null);return e.current===null&&(e.current=t()),e.current}const Q1={hasAnimatedSinceResize:!0,hasEverUpdated:!1};let B7=1;function U7(){return _s(()=>{if(Q1.hasEverUpdated)return B7++})}const Fl=T.createContext({});class z7 extends $.Component{getSnapshotBeforeUpdate(){const{visualElement:e,props:n}=this.props;return e&&e.setProps(n),null}componentDidUpdate(){}render(){return this.props.children}}const Of=T.createContext({}),$7=Symbol.for("motionComponentSymbol");function H7({preloadedFeatures:t,createVisualElement:e,projectionNodeConstructor:n,useRender:r,useVisualState:o,Component:s}){t&&F7(t);function i(l,c){const u={...T.useContext(jl),...l,layoutId:G7(l)},{isStatic:f}=u;let d=null;const m=j7(l),y=f?void 0:U7(),v=o(l,f);if(!f&&An){m.visualElement=O7(s,v,u,e);const x=T.useContext(Rf).strict,p=T.useContext(Of);m.visualElement&&(d=m.visualElement.loadFeatures(u,x,t,y,n||wr.projectionNodeConstructor,p))}return T.createElement(z7,{visualElement:m.visualElement,props:u},d,T.createElement(Es.Provider,{value:m},r(s,l,y,V7(v,m.visualElement,c),v,f,m.visualElement)))}const a=T.forwardRef(i);return a[$7]=s,a}function G7({layoutId:t}){const e=T.useContext(Fl).id;return e&&t!==void 0?e+"-"+t:t}function W7(t){function e(r,o={}){return H7(t(r,o))}if(typeof Proxy>"u")return e;const n=new Map;return new Proxy(e,{get:(r,o)=>(n.has(o)||n.set(o,e(o)),n.get(o))})}const Q7=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Bl(t){return typeof t!="string"||t.includes("-")?!1:!!(Q7.indexOf(t)>-1||/[A-Z]/.test(t))}const Qo={};function K7(t){Object.assign(Qo,t)}const Ko=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Pn=new Set(Ko);function Vf(t,{layout:e,layoutId:n}){return Pn.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!Qo[t]||t==="opacity")}const ve=t=>!!(t!=null&&t.getVelocity),Y7={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},X7=(t,e)=>Ko.indexOf(t)-Ko.indexOf(e);function Z7({transform:t,transformKeys:e},{enableHardwareAcceleration:n=!0,allowTransformNone:r=!0},o,s){let i="";e.sort(X7);for(const a of e)i+=`${Y7[a]||a}(${t[a]}) `;return n&&!t.z&&(i+="translateZ(0)"),i=i.trim(),s?i=s(t,o?"":i):r&&o&&(i="none"),i}function If(t){return t.startsWith("--")}const J7=(t,e)=>e&&typeof t=="number"?e.transform(t):t,h1=(t,e,n)=>Math.min(Math.max(n,t),e),Ln={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},K1={...Ln,transform:t=>h1(0,1,t)},no={...Ln,default:1},Y1=t=>Math.round(t*1e5)/1e5,xr=/(-)?([\d]*\.?[\d])+/g,Sa=/(#[0-9a-f]{6}|#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,t3=/^(#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function Pr(t){return typeof t=="string"}const Lr=t=>({test:e=>Pr(e)&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Oe=Lr("deg"),me=Lr("%"),P=Lr("px"),e3=Lr("vh"),n3=Lr("vw"),zu={...me,parse:t=>me.parse(t)/100,transform:t=>me.transform(t*100)},$u={...Ln,transform:Math.round},qf={borderWidth:P,borderTopWidth:P,borderRightWidth:P,borderBottomWidth:P,borderLeftWidth:P,borderRadius:P,radius:P,borderTopLeftRadius:P,borderTopRightRadius:P,borderBottomRightRadius:P,borderBottomLeftRadius:P,width:P,maxWidth:P,height:P,maxHeight:P,size:P,top:P,right:P,bottom:P,left:P,padding:P,paddingTop:P,paddingRight:P,paddingBottom:P,paddingLeft:P,margin:P,marginTop:P,marginRight:P,marginBottom:P,marginLeft:P,rotate:Oe,rotateX:Oe,rotateY:Oe,rotateZ:Oe,scale:no,scaleX:no,scaleY:no,scaleZ:no,skew:Oe,skewX:Oe,skewY:Oe,distance:P,translateX:P,translateY:P,translateZ:P,x:P,y:P,z:P,perspective:P,transformPerspective:P,opacity:K1,originX:zu,originY:zu,originZ:P,zIndex:$u,fillOpacity:K1,strokeOpacity:K1,numOctaves:$u};function Ul(t,e,n,r){const{style:o,vars:s,transform:i,transformKeys:a,transformOrigin:l}=t;a.length=0;let c=!1,u=!1,f=!0;for(const d in e){const m=e[d];if(If(d)){s[d]=m;continue}const y=qf[d],v=J7(m,y);if(Pn.has(d)){if(c=!0,i[d]=v,a.push(d),!f)continue;m!==(y.default||0)&&(f=!1)}else d.startsWith("origin")?(u=!0,l[d]=v):o[d]=v}if(e.transform||(c||r?o.transform=Z7(t,n,f,r):o.transform&&(o.transform="none")),u){const{originX:d="50%",originY:m="50%",originZ:y=0}=l;o.transformOrigin=`${d} ${m} ${y}`}}const zl=()=>({style:{},transform:{},transformKeys:[],transformOrigin:{},vars:{}});function jf(t,e,n){for(const r in e)!ve(e[r])&&!Vf(r,n)&&(t[r]=e[r])}function r3({transformTemplate:t},e,n){return T.useMemo(()=>{const r=zl();return Ul(r,e,{enableHardwareAcceleration:!n},t),Object.assign({},r.vars,r.style)},[e])}function o3(t,e,n){const r=t.style||{},o={};return jf(o,r,t),Object.assign(o,r3(t,e,n)),t.transformValues?t.transformValues(o):o}function s3(t,e,n){const r={},o=o3(t,e,n);return t.drag&&t.dragListener!==!1&&(r.draggable=!1,o.userSelect=o.WebkitUserSelect=o.WebkitTouchCallout="none",o.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),r.style=o,r}const i3=["animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView"],a3=["whileTap","onTap","onTapStart","onTapCancel"],l3=["onPan","onPanStart","onPanSessionStart","onPanEnd"],c3=["whileInView","onViewportEnter","onViewportLeave","viewport"],u3=new Set(["initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","layout","layoutId","layoutDependency","onLayoutAnimationStart","onLayoutAnimationComplete","onLayoutMeasure","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","drag","dragControls","dragListener","dragConstraints","dragDirectionLock","dragSnapToOrigin","_dragX","_dragY","dragElastic","dragMomentum","dragPropagation","dragTransition","onHoverStart","onHoverEnd","layoutScroll",...c3,...a3,...i3,...l3]);function Yo(t){return u3.has(t)}let Ff=t=>!Yo(t);function f3(t){t&&(Ff=e=>e.startsWith("on")?!Yo(e):t(e))}try{f3(require("@emotion/is-prop-valid").default)}catch{}function d3(t,e,n){const r={};for(const o in t)(Ff(o)||n===!0&&Yo(o)||!e&&!Yo(o)||t.draggable&&o.startsWith("onDrag"))&&(r[o]=t[o]);return r}function Hu(t,e,n){return typeof t=="string"?t:P.transform(e+n*t)}function p3(t,e,n){const r=Hu(e,t.x,t.width),o=Hu(n,t.y,t.height);return`${r} ${o}`}const h3={offset:"stroke-dashoffset",array:"stroke-dasharray"},m3={offset:"strokeDashoffset",array:"strokeDasharray"};function g3(t,e,n=1,r=0,o=!0){t.pathLength=1;const s=o?h3:m3;t[s.offset]=P.transform(-r);const i=P.transform(e),a=P.transform(n);t[s.array]=`${i} ${a}`}function $l(t,{attrX:e,attrY:n,originX:r,originY:o,pathLength:s,pathSpacing:i=1,pathOffset:a=0,...l},c,u,f){if(Ul(t,l,c,f),u){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:d,style:m,dimensions:y}=t;d.transform&&(y&&(m.transform=d.transform),delete d.transform),y&&(r!==void 0||o!==void 0||m.transform)&&(m.transformOrigin=p3(y,r!==void 0?r:.5,o!==void 0?o:.5)),e!==void 0&&(d.x=e),n!==void 0&&(d.y=n),s!==void 0&&g3(d,s,i,a,!1)}const Bf=()=>({...zl(),attrs:{}}),Hl=t=>typeof t=="string"&&t.toLowerCase()==="svg";function v3(t,e,n,r){const o=T.useMemo(()=>{const s=Bf();return $l(s,e,{enableHardwareAcceleration:!1},Hl(r),t.transformTemplate),{...s.attrs,style:{...s.style}}},[e]);if(t.style){const s={};jf(s,t.style,t),o.style={...s,...o.style}}return o}function y3(t=!1){return(n,r,o,s,{latestValues:i},a)=>{const c=(Bl(n)?v3:s3)(r,i,a,n),f={...d3(r,typeof n=="string",t),...c,ref:s};return o&&(f["data-projection-id"]=o),T.createElement(n,f)}}const Gl=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();function Uf(t,{style:e,vars:n},r,o){Object.assign(t.style,e,o&&o.getProjectionStyles(r));for(const s in n)t.style.setProperty(s,n[s])}const zf=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function $f(t,e,n,r){Uf(t,e,void 0,r);for(const o in e.attrs)t.setAttribute(zf.has(o)?o:Gl(o),e.attrs[o])}function Wl(t){const{style:e}=t,n={};for(const r in e)(ve(e[r])||Vf(r,t))&&(n[r]=e[r]);return n}function Hf(t){const e=Wl(t);for(const n in t)if(ve(t[n])){const r=n==="x"||n==="y"?"attr"+n.toUpperCase():n;e[r]=t[n]}return e}function Ql(t,e,n,r={},o={}){return typeof e=="function"&&(e=e(n!==void 0?n:t.custom,r,o)),typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"&&(e=e(n!==void 0?n:t.custom,r,o)),e}const Xo=t=>Array.isArray(t),w3=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),x3=t=>Xo(t)?t[t.length-1]||0:t;function So(t){const e=ve(t)?t.get():t;return w3(e)?e.toValue():e}function S3({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:n},r,o,s){const i={latestValues:T3(r,o,s,t),renderState:e()};return n&&(i.mount=a=>n(r,a,i)),i}const Gf=t=>(e,n)=>{const r=T.useContext(Es),o=T.useContext(Ar),s=()=>S3(t,e,r,o);return n?s():_s(s)};function T3(t,e,n,r){const o={},s=r(t);for(const d in s)o[d]=So(s[d]);let{initial:i,animate:a}=t;const l=bs(t),c=Mf(t);e&&c&&!l&&t.inherit!==!1&&(i===void 0&&(i=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||i===!1;const f=u?a:i;return f&&typeof f!="boolean"&&!Cs(f)&&(Array.isArray(f)?f:[f]).forEach(m=>{const y=Ql(t,m);if(!y)return;const{transitionEnd:v,transition:x,...p}=y;for(const g in p){let h=p[g];if(Array.isArray(h)){const w=u?h.length-1:0;h=h[w]}h!==null&&(o[g]=h)}for(const g in v)o[g]=v[g]}),o}const E3={useVisualState:Gf({scrapeMotionValuesFromProps:Hf,createRenderState:Bf,onMount:(t,e,{renderState:n,latestValues:r})=>{try{n.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}$l(n,r,{enableHardwareAcceleration:!1},Hl(e.tagName),t.transformTemplate),$f(e,n)}})},C3={useVisualState:Gf({scrapeMotionValuesFromProps:Wl,createRenderState:zl})};function b3(t,{forwardMotionProps:e=!1},n,r,o){return{...Bl(t)?E3:C3,preloadedFeatures:n,useRender:y3(e),createVisualElement:r,projectionNodeConstructor:o,Component:t}}var H;(function(t){t.Animate="animate",t.Hover="whileHover",t.Tap="whileTap",t.Drag="whileDrag",t.Focus="whileFocus",t.InView="whileInView",t.Exit="exit"})(H||(H={}));function ks(t,e,n,r={passive:!0}){return t.addEventListener(e,n,r),()=>t.removeEventListener(e,n)}function Ta(t,e,n,r){T.useEffect(()=>{const o=t.current;if(n&&o)return ks(o,e,n,r)},[t,e,n,r])}function _3({whileFocus:t,visualElement:e}){const{animationState:n}=e,r=()=>{n&&n.setActive(H.Focus,!0)},o=()=>{n&&n.setActive(H.Focus,!1)};Ta(e,"focus",t?r:void 0),Ta(e,"blur",t?o:void 0)}function Wf(t){return typeof PointerEvent<"u"&&t instanceof PointerEvent?t.pointerType==="mouse":t instanceof MouseEvent}function Qf(t){return!!t.touches}function k3(t){return e=>{const n=e instanceof MouseEvent;(!n||n&&e.button===0)&&t(e)}}const D3={pageX:0,pageY:0};function A3(t,e="page"){const r=t.touches[0]||t.changedTouches[0]||D3;return{x:r[e+"X"],y:r[e+"Y"]}}function P3(t,e="page"){return{x:t[e+"X"],y:t[e+"Y"]}}function Kl(t,e="page"){return{point:Qf(t)?A3(t,e):P3(t,e)}}const Kf=(t,e=!1)=>{const n=r=>t(r,Kl(r));return e?k3(n):n},L3=()=>An&&window.onpointerdown===null,N3=()=>An&&window.ontouchstart===null,R3=()=>An&&window.onmousedown===null,M3={pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointercancel:"mousecancel",pointerover:"mouseover",pointerout:"mouseout",pointerenter:"mouseenter",pointerleave:"mouseleave"},O3={pointerdown:"touchstart",pointermove:"touchmove",pointerup:"touchend",pointercancel:"touchcancel"};function Yf(t){return L3()?t:N3()?O3[t]:R3()?M3[t]:t}function i1(t,e,n,r){return ks(t,Yf(e),Kf(n,e==="pointerdown"),r)}function Zo(t,e,n,r){return Ta(t,Yf(e),n&&Kf(n,e==="pointerdown"),r)}function Xf(t){let e=null;return()=>{const n=()=>{e=null};return e===null?(e=t,n):!1}}const Gu=Xf("dragHorizontal"),Wu=Xf("dragVertical");function Zf(t){let e=!1;if(t==="y")e=Wu();else if(t==="x")e=Gu();else{const n=Gu(),r=Wu();n&&r?e=()=>{n(),r()}:(n&&n(),r&&r())}return e}function Jf(){const t=Zf(!0);return t?(t(),!1):!0}function Qu(t,e,n){return(r,o)=>{!Wf(r)||Jf()||(t.animationState&&t.animationState.setActive(H.Hover,e),n&&n(r,o))}}function V3({onHoverStart:t,onHoverEnd:e,whileHover:n,visualElement:r}){Zo(r,"pointerenter",t||n?Qu(r,!0,t):void 0,{passive:!t}),Zo(r,"pointerleave",e||n?Qu(r,!1,e):void 0,{passive:!e})}const t2=(t,e)=>e?t===e?!0:t2(t,e.parentElement):!1;function Yl(t){return T.useEffect(()=>()=>t(),[])}const I3=(t,e)=>n=>e(t(n)),Ds=(...t)=>t.reduce(I3);function q3({onTap:t,onTapStart:e,onTapCancel:n,whileTap:r,visualElement:o}){const s=t||e||n||r,i=T.useRef(!1),a=T.useRef(null),l={passive:!(e||t||n||m)};function c(){a.current&&a.current(),a.current=null}function u(){return c(),i.current=!1,o.animationState&&o.animationState.setActive(H.Tap,!1),!Jf()}function f(y,v){u()&&(t2(o.current,y.target)?t&&t(y,v):n&&n(y,v))}function d(y,v){u()&&n&&n(y,v)}function m(y,v){c(),!i.current&&(i.current=!0,a.current=Ds(i1(window,"pointerup",f,l),i1(window,"pointercancel",d,l)),o.animationState&&o.animationState.setActive(H.Tap,!0),e&&e(y,v))}Zo(o,"pointerdown",s?m:void 0,l),Yl(c)}var j3={};const F3="production",e2=typeof process>"u"||j3===void 0?F3:"production",Ku=new Set;function n2(t,e,n){Ku.has(e)||(console.warn(e),Ku.add(e))}const Ea=new WeakMap,pi=new WeakMap,B3=t=>{const e=Ea.get(t.target);e&&e(t)},U3=t=>{t.forEach(B3)};function z3({root:t,...e}){const n=t||document;pi.has(n)||pi.set(n,{});const r=pi.get(n),o=JSON.stringify(e);return r[o]||(r[o]=new IntersectionObserver(U3,{root:t,...e})),r[o]}function $3(t,e,n){const r=z3(e);return Ea.set(t,n),r.observe(t),()=>{Ea.delete(t),r.unobserve(t)}}function H3({visualElement:t,whileInView:e,onViewportEnter:n,onViewportLeave:r,viewport:o={}}){const s=T.useRef({hasEnteredView:!1,isInView:!1});let i=!!(e||n||r);o.once&&s.current.hasEnteredView&&(i=!1),(typeof IntersectionObserver>"u"?Q3:W3)(i,s.current,t,o)}const G3={some:0,all:1};function W3(t,e,n,{root:r,margin:o,amount:s="some",once:i}){T.useEffect(()=>{if(!t||!n.current)return;const a={root:r==null?void 0:r.current,rootMargin:o,threshold:typeof s=="number"?s:G3[s]},l=c=>{const{isIntersecting:u}=c;if(e.isInView===u||(e.isInView=u,i&&!u&&e.hasEnteredView))return;u&&(e.hasEnteredView=!0),n.animationState&&n.animationState.setActive(H.InView,u);const f=n.getProps(),d=u?f.onViewportEnter:f.onViewportLeave;d&&d(c)};return $3(n.current,a,l)},[t,r,o,s])}function Q3(t,e,n,{fallback:r=!0}){T.useEffect(()=>{!t||!r||(e2!=="production"&&n2(!1,"IntersectionObserver not available on this device. whileInView animations will trigger on mount."),requestAnimationFrame(()=>{e.hasEnteredView=!0;const{onViewportEnter:o}=n.getProps();o&&o(null),n.animationState&&n.animationState.setActive(H.InView,!0)}))},[t])}const ze=t=>e=>(t(e),null),K3={inView:ze(H3),tap:ze(q3),focus:ze(_3),hover:ze(V3)};function r2(){const t=T.useContext(Ar);if(t===null)return[!0,null];const{isPresent:e,onExitComplete:n,register:r}=t,o=T.useId();return T.useEffect(()=>r(o),[]),!e&&n?[!1,()=>n&&n(o)]:[!0]}function o2(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let r=0;r<n;r++)if(e[r]!==t[r])return!1;return!0}const Y3=t=>/^\-?\d*\.?\d+$/.test(t),X3=t=>/^0[^.\s]+$/.test(t),_e={delta:0,timestamp:0},s2=1/60*1e3,Z3=typeof performance<"u"?()=>performance.now():()=>Date.now(),i2=typeof window<"u"?t=>window.requestAnimationFrame(t):t=>setTimeout(()=>t(Z3()),s2);function J3(t){let e=[],n=[],r=0,o=!1,s=!1;const i=new WeakSet,a={schedule:(l,c=!1,u=!1)=>{const f=u&&o,d=f?e:n;return c&&i.add(l),d.indexOf(l)===-1&&(d.push(l),f&&o&&(r=e.length)),l},cancel:l=>{const c=n.indexOf(l);c!==-1&&n.splice(c,1),i.delete(l)},process:l=>{if(o){s=!0;return}if(o=!0,[e,n]=[n,e],n.length=0,r=e.length,r)for(let c=0;c<r;c++){const u=e[c];u(l),i.has(u)&&(a.schedule(u),t())}o=!1,s&&(s=!1,a.process(l))}};return a}const tp=40;let Ca=!0,Sr=!1,ba=!1;const Nr=["read","update","preRender","render","postRender"],As=Nr.reduce((t,e)=>(t[e]=J3(()=>Sr=!0),t),{}),It=Nr.reduce((t,e)=>{const n=As[e];return t[e]=(r,o=!1,s=!1)=>(Sr||np(),n.schedule(r,o,s)),t},{}),en=Nr.reduce((t,e)=>(t[e]=As[e].cancel,t),{}),hi=Nr.reduce((t,e)=>(t[e]=()=>As[e].process(_e),t),{}),ep=t=>As[t].process(_e),a2=t=>{Sr=!1,_e.delta=Ca?s2:Math.max(Math.min(t-_e.timestamp,tp),1),_e.timestamp=t,ba=!0,Nr.forEach(ep),ba=!1,Sr&&(Ca=!1,i2(a2))},np=()=>{Sr=!0,Ca=!0,ba||i2(a2)};function Xl(t,e){t.indexOf(e)===-1&&t.push(e)}function Zl(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class Jl{constructor(){this.subscriptions=[]}add(e){return Xl(this.subscriptions,e),()=>Zl(this.subscriptions,e)}notify(e,n,r){const o=this.subscriptions.length;if(o)if(o===1)this.subscriptions[0](e,n,r);else for(let s=0;s<o;s++){const i=this.subscriptions[s];i&&i(e,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function tc(t,e){return e?t*(1e3/e):0}const rp=t=>!isNaN(parseFloat(t));class op{constructor(e,n={}){this.version="7.10.3",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,o=!0)=>{this.prev=this.current,this.current=r;const{delta:s,timestamp:i}=_e;this.lastUpdated!==i&&(this.timeDelta=s,this.lastUpdated=i,It.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),o&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>It.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=rp(this.current),this.owner=n.owner}onChange(e){return this.on("change",e)}on(e,n){return this.events[e]||(this.events[e]=new Jl),this.events[e].add(n)}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e){this.passiveEffect=e}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,r){this.set(n),this.prev=e,this.timeDelta=r}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?tc(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.stopAnimation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.stopAnimation&&(this.stopAnimation(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.stopAnimation}clearAnimation(){this.stopAnimation=null}destroy(){this.clearListeners(),this.stop()}}function m1(t,e){return new op(t,e)}const ec=(t,e)=>n=>!!(Pr(n)&&t3.test(n)&&n.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(n,e)),l2=(t,e,n)=>r=>{if(!Pr(r))return r;const[o,s,i,a]=r.match(xr);return{[t]:parseFloat(o),[e]:parseFloat(s),[n]:parseFloat(i),alpha:a!==void 0?parseFloat(a):1}},sp=t=>h1(0,255,t),mi={...Ln,transform:t=>Math.round(sp(t))},yn={test:ec("rgb","red"),parse:l2("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:r=1})=>"rgba("+mi.transform(t)+", "+mi.transform(e)+", "+mi.transform(n)+", "+Y1(K1.transform(r))+")"};function ip(t){let e="",n="",r="",o="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),r=t.substring(5,7),o=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),r=t.substring(3,4),o=t.substring(4,5),e+=e,n+=n,r+=r,o+=o),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const _a={test:ec("#"),parse:ip,transform:yn.transform},Yn={test:ec("hsl","hue"),parse:l2("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:r=1})=>"hsla("+Math.round(t)+", "+me.transform(Y1(e))+", "+me.transform(Y1(n))+", "+Y1(K1.transform(r))+")"},Et={test:t=>yn.test(t)||_a.test(t)||Yn.test(t),parse:t=>yn.test(t)?yn.parse(t):Yn.test(t)?Yn.parse(t):_a.parse(t),transform:t=>Pr(t)?t:t.hasOwnProperty("red")?yn.transform(t):Yn.transform(t)},c2="${c}",u2="${n}";function ap(t){var e,n;return isNaN(t)&&Pr(t)&&(((e=t.match(xr))===null||e===void 0?void 0:e.length)||0)+(((n=t.match(Sa))===null||n===void 0?void 0:n.length)||0)>0}function Jo(t){typeof t=="number"&&(t=`${t}`);const e=[];let n=0,r=0;const o=t.match(Sa);o&&(n=o.length,t=t.replace(Sa,c2),e.push(...o.map(Et.parse)));const s=t.match(xr);return s&&(r=s.length,t=t.replace(xr,u2),e.push(...s.map(Ln.parse))),{values:e,numColors:n,numNumbers:r,tokenised:t}}function f2(t){return Jo(t).values}function d2(t){const{values:e,numColors:n,tokenised:r}=Jo(t),o=e.length;return s=>{let i=r;for(let a=0;a<o;a++)i=i.replace(a<n?c2:u2,a<n?Et.transform(s[a]):Y1(s[a]));return i}}const lp=t=>typeof t=="number"?0:t;function cp(t){const e=f2(t);return d2(t)(e.map(lp))}const nn={test:ap,parse:f2,createTransformer:d2,getAnimatableNone:cp},up=new Set(["brightness","contrast","saturate","opacity"]);function fp(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[r]=n.match(xr)||[];if(!r)return t;const o=n.replace(r,"");let s=up.has(e)?1:0;return r!==n&&(s*=100),e+"("+s+o+")"}const dp=/([a-z-]*)\(.*?\)/g,ka={...nn,getAnimatableNone:t=>{const e=t.match(dp);return e?e.map(fp).join(" "):t}},pp={...qf,color:Et,backgroundColor:Et,outlineColor:Et,fill:Et,stroke:Et,borderColor:Et,borderTopColor:Et,borderRightColor:Et,borderBottomColor:Et,borderLeftColor:Et,filter:ka,WebkitFilter:ka},nc=t=>pp[t];function rc(t,e){var n;let r=nc(t);return r!==ka&&(r=nn),(n=r.getAnimatableNone)===null||n===void 0?void 0:n.call(r,e)}const p2=t=>e=>e.test(t),hp={test:t=>t==="auto",parse:t=>t},h2=[Ln,P,me,Oe,n3,e3,hp],A1=t=>h2.find(p2(t)),mp=[...h2,Et,nn],gp=t=>mp.find(p2(t));function vp(t){const e={};return t.values.forEach((n,r)=>e[r]=n.get()),e}function yp(t){const e={};return t.values.forEach((n,r)=>e[r]=n.getVelocity()),e}function Ps(t,e,n){const r=t.getProps();return Ql(r,e,n!==void 0?n:r.custom,vp(t),yp(t))}function wp(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,m1(n))}function xp(t,e){const n=Ps(t,e);let{transitionEnd:r={},transition:o={},...s}=n?t.makeTargetAnimatable(n,!1):{};s={...s,...r};for(const i in s){const a=x3(s[i]);wp(t,i,a)}}function Sp(t,e,n){var r,o;const s=Object.keys(e).filter(a=>!t.hasValue(a)),i=s.length;if(i)for(let a=0;a<i;a++){const l=s[a],c=e[l];let u=null;Array.isArray(c)&&(u=c[0]),u===null&&(u=(o=(r=n[l])!==null&&r!==void 0?r:t.readValue(l))!==null&&o!==void 0?o:e[l]),u!=null&&(typeof u=="string"&&(Y3(u)||X3(u))?u=parseFloat(u):!gp(u)&&nn.test(c)&&(u=rc(l,c)),t.addValue(l,m1(u,{owner:t})),n[l]===void 0&&(n[l]=u),u!==null&&t.setBaseTarget(l,u))}}function Tp(t,e){return e?(e[t]||e.default||e).from:void 0}function Ep(t,e,n){var r;const o={};for(const s in t){const i=Tp(s,e);o[s]=i!==void 0?i:(r=n.getValue(s))===null||r===void 0?void 0:r.get()}return o}function ts(t){return!!(ve(t)&&t.add)}const Cp=(t,e)=>`${t}: ${e}`;function bp(t,e){const{MotionAppearAnimations:n}=window,r=Cp(t,Pn.has(e)?"transform":e),o=n&&n.get(r);return o?(It.render(()=>{try{o.cancel(),n.delete(r)}catch{}}),o.currentTime||0):0}const _p="framerAppearId",kp="data-"+Gl(_p);var Tr=function(){};const To=t=>t*1e3,Dp={current:!1},oc=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,sc=t=>e=>1-t(1-e),ic=t=>t*t,Ap=sc(ic),ac=oc(ic),rt=(t,e,n)=>-n*t+n*e+t;function gi(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Pp({hue:t,saturation:e,lightness:n,alpha:r}){t/=360,e/=100,n/=100;let o=0,s=0,i=0;if(!e)o=s=i=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;o=gi(l,a,t+1/3),s=gi(l,a,t),i=gi(l,a,t-1/3)}return{red:Math.round(o*255),green:Math.round(s*255),blue:Math.round(i*255),alpha:r}}const vi=(t,e,n)=>{const r=t*t;return Math.sqrt(Math.max(0,n*(e*e-r)+r))},Lp=[_a,yn,Yn],Np=t=>Lp.find(e=>e.test(t));function Yu(t){const e=Np(t);let n=e.parse(t);return e===Yn&&(n=Pp(n)),n}const m2=(t,e)=>{const n=Yu(t),r=Yu(e),o={...n};return s=>(o.red=vi(n.red,r.red,s),o.green=vi(n.green,r.green,s),o.blue=vi(n.blue,r.blue,s),o.alpha=rt(n.alpha,r.alpha,s),yn.transform(o))};function g2(t,e){return typeof t=="number"?n=>rt(t,e,n):Et.test(t)?m2(t,e):y2(t,e)}const v2=(t,e)=>{const n=[...t],r=n.length,o=t.map((s,i)=>g2(s,e[i]));return s=>{for(let i=0;i<r;i++)n[i]=o[i](s);return n}},Rp=(t,e)=>{const n={...t,...e},r={};for(const o in n)t[o]!==void 0&&e[o]!==void 0&&(r[o]=g2(t[o],e[o]));return o=>{for(const s in r)n[s]=r[s](o);return n}},y2=(t,e)=>{const n=nn.createTransformer(e),r=Jo(t),o=Jo(e);return r.numColors===o.numColors&&r.numNumbers>=o.numNumbers?Ds(v2(r.values,o.values),n):i=>`${i>0?e:t}`},es=(t,e,n)=>{const r=e-t;return r===0?1:(n-t)/r},Xu=(t,e)=>n=>rt(t,e,n);function Mp(t){return typeof t=="number"?Xu:typeof t=="string"?Et.test(t)?m2:y2:Array.isArray(t)?v2:typeof t=="object"?Rp:Xu}function Op(t,e,n){const r=[],o=n||Mp(t[0]),s=t.length-1;for(let i=0;i<s;i++){let a=o(t[i],t[i+1]);if(e){const l=Array.isArray(e)?e[i]:e;a=Ds(l,a)}r.push(a)}return r}function w2(t,e,{clamp:n=!0,ease:r,mixer:o}={}){const s=t.length;Tr(s===e.length),Tr(!r||!Array.isArray(r)||r.length===s-1),t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());const i=Op(e,r,o),a=i.length,l=c=>{let u=0;if(a>1)for(;u<t.length-2&&!(c<t[u+1]);u++);const f=es(t[u],t[u+1],c);return i[u](f)};return n?c=>l(h1(t[0],t[s-1],c)):l}const lc=t=>t,x2=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,Vp=1e-7,Ip=12;function qp(t,e,n,r,o){let s,i,a=0;do i=e+(n-e)/2,s=x2(i,r,o)-t,s>0?n=i:e=i;while(Math.abs(s)>Vp&&++a<Ip);return i}function S2(t,e,n,r){if(t===e&&n===r)return lc;const o=s=>qp(s,0,1,t,n);return s=>s===0||s===1?s:x2(o(s),e,r)}const T2=t=>1-Math.sin(Math.acos(t)),cc=sc(T2),jp=oc(cc),E2=S2(.33,1.53,.69,.99),uc=sc(E2),Fp=oc(uc),Bp=t=>(t*=2)<1?.5*uc(t):.5*(2-Math.pow(2,-10*(t-1))),Zu={linear:lc,easeIn:ic,easeInOut:ac,easeOut:Ap,circIn:T2,circInOut:jp,circOut:cc,backIn:uc,backInOut:Fp,backOut:E2,anticipate:Bp},Ju=t=>{if(Array.isArray(t)){Tr(t.length===4);const[e,n,r,o]=t;return S2(e,n,r,o)}else if(typeof t=="string")return Tr(Zu[t]!==void 0),Zu[t];return t},Up=t=>Array.isArray(t)&&typeof t[0]!="number";function zp(t,e){return t.map(()=>e||ac).splice(0,t.length-1)}function $p(t){const e=t.length;return t.map((n,r)=>r!==0?r/(e-1):0)}function Hp(t,e){return t.map(n=>n*e)}function ns({keyframes:t,ease:e=ac,times:n,duration:r=300}){t=[...t];const o=ns[0],s=Up(e)?e.map(Ju):Ju(e),i={done:!1,value:o},a=Hp(n&&n.length===ns.length?n:$p(t),r);function l(){return w2(a,t,{ease:Array.isArray(s)?s:zp(t,s)})}let c=l();return{next:u=>(i.value=c(u),i.done=u>=r,i),flipTarget:()=>{t.reverse(),c=l()}}}const yi=.001,Gp=.01,Wp=10,Qp=.05,Kp=1;function Yp({duration:t=800,bounce:e=.25,velocity:n=0,mass:r=1}){let o,s,i=1-e;i=h1(Qp,Kp,i),t=h1(Gp,Wp,t/1e3),i<1?(o=c=>{const u=c*i,f=u*t,d=u-n,m=Da(c,i),y=Math.exp(-f);return yi-d/m*y},s=c=>{const f=c*i*t,d=f*n+n,m=Math.pow(i,2)*Math.pow(c,2)*t,y=Math.exp(-f),v=Da(Math.pow(c,2),i);return(-o(c)+yi>0?-1:1)*((d-m)*y)/v}):(o=c=>{const u=Math.exp(-c*t),f=(c-n)*t+1;return-yi+u*f},s=c=>{const u=Math.exp(-c*t),f=(n-c)*(t*t);return u*f});const a=5/t,l=Zp(o,s,a);if(t=t*1e3,isNaN(l))return{stiffness:100,damping:10,duration:t};{const c=Math.pow(l,2)*r;return{stiffness:c,damping:i*2*Math.sqrt(r*c),duration:t}}}const Xp=12;function Zp(t,e,n){let r=n;for(let o=1;o<Xp;o++)r=r-t(r)/e(r);return r}function Da(t,e){return t*Math.sqrt(1-e*e)}const Jp=["duration","bounce"],th=["stiffness","damping","mass"];function t0(t,e){return e.some(n=>t[n]!==void 0)}function eh(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!t0(t,th)&&t0(t,Jp)){const n=Yp(t);e={...e,...n,velocity:0,mass:1},e.isResolvedFromDuration=!0}return e}const nh=5;function C2({keyframes:t,restSpeed:e=2,restDelta:n=.01,...r}){let o=t[0],s=t[t.length-1];const i={done:!1,value:o},{stiffness:a,damping:l,mass:c,velocity:u,duration:f,isResolvedFromDuration:d}=eh(r);let m=rh,y=u?-(u/1e3):0;const v=l/(2*Math.sqrt(a*c));function x(){const p=s-o,g=Math.sqrt(a/c)/1e3;if(n===void 0&&(n=Math.min(Math.abs(s-o)/100,.4)),v<1){const h=Da(g,v);m=w=>{const S=Math.exp(-v*g*w);return s-S*((y+v*g*p)/h*Math.sin(h*w)+p*Math.cos(h*w))}}else if(v===1)m=h=>s-Math.exp(-g*h)*(p+(y+g*p)*h);else{const h=g*Math.sqrt(v*v-1);m=w=>{const S=Math.exp(-v*g*w),E=Math.min(h*w,300);return s-S*((y+v*g*p)*Math.sinh(E)+h*p*Math.cosh(E))/h}}}return x(),{next:p=>{const g=m(p);if(d)i.done=p>=f;else{let h=y;if(p!==0)if(v<1){const E=Math.max(0,p-nh);h=tc(g-m(E),p-E)}else h=0;const w=Math.abs(h)<=e,S=Math.abs(s-g)<=n;i.done=w&&S}return i.value=i.done?s:g,i},flipTarget:()=>{y=-y,[o,s]=[s,o],x()}}}C2.needsInterpolation=(t,e)=>typeof t=="string"||typeof e=="string";const rh=t=>0;function oh({keyframes:t=[0],velocity:e=0,power:n=.8,timeConstant:r=350,restDelta:o=.5,modifyTarget:s}){const i=t[0],a={done:!1,value:i};let l=n*e;const c=i+l,u=s===void 0?c:s(c);return u!==c&&(l=u-i),{next:f=>{const d=-l*Math.exp(-f/r);return a.done=!(d>o||d<-o),a.value=a.done?u:u+d,a},flipTarget:()=>{}}}const sh={decay:oh,keyframes:ns,tween:ns,spring:C2};function b2(t,e,n=0){return t-e-n}function ih(t,e=0,n=0,r=!0){return r?b2(e+-t,e,n):e-(t-e)+n}function ah(t,e,n,r){return r?t>=e+n:t<=-n}const lh=t=>{const e=({delta:n})=>t(n);return{start:()=>It.update(e,!0),stop:()=>en.update(e)}};function rs({duration:t,driver:e=lh,elapsed:n=0,repeat:r=0,repeatType:o="loop",repeatDelay:s=0,keyframes:i,autoplay:a=!0,onPlay:l,onStop:c,onComplete:u,onRepeat:f,onUpdate:d,type:m="keyframes",...y}){var v,x;let p,g=0,h=t,w,S=!1,E=!0,b;const _=sh[i.length>2?"keyframes":m],M=i[0],A=i[i.length-1];!((x=(v=_).needsInterpolation)===null||x===void 0)&&x.call(v,M,A)&&(b=w2([0,100],[M,A],{clamp:!1}),i=[0,100]);const O=_({...y,duration:t,keyframes:i});function W(){g++,o==="reverse"?(E=g%2===0,n=ih(n,h,s,E)):(n=b2(n,h,s),o==="mirror"&&O.flipTarget()),S=!1,f&&f()}function ut(){p.stop(),u&&u()}function Tt(Q){if(E||(Q=-Q),n+=Q,!S){const st=O.next(Math.max(0,n));w=st.value,b&&(w=b(w)),S=E?st.done:n<=0}d&&d(w),S&&(g===0&&(h=h!==void 0?h:n),g<r?ah(n,h,s,E)&&W():ut())}function B(){l&&l(),p=e(Tt),p.start()}return a&&B(),{stop:()=>{c&&c(),p.stop()},sample:Q=>O.next(Math.max(0,Q))}}function ch(t){return!t||Array.isArray(t)||typeof t=="string"&&_2[t]}const I1=([t,e,n,r])=>`cubic-bezier(${t}, ${e}, ${n}, ${r})`,_2={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:I1([0,.65,.55,1]),circOut:I1([.55,0,1,.45]),backIn:I1([.31,.01,.66,-.59]),backOut:I1([.33,1.53,.69,.99])};function uh(t){if(t)return Array.isArray(t)?I1(t):_2[t]}function fh(t,e,n,{delay:r=0,duration:o,repeat:s=0,repeatType:i="loop",ease:a,times:l}={}){return t.animate({[e]:n,offset:l},{delay:r,duration:o,easing:uh(a),fill:"both",iterations:s+1,direction:i==="reverse"?"alternate":"normal"})}const ro=10;function dh(t,e,{onUpdate:n,onComplete:r,...o}){let{keyframes:s,duration:i=.3,elapsed:a=0,ease:l}=o;if(o.type==="spring"||!ch(o.ease)){const u=rs(o);let f={done:!1,value:s[0]};const d=[];let m=0;for(;!f.done;)f=u.sample(m),d.push(f.value),m+=ro;s=d,i=m-ro,l="linear"}const c=fh(t.owner.current,e,s,{...o,delay:-a,duration:i,ease:l});return c.onfinish=()=>{t.set(s[s.length-1]),r&&r()},()=>{const{currentTime:u}=c;if(u){const f=rs(o);t.setWithVelocity(f.sample(u-ro).value,f.sample(u).value,ro)}It.update(()=>c.cancel())}}function k2(t,e){const n=performance.now(),r=({timestamp:o})=>{const s=o-n;s>=e&&(en.read(r),t(s-e))};return It.read(r,!0),()=>en.read(r)}function ph({keyframes:t,elapsed:e,onUpdate:n,onComplete:r}){const o=()=>(n&&n(t[t.length-1]),r&&r(),()=>{});return e?k2(o,-e):o()}function hh({keyframes:t,velocity:e=0,min:n,max:r,power:o=.8,timeConstant:s=750,bounceStiffness:i=500,bounceDamping:a=10,restDelta:l=1,modifyTarget:c,driver:u,onUpdate:f,onComplete:d,onStop:m}){const y=t[0];let v;function x(w){return n!==void 0&&w<n||r!==void 0&&w>r}function p(w){return n===void 0?r:r===void 0||Math.abs(n-w)<Math.abs(r-w)?n:r}function g(w){v==null||v.stop(),v=rs({keyframes:[0,1],velocity:0,...w,driver:u,onUpdate:S=>{var E;f==null||f(S),(E=w.onUpdate)===null||E===void 0||E.call(w,S)},onComplete:d,onStop:m})}function h(w){g({type:"spring",stiffness:i,damping:a,restDelta:l,...w})}if(x(y))h({velocity:e,keyframes:[y,p(y)]});else{let w=o*e+y;typeof c<"u"&&(w=c(w));const S=p(w),E=S===n?-1:1;let b,_;const M=A=>{b=_,_=A,e=tc(A-b,_e.delta),(E===1&&A>S||E===-1&&A<S)&&h({keyframes:[A,S],velocity:e})};g({type:"decay",keyframes:[y,0],velocity:e,timeConstant:s,power:o,restDelta:l,modifyTarget:c,onUpdate:x(w)?M:void 0})}return{stop:()=>v==null?void 0:v.stop()}}const cn=()=>({type:"spring",stiffness:500,damping:25,restSpeed:10}),oo=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),wi=()=>({type:"keyframes",ease:"linear",duration:.3}),mh={type:"keyframes",duration:.8},e0={x:cn,y:cn,z:cn,rotate:cn,rotateX:cn,rotateY:cn,rotateZ:cn,scaleX:oo,scaleY:oo,scale:oo,opacity:wi,backgroundColor:wi,color:wi,default:oo},gh=(t,{keyframes:e})=>e.length>2?mh:(e0[t]||e0.default)(e[1]),Aa=(t,e)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&nn.test(e)&&!e.startsWith("url("));function vh({when:t,delay:e,delayChildren:n,staggerChildren:r,staggerDirection:o,repeat:s,repeatType:i,repeatDelay:a,from:l,...c}){return!!Object.keys(c).length}function n0(t){return t===0||typeof t=="string"&&parseFloat(t)===0&&t.indexOf(" ")===-1}function r0(t){return typeof t=="number"?0:rc("",t)}function D2(t,e){return t[e]||t.default||t}function yh(t,e,n,r){const o=Aa(e,n);let s=r.from!==void 0?r.from:t.get();return s==="none"&&o&&typeof n=="string"?s=rc(e,n):n0(s)&&typeof n=="string"?s=r0(n):!Array.isArray(n)&&n0(n)&&typeof s=="string"&&(n=r0(s)),Array.isArray(n)?(n[0]===null&&(n[0]=s),n):[s,n]}const o0={waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate")},xi={},A2={};for(const t in o0)A2[t]=()=>(xi[t]===void 0&&(xi[t]=o0[t]()),xi[t]);const wh=new Set(["opacity"]),fc=(t,e,n,r={})=>o=>{const s=D2(r,t)||{},i=s.delay||r.delay||0;let{elapsed:a=0}=r;a=a-To(i);const l=yh(e,t,n,s),c=l[0],u=l[l.length-1],f=Aa(t,c),d=Aa(t,u);let m={keyframes:l,velocity:e.getVelocity(),...s,elapsed:a,onUpdate:p=>{e.set(p),s.onUpdate&&s.onUpdate(p)},onComplete:()=>{o(),s.onComplete&&s.onComplete()}};if(!f||!d||Dp.current||s.type===!1)return ph(m);if(s.type==="inertia"){const p=hh(m);return()=>p.stop()}vh(s)||(m={...m,...gh(t,m)}),m.duration&&(m.duration=To(m.duration)),m.repeatDelay&&(m.repeatDelay=To(m.repeatDelay));const y=e.owner,v=y&&y.current;if(A2.waapi()&&wh.has(t)&&!m.repeatDelay&&m.repeatType!=="mirror"&&m.damping!==0&&y&&v instanceof HTMLElement&&!y.getProps().onUpdate)return dh(e,t,m);{const p=rs(m);return()=>p.stop()}};function xh(t,e,n={}){t.notify("AnimationStart",e);let r;if(Array.isArray(e)){const o=e.map(s=>Pa(t,s,n));r=Promise.all(o)}else if(typeof e=="string")r=Pa(t,e,n);else{const o=typeof e=="function"?Ps(t,e,n.custom):e;r=P2(t,o,n)}return r.then(()=>t.notify("AnimationComplete",e))}function Pa(t,e,n={}){var r;const o=Ps(t,e,n.custom);let{transition:s=t.getDefaultTransition()||{}}=o||{};n.transitionOverride&&(s=n.transitionOverride);const i=o?()=>P2(t,o,n):()=>Promise.resolve(),a=!((r=t.variantChildren)===null||r===void 0)&&r.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:f,staggerDirection:d}=s;return Sh(t,e,u+c,f,d,n)}:()=>Promise.resolve(),{when:l}=s;if(l){const[c,u]=l==="beforeChildren"?[i,a]:[a,i];return c().then(u)}else return Promise.all([i(),a(n.delay)])}function P2(t,e,{delay:n=0,transitionOverride:r,type:o}={}){var s;let{transition:i=t.getDefaultTransition(),transitionEnd:a,...l}=t.makeTargetAnimatable(e);const c=t.getValue("willChange");r&&(i=r);const u=[],f=o&&((s=t.animationState)===null||s===void 0?void 0:s.getState()[o]);for(const d in l){const m=t.getValue(d),y=l[d];if(!m||y===void 0||f&&Eh(f,d))continue;let v={delay:n,elapsed:0,...i};if(t.shouldReduceMotion&&Pn.has(d)&&(v={...v,type:!1,delay:0}),!m.hasAnimated){const p=t.getProps()[kp];p&&(v.elapsed=bp(p,d))}let x=m.start(fc(d,m,y,v));ts(c)&&(c.add(d),x=x.then(()=>c.remove(d))),u.push(x)}return Promise.all(u).then(()=>{a&&xp(t,a)})}function Sh(t,e,n=0,r=0,o=1,s){const i=[],a=(t.variantChildren.size-1)*r,l=o===1?(c=0)=>c*r:(c=0)=>a-c*r;return Array.from(t.variantChildren).sort(Th).forEach((c,u)=>{i.push(Pa(c,e,{...s,delay:n+l(u)}).then(()=>c.notify("AnimationComplete",e)))}),Promise.all(i)}function Th(t,e){return t.sortNodePosition(e)}function Eh({protectedKeys:t,needsAnimating:e},n){const r=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,r}const dc=[H.Animate,H.InView,H.Focus,H.Hover,H.Tap,H.Drag,H.Exit],Ch=[...dc].reverse(),bh=dc.length;function _h(t){return e=>Promise.all(e.map(({animation:n,options:r})=>xh(t,n,r)))}function kh(t){let e=_h(t);const n=Ah();let r=!0;const o=(l,c)=>{const u=Ps(t,c);if(u){const{transition:f,transitionEnd:d,...m}=u;l={...l,...m,...d}}return l};function s(l){e=l(t)}function i(l,c){const u=t.getProps(),f=t.getVariantContext(!0)||{},d=[],m=new Set;let y={},v=1/0;for(let p=0;p<bh;p++){const g=Ch[p],h=n[g],w=u[g]!==void 0?u[g]:f[g],S=yr(w),E=g===c?h.isActive:null;E===!1&&(v=p);let b=w===f[g]&&w!==u[g]&&S;if(b&&r&&t.manuallyAnimateOnMount&&(b=!1),h.protectedKeys={...y},!h.isActive&&E===null||!w&&!h.prevProp||Cs(w)||typeof w=="boolean")continue;const _=Dh(h.prevProp,w);let M=_||g===c&&h.isActive&&!b&&S||p>v&&S;const A=Array.isArray(w)?w:[w];let O=A.reduce(o,{});E===!1&&(O={});const{prevResolvedValues:W={}}=h,ut={...W,...O},Tt=B=>{M=!0,m.delete(B),h.needsAnimating[B]=!0};for(const B in ut){const Q=O[B],st=W[B];y.hasOwnProperty(B)||(Q!==st?Xo(Q)&&Xo(st)?!o2(Q,st)||_?Tt(B):h.protectedKeys[B]=!0:Q!==void 0?Tt(B):m.add(B):Q!==void 0&&m.has(B)?Tt(B):h.protectedKeys[B]=!0)}h.prevProp=w,h.prevResolvedValues=O,h.isActive&&(y={...y,...O}),r&&t.blockInitialAnimation&&(M=!1),M&&!b&&d.push(...A.map(B=>({animation:B,options:{type:g,...l}})))}if(m.size){const p={};m.forEach(g=>{const h=t.getBaseTarget(g);h!==void 0&&(p[g]=h)}),d.push({animation:p})}let x=!!d.length;return r&&u.initial===!1&&!t.manuallyAnimateOnMount&&(x=!1),r=!1,x?e(d):Promise.resolve()}function a(l,c,u){var f;if(n[l].isActive===c)return Promise.resolve();(f=t.variantChildren)===null||f===void 0||f.forEach(m=>{var y;return(y=m.animationState)===null||y===void 0?void 0:y.setActive(l,c)}),n[l].isActive=c;const d=i(u,l);for(const m in n)n[m].protectedKeys={};return d}return{animateChanges:i,setActive:a,setAnimateFunction:s,getState:()=>n}}function Dh(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!o2(e,t):!1}function un(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Ah(){return{[H.Animate]:un(!0),[H.InView]:un(),[H.Hover]:un(),[H.Tap]:un(),[H.Drag]:un(),[H.Focus]:un(),[H.Exit]:un()}}const Ph={animation:ze(({visualElement:t,animate:e})=>{t.animationState||(t.animationState=kh(t)),Cs(e)&&T.useEffect(()=>e.subscribe(t),[e])}),exit:ze(t=>{const{custom:e,visualElement:n}=t,[r,o]=r2(),s=T.useContext(Ar);T.useEffect(()=>{n.isPresent=r;const i=n.animationState&&n.animationState.setActive(H.Exit,!r,{custom:s&&s.custom||e});i&&!r&&i.then(o)},[r])})},s0=(t,e)=>Math.abs(t-e);function Lh(t,e){const n=s0(t.x,e.x),r=s0(t.y,e.y);return Math.sqrt(n**2+r**2)}class L2{constructor(e,n,{transformPagePoint:r}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const c=Ti(this.lastMoveEventInfo,this.history),u=this.startEvent!==null,f=Lh(c.offset,{x:0,y:0})>=3;if(!u&&!f)return;const{point:d}=c,{timestamp:m}=_e;this.history.push({...d,timestamp:m});const{onStart:y,onMove:v}=this.handlers;u||(y&&y(this.lastMoveEvent,c),this.startEvent=this.lastMoveEvent),v&&v(this.lastMoveEvent,c)},this.handlePointerMove=(c,u)=>{if(this.lastMoveEvent=c,this.lastMoveEventInfo=Si(u,this.transformPagePoint),Wf(c)&&c.buttons===0){this.handlePointerUp(c,u);return}It.update(this.updatePoint,!0)},this.handlePointerUp=(c,u)=>{this.end();const{onEnd:f,onSessionEnd:d}=this.handlers,m=Ti(Si(u,this.transformPagePoint),this.history);this.startEvent&&f&&f(c,m),d&&d(c,m)},Qf(e)&&e.touches.length>1)return;this.handlers=n,this.transformPagePoint=r;const o=Kl(e),s=Si(o,this.transformPagePoint),{point:i}=s,{timestamp:a}=_e;this.history=[{...i,timestamp:a}];const{onSessionStart:l}=n;l&&l(e,Ti(s,this.history)),this.removeListeners=Ds(i1(window,"pointermove",this.handlePointerMove),i1(window,"pointerup",this.handlePointerUp),i1(window,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),en.update(this.updatePoint)}}function Si(t,e){return e?{point:e(t.point)}:t}function i0(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Ti({point:t},e){return{point:t,delta:i0(t,N2(e)),offset:i0(t,Nh(e)),velocity:Rh(e,.1)}}function Nh(t){return t[0]}function N2(t){return t[t.length-1]}function Rh(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,r=null;const o=N2(t);for(;n>=0&&(r=t[n],!(o.timestamp-r.timestamp>To(e)));)n--;if(!r)return{x:0,y:0};const s=(o.timestamp-r.timestamp)/1e3;if(s===0)return{x:0,y:0};const i={x:(o.x-r.x)/s,y:(o.y-r.y)/s};return i.x===1/0&&(i.x=0),i.y===1/0&&(i.y=0),i}function Gt(t){return t.max-t.min}function La(t,e=0,n=.01){return Math.abs(t-e)<=n}function a0(t,e,n,r=.5){t.origin=r,t.originPoint=rt(e.min,e.max,t.origin),t.scale=Gt(n)/Gt(e),(La(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=rt(n.min,n.max,t.origin)-t.originPoint,(La(t.translate)||isNaN(t.translate))&&(t.translate=0)}function X1(t,e,n,r){a0(t.x,e.x,n.x,r==null?void 0:r.originX),a0(t.y,e.y,n.y,r==null?void 0:r.originY)}function l0(t,e,n){t.min=n.min+e.min,t.max=t.min+Gt(e)}function Mh(t,e,n){l0(t.x,e.x,n.x),l0(t.y,e.y,n.y)}function c0(t,e,n){t.min=e.min-n.min,t.max=t.min+Gt(e)}function Z1(t,e,n){c0(t.x,e.x,n.x),c0(t.y,e.y,n.y)}function Oh(t,{min:e,max:n},r){return e!==void 0&&t<e?t=r?rt(e,t,r.min):Math.max(t,e):n!==void 0&&t>n&&(t=r?rt(n,t,r.max):Math.min(t,n)),t}function u0(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function Vh(t,{top:e,left:n,bottom:r,right:o}){return{x:u0(t.x,n,o),y:u0(t.y,e,r)}}function f0(t,e){let n=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,r]=[r,n]),{min:n,max:r}}function Ih(t,e){return{x:f0(t.x,e.x),y:f0(t.y,e.y)}}function qh(t,e){let n=.5;const r=Gt(t),o=Gt(e);return o>r?n=es(e.min,e.max-r,t.min):r>o&&(n=es(t.min,t.max-o,e.min)),h1(0,1,n)}function jh(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const Na=.35;function Fh(t=Na){return t===!1?t=0:t===!0&&(t=Na),{x:d0(t,"left","right"),y:d0(t,"top","bottom")}}function d0(t,e,n){return{min:p0(t,e),max:p0(t,n)}}function p0(t,e){return typeof t=="number"?t:t[e]||0}const h0=()=>({translate:0,scale:1,origin:0,originPoint:0}),J1=()=>({x:h0(),y:h0()}),m0=()=>({min:0,max:0}),at=()=>({x:m0(),y:m0()});function fe(t){return[t("x"),t("y")]}function R2({top:t,left:e,right:n,bottom:r}){return{x:{min:e,max:n},y:{min:t,max:r}}}function Bh({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Uh(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Ei(t){return t===void 0||t===1}function Ra({scale:t,scaleX:e,scaleY:n}){return!Ei(t)||!Ei(e)||!Ei(n)}function pn(t){return Ra(t)||M2(t)||t.z||t.rotate||t.rotateX||t.rotateY}function M2(t){return g0(t.x)||g0(t.y)}function g0(t){return t&&t!=="0%"}function os(t,e,n){const r=t-n,o=e*r;return n+o}function v0(t,e,n,r,o){return o!==void 0&&(t=os(t,o,r)),os(t,n,r)+e}function Ma(t,e=0,n=1,r,o){t.min=v0(t.min,e,n,r,o),t.max=v0(t.max,e,n,r,o)}function O2(t,{x:e,y:n}){Ma(t.x,e.translate,e.scale,e.originPoint),Ma(t.y,n.translate,n.scale,n.originPoint)}function zh(t,e,n,r=!1){var o,s;const i=n.length;if(!i)return;e.x=e.y=1;let a,l;for(let c=0;c<i;c++)a=n[c],l=a.projectionDelta,((s=(o=a.instance)===null||o===void 0?void 0:o.style)===null||s===void 0?void 0:s.display)!=="contents"&&(r&&a.options.layoutScroll&&a.scroll&&a!==a.root&&Xn(t,{x:-a.scroll.offset.x,y:-a.scroll.offset.y}),l&&(e.x*=l.x.scale,e.y*=l.y.scale,O2(t,l)),r&&pn(a.latestValues)&&Xn(t,a.latestValues));e.x=y0(e.x),e.y=y0(e.y)}function y0(t){return Number.isInteger(t)||t>1.0000000000001||t<.999999999999?t:1}function qe(t,e){t.min=t.min+e,t.max=t.max+e}function w0(t,e,[n,r,o]){const s=e[o]!==void 0?e[o]:.5,i=rt(t.min,t.max,s);Ma(t,e[n],e[r],i,e.scale)}const $h=["x","scaleX","originX"],Hh=["y","scaleY","originY"];function Xn(t,e){w0(t.x,e,$h),w0(t.y,e,Hh)}function V2(t,e){return R2(Uh(t.getBoundingClientRect(),e))}function Gh(t,e,n){const r=V2(t,n),{scroll:o}=e;return o&&(qe(r.x,o.offset.x),qe(r.y,o.offset.y)),r}const Wh=new WeakMap;class Qh{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=at(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){if(this.visualElement.isPresent===!1)return;const r=a=>{this.stopAnimation(),n&&this.snapToCursor(Kl(a,"page").point)},o=(a,l)=>{var c;const{drag:u,dragPropagation:f,onDragStart:d}=this.getProps();u&&!f&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Zf(u),!this.openGlobalLock)||(this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),fe(m=>{var y,v;let x=this.getAxisMotionValue(m).get()||0;if(me.test(x)){const p=(v=(y=this.visualElement.projection)===null||y===void 0?void 0:y.layout)===null||v===void 0?void 0:v.layoutBox[m];p&&(x=Gt(p)*(parseFloat(x)/100))}this.originPoint[m]=x}),d==null||d(a,l),(c=this.visualElement.animationState)===null||c===void 0||c.setActive(H.Drag,!0))},s=(a,l)=>{const{dragPropagation:c,dragDirectionLock:u,onDirectionLock:f,onDrag:d}=this.getProps();if(!c&&!this.openGlobalLock)return;const{offset:m}=l;if(u&&this.currentDirection===null){this.currentDirection=Kh(m),this.currentDirection!==null&&(f==null||f(this.currentDirection));return}this.updateAxis("x",l.point,m),this.updateAxis("y",l.point,m),this.visualElement.render(),d==null||d(a,l)},i=(a,l)=>this.stop(a,l);this.panSession=new L2(e,{onSessionStart:r,onStart:o,onMove:s,onSessionEnd:i},{transformPagePoint:this.visualElement.getTransformPagePoint()})}stop(e,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:o}=n;this.startAnimation(o);const{onDragEnd:s}=this.getProps();s==null||s(e,n)}cancel(){var e,n;this.isDragging=!1,this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!1),(e=this.panSession)===null||e===void 0||e.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),(n=this.visualElement.animationState)===null||n===void 0||n.setActive(H.Drag,!1)}updateAxis(e,n,r){const{drag:o}=this.getProps();if(!r||!so(e,o,this.currentDirection))return;const s=this.getAxisMotionValue(e);let i=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(i=Oh(i,this.constraints[e],this.elastic[e])),s.set(i)}resolveConstraints(){const{dragConstraints:e,dragElastic:n}=this.getProps(),{layout:r}=this.visualElement.projection||{},o=this.constraints;e&&Kn(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&r?this.constraints=Vh(r.layoutBox,e):this.constraints=!1,this.elastic=Fh(n),o!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&fe(s=>{this.getAxisMotionValue(s)&&(this.constraints[s]=jh(r.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!Kn(e))return!1;const r=e.current,{projection:o}=this.visualElement;if(!o||!o.layout)return!1;const s=Gh(r,o.root,this.visualElement.getTransformPagePoint());let i=Ih(o.layout.layoutBox,s);if(n){const a=n(Bh(i));this.hasMutatedConstraints=!!a,a&&(i=R2(a))}return i}startAnimation(e){const{drag:n,dragMomentum:r,dragElastic:o,dragTransition:s,dragSnapToOrigin:i,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=fe(u=>{if(!so(u,n,this.currentDirection))return;let f=(l==null?void 0:l[u])||{};i&&(f={min:0,max:0});const d=o?200:1e6,m=o?40:1e7,y={type:"inertia",velocity:r?e[u]:0,bounceStiffness:d,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...s,...f};return this.startAxisValueAnimation(u,y)});return Promise.all(c).then(a)}startAxisValueAnimation(e,n){const r=this.getAxisMotionValue(e);return r.start(fc(e,r,0,n))}stopAnimation(){fe(e=>this.getAxisMotionValue(e).stop())}getAxisMotionValue(e){var n;const r="_drag"+e.toUpperCase(),o=this.visualElement.getProps()[r];return o||this.visualElement.getValue(e,((n=this.visualElement.getProps().initial)===null||n===void 0?void 0:n[e])||0)}snapToCursor(e){fe(n=>{const{drag:r}=this.getProps();if(!so(n,r,this.currentDirection))return;const{projection:o}=this.visualElement,s=this.getAxisMotionValue(n);if(o&&o.layout){const{min:i,max:a}=o.layout.layoutBox[n];s.set(e[n]-rt(i,a,.5))}})}scalePositionWithinConstraints(){var e;if(!this.visualElement.current)return;const{drag:n,dragConstraints:r}=this.getProps(),{projection:o}=this.visualElement;if(!Kn(r)||!o||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};fe(a=>{const l=this.getAxisMotionValue(a);if(l){const c=l.get();s[a]=qh({min:c,max:c},this.constraints[a])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",(e=o.root)===null||e===void 0||e.updateScroll(),o.updateLayout(),this.resolveConstraints(),fe(a=>{if(!so(a,n,null))return;const l=this.getAxisMotionValue(a),{min:c,max:u}=this.constraints[a];l.set(rt(c,u,s[a]))})}addListeners(){var e;if(!this.visualElement.current)return;Wh.set(this.visualElement,this);const n=this.visualElement.current,r=i1(n,"pointerdown",c=>{const{drag:u,dragListener:f=!0}=this.getProps();u&&f&&this.start(c)}),o=()=>{const{dragConstraints:c}=this.getProps();Kn(c)&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,i=s.addEventListener("measure",o);s&&!s.layout&&((e=s.root)===null||e===void 0||e.updateScroll(),s.updateLayout()),o();const a=ks(window,"resize",()=>this.scalePositionWithinConstraints()),l=s.addEventListener("didUpdate",({delta:c,hasLayoutChanged:u})=>{this.isDragging&&u&&(fe(f=>{const d=this.getAxisMotionValue(f);d&&(this.originPoint[f]+=c[f].translate,d.set(d.get()+c[f].translate))}),this.visualElement.render())});return()=>{a(),r(),i(),l==null||l()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:o=!1,dragConstraints:s=!1,dragElastic:i=Na,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:r,dragPropagation:o,dragConstraints:s,dragElastic:i,dragMomentum:a}}}function so(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function Kh(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}function Yh(t){const{dragControls:e,visualElement:n}=t,r=_s(()=>new Qh(n));T.useEffect(()=>e&&e.subscribe(r),[r,e]),T.useEffect(()=>r.addListeners(),[r])}function Xh({onPan:t,onPanStart:e,onPanEnd:n,onPanSessionStart:r,visualElement:o}){const s=t||e||n||r,i=T.useRef(null),{transformPagePoint:a}=T.useContext(jl),l={onSessionStart:r,onStart:e,onMove:t,onEnd:(u,f)=>{i.current=null,n&&n(u,f)}};T.useEffect(()=>{i.current!==null&&i.current.updateHandlers(l)});function c(u){i.current=new L2(u,l,{transformPagePoint:a})}Zo(o,"pointerdown",s&&c),Yl(()=>i.current&&i.current.end())}const Zh={pan:ze(Xh),drag:ze(Yh)};function Oa(t){return typeof t=="string"&&t.startsWith("var(--")}const I2=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Jh(t){const e=I2.exec(t);if(!e)return[,];const[,n,r]=e;return[n,r]}function Va(t,e,n=1){const[r,o]=Jh(t);if(!r)return;const s=window.getComputedStyle(e).getPropertyValue(r);return s?s.trim():Oa(o)?Va(o,e,n+1):o}function t4(t,{...e},n){const r=t.current;if(!(r instanceof Element))return{target:e,transitionEnd:n};n&&(n={...n}),t.values.forEach(o=>{const s=o.get();if(!Oa(s))return;const i=Va(s,r);i&&o.set(i)});for(const o in e){const s=e[o];if(!Oa(s))continue;const i=Va(s,r);i&&(e[o]=i,n&&n[o]===void 0&&(n[o]=s))}return{target:e,transitionEnd:n}}const e4=new Set(["width","height","top","left","right","bottom","x","y"]),q2=t=>e4.has(t),n4=t=>Object.keys(t).some(q2),j2=(t,e)=>{t.set(e,!1),t.set(e)},x0=t=>t===Ln||t===P;var S0;(function(t){t.width="width",t.height="height",t.left="left",t.right="right",t.top="top",t.bottom="bottom"})(S0||(S0={}));const T0=(t,e)=>parseFloat(t.split(", ")[e]),E0=(t,e)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const o=r.match(/^matrix3d\((.+)\)$/);if(o)return T0(o[1],e);{const s=r.match(/^matrix\((.+)\)$/);return s?T0(s[1],t):0}},r4=new Set(["x","y","z"]),o4=Ko.filter(t=>!r4.has(t));function s4(t){const e=[];return o4.forEach(n=>{const r=t.getValue(n);r!==void 0&&(e.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),e.length&&t.render(),e}const C0={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:E0(4,13),y:E0(5,14)},i4=(t,e,n)=>{const r=e.measureViewportBox(),o=e.current,s=getComputedStyle(o),{display:i}=s,a={};i==="none"&&e.setStaticValue("display",t.display||"block"),n.forEach(c=>{a[c]=C0[c](r,s)}),e.render();const l=e.measureViewportBox();return n.forEach(c=>{const u=e.getValue(c);j2(u,a[c]),t[c]=C0[c](l,s)}),t},a4=(t,e,n={},r={})=>{e={...e},r={...r};const o=Object.keys(e).filter(q2);let s=[],i=!1;const a=[];if(o.forEach(l=>{const c=t.getValue(l);if(!t.hasValue(l))return;let u=n[l],f=A1(u);const d=e[l];let m;if(Xo(d)){const y=d.length,v=d[0]===null?1:0;u=d[v],f=A1(u);for(let x=v;x<y;x++)m?Tr(A1(d[x])===m):m=A1(d[x])}else m=A1(d);if(f!==m)if(x0(f)&&x0(m)){const y=c.get();typeof y=="string"&&c.set(parseFloat(y)),typeof d=="string"?e[l]=parseFloat(d):Array.isArray(d)&&m===P&&(e[l]=d.map(parseFloat))}else f!=null&&f.transform&&(m!=null&&m.transform)&&(u===0||d===0)?u===0?c.set(m.transform(u)):e[l]=f.transform(d):(i||(s=s4(t),i=!0),a.push(l),r[l]=r[l]!==void 0?r[l]:e[l],j2(c,d))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,c=i4(e,t,a);return s.length&&s.forEach(([u,f])=>{t.getValue(u).set(f)}),t.render(),An&&l!==null&&window.scrollTo({top:l}),{target:c,transitionEnd:r}}else return{target:e,transitionEnd:r}};function l4(t,e,n,r){return n4(e)?a4(t,e,n,r):{target:e,transitionEnd:r}}const c4=(t,e,n,r)=>{const o=t4(t,e,r);return e=o.target,r=o.transitionEnd,l4(t,e,n,r)},Ia={current:null},F2={current:!1};function u4(){if(F2.current=!0,!!An)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Ia.current=t.matches;t.addListener(e),e()}else Ia.current=!1}function f4(t,e,n){const{willChange:r}=e;for(const o in e){const s=e[o],i=n[o];if(ve(s))t.addValue(o,s),ts(r)&&r.add(o);else if(ve(i))t.addValue(o,m1(s,{owner:t})),ts(r)&&r.remove(o);else if(i!==s)if(t.hasValue(o)){const a=t.getValue(o);!a.hasAnimated&&a.set(s)}else{const a=t.getStaticValue(o);t.addValue(o,m1(a!==void 0?a:s))}}for(const o in n)e[o]===void 0&&t.removeValue(o);return e}const B2=Object.keys(wr),d4=B2.length,b0=["AnimationStart","AnimationComplete","Update","Unmount","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class p4{constructor({parent:e,props:n,reducedMotionConfig:r,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.isPresent=!0,this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>It.render(this.render,!1,!0);const{latestValues:i,renderState:a}=o;this.latestValues=i,this.baseTarget={...i},this.initialValues=n.initial?{...i}:{},this.renderState=a,this.parent=e,this.props=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=s,this.isControllingVariants=bs(n),this.isVariantNode=Mf(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:l,...c}=this.scrapeMotionValuesFromProps(n);for(const u in c){const f=c[u];i[u]!==void 0&&ve(f)&&(f.set(i[u],!1),ts(l)&&l.add(u))}}scrapeMotionValuesFromProps(e){return{}}mount(e){var n;this.current=e,this.projection&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=(n=this.parent)===null||n===void 0?void 0:n.addVariantChild(this)),this.values.forEach((r,o)=>this.bindToMotionValue(o,r)),F2.current||u4(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ia.current,this.parent&&this.parent.children.add(this),this.setProps(this.props)}unmount(){var e,n,r;(e=this.projection)===null||e===void 0||e.unmount(),en.update(this.notifyUpdate),en.render(this.render),this.valueSubscriptions.forEach(o=>o()),(n=this.removeFromVariantTree)===null||n===void 0||n.call(this),(r=this.parent)===null||r===void 0||r.children.delete(this);for(const o in this.events)this.events[o].clear();this.current=null}bindToMotionValue(e,n){const r=Pn.has(e),o=n.on("change",i=>{this.latestValues[e]=i,this.props.onUpdate&&It.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{o(),s()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}loadFeatures(e,n,r,o,s,i){const a=[];for(let l=0;l<d4;l++){const c=B2[l],{isEnabled:u,Component:f}=wr[c];u(e)&&f&&a.push(T.createElement(f,{key:c,...e,visualElement:this}))}if(!this.projection&&s){this.projection=new s(o,this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:c,drag:u,dragConstraints:f,layoutScroll:d}=e;this.projection.setOptions({layoutId:l,layout:c,alwaysMeasureLayout:!!u||f&&Kn(f),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof c=="string"?c:"both",initialPromotionConfig:i,layoutScroll:d})}return a}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):at()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}makeTargetAnimatable(e,n=!0){return this.makeTargetAnimatableFromInstance(e,this.props,n)}setProps(e){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.props=e;for(let n=0;n<b0.length;n++){const r=b0[n];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);const o=e["on"+r];o&&(this.propEventSubscriptions[r]=this.on(r,o))}this.prevMotionValues=f4(this,this.scrapeMotionValuesFromProps(e),this.prevMotionValues)}getProps(){return this.props}getVariant(e){var n;return(n=this.props.variants)===null||n===void 0?void 0:n[e]}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){var e;return this.isVariantNode?this:(e=this.parent)===null||e===void 0?void 0:e.getClosestVariantNode()}getVariantContext(e=!1){var n,r;if(e)return(n=this.parent)===null||n===void 0?void 0:n.getVariantContext();if(!this.isControllingVariants){const s=((r=this.parent)===null||r===void 0?void 0:r.getVariantContext())||{};return this.props.initial!==void 0&&(s.initial=this.props.initial),s}const o={};for(let s=0;s<h4;s++){const i=U2[s],a=this.props[i];(yr(a)||a===!1)&&(o[i]=a)}return o}addVariantChild(e){var n;const r=this.getClosestVariantNode();if(r)return(n=r.variantChildren)===null||n===void 0||n.add(e),()=>r.variantChildren.delete(e)}addValue(e,n){this.hasValue(e)&&this.removeValue(e),this.values.set(e,n),this.latestValues[e]=n.get(),this.bindToMotionValue(e,n)}removeValue(e){var n;this.values.delete(e),(n=this.valueSubscriptions.get(e))===null||n===void 0||n(),this.valueSubscriptions.delete(e),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return r===void 0&&n!==void 0&&(r=m1(n,{owner:this}),this.addValue(e,r)),r}readValue(e){return this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.readValueFromInstance(this.current,e,this.options)}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n;const{initial:r}=this.props,o=typeof r=="string"||typeof r=="object"?(n=Ql(this.props,r))===null||n===void 0?void 0:n[e]:void 0;if(r&&o!==void 0)return o;const s=this.getBaseTargetFromProps(this.props,e);return s!==void 0&&!ve(s)?s:this.initialValues[e]!==void 0&&o===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new Jl),this.events[e].add(n)}notify(e,...n){var r;(r=this.events[e])===null||r===void 0||r.notify(...n)}}const U2=["initial",...dc],h4=U2.length;class z2 extends p4{sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){var r;return(r=e.style)===null||r===void 0?void 0:r[n]}removeValueFromRenderState(e,{vars:n,style:r}){delete n[e],delete r[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:n,...r},{transformValues:o},s){let i=Ep(r,e||{},this);if(o&&(n&&(n=o(n)),r&&(r=o(r)),i&&(i=o(i))),s){Sp(this,r,i);const a=c4(this,r,i,n);n=a.transitionEnd,r=a.target}return{transition:e,transitionEnd:n,...r}}}function m4(t){return window.getComputedStyle(t)}class g4 extends z2{readValueFromInstance(e,n){if(Pn.has(n)){const r=nc(n);return r&&r.default||0}else{const r=m4(e),o=(If(n)?r.getPropertyValue(n):r[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(e,{transformPagePoint:n}){return V2(e,n)}build(e,n,r,o){Ul(e,n,r,o.transformTemplate)}scrapeMotionValuesFromProps(e){return Wl(e)}renderInstance(e,n,r,o){Uf(e,n,r,o)}}class v4 extends z2{constructor(){super(...arguments),this.isSVGTag=!1}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){var r;return Pn.has(n)?((r=nc(n))===null||r===void 0?void 0:r.default)||0:(n=zf.has(n)?n:Gl(n),e.getAttribute(n))}measureInstanceViewportBox(){return at()}scrapeMotionValuesFromProps(e){return Hf(e)}build(e,n,r,o){$l(e,n,r,this.isSVGTag,o.transformTemplate)}renderInstance(e,n,r,o){$f(e,n,r,o)}mount(e){this.isSVGTag=Hl(e.tagName),super.mount(e)}}const y4=(t,e)=>Bl(t)?new v4(e,{enableHardwareAcceleration:!1}):new g4(e,{enableHardwareAcceleration:!0});function _0(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const P1={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(P.test(t))t=parseFloat(t);else return t;const n=_0(t,e.target.x),r=_0(t,e.target.y);return`${n}% ${r}%`}},k0="_$css",w4={correct:(t,{treeScale:e,projectionDelta:n})=>{const r=t,o=t.includes("var("),s=[];o&&(t=t.replace(I2,m=>(s.push(m),k0)));const i=nn.parse(t);if(i.length>5)return r;const a=nn.createTransformer(t),l=typeof i[0]!="number"?1:0,c=n.x.scale*e.x,u=n.y.scale*e.y;i[0+l]/=c,i[1+l]/=u;const f=rt(c,u,.5);typeof i[2+l]=="number"&&(i[2+l]/=f),typeof i[3+l]=="number"&&(i[3+l]/=f);let d=a(i);if(o){let m=0;d=d.replace(k0,()=>{const y=s[m];return m++,y})}return d}};class x4 extends $.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:r,layoutId:o}=this.props,{projection:s}=e;K7(T4),s&&(n.group&&n.group.add(s),r&&r.register&&o&&r.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Q1.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:r,drag:o,isPresent:s}=this.props,i=r.projection;return i&&(i.isPresent=s,o||e.layoutDependency!==n||n===void 0?i.willUpdate():this.safeToRemove(),e.isPresent!==s&&(s?i.promote():i.relegate()||It.postRender(()=>{var a;!((a=i.getStack())===null||a===void 0)&&a.members.length||this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),!e.currentAnimation&&e.isLead()&&this.safeToRemove())}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:o}=e;o&&(o.scheduleCheckAfterUnmount(),n!=null&&n.group&&n.group.remove(o),r!=null&&r.deregister&&r.deregister(o))}safeToRemove(){const{safeToRemove:e}=this.props;e==null||e()}render(){return null}}function S4(t){const[e,n]=r2(),r=T.useContext(Fl);return $.createElement(x4,{...t,layoutGroup:r,switchLayoutGroup:T.useContext(Of),isPresent:e,safeToRemove:n})}const T4={borderRadius:{...P1,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:P1,borderTopRightRadius:P1,borderBottomLeftRadius:P1,borderBottomRightRadius:P1,boxShadow:w4},E4={measureLayout:S4};function C4(t,e,n={}){const r=ve(t)?t:m1(t);return r.start(fc("",r,e,n)),{stop:()=>r.stop(),isAnimating:()=>r.isAnimating()}}const $2=["TopLeft","TopRight","BottomLeft","BottomRight"],b4=$2.length,D0=t=>typeof t=="string"?parseFloat(t):t,A0=t=>typeof t=="number"||P.test(t);function _4(t,e,n,r,o,s){o?(t.opacity=rt(0,n.opacity!==void 0?n.opacity:1,k4(r)),t.opacityExit=rt(e.opacity!==void 0?e.opacity:1,0,D4(r))):s&&(t.opacity=rt(e.opacity!==void 0?e.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let i=0;i<b4;i++){const a=`border${$2[i]}Radius`;let l=P0(e,a),c=P0(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||A0(l)===A0(c)?(t[a]=Math.max(rt(D0(l),D0(c),r),0),(me.test(c)||me.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||n.rotate)&&(t.rotate=rt(e.rotate||0,n.rotate||0,r))}function P0(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const k4=H2(0,.5,cc),D4=H2(.5,.95,lc);function H2(t,e,n){return r=>r<t?0:r>e?1:n(es(t,e,r))}function L0(t,e){t.min=e.min,t.max=e.max}function ne(t,e){L0(t.x,e.x),L0(t.y,e.y)}function N0(t,e,n,r,o){return t-=e,t=os(t,1/n,r),o!==void 0&&(t=os(t,1/o,r)),t}function A4(t,e=0,n=1,r=.5,o,s=t,i=t){if(me.test(e)&&(e=parseFloat(e),e=rt(i.min,i.max,e/100)-i.min),typeof e!="number")return;let a=rt(s.min,s.max,r);t===s&&(a-=e),t.min=N0(t.min,e,n,a,o),t.max=N0(t.max,e,n,a,o)}function R0(t,e,[n,r,o],s,i){A4(t,e[n],e[r],e[o],e.scale,s,i)}const P4=["x","scaleX","originX"],L4=["y","scaleY","originY"];function M0(t,e,n,r){R0(t.x,e,P4,n==null?void 0:n.x,r==null?void 0:r.x),R0(t.y,e,L4,n==null?void 0:n.y,r==null?void 0:r.y)}function O0(t){return t.translate===0&&t.scale===1}function G2(t){return O0(t.x)&&O0(t.y)}function W2(t,e){return t.x.min===e.x.min&&t.x.max===e.x.max&&t.y.min===e.y.min&&t.y.max===e.y.max}function V0(t){return Gt(t.x)/Gt(t.y)}class N4{constructor(){this.members=[]}add(e){Xl(this.members,e),e.scheduleRender()}remove(e){if(Zl(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(o=>e===o);if(n===0)return!1;let r;for(let o=n;o>=0;o--){const s=this.members[o];if(s.isPresent!==!1){r=s;break}}return r?(this.promote(r),!0):!1}promote(e,n){var r;const o=this.lead;if(e!==o&&(this.prevLead=o,this.lead=e,e.show(),o)){o.instance&&o.scheduleRender(),e.scheduleRender(),e.resumeFrom=o,n&&(e.resumeFrom.preserveOpacity=!0),o.snapshot&&(e.snapshot=o.snapshot,e.snapshot.latestValues=o.animationValues||o.latestValues),!((r=e.root)===null||r===void 0)&&r.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:s}=e.options;s===!1&&o.hide()}}exitAnimationComplete(){this.members.forEach(e=>{var n,r,o,s,i;(r=(n=e.options).onExitComplete)===null||r===void 0||r.call(n),(i=(o=e.resumingFrom)===null||o===void 0?void 0:(s=o.options).onExitComplete)===null||i===void 0||i.call(s)})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function I0(t,e,n){let r="";const o=t.x.translate/e.x,s=t.y.translate/e.y;if((o||s)&&(r=`translate3d(${o}px, ${s}px, 0) `),(e.x!==1||e.y!==1)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),n){const{rotate:l,rotateX:c,rotateY:u}=n;l&&(r+=`rotate(${l}deg) `),c&&(r+=`rotateX(${c}deg) `),u&&(r+=`rotateY(${u}deg) `)}const i=t.x.scale*e.x,a=t.y.scale*e.y;return(i!==1||a!==1)&&(r+=`scale(${i}, ${a})`),r||"none"}const R4=(t,e)=>t.depth-e.depth;class M4{constructor(){this.children=[],this.isDirty=!1}add(e){Xl(this.children,e),this.isDirty=!0}remove(e){Zl(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(R4),this.isDirty=!1,this.children.forEach(e)}}const q0=["","X","Y","Z"],j0=1e3;let O4=0;function Q2({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:r,resetTransform:o}){return class{constructor(i,a={},l=e==null?void 0:e()){this.id=O4++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isTransformDirty=!1,this.isProjectionDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.potentialNodes=new Map,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.nodes.forEach(q4),this.nodes.forEach(B4),this.nodes.forEach(U4)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.elementId=i,this.latestValues=a,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0,i&&this.root.registerPotentialNode(i,this);for(let c=0;c<this.path.length;c++)this.path[c].shouldResetTransform=!0;this.root===this&&(this.nodes=new M4)}addEventListener(i,a){return this.eventHandlers.has(i)||this.eventHandlers.set(i,new Jl),this.eventHandlers.get(i).add(a)}notifyListeners(i,...a){const l=this.eventHandlers.get(i);l==null||l.notify(...a)}hasListeners(i){return this.eventHandlers.has(i)}registerPotentialNode(i,a){this.potentialNodes.set(i,a)}mount(i,a=!1){var l;if(this.instance)return;this.isSVG=i instanceof SVGElement&&i.tagName!=="svg",this.instance=i;const{layoutId:c,layout:u,visualElement:f}=this.options;if(f&&!f.current&&f.mount(i),this.root.nodes.add(this),(l=this.parent)===null||l===void 0||l.children.add(this),this.elementId&&this.root.potentialNodes.delete(this.elementId),a&&(u||c)&&(this.isLayoutDirty=!0),t){let d;const m=()=>this.root.updateBlockedByResize=!1;t(i,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=k2(m,250),Q1.hasAnimatedSinceResize&&(Q1.hasAnimatedSinceResize=!1,this.nodes.forEach(B0))})}c&&this.root.registerSharedNode(c,this),this.options.animate!==!1&&f&&(c||u)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:m,hasRelativeTargetChanged:y,layout:v})=>{var x,p,g,h,w;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const S=(p=(x=this.options.transition)!==null&&x!==void 0?x:f.getDefaultTransition())!==null&&p!==void 0?p:W4,{onLayoutAnimationStart:E,onLayoutAnimationComplete:b}=f.getProps(),_=!this.targetLayout||!W2(this.targetLayout,v)||y,M=!m&&y;if(!((g=this.resumeFrom)===null||g===void 0)&&g.instance||M||m&&(_||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,M);const A={...D2(S,"layout"),onPlay:E,onComplete:b};f.shouldReduceMotion&&(A.delay=0,A.type=!1),this.startAnimation(A)}else!m&&this.animationProgress===0&&B0(this),this.isLead()&&((w=(h=this.options).onExitComplete)===null||w===void 0||w.call(h));this.targetLayout=v})}unmount(){var i,a;this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this),(i=this.getStack())===null||i===void 0||i.remove(this),(a=this.parent)===null||a===void 0||a.children.delete(this),this.instance=void 0,en.preRender(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){var i;return this.isAnimationBlocked||((i=this.parent)===null||i===void 0?void 0:i.isTreeAnimationBlocked())||!1}startUpdate(){var i;this.isUpdateBlocked()||(this.isUpdating=!0,(i=this.nodes)===null||i===void 0||i.forEach(z4),this.animationId++)}willUpdate(i=!0){var a,l,c;if(this.root.isUpdateBlocked()){(l=(a=this.options).onExitComplete)===null||l===void 0||l.call(a);return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let m=0;m<this.path.length;m++){const y=this.path[m];y.shouldResetTransform=!0,y.updateScroll("snapshot")}const{layoutId:u,layout:f}=this.options;if(u===void 0&&!f)return;const d=(c=this.options.visualElement)===null||c===void 0?void 0:c.getProps().transformTemplate;this.prevTransformTemplateValue=d==null?void 0:d(this.latestValues,""),this.updateSnapshot(),i&&this.notifyListeners("willUpdate")}didUpdate(){if(this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(F0);return}this.isUpdating&&(this.isUpdating=!1,this.potentialNodes.size&&(this.potentialNodes.forEach(Q4),this.potentialNodes.clear()),this.nodes.forEach(F4),this.nodes.forEach(V4),this.nodes.forEach(I4),this.clearAllSnapshots(),hi.update(),hi.preRender(),hi.render())}clearAllSnapshots(){this.nodes.forEach(j4),this.sharedNodes.forEach($4)}scheduleUpdateProjection(){It.preRender(this.updateProjection,!1,!0)}scheduleCheckAfterUnmount(){It.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){var i;if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=at(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox),(i=this.options.visualElement)===null||i===void 0||i.notify("LayoutMeasure",this.layout.layoutBox,a==null?void 0:a.layoutBox)}updateScroll(i="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===i&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:i,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){var i;if(!o)return;const a=this.isLayoutDirty||this.shouldResetTransform,l=this.projectionDelta&&!G2(this.projectionDelta),c=(i=this.options.visualElement)===null||i===void 0?void 0:i.getProps().transformTemplate,u=c==null?void 0:c(this.latestValues,""),f=u!==this.prevTransformTemplateValue;a&&(l||pn(this.latestValues)||f)&&(o(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(i=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return i&&(l=this.removeTransform(l)),K4(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:i}=this.options;if(!i)return at();const a=i.measureViewportBox(),{scroll:l}=this.root;return l&&(qe(a.x,l.offset.x),qe(a.y,l.offset.y)),a}removeElementScroll(i){const a=at();ne(a,i);for(let l=0;l<this.path.length;l++){const c=this.path[l],{scroll:u,options:f}=c;if(c!==this.root&&u&&f.layoutScroll){if(u.isRoot){ne(a,i);const{scroll:d}=this.root;d&&(qe(a.x,-d.offset.x),qe(a.y,-d.offset.y))}qe(a.x,u.offset.x),qe(a.y,u.offset.y)}}return a}applyTransform(i,a=!1){const l=at();ne(l,i);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&Xn(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),pn(u.latestValues)&&Xn(l,u.latestValues)}return pn(this.latestValues)&&Xn(l,this.latestValues),l}removeTransform(i){var a;const l=at();ne(l,i);for(let c=0;c<this.path.length;c++){const u=this.path[c];if(!u.instance||!pn(u.latestValues))continue;Ra(u.latestValues)&&u.updateSnapshot();const f=at(),d=u.measurePageBox();ne(f,d),M0(l,u.latestValues,(a=u.snapshot)===null||a===void 0?void 0:a.layoutBox,f)}return pn(this.latestValues)&&M0(l,this.latestValues),l}setTargetDelta(i){this.targetDelta=i,this.isProjectionDirty=!0,this.root.scheduleUpdateProjection()}setOptions(i){this.options={...this.options,...i,crossfade:i.crossfade!==void 0?i.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}resolveTargetDelta(){var i;const a=this.getLead();if(this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),!this.isProjectionDirty&&!this.attemptToResolveRelativeTarget)return;const{layout:l,layoutId:c}=this.options;if(!(!this.layout||!(l||c))){if(!this.targetDelta&&!this.relativeTarget){const u=this.getClosestProjectingParent();u&&u.layout?(this.relativeParent=u,this.relativeTarget=at(),this.relativeTargetOrigin=at(),Z1(this.relativeTargetOrigin,this.layout.layoutBox,u.layout.layoutBox),ne(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=at(),this.targetWithTransforms=at()),this.relativeTarget&&this.relativeTargetOrigin&&(!((i=this.relativeParent)===null||i===void 0)&&i.target)?Mh(this.target,this.relativeTarget,this.relativeParent.target):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ne(this.target,this.layout.layoutBox),O2(this.target,this.targetDelta)):ne(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const u=this.getClosestProjectingParent();u&&!!u.resumingFrom==!!this.resumingFrom&&!u.options.layoutScroll&&u.target?(this.relativeParent=u,this.relativeTarget=at(),this.relativeTargetOrigin=at(),Z1(this.relativeTargetOrigin,this.target,u.target),ne(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Ra(this.parent.latestValues)||M2(this.parent.latestValues)))return(this.parent.relativeTarget||this.parent.targetDelta)&&this.parent.layout?this.parent:this.parent.getClosestProjectingParent()}calcProjection(){var i;const{isProjectionDirty:a,isTransformDirty:l}=this;this.isProjectionDirty=this.isTransformDirty=!1;const c=this.getLead(),u=!!this.resumingFrom||this!==c;let f=!0;if(a&&(f=!1),u&&l&&(f=!1),f)return;const{layout:d,layoutId:m}=this.options;if(this.isTreeAnimating=!!(!((i=this.parent)===null||i===void 0)&&i.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||m))return;ne(this.layoutCorrected,this.layout.layoutBox),zh(this.layoutCorrected,this.treeScale,this.path,u);const{target:y}=c;if(!y)return;this.projectionDelta||(this.projectionDelta=J1(),this.projectionDeltaWithTransform=J1());const v=this.treeScale.x,x=this.treeScale.y,p=this.projectionTransform;X1(this.projectionDelta,this.layoutCorrected,y,this.latestValues),this.projectionTransform=I0(this.projectionDelta,this.treeScale),(this.projectionTransform!==p||this.treeScale.x!==v||this.treeScale.y!==x)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",y))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(i=!0){var a,l,c;(l=(a=this.options).scheduleRender)===null||l===void 0||l.call(a),i&&((c=this.getStack())===null||c===void 0||c.scheduleRender()),this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(i,a=!1){var l,c;const u=this.snapshot,f=(u==null?void 0:u.latestValues)||{},d={...this.latestValues},m=J1();this.relativeTarget=this.relativeTargetOrigin=void 0,this.attemptToResolveRelativeTarget=!a;const y=at(),v=(u==null?void 0:u.source)!==((l=this.layout)===null||l===void 0?void 0:l.source),x=(((c=this.getStack())===null||c===void 0?void 0:c.members.length)||0)<=1,p=!!(v&&!x&&this.options.crossfade===!0&&!this.path.some(G4));this.animationProgress=0,this.mixTargetDelta=g=>{var h;const w=g/1e3;U0(m.x,i.x,w),U0(m.y,i.y,w),this.setTargetDelta(m),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&(!((h=this.relativeParent)===null||h===void 0)&&h.layout)&&(Z1(y,this.layout.layoutBox,this.relativeParent.layout.layoutBox),H4(this.relativeTarget,this.relativeTargetOrigin,y,w)),v&&(this.animationValues=d,_4(d,f,this.latestValues,w,p,x)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=w},this.mixTargetDelta(0)}startAnimation(i){var a,l;this.notifyListeners("animationStart"),(a=this.currentAnimation)===null||a===void 0||a.stop(),this.resumingFrom&&((l=this.resumingFrom.currentAnimation)===null||l===void 0||l.stop()),this.pendingAnimation&&(en.update(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=It.update(()=>{Q1.hasAnimatedSinceResize=!0,this.currentAnimation=C4(0,j0,{...i,onUpdate:c=>{var u;this.mixTargetDelta(c),(u=i.onUpdate)===null||u===void 0||u.call(i,c)},onComplete:()=>{var c;(c=i.onComplete)===null||c===void 0||c.call(i),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){var i;this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0),(i=this.getStack())===null||i===void 0||i.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){var i;this.currentAnimation&&((i=this.mixTargetDelta)===null||i===void 0||i.call(this,j0),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const i=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=i;if(!(!a||!l||!c)){if(this!==i&&this.layout&&c&&K2(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||at();const f=Gt(this.layout.layoutBox.x);l.x.min=i.target.x.min,l.x.max=l.x.min+f;const d=Gt(this.layout.layoutBox.y);l.y.min=i.target.y.min,l.y.max=l.y.min+d}ne(a,l),Xn(a,u),X1(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(i,a){var l,c,u;this.sharedNodes.has(i)||this.sharedNodes.set(i,new N4),this.sharedNodes.get(i).add(a),a.promote({transition:(l=a.options.initialPromotionConfig)===null||l===void 0?void 0:l.transition,preserveFollowOpacity:(u=(c=a.options.initialPromotionConfig)===null||c===void 0?void 0:c.shouldPreserveFollowOpacity)===null||u===void 0?void 0:u.call(c,a)})}isLead(){const i=this.getStack();return i?i.lead===this:!0}getLead(){var i;const{layoutId:a}=this.options;return a?((i=this.getStack())===null||i===void 0?void 0:i.lead)||this:this}getPrevLead(){var i;const{layoutId:a}=this.options;return a?(i=this.getStack())===null||i===void 0?void 0:i.prevLead:void 0}getStack(){const{layoutId:i}=this.options;if(i)return this.root.sharedNodes.get(i)}promote({needsReset:i,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),i&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const i=this.getStack();return i?i.relegate(this):!1}resetRotation(){const{visualElement:i}=this.options;if(!i)return;let a=!1;const{latestValues:l}=i;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const c={};for(let u=0;u<q0.length;u++){const f="rotate"+q0[u];l[f]&&(c[f]=l[f],i.setStaticValue(f,0))}i==null||i.render();for(const u in c)i.setStaticValue(u,c[u]);i.scheduleRender()}getProjectionStyles(i={}){var a,l,c;const u={};if(!this.instance||this.isSVG)return u;if(this.isVisible)u.visibility="";else return{visibility:"hidden"};const f=(a=this.options.visualElement)===null||a===void 0?void 0:a.getProps().transformTemplate;if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=So(i.pointerEvents)||"",u.transform=f?f(this.latestValues,""):"none",u;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=So(i.pointerEvents)||""),this.hasProjected&&!pn(this.latestValues)&&(x.transform=f?f({},""):"none",this.hasProjected=!1),x}const m=d.animationValues||d.latestValues;this.applyTransformsToTarget(),u.transform=I0(this.projectionDeltaWithTransform,this.treeScale,m),f&&(u.transform=f(m,u.transform));const{x:y,y:v}=this.projectionDelta;u.transformOrigin=`${y.origin*100}% ${v.origin*100}% 0`,d.animationValues?u.opacity=d===this?(c=(l=m.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&c!==void 0?c:1:this.preserveOpacity?this.latestValues.opacity:m.opacityExit:u.opacity=d===this?m.opacity!==void 0?m.opacity:"":m.opacityExit!==void 0?m.opacityExit:0;for(const x in Qo){if(m[x]===void 0)continue;const{correct:p,applyTo:g}=Qo[x],h=p(m[x],d);if(g){const w=g.length;for(let S=0;S<w;S++)u[g[S]]=h}else u[x]=h}return this.options.layoutId&&(u.pointerEvents=d===this?So(i.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(i=>{var a;return(a=i.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(F0),this.root.sharedNodes.clear()}}}function V4(t){t.updateLayout()}function I4(t){var e,n,r;const o=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&o&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:a}=t.options,l=o.source!==t.layout.source;a==="size"?fe(m=>{const y=l?o.measuredBox[m]:o.layoutBox[m],v=Gt(y);y.min=s[m].min,y.max=y.min+v}):K2(a,o.layoutBox,s)&&fe(m=>{const y=l?o.measuredBox[m]:o.layoutBox[m],v=Gt(s[m]);y.max=y.min+v});const c=J1();X1(c,s,o.layoutBox);const u=J1();l?X1(u,t.applyTransform(i,!0),o.measuredBox):X1(u,s,o.layoutBox);const f=!G2(c);let d=!1;if(!t.resumeFrom){const m=t.getClosestProjectingParent();if(m&&!m.resumeFrom){const{snapshot:y,layout:v}=m;if(y&&v){const x=at();Z1(x,o.layoutBox,y.layoutBox);const p=at();Z1(p,s,v.layoutBox),W2(x,p)||(d=!0)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:o,delta:u,layoutDelta:c,hasLayoutChanged:f,hasRelativeTargetChanged:d})}else t.isLead()&&((r=(n=t.options).onExitComplete)===null||r===void 0||r.call(n));t.options.transition=void 0}function q4(t){t.isProjectionDirty||(t.isProjectionDirty=!!(t.parent&&t.parent.isProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=!!(t.parent&&t.parent.isTransformDirty))}function j4(t){t.clearSnapshot()}function F0(t){t.clearMeasurements()}function F4(t){const{visualElement:e}=t.options;e!=null&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function B0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0}function B4(t){t.resolveTargetDelta()}function U4(t){t.calcProjection()}function z4(t){t.resetRotation()}function $4(t){t.removeLeadSnapshot()}function U0(t,e,n){t.translate=rt(e.translate,0,n),t.scale=rt(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function z0(t,e,n,r){t.min=rt(e.min,n.min,r),t.max=rt(e.max,n.max,r)}function H4(t,e,n,r){z0(t.x,e.x,n.x,r),z0(t.y,e.y,n.y,r)}function G4(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const W4={duration:.45,ease:[.4,0,.1,1]};function Q4(t,e){let n=t.root;for(let s=t.path.length-1;s>=0;s--)if(t.path[s].instance){n=t.path[s];break}const o=(n&&n!==t.root?n.instance:document).querySelector(`[data-projection-id="${e}"]`);o&&t.mount(o,!0)}function $0(t){t.min=Math.round(t.min),t.max=Math.round(t.max)}function K4(t){$0(t.x),$0(t.y)}function K2(t,e,n){return t==="position"||t==="preserve-aspect"&&!La(V0(e),V0(n),.2)}const Y4=Q2({attachResizeListener:(t,e)=>ks(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Ci={current:void 0},X4=Q2({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Ci.current){const t=new Y4(0,{});t.mount(window),t.setOptions({layoutScroll:!0}),Ci.current=t}return Ci.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),Z4={...Ph,...K3,...Zh,...E4},Y2=W7((t,e)=>b3(t,e,Z4,y4,X4));function X2(){const t=T.useRef(!1);return W1(()=>(t.current=!0,()=>{t.current=!1}),[]),t}function J4(){const t=X2(),[e,n]=T.useState(0),r=T.useCallback(()=>{t.current&&n(e+1)},[e]);return[T.useCallback(()=>It.postRender(r),[r]),e]}class tm extends T.Component{getSnapshotBeforeUpdate(e){const n=this.props.childRef.current;if(n&&e.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function em({children:t,isPresent:e}){const n=T.useId(),r=T.useRef(null),o=T.useRef({width:0,height:0,top:0,left:0});return T.useInsertionEffect(()=>{const{width:s,height:i,top:a,left:l}=o.current;if(e||!r.current||!s||!i)return;r.current.dataset.motionPopId=n;const c=document.createElement("style");return document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${s}px !important;
            height: ${i}px !important;
            top: ${a}px !important;
            left: ${l}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[e]),T.createElement(tm,{isPresent:e,childRef:r,sizeRef:o},T.cloneElement(t,{ref:r}))}const bi=({children:t,initial:e,isPresent:n,onExitComplete:r,custom:o,presenceAffectsLayout:s,mode:i})=>{const a=_s(nm),l=T.useId(),c=T.useMemo(()=>({id:l,initial:e,isPresent:n,custom:o,onExitComplete:u=>{a.set(u,!0);for(const f of a.values())if(!f)return;r&&r()},register:u=>(a.set(u,!1),()=>a.delete(u))}),s?void 0:[n]);return T.useMemo(()=>{a.forEach((u,f)=>a.set(f,!1))},[n]),T.useEffect(()=>{!n&&!a.size&&r&&r()},[n]),i==="popLayout"&&(t=T.createElement(em,{isPresent:n},t)),T.createElement(Ar.Provider,{value:c},t)};function nm(){return new Map}const Vn=t=>t.key||"";function rm(t,e){t.forEach(n=>{const r=Vn(n);e.set(r,n)})}function om(t){const e=[];return T.Children.forEach(t,n=>{T.isValidElement(n)&&e.push(n)}),e}const sm=({children:t,custom:e,initial:n=!0,onExitComplete:r,exitBeforeEnter:o,presenceAffectsLayout:s=!0,mode:i="sync"})=>{o&&(i="wait",n2(!1,"Replace exitBeforeEnter with mode='wait'"));let[a]=J4();const l=T.useContext(Fl).forceRender;l&&(a=l);const c=X2(),u=om(t);let f=u;const d=new Set,m=T.useRef(f),y=T.useRef(new Map).current,v=T.useRef(!0);if(W1(()=>{v.current=!1,rm(u,y),m.current=f}),Yl(()=>{v.current=!0,y.clear(),d.clear()}),v.current)return T.createElement(T.Fragment,null,f.map(h=>T.createElement(bi,{key:Vn(h),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:s,mode:i},h)));f=[...f];const x=m.current.map(Vn),p=u.map(Vn),g=x.length;for(let h=0;h<g;h++){const w=x[h];p.indexOf(w)===-1&&d.add(w)}return i==="wait"&&d.size&&(f=[]),d.forEach(h=>{if(p.indexOf(h)!==-1)return;const w=y.get(h);if(!w)return;const S=x.indexOf(h),E=()=>{y.delete(h),d.delete(h);const b=m.current.findIndex(_=>_.key===h);if(m.current.splice(b,1),!d.size){if(m.current=u,c.current===!1)return;a(),r&&r()}};f.splice(S,0,T.createElement(bi,{key:Vn(w),isPresent:!1,onExitComplete:E,custom:e,presenceAffectsLayout:s,mode:i},w))}),f=f.map(h=>{const w=h.key;return d.has(w)?h:T.createElement(bi,{key:Vn(h),isPresent:!0,presenceAffectsLayout:s,mode:i},h)}),e2!=="production"&&i==="wait"&&f.length>1&&console.warn(`You're attempting to animate multiple children within AnimatePresence, but its mode is set to "wait". This will lead to odd visual behaviour.`),T.createElement(T.Fragment,null,d.size?f:f.map(h=>T.cloneElement(h)))};var im=t=>{var{children:e,hide:n,mode:r="wait"}=t,{isPlaying:o,safeToRemove:s,isStopped:i}=Nf();return $.createElement(sm,{mode:r,onExitComplete:i?s:null},!n&&o?e:null)},am=$.memo(t=>{var{play:e,items:n}=t,[r,o]=$.useState(e);return!r&&e&&o(!0),!(n!=null&&n.length)||!r?null:$.createElement(lm,t)}),H0=0,lm=t=>{var{items:e,renderItem:n,pixelsPerFrame:r=5,frameRate:o=25,play:s,loop:i=!0,onExit:a}=t,l=$.createRef(),[c,u]=$.useState(),[f,d]=$.useState([[H0,e[0]]]);$.useLayoutEffect(()=>{u(l.current.getBoundingClientRect())},[]);var m=v=>{var x=e.findIndex(h=>{var{id:w}=h;return w===v.id}),p=(x+1)%e.length,g=e[p];d(h=>[...h,[++H0,g]])},y=()=>{f.length===1&&a&&a(),d(v=>v.slice(1))};return $.createElement("div",{ref:l,style:{position:"relative",width:"100%",height:"100%"}},c!=null&&s&&f.map(v=>{var[x,p]=v,g=e.findIndex(h=>{var{id:w}=h;return w===p.id});return $.createElement(cm,{key:x,item:p,offset:Math.round(c.width),pixelsPerSecond:r*o,onEntered:m,onExited:y},n(p,{showSeparator:i||g<e.length-1}))}))},cm=t=>{var{item:e,children:n,offset:r,pixelsPerSecond:o,onEntered:s,onExited:i}=t,a=$.useRef(),[l,c]=$.useState();$.useEffect(()=>{c(a.current.getBoundingClientRect().width)},[]);var u=r+(l!=null?l:0);return $.createElement(Y2.div,{ref:a,style:{position:"absolute",left:r},animate:l?{x:-u,transition:{duration:u/o,ease:"linear"}}:!1,onAnimationStart:()=>{window.setTimeout(()=>{s(e)},l/o*1e3)},onAnimationComplete:()=>{i(e)}},n)},Z2={},Rr={},Ls={},Ns={};Object.defineProperty(Ns,"__esModule",{value:!0});var um=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),fm=String.fromCodePoint||function(t){var e="";return t>65535&&(t-=65536,e+=String.fromCharCode(t>>>10&1023|55296),t=56320|t&1023),e+=String.fromCharCode(t),e};function dm(t){var e;return t>=55296&&t<=57343||t>1114111?"�":fm((e=um.get(t))!==null&&e!==void 0?e:t)}Ns.default=dm;var J2={},pc={};Object.defineProperty(pc,"__esModule",{value:!0});pc.default=new Uint16Array([14866,60,237,340,721,1312,1562,1654,1838,1957,2183,2239,2301,2958,3037,3893,4123,4298,4330,4801,5191,5395,5752,5903,5943,5972,6050,0,0,0,0,0,0,6135,6565,7422,8183,8738,9242,9503,9938,10189,10573,10637,10715,11950,12246,13539,13950,14445,14533,15364,16514,16980,17390,17763,17849,18036,18125,4096,69,77,97,98,99,102,103,108,109,110,111,112,114,115,116,117,92,100,106,115,122,137,142,151,157,163,167,182,196,204,220,229,108,105,103,33024,198,59,32768,198,80,33024,38,59,32768,38,99,117,116,101,33024,193,59,32768,193,114,101,118,101,59,32768,258,512,105,121,127,134,114,99,33024,194,59,32768,194,59,32768,1040,114,59,32896,55349,56580,114,97,118,101,33024,192,59,32768,192,112,104,97,59,32768,913,97,99,114,59,32768,256,100,59,32768,10835,512,103,112,172,177,111,110,59,32768,260,102,59,32896,55349,56632,112,108,121,70,117,110,99,116,105,111,110,59,32768,8289,105,110,103,33024,197,59,32768,197,512,99,115,209,214,114,59,32896,55349,56476,105,103,110,59,32768,8788,105,108,100,101,33024,195,59,32768,195,109,108,33024,196,59,32768,196,2048,97,99,101,102,111,114,115,117,253,278,282,310,315,321,327,332,512,99,114,258,267,107,115,108,97,115,104,59,32768,8726,583,271,274,59,32768,10983,101,100,59,32768,8966,121,59,32768,1041,768,99,114,116,289,296,306,97,117,115,101,59,32768,8757,110,111,117,108,108,105,115,59,32768,8492,97,59,32768,914,114,59,32896,55349,56581,112,102,59,32896,55349,56633,101,118,101,59,32768,728,99,114,59,32768,8492,109,112,101,113,59,32768,8782,3584,72,79,97,99,100,101,102,104,105,108,111,114,115,117,368,373,380,426,461,466,487,491,495,533,593,695,701,707,99,121,59,32768,1063,80,89,33024,169,59,32768,169,768,99,112,121,387,393,419,117,116,101,59,32768,262,512,59,105,398,400,32768,8914,116,97,108,68,105,102,102,101,114,101,110,116,105,97,108,68,59,32768,8517,108,101,121,115,59,32768,8493,1024,97,101,105,111,435,441,449,454,114,111,110,59,32768,268,100,105,108,33024,199,59,32768,199,114,99,59,32768,264,110,105,110,116,59,32768,8752,111,116,59,32768,266,512,100,110,471,478,105,108,108,97,59,32768,184,116,101,114,68,111,116,59,32768,183,114,59,32768,8493,105,59,32768,935,114,99,108,101,1024,68,77,80,84,508,513,520,526,111,116,59,32768,8857,105,110,117,115,59,32768,8854,108,117,115,59,32768,8853,105,109,101,115,59,32768,8855,111,512,99,115,539,562,107,119,105,115,101,67,111,110,116,111,117,114,73,110,116,101,103,114,97,108,59,32768,8754,101,67,117,114,108,121,512,68,81,573,586,111,117,98,108,101,81,117,111,116,101,59,32768,8221,117,111,116,101,59,32768,8217,1024,108,110,112,117,602,614,648,664,111,110,512,59,101,609,611,32768,8759,59,32768,10868,768,103,105,116,621,629,634,114,117,101,110,116,59,32768,8801,110,116,59,32768,8751,111,117,114,73,110,116,101,103,114,97,108,59,32768,8750,512,102,114,653,656,59,32768,8450,111,100,117,99,116,59,32768,8720,110,116,101,114,67,108,111,99,107,119,105,115,101,67,111,110,116,111,117,114,73,110,116,101,103,114,97,108,59,32768,8755,111,115,115,59,32768,10799,99,114,59,32896,55349,56478,112,512,59,67,713,715,32768,8915,97,112,59,32768,8781,2816,68,74,83,90,97,99,101,102,105,111,115,743,758,763,768,773,795,809,821,826,910,1295,512,59,111,748,750,32768,8517,116,114,97,104,100,59,32768,10513,99,121,59,32768,1026,99,121,59,32768,1029,99,121,59,32768,1039,768,103,114,115,780,786,790,103,101,114,59,32768,8225,114,59,32768,8609,104,118,59,32768,10980,512,97,121,800,806,114,111,110,59,32768,270,59,32768,1044,108,512,59,116,815,817,32768,8711,97,59,32768,916,114,59,32896,55349,56583,512,97,102,831,897,512,99,109,836,891,114,105,116,105,99,97,108,1024,65,68,71,84,852,859,877,884,99,117,116,101,59,32768,180,111,581,864,867,59,32768,729,98,108,101,65,99,117,116,101,59,32768,733,114,97,118,101,59,32768,96,105,108,100,101,59,32768,732,111,110,100,59,32768,8900,102,101,114,101,110,116,105,97,108,68,59,32768,8518,2113,920,0,0,0,925,946,0,1139,102,59,32896,55349,56635,768,59,68,69,931,933,938,32768,168,111,116,59,32768,8412,113,117,97,108,59,32768,8784,98,108,101,1536,67,68,76,82,85,86,961,978,996,1080,1101,1125,111,110,116,111,117,114,73,110,116,101,103,114,97,108,59,32768,8751,111,1093,985,0,0,988,59,32768,168,110,65,114,114,111,119,59,32768,8659,512,101,111,1001,1034,102,116,768,65,82,84,1010,1017,1029,114,114,111,119,59,32768,8656,105,103,104,116,65,114,114,111,119,59,32768,8660,101,101,59,32768,10980,110,103,512,76,82,1041,1068,101,102,116,512,65,82,1049,1056,114,114,111,119,59,32768,10232,105,103,104,116,65,114,114,111,119,59,32768,10234,105,103,104,116,65,114,114,111,119,59,32768,10233,105,103,104,116,512,65,84,1089,1096,114,114,111,119,59,32768,8658,101,101,59,32768,8872,112,1042,1108,0,0,1115,114,114,111,119,59,32768,8657,111,119,110,65,114,114,111,119,59,32768,8661,101,114,116,105,99,97,108,66,97,114,59,32768,8741,110,1536,65,66,76,82,84,97,1152,1179,1186,1236,1272,1288,114,114,111,119,768,59,66,85,1163,1165,1170,32768,8595,97,114,59,32768,10515,112,65,114,114,111,119,59,32768,8693,114,101,118,101,59,32768,785,101,102,116,1315,1196,0,1209,0,1220,105,103,104,116,86,101,99,116,111,114,59,32768,10576,101,101,86,101,99,116,111,114,59,32768,10590,101,99,116,111,114,512,59,66,1229,1231,32768,8637,97,114,59,32768,10582,105,103,104,116,805,1245,0,1256,101,101,86,101,99,116,111,114,59,32768,10591,101,99,116,111,114,512,59,66,1265,1267,32768,8641,97,114,59,32768,10583,101,101,512,59,65,1279,1281,32768,8868,114,114,111,119,59,32768,8615,114,114,111,119,59,32768,8659,512,99,116,1300,1305,114,59,32896,55349,56479,114,111,107,59,32768,272,4096,78,84,97,99,100,102,103,108,109,111,112,113,115,116,117,120,1344,1348,1354,1363,1386,1391,1396,1405,1413,1460,1475,1483,1514,1527,1531,1538,71,59,32768,330,72,33024,208,59,32768,208,99,117,116,101,33024,201,59,32768,201,768,97,105,121,1370,1376,1383,114,111,110,59,32768,282,114,99,33024,202,59,32768,202,59,32768,1069,111,116,59,32768,278,114,59,32896,55349,56584,114,97,118,101,33024,200,59,32768,200,101,109,101,110,116,59,32768,8712,512,97,112,1418,1423,99,114,59,32768,274,116,121,1060,1431,0,0,1444,109,97,108,108,83,113,117,97,114,101,59,32768,9723,101,114,121,83,109,97,108,108,83,113,117,97,114,101,59,32768,9643,512,103,112,1465,1470,111,110,59,32768,280,102,59,32896,55349,56636,115,105,108,111,110,59,32768,917,117,512,97,105,1489,1504,108,512,59,84,1495,1497,32768,10869,105,108,100,101,59,32768,8770,108,105,98,114,105,117,109,59,32768,8652,512,99,105,1519,1523,114,59,32768,8496,109,59,32768,10867,97,59,32768,919,109,108,33024,203,59,32768,203,512,105,112,1543,1549,115,116,115,59,32768,8707,111,110,101,110,116,105,97,108,69,59,32768,8519,1280,99,102,105,111,115,1572,1576,1581,1620,1648,121,59,32768,1060,114,59,32896,55349,56585,108,108,101,100,1060,1591,0,0,1604,109,97,108,108,83,113,117,97,114,101,59,32768,9724,101,114,121,83,109,97,108,108,83,113,117,97,114,101,59,32768,9642,1601,1628,0,1633,0,0,1639,102,59,32896,55349,56637,65,108,108,59,32768,8704,114,105,101,114,116,114,102,59,32768,8497,99,114,59,32768,8497,3072,74,84,97,98,99,100,102,103,111,114,115,116,1678,1683,1688,1701,1708,1729,1734,1739,1742,1748,1828,1834,99,121,59,32768,1027,33024,62,59,32768,62,109,109,97,512,59,100,1696,1698,32768,915,59,32768,988,114,101,118,101,59,32768,286,768,101,105,121,1715,1721,1726,100,105,108,59,32768,290,114,99,59,32768,284,59,32768,1043,111,116,59,32768,288,114,59,32896,55349,56586,59,32768,8921,112,102,59,32896,55349,56638,101,97,116,101,114,1536,69,70,71,76,83,84,1766,1783,1794,1803,1809,1821,113,117,97,108,512,59,76,1775,1777,32768,8805,101,115,115,59,32768,8923,117,108,108,69,113,117,97,108,59,32768,8807,114,101,97,116,101,114,59,32768,10914,101,115,115,59,32768,8823,108,97,110,116,69,113,117,97,108,59,32768,10878,105,108,100,101,59,32768,8819,99,114,59,32896,55349,56482,59,32768,8811,2048,65,97,99,102,105,111,115,117,1854,1861,1874,1880,1884,1897,1919,1934,82,68,99,121,59,32768,1066,512,99,116,1866,1871,101,107,59,32768,711,59,32768,94,105,114,99,59,32768,292,114,59,32768,8460,108,98,101,114,116,83,112,97,99,101,59,32768,8459,833,1902,0,1906,102,59,32768,8461,105,122,111,110,116,97,108,76,105,110,101,59,32768,9472,512,99,116,1924,1928,114,59,32768,8459,114,111,107,59,32768,294,109,112,533,1940,1950,111,119,110,72,117,109,112,59,32768,8782,113,117,97,108,59,32768,8783,3584,69,74,79,97,99,100,102,103,109,110,111,115,116,117,1985,1990,1996,2001,2010,2025,2030,2034,2043,2077,2134,2155,2160,2167,99,121,59,32768,1045,108,105,103,59,32768,306,99,121,59,32768,1025,99,117,116,101,33024,205,59,32768,205,512,105,121,2015,2022,114,99,33024,206,59,32768,206,59,32768,1048,111,116,59,32768,304,114,59,32768,8465,114,97,118,101,33024,204,59,32768,204,768,59,97,112,2050,2052,2070,32768,8465,512,99,103,2057,2061,114,59,32768,298,105,110,97,114,121,73,59,32768,8520,108,105,101,115,59,32768,8658,837,2082,0,2110,512,59,101,2086,2088,32768,8748,512,103,114,2093,2099,114,97,108,59,32768,8747,115,101,99,116,105,111,110,59,32768,8898,105,115,105,98,108,101,512,67,84,2120,2127,111,109,109,97,59,32768,8291,105,109,101,115,59,32768,8290,768,103,112,116,2141,2146,2151,111,110,59,32768,302,102,59,32896,55349,56640,97,59,32768,921,99,114,59,32768,8464,105,108,100,101,59,32768,296,828,2172,0,2177,99,121,59,32768,1030,108,33024,207,59,32768,207,1280,99,102,111,115,117,2193,2206,2211,2217,2232,512,105,121,2198,2203,114,99,59,32768,308,59,32768,1049,114,59,32896,55349,56589,112,102,59,32896,55349,56641,820,2222,0,2227,114,59,32896,55349,56485,114,99,121,59,32768,1032,107,99,121,59,32768,1028,1792,72,74,97,99,102,111,115,2253,2258,2263,2269,2283,2288,2294,99,121,59,32768,1061,99,121,59,32768,1036,112,112,97,59,32768,922,512,101,121,2274,2280,100,105,108,59,32768,310,59,32768,1050,114,59,32896,55349,56590,112,102,59,32896,55349,56642,99,114,59,32896,55349,56486,2816,74,84,97,99,101,102,108,109,111,115,116,2323,2328,2333,2374,2396,2775,2780,2797,2804,2934,2954,99,121,59,32768,1033,33024,60,59,32768,60,1280,99,109,110,112,114,2344,2350,2356,2360,2370,117,116,101,59,32768,313,98,100,97,59,32768,923,103,59,32768,10218,108,97,99,101,116,114,102,59,32768,8466,114,59,32768,8606,768,97,101,121,2381,2387,2393,114,111,110,59,32768,317,100,105,108,59,32768,315,59,32768,1051,512,102,115,2401,2702,116,2560,65,67,68,70,82,84,85,86,97,114,2423,2470,2479,2530,2537,2561,2618,2666,2683,2690,512,110,114,2428,2441,103,108,101,66,114,97,99,107,101,116,59,32768,10216,114,111,119,768,59,66,82,2451,2453,2458,32768,8592,97,114,59,32768,8676,105,103,104,116,65,114,114,111,119,59,32768,8646,101,105,108,105,110,103,59,32768,8968,111,838,2485,0,2498,98,108,101,66,114,97,99,107,101,116,59,32768,10214,110,805,2503,0,2514,101,101,86,101,99,116,111,114,59,32768,10593,101,99,116,111,114,512,59,66,2523,2525,32768,8643,97,114,59,32768,10585,108,111,111,114,59,32768,8970,105,103,104,116,512,65,86,2546,2553,114,114,111,119,59,32768,8596,101,99,116,111,114,59,32768,10574,512,101,114,2566,2591,101,768,59,65,86,2574,2576,2583,32768,8867,114,114,111,119,59,32768,8612,101,99,116,111,114,59,32768,10586,105,97,110,103,108,101,768,59,66,69,2604,2606,2611,32768,8882,97,114,59,32768,10703,113,117,97,108,59,32768,8884,112,768,68,84,86,2626,2638,2649,111,119,110,86,101,99,116,111,114,59,32768,10577,101,101,86,101,99,116,111,114,59,32768,10592,101,99,116,111,114,512,59,66,2659,2661,32768,8639,97,114,59,32768,10584,101,99,116,111,114,512,59,66,2676,2678,32768,8636,97,114,59,32768,10578,114,114,111,119,59,32768,8656,105,103,104,116,97,114,114,111,119,59,32768,8660,115,1536,69,70,71,76,83,84,2716,2730,2741,2750,2756,2768,113,117,97,108,71,114,101,97,116,101,114,59,32768,8922,117,108,108,69,113,117,97,108,59,32768,8806,114,101,97,116,101,114,59,32768,8822,101,115,115,59,32768,10913,108,97,110,116,69,113,117,97,108,59,32768,10877,105,108,100,101,59,32768,8818,114,59,32896,55349,56591,512,59,101,2785,2787,32768,8920,102,116,97,114,114,111,119,59,32768,8666,105,100,111,116,59,32768,319,768,110,112,119,2811,2899,2904,103,1024,76,82,108,114,2821,2848,2860,2887,101,102,116,512,65,82,2829,2836,114,114,111,119,59,32768,10229,105,103,104,116,65,114,114,111,119,59,32768,10231,105,103,104,116,65,114,114,111,119,59,32768,10230,101,102,116,512,97,114,2868,2875,114,114,111,119,59,32768,10232,105,103,104,116,97,114,114,111,119,59,32768,10234,105,103,104,116,97,114,114,111,119,59,32768,10233,102,59,32896,55349,56643,101,114,512,76,82,2911,2922,101,102,116,65,114,114,111,119,59,32768,8601,105,103,104,116,65,114,114,111,119,59,32768,8600,768,99,104,116,2941,2945,2948,114,59,32768,8466,59,32768,8624,114,111,107,59,32768,321,59,32768,8810,2048,97,99,101,102,105,111,115,117,2974,2978,2982,3007,3012,3022,3028,3033,112,59,32768,10501,121,59,32768,1052,512,100,108,2987,2998,105,117,109,83,112,97,99,101,59,32768,8287,108,105,110,116,114,102,59,32768,8499,114,59,32896,55349,56592,110,117,115,80,108,117,115,59,32768,8723,112,102,59,32896,55349,56644,99,114,59,32768,8499,59,32768,924,2304,74,97,99,101,102,111,115,116,117,3055,3060,3067,3089,3201,3206,3874,3880,3889,99,121,59,32768,1034,99,117,116,101,59,32768,323,768,97,101,121,3074,3080,3086,114,111,110,59,32768,327,100,105,108,59,32768,325,59,32768,1053,768,103,115,119,3096,3160,3194,97,116,105,118,101,768,77,84,86,3108,3121,3145,101,100,105,117,109,83,112,97,99,101,59,32768,8203,104,105,512,99,110,3128,3137,107,83,112,97,99,101,59,32768,8203,83,112,97,99,101,59,32768,8203,101,114,121,84,104,105,110,83,112,97,99,101,59,32768,8203,116,101,100,512,71,76,3168,3184,114,101,97,116,101,114,71,114,101,97,116,101,114,59,32768,8811,101,115,115,76,101,115,115,59,32768,8810,76,105,110,101,59,32768,10,114,59,32896,55349,56593,1024,66,110,112,116,3215,3222,3238,3242,114,101,97,107,59,32768,8288,66,114,101,97,107,105,110,103,83,112,97,99,101,59,32768,160,102,59,32768,8469,3328,59,67,68,69,71,72,76,78,80,82,83,84,86,3269,3271,3293,3312,3352,3430,3455,3551,3589,3625,3678,3821,3861,32768,10988,512,111,117,3276,3286,110,103,114,117,101,110,116,59,32768,8802,112,67,97,112,59,32768,8813,111,117,98,108,101,86,101,114,116,105,99,97,108,66,97,114,59,32768,8742,768,108,113,120,3319,3327,3345,101,109,101,110,116,59,32768,8713,117,97,108,512,59,84,3335,3337,32768,8800,105,108,100,101,59,32896,8770,824,105,115,116,115,59,32768,8708,114,101,97,116,101,114,1792,59,69,70,71,76,83,84,3373,3375,3382,3394,3404,3410,3423,32768,8815,113,117,97,108,59,32768,8817,117,108,108,69,113,117,97,108,59,32896,8807,824,114,101,97,116,101,114,59,32896,8811,824,101,115,115,59,32768,8825,108,97,110,116,69,113,117,97,108,59,32896,10878,824,105,108,100,101,59,32768,8821,117,109,112,533,3437,3448,111,119,110,72,117,109,112,59,32896,8782,824,113,117,97,108,59,32896,8783,824,101,512,102,115,3461,3492,116,84,114,105,97,110,103,108,101,768,59,66,69,3477,3479,3485,32768,8938,97,114,59,32896,10703,824,113,117,97,108,59,32768,8940,115,1536,59,69,71,76,83,84,3506,3508,3515,3524,3531,3544,32768,8814,113,117,97,108,59,32768,8816,114,101,97,116,101,114,59,32768,8824,101,115,115,59,32896,8810,824,108,97,110,116,69,113,117,97,108,59,32896,10877,824,105,108,100,101,59,32768,8820,101,115,116,101,100,512,71,76,3561,3578,114,101,97,116,101,114,71,114,101,97,116,101,114,59,32896,10914,824,101,115,115,76,101,115,115,59,32896,10913,824,114,101,99,101,100,101,115,768,59,69,83,3603,3605,3613,32768,8832,113,117,97,108,59,32896,10927,824,108,97,110,116,69,113,117,97,108,59,32768,8928,512,101,105,3630,3645,118,101,114,115,101,69,108,101,109,101,110,116,59,32768,8716,103,104,116,84,114,105,97,110,103,108,101,768,59,66,69,3663,3665,3671,32768,8939,97,114,59,32896,10704,824,113,117,97,108,59,32768,8941,512,113,117,3683,3732,117,97,114,101,83,117,512,98,112,3694,3712,115,101,116,512,59,69,3702,3705,32896,8847,824,113,117,97,108,59,32768,8930,101,114,115,101,116,512,59,69,3722,3725,32896,8848,824,113,117,97,108,59,32768,8931,768,98,99,112,3739,3757,3801,115,101,116,512,59,69,3747,3750,32896,8834,8402,113,117,97,108,59,32768,8840,99,101,101,100,115,1024,59,69,83,84,3771,3773,3781,3793,32768,8833,113,117,97,108,59,32896,10928,824,108,97,110,116,69,113,117,97,108,59,32768,8929,105,108,100,101,59,32896,8831,824,101,114,115,101,116,512,59,69,3811,3814,32896,8835,8402,113,117,97,108,59,32768,8841,105,108,100,101,1024,59,69,70,84,3834,3836,3843,3854,32768,8769,113,117,97,108,59,32768,8772,117,108,108,69,113,117,97,108,59,32768,8775,105,108,100,101,59,32768,8777,101,114,116,105,99,97,108,66,97,114,59,32768,8740,99,114,59,32896,55349,56489,105,108,100,101,33024,209,59,32768,209,59,32768,925,3584,69,97,99,100,102,103,109,111,112,114,115,116,117,118,3921,3927,3936,3951,3958,3963,3972,3996,4002,4034,4037,4055,4071,4078,108,105,103,59,32768,338,99,117,116,101,33024,211,59,32768,211,512,105,121,3941,3948,114,99,33024,212,59,32768,212,59,32768,1054,98,108,97,99,59,32768,336,114,59,32896,55349,56594,114,97,118,101,33024,210,59,32768,210,768,97,101,105,3979,3984,3989,99,114,59,32768,332,103,97,59,32768,937,99,114,111,110,59,32768,927,112,102,59,32896,55349,56646,101,110,67,117,114,108,121,512,68,81,4014,4027,111,117,98,108,101,81,117,111,116,101,59,32768,8220,117,111,116,101,59,32768,8216,59,32768,10836,512,99,108,4042,4047,114,59,32896,55349,56490,97,115,104,33024,216,59,32768,216,105,573,4060,4067,100,101,33024,213,59,32768,213,101,115,59,32768,10807,109,108,33024,214,59,32768,214,101,114,512,66,80,4085,4109,512,97,114,4090,4094,114,59,32768,8254,97,99,512,101,107,4101,4104,59,32768,9182,101,116,59,32768,9140,97,114,101,110,116,104,101,115,105,115,59,32768,9180,2304,97,99,102,104,105,108,111,114,115,4141,4150,4154,4159,4163,4166,4176,4198,4284,114,116,105,97,108,68,59,32768,8706,121,59,32768,1055,114,59,32896,55349,56595,105,59,32768,934,59,32768,928,117,115,77,105,110,117,115,59,32768,177,512,105,112,4181,4194,110,99,97,114,101,112,108,97,110,101,59,32768,8460,102,59,32768,8473,1024,59,101,105,111,4207,4209,4251,4256,32768,10939,99,101,100,101,115,1024,59,69,83,84,4223,4225,4232,4244,32768,8826,113,117,97,108,59,32768,10927,108,97,110,116,69,113,117,97,108,59,32768,8828,105,108,100,101,59,32768,8830,109,101,59,32768,8243,512,100,112,4261,4267,117,99,116,59,32768,8719,111,114,116,105,111,110,512,59,97,4278,4280,32768,8759,108,59,32768,8733,512,99,105,4289,4294,114,59,32896,55349,56491,59,32768,936,1024,85,102,111,115,4306,4313,4318,4323,79,84,33024,34,59,32768,34,114,59,32896,55349,56596,112,102,59,32768,8474,99,114,59,32896,55349,56492,3072,66,69,97,99,101,102,104,105,111,114,115,117,4354,4360,4366,4395,4417,4473,4477,4481,4743,4764,4776,4788,97,114,114,59,32768,10512,71,33024,174,59,32768,174,768,99,110,114,4373,4379,4383,117,116,101,59,32768,340,103,59,32768,10219,114,512,59,116,4389,4391,32768,8608,108,59,32768,10518,768,97,101,121,4402,4408,4414,114,111,110,59,32768,344,100,105,108,59,32768,342,59,32768,1056,512,59,118,4422,4424,32768,8476,101,114,115,101,512,69,85,4433,4458,512,108,113,4438,4446,101,109,101,110,116,59,32768,8715,117,105,108,105,98,114,105,117,109,59,32768,8651,112,69,113,117,105,108,105,98,114,105,117,109,59,32768,10607,114,59,32768,8476,111,59,32768,929,103,104,116,2048,65,67,68,70,84,85,86,97,4501,4547,4556,4607,4614,4671,4719,4736,512,110,114,4506,4519,103,108,101,66,114,97,99,107,101,116,59,32768,10217,114,111,119,768,59,66,76,4529,4531,4536,32768,8594,97,114,59,32768,8677,101,102,116,65,114,114,111,119,59,32768,8644,101,105,108,105,110,103,59,32768,8969,111,838,4562,0,4575,98,108,101,66,114,97,99,107,101,116,59,32768,10215,110,805,4580,0,4591,101,101,86,101,99,116,111,114,59,32768,10589,101,99,116,111,114,512,59,66,4600,4602,32768,8642,97,114,59,32768,10581,108,111,111,114,59,32768,8971,512,101,114,4619,4644,101,768,59,65,86,4627,4629,4636,32768,8866,114,114,111,119,59,32768,8614,101,99,116,111,114,59,32768,10587,105,97,110,103,108,101,768,59,66,69,4657,4659,4664,32768,8883,97,114,59,32768,10704,113,117,97,108,59,32768,8885,112,768,68,84,86,4679,4691,4702,111,119,110,86,101,99,116,111,114,59,32768,10575,101,101,86,101,99,116,111,114,59,32768,10588,101,99,116,111,114,512,59,66,4712,4714,32768,8638,97,114,59,32768,10580,101,99,116,111,114,512,59,66,4729,4731,32768,8640,97,114,59,32768,10579,114,114,111,119,59,32768,8658,512,112,117,4748,4752,102,59,32768,8477,110,100,73,109,112,108,105,101,115,59,32768,10608,105,103,104,116,97,114,114,111,119,59,32768,8667,512,99,104,4781,4785,114,59,32768,8475,59,32768,8625,108,101,68,101,108,97,121,101,100,59,32768,10740,3328,72,79,97,99,102,104,105,109,111,113,115,116,117,4827,4842,4849,4856,4889,4894,4949,4955,4967,4973,5059,5065,5070,512,67,99,4832,4838,72,99,121,59,32768,1065,121,59,32768,1064,70,84,99,121,59,32768,1068,99,117,116,101,59,32768,346,1280,59,97,101,105,121,4867,4869,4875,4881,4886,32768,10940,114,111,110,59,32768,352,100,105,108,59,32768,350,114,99,59,32768,348,59,32768,1057,114,59,32896,55349,56598,111,114,116,1024,68,76,82,85,4906,4917,4928,4940,111,119,110,65,114,114,111,119,59,32768,8595,101,102,116,65,114,114,111,119,59,32768,8592,105,103,104,116,65,114,114,111,119,59,32768,8594,112,65,114,114,111,119,59,32768,8593,103,109,97,59,32768,931,97,108,108,67,105,114,99,108,101,59,32768,8728,112,102,59,32896,55349,56650,1091,4979,0,0,4983,116,59,32768,8730,97,114,101,1024,59,73,83,85,4994,4996,5010,5052,32768,9633,110,116,101,114,115,101,99,116,105,111,110,59,32768,8851,117,512,98,112,5016,5033,115,101,116,512,59,69,5024,5026,32768,8847,113,117,97,108,59,32768,8849,101,114,115,101,116,512,59,69,5043,5045,32768,8848,113,117,97,108,59,32768,8850,110,105,111,110,59,32768,8852,99,114,59,32896,55349,56494,97,114,59,32768,8902,1024,98,99,109,112,5079,5102,5155,5158,512,59,115,5084,5086,32768,8912,101,116,512,59,69,5093,5095,32768,8912,113,117,97,108,59,32768,8838,512,99,104,5107,5148,101,101,100,115,1024,59,69,83,84,5120,5122,5129,5141,32768,8827,113,117,97,108,59,32768,10928,108,97,110,116,69,113,117,97,108,59,32768,8829,105,108,100,101,59,32768,8831,84,104,97,116,59,32768,8715,59,32768,8721,768,59,101,115,5165,5167,5185,32768,8913,114,115,101,116,512,59,69,5176,5178,32768,8835,113,117,97,108,59,32768,8839,101,116,59,32768,8913,2816,72,82,83,97,99,102,104,105,111,114,115,5213,5221,5227,5241,5252,5274,5279,5323,5362,5368,5378,79,82,78,33024,222,59,32768,222,65,68,69,59,32768,8482,512,72,99,5232,5237,99,121,59,32768,1035,121,59,32768,1062,512,98,117,5246,5249,59,32768,9,59,32768,932,768,97,101,121,5259,5265,5271,114,111,110,59,32768,356,100,105,108,59,32768,354,59,32768,1058,114,59,32896,55349,56599,512,101,105,5284,5300,835,5289,0,5297,101,102,111,114,101,59,32768,8756,97,59,32768,920,512,99,110,5305,5315,107,83,112,97,99,101,59,32896,8287,8202,83,112,97,99,101,59,32768,8201,108,100,101,1024,59,69,70,84,5335,5337,5344,5355,32768,8764,113,117,97,108,59,32768,8771,117,108,108,69,113,117,97,108,59,32768,8773,105,108,100,101,59,32768,8776,112,102,59,32896,55349,56651,105,112,108,101,68,111,116,59,32768,8411,512,99,116,5383,5388,114,59,32896,55349,56495,114,111,107,59,32768,358,5426,5417,5444,5458,5473,0,5480,5485,0,0,0,0,0,5494,5500,5564,5579,0,5726,5732,5738,5745,512,99,114,5421,5429,117,116,101,33024,218,59,32768,218,114,512,59,111,5435,5437,32768,8607,99,105,114,59,32768,10569,114,820,5449,0,5453,121,59,32768,1038,118,101,59,32768,364,512,105,121,5462,5469,114,99,33024,219,59,32768,219,59,32768,1059,98,108,97,99,59,32768,368,114,59,32896,55349,56600,114,97,118,101,33024,217,59,32768,217,97,99,114,59,32768,362,512,100,105,5504,5548,101,114,512,66,80,5511,5535,512,97,114,5516,5520,114,59,32768,95,97,99,512,101,107,5527,5530,59,32768,9183,101,116,59,32768,9141,97,114,101,110,116,104,101,115,105,115,59,32768,9181,111,110,512,59,80,5555,5557,32768,8899,108,117,115,59,32768,8846,512,103,112,5568,5573,111,110,59,32768,370,102,59,32896,55349,56652,2048,65,68,69,84,97,100,112,115,5595,5624,5635,5648,5664,5671,5682,5712,114,114,111,119,768,59,66,68,5606,5608,5613,32768,8593,97,114,59,32768,10514,111,119,110,65,114,114,111,119,59,32768,8645,111,119,110,65,114,114,111,119,59,32768,8597,113,117,105,108,105,98,114,105,117,109,59,32768,10606,101,101,512,59,65,5655,5657,32768,8869,114,114,111,119,59,32768,8613,114,114,111,119,59,32768,8657,111,119,110,97,114,114,111,119,59,32768,8661,101,114,512,76,82,5689,5700,101,102,116,65,114,114,111,119,59,32768,8598,105,103,104,116,65,114,114,111,119,59,32768,8599,105,512,59,108,5718,5720,32768,978,111,110,59,32768,933,105,110,103,59,32768,366,99,114,59,32896,55349,56496,105,108,100,101,59,32768,360,109,108,33024,220,59,32768,220,2304,68,98,99,100,101,102,111,115,118,5770,5776,5781,5785,5798,5878,5883,5889,5895,97,115,104,59,32768,8875,97,114,59,32768,10987,121,59,32768,1042,97,115,104,512,59,108,5793,5795,32768,8873,59,32768,10982,512,101,114,5803,5806,59,32768,8897,768,98,116,121,5813,5818,5866,97,114,59,32768,8214,512,59,105,5823,5825,32768,8214,99,97,108,1024,66,76,83,84,5837,5842,5848,5859,97,114,59,32768,8739,105,110,101,59,32768,124,101,112,97,114,97,116,111,114,59,32768,10072,105,108,100,101,59,32768,8768,84,104,105,110,83,112,97,99,101,59,32768,8202,114,59,32896,55349,56601,112,102,59,32896,55349,56653,99,114,59,32896,55349,56497,100,97,115,104,59,32768,8874,1280,99,101,102,111,115,5913,5919,5925,5930,5936,105,114,99,59,32768,372,100,103,101,59,32768,8896,114,59,32896,55349,56602,112,102,59,32896,55349,56654,99,114,59,32896,55349,56498,1024,102,105,111,115,5951,5956,5959,5965,114,59,32896,55349,56603,59,32768,926,112,102,59,32896,55349,56655,99,114,59,32896,55349,56499,2304,65,73,85,97,99,102,111,115,117,5990,5995,6e3,6005,6014,6027,6032,6038,6044,99,121,59,32768,1071,99,121,59,32768,1031,99,121,59,32768,1070,99,117,116,101,33024,221,59,32768,221,512,105,121,6019,6024,114,99,59,32768,374,59,32768,1067,114,59,32896,55349,56604,112,102,59,32896,55349,56656,99,114,59,32896,55349,56500,109,108,59,32768,376,2048,72,97,99,100,101,102,111,115,6066,6071,6078,6092,6097,6119,6123,6128,99,121,59,32768,1046,99,117,116,101,59,32768,377,512,97,121,6083,6089,114,111,110,59,32768,381,59,32768,1047,111,116,59,32768,379,835,6102,0,6116,111,87,105,100,116,104,83,112,97,99,101,59,32768,8203,97,59,32768,918,114,59,32768,8488,112,102,59,32768,8484,99,114,59,32896,55349,56501,5938,6159,6168,6175,0,6214,6222,6233,0,0,0,0,6242,6267,6290,6429,6444,0,6495,6503,6531,6540,0,6547,99,117,116,101,33024,225,59,32768,225,114,101,118,101,59,32768,259,1536,59,69,100,105,117,121,6187,6189,6193,6196,6203,6210,32768,8766,59,32896,8766,819,59,32768,8767,114,99,33024,226,59,32768,226,116,101,33024,180,59,32768,180,59,32768,1072,108,105,103,33024,230,59,32768,230,512,59,114,6226,6228,32768,8289,59,32896,55349,56606,114,97,118,101,33024,224,59,32768,224,512,101,112,6246,6261,512,102,112,6251,6257,115,121,109,59,32768,8501,104,59,32768,8501,104,97,59,32768,945,512,97,112,6271,6284,512,99,108,6276,6280,114,59,32768,257,103,59,32768,10815,33024,38,59,32768,38,1077,6295,0,0,6326,1280,59,97,100,115,118,6305,6307,6312,6315,6322,32768,8743,110,100,59,32768,10837,59,32768,10844,108,111,112,101,59,32768,10840,59,32768,10842,1792,59,101,108,109,114,115,122,6340,6342,6345,6349,6391,6410,6422,32768,8736,59,32768,10660,101,59,32768,8736,115,100,512,59,97,6356,6358,32768,8737,2098,6368,6371,6374,6377,6380,6383,6386,6389,59,32768,10664,59,32768,10665,59,32768,10666,59,32768,10667,59,32768,10668,59,32768,10669,59,32768,10670,59,32768,10671,116,512,59,118,6397,6399,32768,8735,98,512,59,100,6405,6407,32768,8894,59,32768,10653,512,112,116,6415,6419,104,59,32768,8738,59,32768,197,97,114,114,59,32768,9084,512,103,112,6433,6438,111,110,59,32768,261,102,59,32896,55349,56658,1792,59,69,97,101,105,111,112,6458,6460,6463,6469,6472,6476,6480,32768,8776,59,32768,10864,99,105,114,59,32768,10863,59,32768,8778,100,59,32768,8779,115,59,32768,39,114,111,120,512,59,101,6488,6490,32768,8776,113,59,32768,8778,105,110,103,33024,229,59,32768,229,768,99,116,121,6509,6514,6517,114,59,32896,55349,56502,59,32768,42,109,112,512,59,101,6524,6526,32768,8776,113,59,32768,8781,105,108,100,101,33024,227,59,32768,227,109,108,33024,228,59,32768,228,512,99,105,6551,6559,111,110,105,110,116,59,32768,8755,110,116,59,32768,10769,4096,78,97,98,99,100,101,102,105,107,108,110,111,112,114,115,117,6597,6602,6673,6688,6701,6707,6768,6773,6891,6898,6999,7023,7309,7316,7334,7383,111,116,59,32768,10989,512,99,114,6607,6652,107,1024,99,101,112,115,6617,6623,6632,6639,111,110,103,59,32768,8780,112,115,105,108,111,110,59,32768,1014,114,105,109,101,59,32768,8245,105,109,512,59,101,6646,6648,32768,8765,113,59,32768,8909,583,6656,6661,101,101,59,32768,8893,101,100,512,59,103,6667,6669,32768,8965,101,59,32768,8965,114,107,512,59,116,6680,6682,32768,9141,98,114,107,59,32768,9142,512,111,121,6693,6698,110,103,59,32768,8780,59,32768,1073,113,117,111,59,32768,8222,1280,99,109,112,114,116,6718,6731,6738,6743,6749,97,117,115,512,59,101,6726,6728,32768,8757,59,32768,8757,112,116,121,118,59,32768,10672,115,105,59,32768,1014,110,111,117,59,32768,8492,768,97,104,119,6756,6759,6762,59,32768,946,59,32768,8502,101,101,110,59,32768,8812,114,59,32896,55349,56607,103,1792,99,111,115,116,117,118,119,6789,6809,6834,6850,6872,6879,6884,768,97,105,117,6796,6800,6805,112,59,32768,8898,114,99,59,32768,9711,112,59,32768,8899,768,100,112,116,6816,6821,6827,111,116,59,32768,10752,108,117,115,59,32768,10753,105,109,101,115,59,32768,10754,1090,6840,0,0,6846,99,117,112,59,32768,10758,97,114,59,32768,9733,114,105,97,110,103,108,101,512,100,117,6862,6868,111,119,110,59,32768,9661,112,59,32768,9651,112,108,117,115,59,32768,10756,101,101,59,32768,8897,101,100,103,101,59,32768,8896,97,114,111,119,59,32768,10509,768,97,107,111,6905,6976,6994,512,99,110,6910,6972,107,768,108,115,116,6918,6927,6935,111,122,101,110,103,101,59,32768,10731,113,117,97,114,101,59,32768,9642,114,105,97,110,103,108,101,1024,59,100,108,114,6951,6953,6959,6965,32768,9652,111,119,110,59,32768,9662,101,102,116,59,32768,9666,105,103,104,116,59,32768,9656,107,59,32768,9251,770,6981,0,6991,771,6985,0,6988,59,32768,9618,59,32768,9617,52,59,32768,9619,99,107,59,32768,9608,512,101,111,7004,7019,512,59,113,7009,7012,32896,61,8421,117,105,118,59,32896,8801,8421,116,59,32768,8976,1024,112,116,119,120,7032,7037,7049,7055,102,59,32896,55349,56659,512,59,116,7042,7044,32768,8869,111,109,59,32768,8869,116,105,101,59,32768,8904,3072,68,72,85,86,98,100,104,109,112,116,117,118,7080,7101,7126,7147,7182,7187,7208,7233,7240,7246,7253,7274,1024,76,82,108,114,7089,7092,7095,7098,59,32768,9559,59,32768,9556,59,32768,9558,59,32768,9555,1280,59,68,85,100,117,7112,7114,7117,7120,7123,32768,9552,59,32768,9574,59,32768,9577,59,32768,9572,59,32768,9575,1024,76,82,108,114,7135,7138,7141,7144,59,32768,9565,59,32768,9562,59,32768,9564,59,32768,9561,1792,59,72,76,82,104,108,114,7162,7164,7167,7170,7173,7176,7179,32768,9553,59,32768,9580,59,32768,9571,59,32768,9568,59,32768,9579,59,32768,9570,59,32768,9567,111,120,59,32768,10697,1024,76,82,108,114,7196,7199,7202,7205,59,32768,9557,59,32768,9554,59,32768,9488,59,32768,9484,1280,59,68,85,100,117,7219,7221,7224,7227,7230,32768,9472,59,32768,9573,59,32768,9576,59,32768,9516,59,32768,9524,105,110,117,115,59,32768,8863,108,117,115,59,32768,8862,105,109,101,115,59,32768,8864,1024,76,82,108,114,7262,7265,7268,7271,59,32768,9563,59,32768,9560,59,32768,9496,59,32768,9492,1792,59,72,76,82,104,108,114,7289,7291,7294,7297,7300,7303,7306,32768,9474,59,32768,9578,59,32768,9569,59,32768,9566,59,32768,9532,59,32768,9508,59,32768,9500,114,105,109,101,59,32768,8245,512,101,118,7321,7326,118,101,59,32768,728,98,97,114,33024,166,59,32768,166,1024,99,101,105,111,7343,7348,7353,7364,114,59,32896,55349,56503,109,105,59,32768,8271,109,512,59,101,7359,7361,32768,8765,59,32768,8909,108,768,59,98,104,7372,7374,7377,32768,92,59,32768,10693,115,117,98,59,32768,10184,573,7387,7399,108,512,59,101,7392,7394,32768,8226,116,59,32768,8226,112,768,59,69,101,7406,7408,7411,32768,8782,59,32768,10926,512,59,113,7416,7418,32768,8783,59,32768,8783,6450,7448,0,7523,7571,7576,7613,0,7618,7647,0,0,7764,0,0,7779,0,0,7899,7914,7949,7955,0,8158,0,8176,768,99,112,114,7454,7460,7509,117,116,101,59,32768,263,1536,59,97,98,99,100,115,7473,7475,7480,7487,7500,7505,32768,8745,110,100,59,32768,10820,114,99,117,112,59,32768,10825,512,97,117,7492,7496,112,59,32768,10827,112,59,32768,10823,111,116,59,32768,10816,59,32896,8745,65024,512,101,111,7514,7518,116,59,32768,8257,110,59,32768,711,1024,97,101,105,117,7531,7544,7552,7557,833,7536,0,7540,115,59,32768,10829,111,110,59,32768,269,100,105,108,33024,231,59,32768,231,114,99,59,32768,265,112,115,512,59,115,7564,7566,32768,10828,109,59,32768,10832,111,116,59,32768,267,768,100,109,110,7582,7589,7596,105,108,33024,184,59,32768,184,112,116,121,118,59,32768,10674,116,33280,162,59,101,7603,7605,32768,162,114,100,111,116,59,32768,183,114,59,32896,55349,56608,768,99,101,105,7624,7628,7643,121,59,32768,1095,99,107,512,59,109,7635,7637,32768,10003,97,114,107,59,32768,10003,59,32768,967,114,1792,59,69,99,101,102,109,115,7662,7664,7667,7742,7745,7752,7757,32768,9675,59,32768,10691,768,59,101,108,7674,7676,7680,32768,710,113,59,32768,8791,101,1074,7687,0,0,7709,114,114,111,119,512,108,114,7695,7701,101,102,116,59,32768,8634,105,103,104,116,59,32768,8635,1280,82,83,97,99,100,7719,7722,7725,7730,7736,59,32768,174,59,32768,9416,115,116,59,32768,8859,105,114,99,59,32768,8858,97,115,104,59,32768,8861,59,32768,8791,110,105,110,116,59,32768,10768,105,100,59,32768,10991,99,105,114,59,32768,10690,117,98,115,512,59,117,7771,7773,32768,9827,105,116,59,32768,9827,1341,7785,7804,7850,0,7871,111,110,512,59,101,7791,7793,32768,58,512,59,113,7798,7800,32768,8788,59,32768,8788,1086,7809,0,0,7820,97,512,59,116,7814,7816,32768,44,59,32768,64,768,59,102,108,7826,7828,7832,32768,8705,110,59,32768,8728,101,512,109,120,7838,7844,101,110,116,59,32768,8705,101,115,59,32768,8450,824,7854,0,7866,512,59,100,7858,7860,32768,8773,111,116,59,32768,10861,110,116,59,32768,8750,768,102,114,121,7877,7881,7886,59,32896,55349,56660,111,100,59,32768,8720,33280,169,59,115,7892,7894,32768,169,114,59,32768,8471,512,97,111,7903,7908,114,114,59,32768,8629,115,115,59,32768,10007,512,99,117,7918,7923,114,59,32896,55349,56504,512,98,112,7928,7938,512,59,101,7933,7935,32768,10959,59,32768,10961,512,59,101,7943,7945,32768,10960,59,32768,10962,100,111,116,59,32768,8943,1792,100,101,108,112,114,118,119,7969,7983,7996,8009,8057,8147,8152,97,114,114,512,108,114,7977,7980,59,32768,10552,59,32768,10549,1089,7989,0,0,7993,114,59,32768,8926,99,59,32768,8927,97,114,114,512,59,112,8004,8006,32768,8630,59,32768,10557,1536,59,98,99,100,111,115,8022,8024,8031,8044,8049,8053,32768,8746,114,99,97,112,59,32768,10824,512,97,117,8036,8040,112,59,32768,10822,112,59,32768,10826,111,116,59,32768,8845,114,59,32768,10821,59,32896,8746,65024,1024,97,108,114,118,8066,8078,8116,8123,114,114,512,59,109,8073,8075,32768,8631,59,32768,10556,121,768,101,118,119,8086,8104,8109,113,1089,8093,0,0,8099,114,101,99,59,32768,8926,117,99,99,59,32768,8927,101,101,59,32768,8910,101,100,103,101,59,32768,8911,101,110,33024,164,59,32768,164,101,97,114,114,111,119,512,108,114,8134,8140,101,102,116,59,32768,8630,105,103,104,116,59,32768,8631,101,101,59,32768,8910,101,100,59,32768,8911,512,99,105,8162,8170,111,110,105,110,116,59,32768,8754,110,116,59,32768,8753,108,99,116,121,59,32768,9005,4864,65,72,97,98,99,100,101,102,104,105,106,108,111,114,115,116,117,119,122,8221,8226,8231,8267,8282,8296,8327,8351,8366,8379,8466,8471,8487,8621,8647,8676,8697,8712,8720,114,114,59,32768,8659,97,114,59,32768,10597,1024,103,108,114,115,8240,8246,8252,8256,103,101,114,59,32768,8224,101,116,104,59,32768,8504,114,59,32768,8595,104,512,59,118,8262,8264,32768,8208,59,32768,8867,572,8271,8278,97,114,111,119,59,32768,10511,97,99,59,32768,733,512,97,121,8287,8293,114,111,110,59,32768,271,59,32768,1076,768,59,97,111,8303,8305,8320,32768,8518,512,103,114,8310,8316,103,101,114,59,32768,8225,114,59,32768,8650,116,115,101,113,59,32768,10871,768,103,108,109,8334,8339,8344,33024,176,59,32768,176,116,97,59,32768,948,112,116,121,118,59,32768,10673,512,105,114,8356,8362,115,104,116,59,32768,10623,59,32896,55349,56609,97,114,512,108,114,8373,8376,59,32768,8643,59,32768,8642,1280,97,101,103,115,118,8390,8418,8421,8428,8433,109,768,59,111,115,8398,8400,8415,32768,8900,110,100,512,59,115,8407,8409,32768,8900,117,105,116,59,32768,9830,59,32768,9830,59,32768,168,97,109,109,97,59,32768,989,105,110,59,32768,8946,768,59,105,111,8440,8442,8461,32768,247,100,101,33280,247,59,111,8450,8452,32768,247,110,116,105,109,101,115,59,32768,8903,110,120,59,32768,8903,99,121,59,32768,1106,99,1088,8478,0,0,8483,114,110,59,32768,8990,111,112,59,32768,8973,1280,108,112,116,117,119,8498,8504,8509,8556,8570,108,97,114,59,32768,36,102,59,32896,55349,56661,1280,59,101,109,112,115,8520,8522,8535,8542,8548,32768,729,113,512,59,100,8528,8530,32768,8784,111,116,59,32768,8785,105,110,117,115,59,32768,8760,108,117,115,59,32768,8724,113,117,97,114,101,59,32768,8865,98,108,101,98,97,114,119,101,100,103,101,59,32768,8966,110,768,97,100,104,8578,8585,8597,114,114,111,119,59,32768,8595,111,119,110,97,114,114,111,119,115,59,32768,8650,97,114,112,111,111,110,512,108,114,8608,8614,101,102,116,59,32768,8643,105,103,104,116,59,32768,8642,563,8625,8633,107,97,114,111,119,59,32768,10512,1088,8638,0,0,8643,114,110,59,32768,8991,111,112,59,32768,8972,768,99,111,116,8654,8666,8670,512,114,121,8659,8663,59,32896,55349,56505,59,32768,1109,108,59,32768,10742,114,111,107,59,32768,273,512,100,114,8681,8686,111,116,59,32768,8945,105,512,59,102,8692,8694,32768,9663,59,32768,9662,512,97,104,8702,8707,114,114,59,32768,8693,97,114,59,32768,10607,97,110,103,108,101,59,32768,10662,512,99,105,8725,8729,121,59,32768,1119,103,114,97,114,114,59,32768,10239,4608,68,97,99,100,101,102,103,108,109,110,111,112,113,114,115,116,117,120,8774,8788,8807,8844,8849,8852,8866,8895,8929,8977,8989,9004,9046,9136,9151,9171,9184,9199,512,68,111,8779,8784,111,116,59,32768,10871,116,59,32768,8785,512,99,115,8793,8801,117,116,101,33024,233,59,32768,233,116,101,114,59,32768,10862,1024,97,105,111,121,8816,8822,8835,8841,114,111,110,59,32768,283,114,512,59,99,8828,8830,32768,8790,33024,234,59,32768,234,108,111,110,59,32768,8789,59,32768,1101,111,116,59,32768,279,59,32768,8519,512,68,114,8857,8862,111,116,59,32768,8786,59,32896,55349,56610,768,59,114,115,8873,8875,8883,32768,10906,97,118,101,33024,232,59,32768,232,512,59,100,8888,8890,32768,10902,111,116,59,32768,10904,1024,59,105,108,115,8904,8906,8914,8917,32768,10905,110,116,101,114,115,59,32768,9191,59,32768,8467,512,59,100,8922,8924,32768,10901,111,116,59,32768,10903,768,97,112,115,8936,8941,8960,99,114,59,32768,275,116,121,768,59,115,118,8950,8952,8957,32768,8709,101,116,59,32768,8709,59,32768,8709,112,512,49,59,8966,8975,516,8970,8973,59,32768,8196,59,32768,8197,32768,8195,512,103,115,8982,8985,59,32768,331,112,59,32768,8194,512,103,112,8994,8999,111,110,59,32768,281,102,59,32896,55349,56662,768,97,108,115,9011,9023,9028,114,512,59,115,9017,9019,32768,8917,108,59,32768,10723,117,115,59,32768,10865,105,768,59,108,118,9036,9038,9043,32768,949,111,110,59,32768,949,59,32768,1013,1024,99,115,117,118,9055,9071,9099,9128,512,105,111,9060,9065,114,99,59,32768,8790,108,111,110,59,32768,8789,1082,9077,0,0,9081,109,59,32768,8770,97,110,116,512,103,108,9088,9093,116,114,59,32768,10902,101,115,115,59,32768,10901,768,97,101,105,9106,9111,9116,108,115,59,32768,61,115,116,59,32768,8799,118,512,59,68,9122,9124,32768,8801,68,59,32768,10872,112,97,114,115,108,59,32768,10725,512,68,97,9141,9146,111,116,59,32768,8787,114,114,59,32768,10609,768,99,100,105,9158,9162,9167,114,59,32768,8495,111,116,59,32768,8784,109,59,32768,8770,512,97,104,9176,9179,59,32768,951,33024,240,59,32768,240,512,109,114,9189,9195,108,33024,235,59,32768,235,111,59,32768,8364,768,99,105,112,9206,9210,9215,108,59,32768,33,115,116,59,32768,8707,512,101,111,9220,9230,99,116,97,116,105,111,110,59,32768,8496,110,101,110,116,105,97,108,101,59,32768,8519,4914,9262,0,9276,0,9280,9287,0,0,9318,9324,0,9331,0,9352,9357,9386,0,9395,9497,108,108,105,110,103,100,111,116,115,101,113,59,32768,8786,121,59,32768,1092,109,97,108,101,59,32768,9792,768,105,108,114,9293,9299,9313,108,105,103,59,32768,64259,1082,9305,0,0,9309,103,59,32768,64256,105,103,59,32768,64260,59,32896,55349,56611,108,105,103,59,32768,64257,108,105,103,59,32896,102,106,768,97,108,116,9337,9341,9346,116,59,32768,9837,105,103,59,32768,64258,110,115,59,32768,9649,111,102,59,32768,402,833,9361,0,9366,102,59,32896,55349,56663,512,97,107,9370,9375,108,108,59,32768,8704,512,59,118,9380,9382,32768,8916,59,32768,10969,97,114,116,105,110,116,59,32768,10765,512,97,111,9399,9491,512,99,115,9404,9487,1794,9413,9443,9453,9470,9474,0,9484,1795,9421,9426,9429,9434,9437,0,9440,33024,189,59,32768,189,59,32768,8531,33024,188,59,32768,188,59,32768,8533,59,32768,8537,59,32768,8539,772,9447,0,9450,59,32768,8532,59,32768,8534,1285,9459,9464,0,0,9467,33024,190,59,32768,190,59,32768,8535,59,32768,8540,53,59,32768,8536,775,9478,0,9481,59,32768,8538,59,32768,8541,56,59,32768,8542,108,59,32768,8260,119,110,59,32768,8994,99,114,59,32896,55349,56507,4352,69,97,98,99,100,101,102,103,105,106,108,110,111,114,115,116,118,9537,9547,9575,9582,9595,9600,9679,9684,9694,9700,9705,9725,9773,9779,9785,9810,9917,512,59,108,9542,9544,32768,8807,59,32768,10892,768,99,109,112,9554,9560,9572,117,116,101,59,32768,501,109,97,512,59,100,9567,9569,32768,947,59,32768,989,59,32768,10886,114,101,118,101,59,32768,287,512,105,121,9587,9592,114,99,59,32768,285,59,32768,1075,111,116,59,32768,289,1024,59,108,113,115,9609,9611,9614,9633,32768,8805,59,32768,8923,768,59,113,115,9621,9623,9626,32768,8805,59,32768,8807,108,97,110,116,59,32768,10878,1024,59,99,100,108,9642,9644,9648,9667,32768,10878,99,59,32768,10921,111,116,512,59,111,9655,9657,32768,10880,512,59,108,9662,9664,32768,10882,59,32768,10884,512,59,101,9672,9675,32896,8923,65024,115,59,32768,10900,114,59,32896,55349,56612,512,59,103,9689,9691,32768,8811,59,32768,8921,109,101,108,59,32768,8503,99,121,59,32768,1107,1024,59,69,97,106,9714,9716,9719,9722,32768,8823,59,32768,10898,59,32768,10917,59,32768,10916,1024,69,97,101,115,9734,9737,9751,9768,59,32768,8809,112,512,59,112,9743,9745,32768,10890,114,111,120,59,32768,10890,512,59,113,9756,9758,32768,10888,512,59,113,9763,9765,32768,10888,59,32768,8809,105,109,59,32768,8935,112,102,59,32896,55349,56664,97,118,101,59,32768,96,512,99,105,9790,9794,114,59,32768,8458,109,768,59,101,108,9802,9804,9807,32768,8819,59,32768,10894,59,32768,10896,34304,62,59,99,100,108,113,114,9824,9826,9838,9843,9849,9856,32768,62,512,99,105,9831,9834,59,32768,10919,114,59,32768,10874,111,116,59,32768,8919,80,97,114,59,32768,10645,117,101,115,116,59,32768,10876,1280,97,100,101,108,115,9867,9882,9887,9906,9912,833,9872,0,9879,112,114,111,120,59,32768,10886,114,59,32768,10616,111,116,59,32768,8919,113,512,108,113,9893,9899,101,115,115,59,32768,8923,108,101,115,115,59,32768,10892,101,115,115,59,32768,8823,105,109,59,32768,8819,512,101,110,9922,9932,114,116,110,101,113,113,59,32896,8809,65024,69,59,32896,8809,65024,2560,65,97,98,99,101,102,107,111,115,121,9958,9963,10015,10020,10026,10060,10065,10085,10147,10171,114,114,59,32768,8660,1024,105,108,109,114,9972,9978,9982,9988,114,115,112,59,32768,8202,102,59,32768,189,105,108,116,59,32768,8459,512,100,114,9993,9998,99,121,59,32768,1098,768,59,99,119,10005,10007,10012,32768,8596,105,114,59,32768,10568,59,32768,8621,97,114,59,32768,8463,105,114,99,59,32768,293,768,97,108,114,10033,10048,10054,114,116,115,512,59,117,10041,10043,32768,9829,105,116,59,32768,9829,108,105,112,59,32768,8230,99,111,110,59,32768,8889,114,59,32896,55349,56613,115,512,101,119,10071,10078,97,114,111,119,59,32768,10533,97,114,111,119,59,32768,10534,1280,97,109,111,112,114,10096,10101,10107,10136,10141,114,114,59,32768,8703,116,104,116,59,32768,8763,107,512,108,114,10113,10124,101,102,116,97,114,114,111,119,59,32768,8617,105,103,104,116,97,114,114,111,119,59,32768,8618,102,59,32896,55349,56665,98,97,114,59,32768,8213,768,99,108,116,10154,10159,10165,114,59,32896,55349,56509,97,115,104,59,32768,8463,114,111,107,59,32768,295,512,98,112,10176,10182,117,108,108,59,32768,8259,104,101,110,59,32768,8208,5426,10211,0,10220,0,10239,10255,10267,0,10276,10312,0,0,10318,10371,10458,10485,10491,0,10500,10545,10558,99,117,116,101,33024,237,59,32768,237,768,59,105,121,10226,10228,10235,32768,8291,114,99,33024,238,59,32768,238,59,32768,1080,512,99,120,10243,10247,121,59,32768,1077,99,108,33024,161,59,32768,161,512,102,114,10259,10262,59,32768,8660,59,32896,55349,56614,114,97,118,101,33024,236,59,32768,236,1024,59,105,110,111,10284,10286,10300,10306,32768,8520,512,105,110,10291,10296,110,116,59,32768,10764,116,59,32768,8749,102,105,110,59,32768,10716,116,97,59,32768,8489,108,105,103,59,32768,307,768,97,111,112,10324,10361,10365,768,99,103,116,10331,10335,10357,114,59,32768,299,768,101,108,112,10342,10345,10351,59,32768,8465,105,110,101,59,32768,8464,97,114,116,59,32768,8465,104,59,32768,305,102,59,32768,8887,101,100,59,32768,437,1280,59,99,102,111,116,10381,10383,10389,10403,10409,32768,8712,97,114,101,59,32768,8453,105,110,512,59,116,10396,10398,32768,8734,105,101,59,32768,10717,100,111,116,59,32768,305,1280,59,99,101,108,112,10420,10422,10427,10444,10451,32768,8747,97,108,59,32768,8890,512,103,114,10432,10438,101,114,115,59,32768,8484,99,97,108,59,32768,8890,97,114,104,107,59,32768,10775,114,111,100,59,32768,10812,1024,99,103,112,116,10466,10470,10475,10480,121,59,32768,1105,111,110,59,32768,303,102,59,32896,55349,56666,97,59,32768,953,114,111,100,59,32768,10812,117,101,115,116,33024,191,59,32768,191,512,99,105,10504,10509,114,59,32896,55349,56510,110,1280,59,69,100,115,118,10521,10523,10526,10531,10541,32768,8712,59,32768,8953,111,116,59,32768,8949,512,59,118,10536,10538,32768,8948,59,32768,8947,59,32768,8712,512,59,105,10549,10551,32768,8290,108,100,101,59,32768,297,828,10562,0,10567,99,121,59,32768,1110,108,33024,239,59,32768,239,1536,99,102,109,111,115,117,10585,10598,10603,10609,10615,10630,512,105,121,10590,10595,114,99,59,32768,309,59,32768,1081,114,59,32896,55349,56615,97,116,104,59,32768,567,112,102,59,32896,55349,56667,820,10620,0,10625,114,59,32896,55349,56511,114,99,121,59,32768,1112,107,99,121,59,32768,1108,2048,97,99,102,103,104,106,111,115,10653,10666,10680,10685,10692,10697,10702,10708,112,112,97,512,59,118,10661,10663,32768,954,59,32768,1008,512,101,121,10671,10677,100,105,108,59,32768,311,59,32768,1082,114,59,32896,55349,56616,114,101,101,110,59,32768,312,99,121,59,32768,1093,99,121,59,32768,1116,112,102,59,32896,55349,56668,99,114,59,32896,55349,56512,5888,65,66,69,72,97,98,99,100,101,102,103,104,106,108,109,110,111,112,114,115,116,117,118,10761,10783,10789,10799,10804,10957,11011,11047,11094,11349,11372,11382,11409,11414,11451,11478,11526,11698,11711,11755,11823,11910,11929,768,97,114,116,10768,10773,10777,114,114,59,32768,8666,114,59,32768,8656,97,105,108,59,32768,10523,97,114,114,59,32768,10510,512,59,103,10794,10796,32768,8806,59,32768,10891,97,114,59,32768,10594,4660,10824,0,10830,0,10838,0,0,0,0,0,10844,10850,0,10867,10870,10877,0,10933,117,116,101,59,32768,314,109,112,116,121,118,59,32768,10676,114,97,110,59,32768,8466,98,100,97,59,32768,955,103,768,59,100,108,10857,10859,10862,32768,10216,59,32768,10641,101,59,32768,10216,59,32768,10885,117,111,33024,171,59,32768,171,114,2048,59,98,102,104,108,112,115,116,10894,10896,10907,10911,10915,10919,10923,10928,32768,8592,512,59,102,10901,10903,32768,8676,115,59,32768,10527,115,59,32768,10525,107,59,32768,8617,112,59,32768,8619,108,59,32768,10553,105,109,59,32768,10611,108,59,32768,8610,768,59,97,101,10939,10941,10946,32768,10923,105,108,59,32768,10521,512,59,115,10951,10953,32768,10925,59,32896,10925,65024,768,97,98,114,10964,10969,10974,114,114,59,32768,10508,114,107,59,32768,10098,512,97,107,10979,10991,99,512,101,107,10985,10988,59,32768,123,59,32768,91,512,101,115,10996,10999,59,32768,10635,108,512,100,117,11005,11008,59,32768,10639,59,32768,10637,1024,97,101,117,121,11020,11026,11040,11044,114,111,110,59,32768,318,512,100,105,11031,11036,105,108,59,32768,316,108,59,32768,8968,98,59,32768,123,59,32768,1083,1024,99,113,114,115,11056,11060,11072,11090,97,59,32768,10550,117,111,512,59,114,11067,11069,32768,8220,59,32768,8222,512,100,117,11077,11083,104,97,114,59,32768,10599,115,104,97,114,59,32768,10571,104,59,32768,8626,1280,59,102,103,113,115,11105,11107,11228,11231,11250,32768,8804,116,1280,97,104,108,114,116,11119,11136,11157,11169,11216,114,114,111,119,512,59,116,11128,11130,32768,8592,97,105,108,59,32768,8610,97,114,112,111,111,110,512,100,117,11147,11153,111,119,110,59,32768,8637,112,59,32768,8636,101,102,116,97,114,114,111,119,115,59,32768,8647,105,103,104,116,768,97,104,115,11180,11194,11204,114,114,111,119,512,59,115,11189,11191,32768,8596,59,32768,8646,97,114,112,111,111,110,115,59,32768,8651,113,117,105,103,97,114,114,111,119,59,32768,8621,104,114,101,101,116,105,109,101,115,59,32768,8907,59,32768,8922,768,59,113,115,11238,11240,11243,32768,8804,59,32768,8806,108,97,110,116,59,32768,10877,1280,59,99,100,103,115,11261,11263,11267,11286,11298,32768,10877,99,59,32768,10920,111,116,512,59,111,11274,11276,32768,10879,512,59,114,11281,11283,32768,10881,59,32768,10883,512,59,101,11291,11294,32896,8922,65024,115,59,32768,10899,1280,97,100,101,103,115,11309,11317,11322,11339,11344,112,112,114,111,120,59,32768,10885,111,116,59,32768,8918,113,512,103,113,11328,11333,116,114,59,32768,8922,103,116,114,59,32768,10891,116,114,59,32768,8822,105,109,59,32768,8818,768,105,108,114,11356,11362,11368,115,104,116,59,32768,10620,111,111,114,59,32768,8970,59,32896,55349,56617,512,59,69,11377,11379,32768,8822,59,32768,10897,562,11386,11405,114,512,100,117,11391,11394,59,32768,8637,512,59,108,11399,11401,32768,8636,59,32768,10602,108,107,59,32768,9604,99,121,59,32768,1113,1280,59,97,99,104,116,11425,11427,11432,11440,11446,32768,8810,114,114,59,32768,8647,111,114,110,101,114,59,32768,8990,97,114,100,59,32768,10603,114,105,59,32768,9722,512,105,111,11456,11462,100,111,116,59,32768,320,117,115,116,512,59,97,11470,11472,32768,9136,99,104,101,59,32768,9136,1024,69,97,101,115,11487,11490,11504,11521,59,32768,8808,112,512,59,112,11496,11498,32768,10889,114,111,120,59,32768,10889,512,59,113,11509,11511,32768,10887,512,59,113,11516,11518,32768,10887,59,32768,8808,105,109,59,32768,8934,2048,97,98,110,111,112,116,119,122,11543,11556,11561,11616,11640,11660,11667,11680,512,110,114,11548,11552,103,59,32768,10220,114,59,32768,8701,114,107,59,32768,10214,103,768,108,109,114,11569,11596,11604,101,102,116,512,97,114,11577,11584,114,114,111,119,59,32768,10229,105,103,104,116,97,114,114,111,119,59,32768,10231,97,112,115,116,111,59,32768,10236,105,103,104,116,97,114,114,111,119,59,32768,10230,112,97,114,114,111,119,512,108,114,11627,11633,101,102,116,59,32768,8619,105,103,104,116,59,32768,8620,768,97,102,108,11647,11651,11655,114,59,32768,10629,59,32896,55349,56669,117,115,59,32768,10797,105,109,101,115,59,32768,10804,562,11671,11676,115,116,59,32768,8727,97,114,59,32768,95,768,59,101,102,11687,11689,11695,32768,9674,110,103,101,59,32768,9674,59,32768,10731,97,114,512,59,108,11705,11707,32768,40,116,59,32768,10643,1280,97,99,104,109,116,11722,11727,11735,11747,11750,114,114,59,32768,8646,111,114,110,101,114,59,32768,8991,97,114,512,59,100,11742,11744,32768,8651,59,32768,10605,59,32768,8206,114,105,59,32768,8895,1536,97,99,104,105,113,116,11768,11774,11779,11782,11798,11817,113,117,111,59,32768,8249,114,59,32896,55349,56513,59,32768,8624,109,768,59,101,103,11790,11792,11795,32768,8818,59,32768,10893,59,32768,10895,512,98,117,11803,11806,59,32768,91,111,512,59,114,11812,11814,32768,8216,59,32768,8218,114,111,107,59,32768,322,34816,60,59,99,100,104,105,108,113,114,11841,11843,11855,11860,11866,11872,11878,11885,32768,60,512,99,105,11848,11851,59,32768,10918,114,59,32768,10873,111,116,59,32768,8918,114,101,101,59,32768,8907,109,101,115,59,32768,8905,97,114,114,59,32768,10614,117,101,115,116,59,32768,10875,512,80,105,11890,11895,97,114,59,32768,10646,768,59,101,102,11902,11904,11907,32768,9667,59,32768,8884,59,32768,9666,114,512,100,117,11916,11923,115,104,97,114,59,32768,10570,104,97,114,59,32768,10598,512,101,110,11934,11944,114,116,110,101,113,113,59,32896,8808,65024,69,59,32896,8808,65024,3584,68,97,99,100,101,102,104,105,108,110,111,112,115,117,11978,11984,12061,12075,12081,12095,12100,12104,12170,12181,12188,12204,12207,12223,68,111,116,59,32768,8762,1024,99,108,112,114,11993,11999,12019,12055,114,33024,175,59,32768,175,512,101,116,12004,12007,59,32768,9794,512,59,101,12012,12014,32768,10016,115,101,59,32768,10016,512,59,115,12024,12026,32768,8614,116,111,1024,59,100,108,117,12037,12039,12045,12051,32768,8614,111,119,110,59,32768,8615,101,102,116,59,32768,8612,112,59,32768,8613,107,101,114,59,32768,9646,512,111,121,12066,12072,109,109,97,59,32768,10793,59,32768,1084,97,115,104,59,32768,8212,97,115,117,114,101,100,97,110,103,108,101,59,32768,8737,114,59,32896,55349,56618,111,59,32768,8487,768,99,100,110,12111,12118,12146,114,111,33024,181,59,32768,181,1024,59,97,99,100,12127,12129,12134,12139,32768,8739,115,116,59,32768,42,105,114,59,32768,10992,111,116,33024,183,59,32768,183,117,115,768,59,98,100,12155,12157,12160,32768,8722,59,32768,8863,512,59,117,12165,12167,32768,8760,59,32768,10794,564,12174,12178,112,59,32768,10971,114,59,32768,8230,112,108,117,115,59,32768,8723,512,100,112,12193,12199,101,108,115,59,32768,8871,102,59,32896,55349,56670,59,32768,8723,512,99,116,12212,12217,114,59,32896,55349,56514,112,111,115,59,32768,8766,768,59,108,109,12230,12232,12240,32768,956,116,105,109,97,112,59,32768,8888,97,112,59,32768,8888,6144,71,76,82,86,97,98,99,100,101,102,103,104,105,106,108,109,111,112,114,115,116,117,118,119,12294,12315,12364,12376,12393,12472,12496,12547,12553,12636,12641,12703,12725,12747,12752,12876,12881,12957,13033,13089,13294,13359,13384,13499,512,103,116,12299,12303,59,32896,8921,824,512,59,118,12308,12311,32896,8811,8402,59,32896,8811,824,768,101,108,116,12322,12348,12352,102,116,512,97,114,12329,12336,114,114,111,119,59,32768,8653,105,103,104,116,97,114,114,111,119,59,32768,8654,59,32896,8920,824,512,59,118,12357,12360,32896,8810,8402,59,32896,8810,824,105,103,104,116,97,114,114,111,119,59,32768,8655,512,68,100,12381,12387,97,115,104,59,32768,8879,97,115,104,59,32768,8878,1280,98,99,110,112,116,12404,12409,12415,12420,12452,108,97,59,32768,8711,117,116,101,59,32768,324,103,59,32896,8736,8402,1280,59,69,105,111,112,12431,12433,12437,12442,12446,32768,8777,59,32896,10864,824,100,59,32896,8779,824,115,59,32768,329,114,111,120,59,32768,8777,117,114,512,59,97,12459,12461,32768,9838,108,512,59,115,12467,12469,32768,9838,59,32768,8469,836,12477,0,12483,112,33024,160,59,32768,160,109,112,512,59,101,12489,12492,32896,8782,824,59,32896,8783,824,1280,97,101,111,117,121,12507,12519,12525,12540,12544,833,12512,0,12515,59,32768,10819,111,110,59,32768,328,100,105,108,59,32768,326,110,103,512,59,100,12532,12534,32768,8775,111,116,59,32896,10861,824,112,59,32768,10818,59,32768,1085,97,115,104,59,32768,8211,1792,59,65,97,100,113,115,120,12568,12570,12575,12596,12602,12608,12623,32768,8800,114,114,59,32768,8663,114,512,104,114,12581,12585,107,59,32768,10532,512,59,111,12590,12592,32768,8599,119,59,32768,8599,111,116,59,32896,8784,824,117,105,118,59,32768,8802,512,101,105,12613,12618,97,114,59,32768,10536,109,59,32896,8770,824,105,115,116,512,59,115,12631,12633,32768,8708,59,32768,8708,114,59,32896,55349,56619,1024,69,101,115,116,12650,12654,12688,12693,59,32896,8807,824,768,59,113,115,12661,12663,12684,32768,8817,768,59,113,115,12670,12672,12676,32768,8817,59,32896,8807,824,108,97,110,116,59,32896,10878,824,59,32896,10878,824,105,109,59,32768,8821,512,59,114,12698,12700,32768,8815,59,32768,8815,768,65,97,112,12710,12715,12720,114,114,59,32768,8654,114,114,59,32768,8622,97,114,59,32768,10994,768,59,115,118,12732,12734,12744,32768,8715,512,59,100,12739,12741,32768,8956,59,32768,8954,59,32768,8715,99,121,59,32768,1114,1792,65,69,97,100,101,115,116,12767,12772,12776,12781,12785,12853,12858,114,114,59,32768,8653,59,32896,8806,824,114,114,59,32768,8602,114,59,32768,8229,1024,59,102,113,115,12794,12796,12821,12842,32768,8816,116,512,97,114,12802,12809,114,114,111,119,59,32768,8602,105,103,104,116,97,114,114,111,119,59,32768,8622,768,59,113,115,12828,12830,12834,32768,8816,59,32896,8806,824,108,97,110,116,59,32896,10877,824,512,59,115,12847,12850,32896,10877,824,59,32768,8814,105,109,59,32768,8820,512,59,114,12863,12865,32768,8814,105,512,59,101,12871,12873,32768,8938,59,32768,8940,105,100,59,32768,8740,512,112,116,12886,12891,102,59,32896,55349,56671,33536,172,59,105,110,12899,12901,12936,32768,172,110,1024,59,69,100,118,12911,12913,12917,12923,32768,8713,59,32896,8953,824,111,116,59,32896,8949,824,818,12928,12931,12934,59,32768,8713,59,32768,8951,59,32768,8950,105,512,59,118,12942,12944,32768,8716,818,12949,12952,12955,59,32768,8716,59,32768,8958,59,32768,8957,768,97,111,114,12964,12992,12999,114,1024,59,97,115,116,12974,12976,12983,12988,32768,8742,108,108,101,108,59,32768,8742,108,59,32896,11005,8421,59,32896,8706,824,108,105,110,116,59,32768,10772,768,59,99,101,13006,13008,13013,32768,8832,117,101,59,32768,8928,512,59,99,13018,13021,32896,10927,824,512,59,101,13026,13028,32768,8832,113,59,32896,10927,824,1024,65,97,105,116,13042,13047,13066,13077,114,114,59,32768,8655,114,114,768,59,99,119,13056,13058,13062,32768,8603,59,32896,10547,824,59,32896,8605,824,103,104,116,97,114,114,111,119,59,32768,8603,114,105,512,59,101,13084,13086,32768,8939,59,32768,8941,1792,99,104,105,109,112,113,117,13104,13128,13151,13169,13174,13179,13194,1024,59,99,101,114,13113,13115,13120,13124,32768,8833,117,101,59,32768,8929,59,32896,10928,824,59,32896,55349,56515,111,114,116,1086,13137,0,0,13142,105,100,59,32768,8740,97,114,97,108,108,101,108,59,32768,8742,109,512,59,101,13157,13159,32768,8769,512,59,113,13164,13166,32768,8772,59,32768,8772,105,100,59,32768,8740,97,114,59,32768,8742,115,117,512,98,112,13186,13190,101,59,32768,8930,101,59,32768,8931,768,98,99,112,13201,13241,13254,1024,59,69,101,115,13210,13212,13216,13219,32768,8836,59,32896,10949,824,59,32768,8840,101,116,512,59,101,13226,13229,32896,8834,8402,113,512,59,113,13235,13237,32768,8840,59,32896,10949,824,99,512,59,101,13247,13249,32768,8833,113,59,32896,10928,824,1024,59,69,101,115,13263,13265,13269,13272,32768,8837,59,32896,10950,824,59,32768,8841,101,116,512,59,101,13279,13282,32896,8835,8402,113,512,59,113,13288,13290,32768,8841,59,32896,10950,824,1024,103,105,108,114,13303,13307,13315,13319,108,59,32768,8825,108,100,101,33024,241,59,32768,241,103,59,32768,8824,105,97,110,103,108,101,512,108,114,13330,13344,101,102,116,512,59,101,13338,13340,32768,8938,113,59,32768,8940,105,103,104,116,512,59,101,13353,13355,32768,8939,113,59,32768,8941,512,59,109,13364,13366,32768,957,768,59,101,115,13373,13375,13380,32768,35,114,111,59,32768,8470,112,59,32768,8199,2304,68,72,97,100,103,105,108,114,115,13403,13409,13415,13420,13426,13439,13446,13476,13493,97,115,104,59,32768,8877,97,114,114,59,32768,10500,112,59,32896,8781,8402,97,115,104,59,32768,8876,512,101,116,13431,13435,59,32896,8805,8402,59,32896,62,8402,110,102,105,110,59,32768,10718,768,65,101,116,13453,13458,13462,114,114,59,32768,10498,59,32896,8804,8402,512,59,114,13467,13470,32896,60,8402,105,101,59,32896,8884,8402,512,65,116,13481,13486,114,114,59,32768,10499,114,105,101,59,32896,8885,8402,105,109,59,32896,8764,8402,768,65,97,110,13506,13511,13532,114,114,59,32768,8662,114,512,104,114,13517,13521,107,59,32768,10531,512,59,111,13526,13528,32768,8598,119,59,32768,8598,101,97,114,59,32768,10535,9252,13576,0,0,0,0,0,0,0,0,0,0,0,0,0,13579,0,13596,13617,13653,13659,13673,13695,13708,0,0,13713,13750,0,13788,13794,0,13815,13890,13913,13937,13944,59,32768,9416,512,99,115,13583,13591,117,116,101,33024,243,59,32768,243,116,59,32768,8859,512,105,121,13600,13613,114,512,59,99,13606,13608,32768,8858,33024,244,59,32768,244,59,32768,1086,1280,97,98,105,111,115,13627,13632,13638,13642,13646,115,104,59,32768,8861,108,97,99,59,32768,337,118,59,32768,10808,116,59,32768,8857,111,108,100,59,32768,10684,108,105,103,59,32768,339,512,99,114,13663,13668,105,114,59,32768,10687,59,32896,55349,56620,1600,13680,0,0,13684,0,13692,110,59,32768,731,97,118,101,33024,242,59,32768,242,59,32768,10689,512,98,109,13699,13704,97,114,59,32768,10677,59,32768,937,110,116,59,32768,8750,1024,97,99,105,116,13721,13726,13741,13746,114,114,59,32768,8634,512,105,114,13731,13735,114,59,32768,10686,111,115,115,59,32768,10683,110,101,59,32768,8254,59,32768,10688,768,97,101,105,13756,13761,13766,99,114,59,32768,333,103,97,59,32768,969,768,99,100,110,13773,13779,13782,114,111,110,59,32768,959,59,32768,10678,117,115,59,32768,8854,112,102,59,32896,55349,56672,768,97,101,108,13800,13804,13809,114,59,32768,10679,114,112,59,32768,10681,117,115,59,32768,8853,1792,59,97,100,105,111,115,118,13829,13831,13836,13869,13875,13879,13886,32768,8744,114,114,59,32768,8635,1024,59,101,102,109,13845,13847,13859,13864,32768,10845,114,512,59,111,13853,13855,32768,8500,102,59,32768,8500,33024,170,59,32768,170,33024,186,59,32768,186,103,111,102,59,32768,8886,114,59,32768,10838,108,111,112,101,59,32768,10839,59,32768,10843,768,99,108,111,13896,13900,13908,114,59,32768,8500,97,115,104,33024,248,59,32768,248,108,59,32768,8856,105,573,13917,13924,100,101,33024,245,59,32768,245,101,115,512,59,97,13930,13932,32768,8855,115,59,32768,10806,109,108,33024,246,59,32768,246,98,97,114,59,32768,9021,5426,13972,0,14013,0,14017,14053,0,14058,14086,0,0,14107,14199,0,14202,0,0,14229,14425,0,14438,114,1024,59,97,115,116,13981,13983,13997,14009,32768,8741,33280,182,59,108,13989,13991,32768,182,108,101,108,59,32768,8741,1082,14003,0,0,14007,109,59,32768,10995,59,32768,11005,59,32768,8706,121,59,32768,1087,114,1280,99,105,109,112,116,14028,14033,14038,14043,14046,110,116,59,32768,37,111,100,59,32768,46,105,108,59,32768,8240,59,32768,8869,101,110,107,59,32768,8241,114,59,32896,55349,56621,768,105,109,111,14064,14074,14080,512,59,118,14069,14071,32768,966,59,32768,981,109,97,116,59,32768,8499,110,101,59,32768,9742,768,59,116,118,14092,14094,14103,32768,960,99,104,102,111,114,107,59,32768,8916,59,32768,982,512,97,117,14111,14132,110,512,99,107,14117,14128,107,512,59,104,14123,14125,32768,8463,59,32768,8462,118,59,32768,8463,115,2304,59,97,98,99,100,101,109,115,116,14152,14154,14160,14163,14168,14179,14182,14188,14193,32768,43,99,105,114,59,32768,10787,59,32768,8862,105,114,59,32768,10786,512,111,117,14173,14176,59,32768,8724,59,32768,10789,59,32768,10866,110,33024,177,59,32768,177,105,109,59,32768,10790,119,111,59,32768,10791,59,32768,177,768,105,112,117,14208,14216,14221,110,116,105,110,116,59,32768,10773,102,59,32896,55349,56673,110,100,33024,163,59,32768,163,2560,59,69,97,99,101,105,110,111,115,117,14249,14251,14254,14258,14263,14336,14348,14367,14413,14418,32768,8826,59,32768,10931,112,59,32768,10935,117,101,59,32768,8828,512,59,99,14268,14270,32768,10927,1536,59,97,99,101,110,115,14283,14285,14293,14302,14306,14331,32768,8826,112,112,114,111,120,59,32768,10935,117,114,108,121,101,113,59,32768,8828,113,59,32768,10927,768,97,101,115,14313,14321,14326,112,112,114,111,120,59,32768,10937,113,113,59,32768,10933,105,109,59,32768,8936,105,109,59,32768,8830,109,101,512,59,115,14343,14345,32768,8242,59,32768,8473,768,69,97,115,14355,14358,14362,59,32768,10933,112,59,32768,10937,105,109,59,32768,8936,768,100,102,112,14374,14377,14402,59,32768,8719,768,97,108,115,14384,14390,14396,108,97,114,59,32768,9006,105,110,101,59,32768,8978,117,114,102,59,32768,8979,512,59,116,14407,14409,32768,8733,111,59,32768,8733,105,109,59,32768,8830,114,101,108,59,32768,8880,512,99,105,14429,14434,114,59,32896,55349,56517,59,32768,968,110,99,115,112,59,32768,8200,1536,102,105,111,112,115,117,14457,14462,14467,14473,14480,14486,114,59,32896,55349,56622,110,116,59,32768,10764,112,102,59,32896,55349,56674,114,105,109,101,59,32768,8279,99,114,59,32896,55349,56518,768,97,101,111,14493,14513,14526,116,512,101,105,14499,14508,114,110,105,111,110,115,59,32768,8461,110,116,59,32768,10774,115,116,512,59,101,14520,14522,32768,63,113,59,32768,8799,116,33024,34,59,32768,34,5376,65,66,72,97,98,99,100,101,102,104,105,108,109,110,111,112,114,115,116,117,120,14575,14597,14603,14608,14775,14829,14865,14901,14943,14966,15e3,15139,15159,15176,15182,15236,15261,15267,15309,15352,15360,768,97,114,116,14582,14587,14591,114,114,59,32768,8667,114,59,32768,8658,97,105,108,59,32768,10524,97,114,114,59,32768,10511,97,114,59,32768,10596,1792,99,100,101,110,113,114,116,14623,14637,14642,14650,14672,14679,14751,512,101,117,14628,14632,59,32896,8765,817,116,101,59,32768,341,105,99,59,32768,8730,109,112,116,121,118,59,32768,10675,103,1024,59,100,101,108,14660,14662,14665,14668,32768,10217,59,32768,10642,59,32768,10661,101,59,32768,10217,117,111,33024,187,59,32768,187,114,2816,59,97,98,99,102,104,108,112,115,116,119,14703,14705,14709,14720,14723,14727,14731,14735,14739,14744,14748,32768,8594,112,59,32768,10613,512,59,102,14714,14716,32768,8677,115,59,32768,10528,59,32768,10547,115,59,32768,10526,107,59,32768,8618,112,59,32768,8620,108,59,32768,10565,105,109,59,32768,10612,108,59,32768,8611,59,32768,8605,512,97,105,14756,14761,105,108,59,32768,10522,111,512,59,110,14767,14769,32768,8758,97,108,115,59,32768,8474,768,97,98,114,14782,14787,14792,114,114,59,32768,10509,114,107,59,32768,10099,512,97,107,14797,14809,99,512,101,107,14803,14806,59,32768,125,59,32768,93,512,101,115,14814,14817,59,32768,10636,108,512,100,117,14823,14826,59,32768,10638,59,32768,10640,1024,97,101,117,121,14838,14844,14858,14862,114,111,110,59,32768,345,512,100,105,14849,14854,105,108,59,32768,343,108,59,32768,8969,98,59,32768,125,59,32768,1088,1024,99,108,113,115,14874,14878,14885,14897,97,59,32768,10551,100,104,97,114,59,32768,10601,117,111,512,59,114,14892,14894,32768,8221,59,32768,8221,104,59,32768,8627,768,97,99,103,14908,14934,14938,108,1024,59,105,112,115,14918,14920,14925,14931,32768,8476,110,101,59,32768,8475,97,114,116,59,32768,8476,59,32768,8477,116,59,32768,9645,33024,174,59,32768,174,768,105,108,114,14950,14956,14962,115,104,116,59,32768,10621,111,111,114,59,32768,8971,59,32896,55349,56623,512,97,111,14971,14990,114,512,100,117,14977,14980,59,32768,8641,512,59,108,14985,14987,32768,8640,59,32768,10604,512,59,118,14995,14997,32768,961,59,32768,1009,768,103,110,115,15007,15123,15127,104,116,1536,97,104,108,114,115,116,15022,15039,15060,15086,15099,15111,114,114,111,119,512,59,116,15031,15033,32768,8594,97,105,108,59,32768,8611,97,114,112,111,111,110,512,100,117,15050,15056,111,119,110,59,32768,8641,112,59,32768,8640,101,102,116,512,97,104,15068,15076,114,114,111,119,115,59,32768,8644,97,114,112,111,111,110,115,59,32768,8652,105,103,104,116,97,114,114,111,119,115,59,32768,8649,113,117,105,103,97,114,114,111,119,59,32768,8605,104,114,101,101,116,105,109,101,115,59,32768,8908,103,59,32768,730,105,110,103,100,111,116,115,101,113,59,32768,8787,768,97,104,109,15146,15151,15156,114,114,59,32768,8644,97,114,59,32768,8652,59,32768,8207,111,117,115,116,512,59,97,15168,15170,32768,9137,99,104,101,59,32768,9137,109,105,100,59,32768,10990,1024,97,98,112,116,15191,15204,15209,15229,512,110,114,15196,15200,103,59,32768,10221,114,59,32768,8702,114,107,59,32768,10215,768,97,102,108,15216,15220,15224,114,59,32768,10630,59,32896,55349,56675,117,115,59,32768,10798,105,109,101,115,59,32768,10805,512,97,112,15241,15253,114,512,59,103,15247,15249,32768,41,116,59,32768,10644,111,108,105,110,116,59,32768,10770,97,114,114,59,32768,8649,1024,97,99,104,113,15276,15282,15287,15290,113,117,111,59,32768,8250,114,59,32896,55349,56519,59,32768,8625,512,98,117,15295,15298,59,32768,93,111,512,59,114,15304,15306,32768,8217,59,32768,8217,768,104,105,114,15316,15322,15328,114,101,101,59,32768,8908,109,101,115,59,32768,8906,105,1024,59,101,102,108,15338,15340,15343,15346,32768,9657,59,32768,8885,59,32768,9656,116,114,105,59,32768,10702,108,117,104,97,114,59,32768,10600,59,32768,8478,6706,15391,15398,15404,15499,15516,15592,0,15606,15660,0,0,15752,15758,0,15827,15863,15886,16e3,16006,16038,16086,0,16467,0,0,16506,99,117,116,101,59,32768,347,113,117,111,59,32768,8218,2560,59,69,97,99,101,105,110,112,115,121,15424,15426,15429,15441,15446,15458,15463,15482,15490,15495,32768,8827,59,32768,10932,833,15434,0,15437,59,32768,10936,111,110,59,32768,353,117,101,59,32768,8829,512,59,100,15451,15453,32768,10928,105,108,59,32768,351,114,99,59,32768,349,768,69,97,115,15470,15473,15477,59,32768,10934,112,59,32768,10938,105,109,59,32768,8937,111,108,105,110,116,59,32768,10771,105,109,59,32768,8831,59,32768,1089,111,116,768,59,98,101,15507,15509,15512,32768,8901,59,32768,8865,59,32768,10854,1792,65,97,99,109,115,116,120,15530,15535,15556,15562,15566,15572,15587,114,114,59,32768,8664,114,512,104,114,15541,15545,107,59,32768,10533,512,59,111,15550,15552,32768,8600,119,59,32768,8600,116,33024,167,59,32768,167,105,59,32768,59,119,97,114,59,32768,10537,109,512,105,110,15578,15584,110,117,115,59,32768,8726,59,32768,8726,116,59,32768,10038,114,512,59,111,15597,15600,32896,55349,56624,119,110,59,32768,8994,1024,97,99,111,121,15614,15619,15632,15654,114,112,59,32768,9839,512,104,121,15624,15629,99,121,59,32768,1097,59,32768,1096,114,116,1086,15640,0,0,15645,105,100,59,32768,8739,97,114,97,108,108,101,108,59,32768,8741,33024,173,59,32768,173,512,103,109,15664,15681,109,97,768,59,102,118,15673,15675,15678,32768,963,59,32768,962,59,32768,962,2048,59,100,101,103,108,110,112,114,15698,15700,15705,15715,15725,15735,15739,15745,32768,8764,111,116,59,32768,10858,512,59,113,15710,15712,32768,8771,59,32768,8771,512,59,69,15720,15722,32768,10910,59,32768,10912,512,59,69,15730,15732,32768,10909,59,32768,10911,101,59,32768,8774,108,117,115,59,32768,10788,97,114,114,59,32768,10610,97,114,114,59,32768,8592,1024,97,101,105,116,15766,15788,15796,15808,512,108,115,15771,15783,108,115,101,116,109,105,110,117,115,59,32768,8726,104,112,59,32768,10803,112,97,114,115,108,59,32768,10724,512,100,108,15801,15804,59,32768,8739,101,59,32768,8995,512,59,101,15813,15815,32768,10922,512,59,115,15820,15822,32768,10924,59,32896,10924,65024,768,102,108,112,15833,15839,15857,116,99,121,59,32768,1100,512,59,98,15844,15846,32768,47,512,59,97,15851,15853,32768,10692,114,59,32768,9023,102,59,32896,55349,56676,97,512,100,114,15868,15882,101,115,512,59,117,15875,15877,32768,9824,105,116,59,32768,9824,59,32768,8741,768,99,115,117,15892,15921,15977,512,97,117,15897,15909,112,512,59,115,15903,15905,32768,8851,59,32896,8851,65024,112,512,59,115,15915,15917,32768,8852,59,32896,8852,65024,117,512,98,112,15927,15952,768,59,101,115,15934,15936,15939,32768,8847,59,32768,8849,101,116,512,59,101,15946,15948,32768,8847,113,59,32768,8849,768,59,101,115,15959,15961,15964,32768,8848,59,32768,8850,101,116,512,59,101,15971,15973,32768,8848,113,59,32768,8850,768,59,97,102,15984,15986,15996,32768,9633,114,566,15991,15994,59,32768,9633,59,32768,9642,59,32768,9642,97,114,114,59,32768,8594,1024,99,101,109,116,16014,16019,16025,16031,114,59,32896,55349,56520,116,109,110,59,32768,8726,105,108,101,59,32768,8995,97,114,102,59,32768,8902,512,97,114,16042,16053,114,512,59,102,16048,16050,32768,9734,59,32768,9733,512,97,110,16058,16081,105,103,104,116,512,101,112,16067,16076,112,115,105,108,111,110,59,32768,1013,104,105,59,32768,981,115,59,32768,175,1280,98,99,109,110,112,16096,16221,16288,16291,16295,2304,59,69,100,101,109,110,112,114,115,16115,16117,16120,16125,16137,16143,16154,16160,16166,32768,8834,59,32768,10949,111,116,59,32768,10941,512,59,100,16130,16132,32768,8838,111,116,59,32768,10947,117,108,116,59,32768,10945,512,69,101,16148,16151,59,32768,10955,59,32768,8842,108,117,115,59,32768,10943,97,114,114,59,32768,10617,768,101,105,117,16173,16206,16210,116,768,59,101,110,16181,16183,16194,32768,8834,113,512,59,113,16189,16191,32768,8838,59,32768,10949,101,113,512,59,113,16201,16203,32768,8842,59,32768,10955,109,59,32768,10951,512,98,112,16215,16218,59,32768,10965,59,32768,10963,99,1536,59,97,99,101,110,115,16235,16237,16245,16254,16258,16283,32768,8827,112,112,114,111,120,59,32768,10936,117,114,108,121,101,113,59,32768,8829,113,59,32768,10928,768,97,101,115,16265,16273,16278,112,112,114,111,120,59,32768,10938,113,113,59,32768,10934,105,109,59,32768,8937,105,109,59,32768,8831,59,32768,8721,103,59,32768,9834,3328,49,50,51,59,69,100,101,104,108,109,110,112,115,16322,16327,16332,16337,16339,16342,16356,16368,16382,16388,16394,16405,16411,33024,185,59,32768,185,33024,178,59,32768,178,33024,179,59,32768,179,32768,8835,59,32768,10950,512,111,115,16347,16351,116,59,32768,10942,117,98,59,32768,10968,512,59,100,16361,16363,32768,8839,111,116,59,32768,10948,115,512,111,117,16374,16378,108,59,32768,10185,98,59,32768,10967,97,114,114,59,32768,10619,117,108,116,59,32768,10946,512,69,101,16399,16402,59,32768,10956,59,32768,8843,108,117,115,59,32768,10944,768,101,105,117,16418,16451,16455,116,768,59,101,110,16426,16428,16439,32768,8835,113,512,59,113,16434,16436,32768,8839,59,32768,10950,101,113,512,59,113,16446,16448,32768,8843,59,32768,10956,109,59,32768,10952,512,98,112,16460,16463,59,32768,10964,59,32768,10966,768,65,97,110,16473,16478,16499,114,114,59,32768,8665,114,512,104,114,16484,16488,107,59,32768,10534,512,59,111,16493,16495,32768,8601,119,59,32768,8601,119,97,114,59,32768,10538,108,105,103,33024,223,59,32768,223,5938,16538,16552,16557,16579,16584,16591,0,16596,16692,0,0,0,0,0,16731,16780,0,16787,16908,0,0,0,16938,1091,16543,0,0,16549,103,101,116,59,32768,8982,59,32768,964,114,107,59,32768,9140,768,97,101,121,16563,16569,16575,114,111,110,59,32768,357,100,105,108,59,32768,355,59,32768,1090,111,116,59,32768,8411,108,114,101,99,59,32768,8981,114,59,32896,55349,56625,1024,101,105,107,111,16604,16641,16670,16684,835,16609,0,16624,101,512,52,102,16614,16617,59,32768,8756,111,114,101,59,32768,8756,97,768,59,115,118,16631,16633,16638,32768,952,121,109,59,32768,977,59,32768,977,512,99,110,16646,16665,107,512,97,115,16652,16660,112,112,114,111,120,59,32768,8776,105,109,59,32768,8764,115,112,59,32768,8201,512,97,115,16675,16679,112,59,32768,8776,105,109,59,32768,8764,114,110,33024,254,59,32768,254,829,16696,16701,16727,100,101,59,32768,732,101,115,33536,215,59,98,100,16710,16712,16723,32768,215,512,59,97,16717,16719,32768,8864,114,59,32768,10801,59,32768,10800,116,59,32768,8749,768,101,112,115,16737,16741,16775,97,59,32768,10536,1024,59,98,99,102,16750,16752,16757,16762,32768,8868,111,116,59,32768,9014,105,114,59,32768,10993,512,59,111,16767,16770,32896,55349,56677,114,107,59,32768,10970,97,59,32768,10537,114,105,109,101,59,32768,8244,768,97,105,112,16793,16798,16899,100,101,59,32768,8482,1792,97,100,101,109,112,115,116,16813,16868,16873,16876,16883,16889,16893,110,103,108,101,1280,59,100,108,113,114,16828,16830,16836,16850,16853,32768,9653,111,119,110,59,32768,9663,101,102,116,512,59,101,16844,16846,32768,9667,113,59,32768,8884,59,32768,8796,105,103,104,116,512,59,101,16862,16864,32768,9657,113,59,32768,8885,111,116,59,32768,9708,59,32768,8796,105,110,117,115,59,32768,10810,108,117,115,59,32768,10809,98,59,32768,10701,105,109,101,59,32768,10811,101,122,105,117,109,59,32768,9186,768,99,104,116,16914,16926,16931,512,114,121,16919,16923,59,32896,55349,56521,59,32768,1094,99,121,59,32768,1115,114,111,107,59,32768,359,512,105,111,16942,16947,120,116,59,32768,8812,104,101,97,100,512,108,114,16956,16967,101,102,116,97,114,114,111,119,59,32768,8606,105,103,104,116,97,114,114,111,119,59,32768,8608,4608,65,72,97,98,99,100,102,103,104,108,109,111,112,114,115,116,117,119,17016,17021,17026,17043,17057,17072,17095,17110,17119,17139,17172,17187,17202,17290,17330,17336,17365,17381,114,114,59,32768,8657,97,114,59,32768,10595,512,99,114,17031,17039,117,116,101,33024,250,59,32768,250,114,59,32768,8593,114,820,17049,0,17053,121,59,32768,1118,118,101,59,32768,365,512,105,121,17062,17069,114,99,33024,251,59,32768,251,59,32768,1091,768,97,98,104,17079,17084,17090,114,114,59,32768,8645,108,97,99,59,32768,369,97,114,59,32768,10606,512,105,114,17100,17106,115,104,116,59,32768,10622,59,32896,55349,56626,114,97,118,101,33024,249,59,32768,249,562,17123,17135,114,512,108,114,17128,17131,59,32768,8639,59,32768,8638,108,107,59,32768,9600,512,99,116,17144,17167,1088,17150,0,0,17163,114,110,512,59,101,17156,17158,32768,8988,114,59,32768,8988,111,112,59,32768,8975,114,105,59,32768,9720,512,97,108,17177,17182,99,114,59,32768,363,33024,168,59,32768,168,512,103,112,17192,17197,111,110,59,32768,371,102,59,32896,55349,56678,1536,97,100,104,108,115,117,17215,17222,17233,17257,17262,17280,114,114,111,119,59,32768,8593,111,119,110,97,114,114,111,119,59,32768,8597,97,114,112,111,111,110,512,108,114,17244,17250,101,102,116,59,32768,8639,105,103,104,116,59,32768,8638,117,115,59,32768,8846,105,768,59,104,108,17270,17272,17275,32768,965,59,32768,978,111,110,59,32768,965,112,97,114,114,111,119,115,59,32768,8648,768,99,105,116,17297,17320,17325,1088,17303,0,0,17316,114,110,512,59,101,17309,17311,32768,8989,114,59,32768,8989,111,112,59,32768,8974,110,103,59,32768,367,114,105,59,32768,9721,99,114,59,32896,55349,56522,768,100,105,114,17343,17348,17354,111,116,59,32768,8944,108,100,101,59,32768,361,105,512,59,102,17360,17362,32768,9653,59,32768,9652,512,97,109,17370,17375,114,114,59,32768,8648,108,33024,252,59,32768,252,97,110,103,108,101,59,32768,10663,3840,65,66,68,97,99,100,101,102,108,110,111,112,114,115,122,17420,17425,17437,17443,17613,17617,17623,17667,17672,17678,17693,17699,17705,17711,17754,114,114,59,32768,8661,97,114,512,59,118,17432,17434,32768,10984,59,32768,10985,97,115,104,59,32768,8872,512,110,114,17448,17454,103,114,116,59,32768,10652,1792,101,107,110,112,114,115,116,17469,17478,17485,17494,17515,17526,17578,112,115,105,108,111,110,59,32768,1013,97,112,112,97,59,32768,1008,111,116,104,105,110,103,59,32768,8709,768,104,105,114,17501,17505,17508,105,59,32768,981,59,32768,982,111,112,116,111,59,32768,8733,512,59,104,17520,17522,32768,8597,111,59,32768,1009,512,105,117,17531,17537,103,109,97,59,32768,962,512,98,112,17542,17560,115,101,116,110,101,113,512,59,113,17553,17556,32896,8842,65024,59,32896,10955,65024,115,101,116,110,101,113,512,59,113,17571,17574,32896,8843,65024,59,32896,10956,65024,512,104,114,17583,17589,101,116,97,59,32768,977,105,97,110,103,108,101,512,108,114,17600,17606,101,102,116,59,32768,8882,105,103,104,116,59,32768,8883,121,59,32768,1074,97,115,104,59,32768,8866,768,101,108,114,17630,17648,17654,768,59,98,101,17637,17639,17644,32768,8744,97,114,59,32768,8891,113,59,32768,8794,108,105,112,59,32768,8942,512,98,116,17659,17664,97,114,59,32768,124,59,32768,124,114,59,32896,55349,56627,116,114,105,59,32768,8882,115,117,512,98,112,17685,17689,59,32896,8834,8402,59,32896,8835,8402,112,102,59,32896,55349,56679,114,111,112,59,32768,8733,116,114,105,59,32768,8883,512,99,117,17716,17721,114,59,32896,55349,56523,512,98,112,17726,17740,110,512,69,101,17732,17736,59,32896,10955,65024,59,32896,8842,65024,110,512,69,101,17746,17750,59,32896,10956,65024,59,32896,8843,65024,105,103,122,97,103,59,32768,10650,1792,99,101,102,111,112,114,115,17777,17783,17815,17820,17826,17829,17842,105,114,99,59,32768,373,512,100,105,17788,17809,512,98,103,17793,17798,97,114,59,32768,10847,101,512,59,113,17804,17806,32768,8743,59,32768,8793,101,114,112,59,32768,8472,114,59,32896,55349,56628,112,102,59,32896,55349,56680,59,32768,8472,512,59,101,17834,17836,32768,8768,97,116,104,59,32768,8768,99,114,59,32896,55349,56524,5428,17871,17891,0,17897,0,17902,17917,0,0,17920,17935,17940,17945,0,0,17977,17992,0,18008,18024,18029,768,97,105,117,17877,17881,17886,112,59,32768,8898,114,99,59,32768,9711,112,59,32768,8899,116,114,105,59,32768,9661,114,59,32896,55349,56629,512,65,97,17906,17911,114,114,59,32768,10234,114,114,59,32768,10231,59,32768,958,512,65,97,17924,17929,114,114,59,32768,10232,114,114,59,32768,10229,97,112,59,32768,10236,105,115,59,32768,8955,768,100,112,116,17951,17956,17970,111,116,59,32768,10752,512,102,108,17961,17965,59,32896,55349,56681,117,115,59,32768,10753,105,109,101,59,32768,10754,512,65,97,17981,17986,114,114,59,32768,10233,114,114,59,32768,10230,512,99,113,17996,18001,114,59,32896,55349,56525,99,117,112,59,32768,10758,512,112,116,18012,18018,108,117,115,59,32768,10756,114,105,59,32768,9651,101,101,59,32768,8897,101,100,103,101,59,32768,8896,2048,97,99,101,102,105,111,115,117,18052,18068,18081,18087,18092,18097,18103,18109,99,512,117,121,18058,18065,116,101,33024,253,59,32768,253,59,32768,1103,512,105,121,18073,18078,114,99,59,32768,375,59,32768,1099,110,33024,165,59,32768,165,114,59,32896,55349,56630,99,121,59,32768,1111,112,102,59,32896,55349,56682,99,114,59,32896,55349,56526,512,99,109,18114,18118,121,59,32768,1102,108,33024,255,59,32768,255,2560,97,99,100,101,102,104,105,111,115,119,18145,18152,18166,18171,18186,18191,18196,18204,18210,18216,99,117,116,101,59,32768,378,512,97,121,18157,18163,114,111,110,59,32768,382,59,32768,1079,111,116,59,32768,380,512,101,116,18176,18182,116,114,102,59,32768,8488,97,59,32768,950,114,59,32896,55349,56631,99,121,59,32768,1078,103,114,97,114,114,59,32768,8669,112,102,59,32896,55349,56683,99,114,59,32896,55349,56527,512,106,110,18221,18224,59,32768,8205,106,59,32768,8204]);var hc={};Object.defineProperty(hc,"__esModule",{value:!0});hc.default=new Uint16Array([1024,97,103,108,113,9,23,27,31,1086,15,0,0,19,112,59,32768,38,111,115,59,32768,39,116,59,32768,62,116,59,32768,60,117,111,116,59,32768,34]);(function(t){var e=L&&L.__importDefault||function(m){return m&&m.__esModule?m:{default:m}};Object.defineProperty(t,"__esModule",{value:!0}),t.decodeXML=t.decodeHTMLStrict=t.decodeHTML=t.determineBranch=t.JUMP_OFFSET_BASE=t.BinTrieFlags=t.xmlDecodeTree=t.htmlDecodeTree=void 0;var n=e(pc);t.htmlDecodeTree=n.default;var r=e(hc);t.xmlDecodeTree=r.default;var o=e(Ns),s;(function(m){m[m.HAS_VALUE=32768]="HAS_VALUE",m[m.BRANCH_LENGTH=32512]="BRANCH_LENGTH",m[m.MULTI_BYTE=128]="MULTI_BYTE",m[m.JUMP_TABLE=127]="JUMP_TABLE"})(s=t.BinTrieFlags||(t.BinTrieFlags={})),t.JUMP_OFFSET_BASE=47;function i(m){return function(v,x){for(var p="",g=0,h=0;(h=v.indexOf("&",h))>=0;){if(p+=v.slice(g,h),g=h,h+=1,v.charCodeAt(h)===35){var w=h+1,S=10,E=v.charCodeAt(w);for((E|32)===120&&(S=16,h+=1,w+=1);(E=v.charCodeAt(++h))>=48&&E<=57||S===16&&(E|32)>=97&&(E|32)<=102;);if(w!==h){var b=v.substring(w,h),_=parseInt(b,S);if(v.charCodeAt(h)===59)h+=1;else if(x)continue;p+=o.default(_),g=h}continue}for(var M=null,A=1,O=0,W=m[O];h<v.length&&(O=a(m,W,O+1,v.charCodeAt(h)),!(O<0));h++,A++)W=m[O],W&s.HAS_VALUE&&(x&&v.charCodeAt(h)!==59?O+=1:(M=W&s.MULTI_BYTE?String.fromCharCode(m[++O],m[++O]):String.fromCharCode(m[++O]),A=0));M!=null&&(p+=M,g=h-A+1)}return p+v.slice(g)}}function a(m,y,v,x){if(y<=128)return x===y?v:-1;var p=(y&s.BRANCH_LENGTH)>>8;if(p===0)return-1;if(p===1)return x===m[v]?v+1:-1;var g=y&s.JUMP_TABLE;if(g){var h=x-t.JUMP_OFFSET_BASE-g;return h<0||h>p?-1:m[v+h]-1}for(var w=v,S=w+p-1;w<=S;){var E=w+S>>>1,b=m[E];if(b<x)w=E+1;else if(b>x)S=E-1;else return m[E+p]}return-1}t.determineBranch=a;var l=i(n.default),c=i(r.default);function u(m){return l(m,!1)}t.decodeHTML=u;function f(m){return l(m,!0)}t.decodeHTMLStrict=f;function d(m){return c(m,!0)}t.decodeXML=d})(J2);var pm=L&&L.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Ls,"__esModule",{value:!0});var hm=pm(Ns),L1=J2;function xe(t){return t===32||t===10||t===9||t===12||t===13}function io(t){return t===47||t===62||xe(t)}function G0(t){return t>=48&&t<=57}function mm(t){return t>=97&&t<=122||t>=65&&t<=90}var At={Cdata:new Uint16Array([67,68,65,84,65,91]),CdataEnd:new Uint16Array([93,93,62]),CommentEnd:new Uint16Array([45,45,62]),ScriptEnd:new Uint16Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint16Array([60,47,115,116,121,108,101]),TitleEnd:new Uint16Array([60,47,116,105,116,108,101])},gm=function(){function t(e,n){var r=e.xmlMode,o=r===void 0?!1:r,s=e.decodeEntities,i=s===void 0?!0:s;this.cbs=n,this._state=1,this.buffer="",this.sectionStart=0,this._index=0,this.bufferOffset=0,this.baseState=1,this.isSpecial=!1,this.running=!0,this.ended=!1,this.sequenceIndex=0,this.trieIndex=0,this.trieCurrent=0,this.trieResult=null,this.entityExcess=0,this.xmlMode=o,this.decodeEntities=i,this.entityTrie=o?L1.xmlDecodeTree:L1.htmlDecodeTree}return t.prototype.reset=function(){this._state=1,this.buffer="",this.sectionStart=0,this._index=0,this.bufferOffset=0,this.baseState=1,this.currentSequence=void 0,this.running=!0,this.ended=!1},t.prototype.write=function(e){if(this.ended)return this.cbs.onerror(Error(".write() after done!"));this.buffer+=e,this.parse()},t.prototype.end=function(e){if(this.ended)return this.cbs.onerror(Error(".end() after done!"));e&&this.write(e),this.ended=!0,this.running&&this.finish()},t.prototype.pause=function(){this.running=!1},t.prototype.resume=function(){this.running=!0,this._index<this.buffer.length&&this.parse(),this.ended&&this.finish()},t.prototype.getAbsoluteSectionStart=function(){return this.sectionStart+this.bufferOffset},t.prototype.getAbsoluteIndex=function(){return this.bufferOffset+this._index},t.prototype.stateText=function(e){e===60||!this.decodeEntities&&this.fastForwardTo(60)?(this._index>this.sectionStart&&this.cbs.ontext(this.getSection()),this._state=2,this.sectionStart=this._index):this.decodeEntities&&e===38&&(this._state=25)},t.prototype.stateSpecialStartSequence=function(e){var n=this.sequenceIndex===this.currentSequence.length,r=n?io(e):(e|32)===this.currentSequence[this.sequenceIndex];if(!r)this.isSpecial=!1;else if(!n){this.sequenceIndex++;return}this.sequenceIndex=0,this._state=3,this.stateInTagName(e)},t.prototype.stateInSpecialTag=function(e){if(this.sequenceIndex===this.currentSequence.length){if(e===62||xe(e)){var n=this._index-this.currentSequence.length;if(this.sectionStart<n){var r=this._index;this._index=n,this.cbs.ontext(this.getSection()),this._index=r}this.isSpecial=!1,this.sectionStart=n+2,this.stateInClosingTagName(e);return}this.sequenceIndex=0}(e|32)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:this.sequenceIndex===0?this.currentSequence===At.TitleEnd?this.decodeEntities&&e===38&&(this._state=25):this.fastForwardTo(60)&&(this.sequenceIndex=1):this.sequenceIndex=+(e===60)},t.prototype.stateCDATASequence=function(e){e===At.Cdata[this.sequenceIndex]?++this.sequenceIndex===At.Cdata.length&&(this._state=21,this.currentSequence=At.CdataEnd,this.sequenceIndex=0,this.sectionStart=this._index+1):(this.sequenceIndex=0,this._state=16,this.stateInDeclaration(e))},t.prototype.fastForwardTo=function(e){for(;++this._index<this.buffer.length;)if(this.buffer.charCodeAt(this._index)===e)return!0;return this._index=this.buffer.length-1,!1},t.prototype.stateInCommentLike=function(e){if(e===this.currentSequence[this.sequenceIndex]){if(++this.sequenceIndex===this.currentSequence.length){var n=this.buffer.slice(this.sectionStart,this._index-2);this.currentSequence===At.CdataEnd?this.cbs.oncdata(n):this.cbs.oncomment(n),this.sequenceIndex=0,this.sectionStart=this._index+1,this._state=1}}else this.sequenceIndex===0?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):e!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)},t.prototype.isTagStartChar=function(e){return this.xmlMode?!io(e):mm(e)},t.prototype.startSpecial=function(e,n){this.isSpecial=!0,this.currentSequence=e,this.sequenceIndex=n,this._state=23},t.prototype.stateBeforeTagName=function(e){if(e===33)this._state=15,this.sectionStart=this._index+1;else if(e===63)this._state=17,this.sectionStart=this._index+1;else if(this.isTagStartChar(e)){var n=e|32;this.sectionStart=this._index,!this.xmlMode&&n===At.TitleEnd[2]?this.startSpecial(At.TitleEnd,3):this._state=!this.xmlMode&&n===At.ScriptEnd[2]?22:3}else e===47?this._state=5:(this._state=1,this.stateText(e))},t.prototype.stateInTagName=function(e){io(e)&&(this.cbs.onopentagname(this.getSection()),this.sectionStart=-1,this._state=8,this.stateBeforeAttributeName(e))},t.prototype.stateBeforeClosingTagName=function(e){xe(e)||(e===62?this._state=1:(this._state=this.isTagStartChar(e)?6:20,this.sectionStart=this._index))},t.prototype.stateInClosingTagName=function(e){(e===62||xe(e))&&(this.cbs.onclosetag(this.getSection()),this.sectionStart=-1,this._state=7,this.stateAfterClosingTagName(e))},t.prototype.stateAfterClosingTagName=function(e){(e===62||this.fastForwardTo(62))&&(this._state=1,this.sectionStart=this._index+1)},t.prototype.stateBeforeAttributeName=function(e){e===62?(this.cbs.onopentagend(),this.isSpecial?(this._state=24,this.sequenceIndex=0):this._state=1,this.baseState=this._state,this.sectionStart=this._index+1):e===47?this._state=4:xe(e)||(this._state=9,this.sectionStart=this._index)},t.prototype.stateInSelfClosingTag=function(e){e===62?(this.cbs.onselfclosingtag(),this._state=1,this.baseState=1,this.sectionStart=this._index+1,this.isSpecial=!1):xe(e)||(this._state=8,this.stateBeforeAttributeName(e))},t.prototype.stateInAttributeName=function(e){(e===61||io(e))&&(this.cbs.onattribname(this.getSection()),this.sectionStart=-1,this._state=10,this.stateAfterAttributeName(e))},t.prototype.stateAfterAttributeName=function(e){e===61?this._state=11:e===47||e===62?(this.cbs.onattribend(void 0),this._state=8,this.stateBeforeAttributeName(e)):xe(e)||(this.cbs.onattribend(void 0),this._state=9,this.sectionStart=this._index)},t.prototype.stateBeforeAttributeValue=function(e){e===34?(this._state=12,this.sectionStart=this._index+1):e===39?(this._state=13,this.sectionStart=this._index+1):xe(e)||(this.sectionStart=this._index,this._state=14,this.stateInAttributeValueNoQuotes(e))},t.prototype.handleInAttributeValue=function(e,n){e===n||!this.decodeEntities&&this.fastForwardTo(n)?(this.cbs.onattribdata(this.getSection()),this.sectionStart=-1,this.cbs.onattribend(String.fromCharCode(n)),this._state=8):this.decodeEntities&&e===38&&(this.baseState=this._state,this._state=25)},t.prototype.stateInAttributeValueDoubleQuotes=function(e){this.handleInAttributeValue(e,34)},t.prototype.stateInAttributeValueSingleQuotes=function(e){this.handleInAttributeValue(e,39)},t.prototype.stateInAttributeValueNoQuotes=function(e){xe(e)||e===62?(this.cbs.onattribdata(this.getSection()),this.sectionStart=-1,this.cbs.onattribend(null),this._state=8,this.stateBeforeAttributeName(e)):this.decodeEntities&&e===38&&(this.baseState=this._state,this._state=25)},t.prototype.stateBeforeDeclaration=function(e){e===91?(this._state=19,this.sequenceIndex=0):this._state=e===45?18:16},t.prototype.stateInDeclaration=function(e){(e===62||this.fastForwardTo(62))&&(this.cbs.ondeclaration(this.getSection()),this._state=1,this.sectionStart=this._index+1)},t.prototype.stateInProcessingInstruction=function(e){(e===62||this.fastForwardTo(62))&&(this.cbs.onprocessinginstruction(this.getSection()),this._state=1,this.sectionStart=this._index+1)},t.prototype.stateBeforeComment=function(e){e===45?(this._state=21,this.currentSequence=At.CommentEnd,this.sequenceIndex=2,this.sectionStart=this._index+1):this._state=16},t.prototype.stateInSpecialComment=function(e){(e===62||this.fastForwardTo(62))&&(this.cbs.oncomment(this.getSection()),this._state=1,this.sectionStart=this._index+1)},t.prototype.stateBeforeSpecialS=function(e){var n=e|32;n===At.ScriptEnd[3]?this.startSpecial(At.ScriptEnd,4):n===At.StyleEnd[3]?this.startSpecial(At.StyleEnd,4):(this._state=3,this.stateInTagName(e))},t.prototype.stateBeforeEntity=function(e){this.entityExcess=1,e===35?this._state=26:e===38||(this.trieIndex=0,this.trieCurrent=this.entityTrie[0],this.trieResult=null,this._state=27,this.stateInNamedEntity(e))},t.prototype.stateInNamedEntity=function(e){if(this.entityExcess+=1,this.trieIndex=(0,L1.determineBranch)(this.entityTrie,this.trieCurrent,this.trieIndex+1,e),this.trieIndex<0){this.emitNamedEntity(),this._index--;return}if(this.trieCurrent=this.entityTrie[this.trieIndex],this.trieCurrent&L1.BinTrieFlags.HAS_VALUE)if(!this.allowLegacyEntity()&&e!==59)this.trieIndex+=1;else{var n=this._index-this.entityExcess+1;n>this.sectionStart&&this.emitPartial(this.buffer.substring(this.sectionStart,n)),this.trieResult=this.trieCurrent&L1.BinTrieFlags.MULTI_BYTE?String.fromCharCode(this.entityTrie[++this.trieIndex],this.entityTrie[++this.trieIndex]):String.fromCharCode(this.entityTrie[++this.trieIndex]),this.entityExcess=0,this.sectionStart=this._index+1}},t.prototype.emitNamedEntity=function(){this.trieResult&&this.emitPartial(this.trieResult),this._state=this.baseState},t.prototype.stateBeforeNumericEntity=function(e){(e|32)===120?(this.entityExcess++,this._state=29):(this._state=28,this.stateInNumericEntity(e))},t.prototype.decodeNumericEntity=function(e,n){var r=this._index-this.entityExcess-1,o=r+2+(e>>4);if(o!==this._index){r>this.sectionStart&&this.emitPartial(this.buffer.substring(this.sectionStart,r));var s=this.buffer.substring(o,this._index),i=parseInt(s,e);this.emitPartial((0,hm.default)(i)),this.sectionStart=this._index+Number(n)}this._state=this.baseState},t.prototype.stateInNumericEntity=function(e){e===59?this.decodeNumericEntity(10,!0):G0(e)?this.entityExcess++:(this.allowLegacyEntity()?this.decodeNumericEntity(10,!1):this._state=this.baseState,this._index--)},t.prototype.stateInHexEntity=function(e){e===59?this.decodeNumericEntity(16,!0):(e<97||e>102)&&(e<65||e>70)&&!G0(e)?(this.allowLegacyEntity()?this.decodeNumericEntity(16,!1):this._state=this.baseState,this._index--):this.entityExcess++},t.prototype.allowLegacyEntity=function(){return!this.xmlMode&&(this.baseState===1||this.baseState===24)},t.prototype.cleanup=function(){this.running&&this.sectionStart!==this._index&&(this._state===1||this._state===24&&this.sequenceIndex===0)&&(this.cbs.ontext(this.buffer.substr(this.sectionStart)),this.sectionStart=this._index);var e=this.sectionStart<0?this._index:this.sectionStart;this.buffer=e===this.buffer.length?"":this.buffer.substr(e),this._index-=e,this.bufferOffset+=e,this.sectionStart>0&&(this.sectionStart=0)},t.prototype.shouldContinue=function(){return this._index<this.buffer.length&&this.running},t.prototype.parse=function(){for(;this.shouldContinue();){var e=this.buffer.charCodeAt(this._index);this._state===1?this.stateText(e):this._state===23?this.stateSpecialStartSequence(e):this._state===24?this.stateInSpecialTag(e):this._state===19?this.stateCDATASequence(e):this._state===12?this.stateInAttributeValueDoubleQuotes(e):this._state===9?this.stateInAttributeName(e):this._state===21?this.stateInCommentLike(e):this._state===20?this.stateInSpecialComment(e):this._state===8?this.stateBeforeAttributeName(e):this._state===3?this.stateInTagName(e):this._state===6?this.stateInClosingTagName(e):this._state===2?this.stateBeforeTagName(e):this._state===10?this.stateAfterAttributeName(e):this._state===13?this.stateInAttributeValueSingleQuotes(e):this._state===11?this.stateBeforeAttributeValue(e):this._state===5?this.stateBeforeClosingTagName(e):this._state===7?this.stateAfterClosingTagName(e):this._state===22?this.stateBeforeSpecialS(e):this._state===14?this.stateInAttributeValueNoQuotes(e):this._state===4?this.stateInSelfClosingTag(e):this._state===16?this.stateInDeclaration(e):this._state===15?this.stateBeforeDeclaration(e):this._state===18?this.stateBeforeComment(e):this._state===17?this.stateInProcessingInstruction(e):this._state===27?this.stateInNamedEntity(e):this._state===25?this.stateBeforeEntity(e):this._state===29?this.stateInHexEntity(e):this._state===28?this.stateInNumericEntity(e):this.stateBeforeNumericEntity(e),this._index++}this.cleanup()},t.prototype.finish=function(){this._state===27&&this.emitNamedEntity(),this.sectionStart<this._index&&this.handleTrailingData(),this.cbs.onend()},t.prototype.handleTrailingData=function(){var e=this.buffer.substr(this.sectionStart);this._state===21?this.currentSequence===At.CdataEnd?this.cbs.oncdata(e):this.cbs.oncomment(e):this._state===28&&this.allowLegacyEntity()?this.decodeNumericEntity(10,!1):this._state===29&&this.allowLegacyEntity()?this.decodeNumericEntity(16,!1):this._state===3||this._state===8||this._state===11||this._state===10||this._state===9||this._state===13||this._state===12||this._state===14||this._state===6||this.cbs.ontext(e)},t.prototype.getSection=function(){return this.buffer.substring(this.sectionStart,this._index)},t.prototype.emitPartial=function(e){this.baseState!==1&&this.baseState!==24?this.cbs.onattribdata(e):this.cbs.ontext(e)},t}();Ls.default=gm;var vm=L&&L.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Rr,"__esModule",{value:!0});Rr.Parser=void 0;var ym=vm(Ls),On=new Set(["input","option","optgroup","select","button","datalist","textarea"]),z=new Set(["p"]),W0=new Set(["thead","tbody"]),Q0=new Set(["dd","dt"]),K0=new Set(["rt","rp"]),wm=new Map([["tr",new Set(["tr","th","td"])],["th",new Set(["th"])],["td",new Set(["thead","th","td"])],["body",new Set(["head","link","script"])],["li",new Set(["li"])],["p",z],["h1",z],["h2",z],["h3",z],["h4",z],["h5",z],["h6",z],["select",On],["input",On],["output",On],["button",On],["datalist",On],["textarea",On],["option",new Set(["option"])],["optgroup",new Set(["optgroup","option"])],["dd",Q0],["dt",Q0],["address",z],["article",z],["aside",z],["blockquote",z],["details",z],["div",z],["dl",z],["fieldset",z],["figcaption",z],["figure",z],["footer",z],["form",z],["header",z],["hr",z],["main",z],["nav",z],["ol",z],["pre",z],["section",z],["table",z],["ul",z],["rt",K0],["rp",K0],["tbody",W0],["tfoot",W0]]),xm=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]),Y0=new Set(["math","svg"]),X0=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignobject","desc","title"]),Sm=/\s|\//,Tm=function(){function t(e,n){n===void 0&&(n={});var r,o,s,i,a;this.options=n,this.startIndex=0,this.endIndex=0,this.openTagStart=0,this.tagname="",this.attribname="",this.attribvalue="",this.attribs=null,this.stack=[],this.foreignContext=[],this.cbs=e!=null?e:{},this.lowerCaseTagNames=(r=n.lowerCaseTags)!==null&&r!==void 0?r:!n.xmlMode,this.lowerCaseAttributeNames=(o=n.lowerCaseAttributeNames)!==null&&o!==void 0?o:!n.xmlMode,this.tokenizer=new((s=n.Tokenizer)!==null&&s!==void 0?s:ym.default)(this.options,this),(a=(i=this.cbs).onparserinit)===null||a===void 0||a.call(i,this)}return t.prototype.ontext=function(e){var n,r,o=this.tokenizer.getAbsoluteIndex();this.endIndex=o-1,(r=(n=this.cbs).ontext)===null||r===void 0||r.call(n,e),this.startIndex=o},t.prototype.isVoidElement=function(e){return!this.options.xmlMode&&xm.has(e)},t.prototype.onopentagname=function(e){this.endIndex=this.tokenizer.getAbsoluteIndex(),this.lowerCaseTagNames&&(e=e.toLowerCase()),this.emitOpenTag(e)},t.prototype.emitOpenTag=function(e){var n,r,o,s;this.openTagStart=this.startIndex,this.tagname=e;var i=!this.options.xmlMode&&wm.get(e);if(i)for(;this.stack.length>0&&i.has(this.stack[this.stack.length-1]);){var a=this.stack.pop();(r=(n=this.cbs).onclosetag)===null||r===void 0||r.call(n,a,!0)}this.isVoidElement(e)||(this.stack.push(e),Y0.has(e)?this.foreignContext.push(!0):X0.has(e)&&this.foreignContext.push(!1)),(s=(o=this.cbs).onopentagname)===null||s===void 0||s.call(o,e),this.cbs.onopentag&&(this.attribs={})},t.prototype.endOpenTag=function(e){var n,r;this.startIndex=this.openTagStart,this.endIndex=this.tokenizer.getAbsoluteIndex(),this.attribs&&((r=(n=this.cbs).onopentag)===null||r===void 0||r.call(n,this.tagname,this.attribs,e),this.attribs=null),this.cbs.onclosetag&&this.isVoidElement(this.tagname)&&this.cbs.onclosetag(this.tagname,!0),this.tagname=""},t.prototype.onopentagend=function(){this.endOpenTag(!1),this.startIndex=this.endIndex+1},t.prototype.onclosetag=function(e){var n,r,o,s,i,a;if(this.endIndex=this.tokenizer.getAbsoluteIndex(),this.lowerCaseTagNames&&(e=e.toLowerCase()),(Y0.has(e)||X0.has(e))&&this.foreignContext.pop(),this.isVoidElement(e))!this.options.xmlMode&&e==="br"&&((r=(n=this.cbs).onopentagname)===null||r===void 0||r.call(n,e),(s=(o=this.cbs).onopentag)===null||s===void 0||s.call(o,e,{},!0),(a=(i=this.cbs).onclosetag)===null||a===void 0||a.call(i,e,!1));else{var l=this.stack.lastIndexOf(e);if(l!==-1)if(this.cbs.onclosetag)for(var c=this.stack.length-l;c--;)this.cbs.onclosetag(this.stack.pop(),c!==0);else this.stack.length=l;else!this.options.xmlMode&&e==="p"&&(this.emitOpenTag(e),this.closeCurrentTag(!0))}this.startIndex=this.endIndex+1},t.prototype.onselfclosingtag=function(){this.options.xmlMode||this.options.recognizeSelfClosing||this.foreignContext[this.foreignContext.length-1]?(this.closeCurrentTag(!1),this.startIndex=this.endIndex+1):this.onopentagend()},t.prototype.closeCurrentTag=function(e){var n,r,o=this.tagname;this.endOpenTag(e),this.stack[this.stack.length-1]===o&&((r=(n=this.cbs).onclosetag)===null||r===void 0||r.call(n,o,!e),this.stack.pop())},t.prototype.onattribname=function(e){this.startIndex=this.tokenizer.getAbsoluteSectionStart(),this.lowerCaseAttributeNames&&(e=e.toLowerCase()),this.attribname=e},t.prototype.onattribdata=function(e){this.attribvalue+=e},t.prototype.onattribend=function(e){var n,r;this.endIndex=this.tokenizer.getAbsoluteIndex(),(r=(n=this.cbs).onattribute)===null||r===void 0||r.call(n,this.attribname,this.attribvalue,e),this.attribs&&!Object.prototype.hasOwnProperty.call(this.attribs,this.attribname)&&(this.attribs[this.attribname]=this.attribvalue),this.attribname="",this.attribvalue=""},t.prototype.getInstructionName=function(e){var n=e.search(Sm),r=n<0?e:e.substr(0,n);return this.lowerCaseTagNames&&(r=r.toLowerCase()),r},t.prototype.ondeclaration=function(e){if(this.endIndex=this.tokenizer.getAbsoluteIndex(),this.cbs.onprocessinginstruction){var n=this.getInstructionName(e);this.cbs.onprocessinginstruction("!"+n,"!"+e)}this.startIndex=this.endIndex+1},t.prototype.onprocessinginstruction=function(e){if(this.endIndex=this.tokenizer.getAbsoluteIndex(),this.cbs.onprocessinginstruction){var n=this.getInstructionName(e);this.cbs.onprocessinginstruction("?"+n,"?"+e)}this.startIndex=this.endIndex+1},t.prototype.oncomment=function(e){var n,r,o,s;this.endIndex=this.tokenizer.getAbsoluteIndex(),(r=(n=this.cbs).oncomment)===null||r===void 0||r.call(n,e),(s=(o=this.cbs).oncommentend)===null||s===void 0||s.call(o),this.startIndex=this.endIndex+1},t.prototype.oncdata=function(e){var n,r,o,s,i,a,l,c,u,f;this.endIndex=this.tokenizer.getAbsoluteIndex(),this.options.xmlMode||this.options.recognizeCDATA?((r=(n=this.cbs).oncdatastart)===null||r===void 0||r.call(n),(s=(o=this.cbs).ontext)===null||s===void 0||s.call(o,e),(a=(i=this.cbs).oncdataend)===null||a===void 0||a.call(i)):((c=(l=this.cbs).oncomment)===null||c===void 0||c.call(l,"[CDATA["+e+"]]"),(f=(u=this.cbs).oncommentend)===null||f===void 0||f.call(u)),this.startIndex=this.endIndex+1},t.prototype.onerror=function(e){var n,r;(r=(n=this.cbs).onerror)===null||r===void 0||r.call(n,e)},t.prototype.onend=function(){var e,n;if(this.cbs.onclosetag){this.endIndex=this.startIndex;for(var r=this.stack.length;r>0;this.cbs.onclosetag(this.stack[--r],!0));}(n=(e=this.cbs).onend)===null||n===void 0||n.call(e)},t.prototype.reset=function(){var e,n,r,o;(n=(e=this.cbs).onreset)===null||n===void 0||n.call(e),this.tokenizer.reset(),this.tagname="",this.attribname="",this.attribs=null,this.stack=[],this.startIndex=0,this.endIndex=0,(o=(r=this.cbs).onparserinit)===null||o===void 0||o.call(r,this)},t.prototype.parseComplete=function(e){this.reset(),this.end(e)},t.prototype.write=function(e){this.tokenizer.write(e)},t.prototype.end=function(e){this.tokenizer.end(e)},t.prototype.pause=function(){this.tokenizer.pause()},t.prototype.resume=function(){this.tokenizer.resume()},t.prototype.parseChunk=function(e){this.write(e)},t.prototype.done=function(e){this.end(e)},t}();Rr.Parser=Tm;var Re={},mc={};(function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Doctype=t.CDATA=t.Tag=t.Style=t.Script=t.Comment=t.Directive=t.Text=t.Root=t.isTag=t.ElementType=void 0;var e;(function(r){r.Root="root",r.Text="text",r.Directive="directive",r.Comment="comment",r.Script="script",r.Style="style",r.Tag="tag",r.CDATA="cdata",r.Doctype="doctype"})(e=t.ElementType||(t.ElementType={}));function n(r){return r.type===e.Tag||r.type===e.Script||r.type===e.Style}t.isTag=n,t.Root=e.Root,t.Text=e.Text,t.Directive=e.Directive,t.Comment=e.Comment,t.Script=e.Script,t.Style=e.Style,t.Tag=e.Tag,t.CDATA=e.CDATA,t.Doctype=e.Doctype})(mc);var q={},Nn=L&&L.__extends||function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,o){r.__proto__=o}||function(r,o){for(var s in o)Object.prototype.hasOwnProperty.call(o,s)&&(r[s]=o[s])},t(e,n)};return function(e,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");t(e,n);function r(){this.constructor=e}e.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),tr=L&&L.__assign||function(){return tr=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++){e=arguments[n];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t},tr.apply(this,arguments)};Object.defineProperty(q,"__esModule",{value:!0});q.cloneNode=q.hasChildren=q.isDocument=q.isDirective=q.isComment=q.isText=q.isCDATA=q.isTag=q.Element=q.Document=q.NodeWithChildren=q.ProcessingInstruction=q.Comment=q.Text=q.DataNode=q.Node=void 0;var nt=mc,Em=new Map([[nt.ElementType.Tag,1],[nt.ElementType.Script,1],[nt.ElementType.Style,1],[nt.ElementType.Directive,1],[nt.ElementType.Text,3],[nt.ElementType.CDATA,4],[nt.ElementType.Comment,8],[nt.ElementType.Root,9]]),gc=function(){function t(e){this.type=e,this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}return Object.defineProperty(t.prototype,"nodeType",{get:function(){var e;return(e=Em.get(this.type))!==null&&e!==void 0?e:1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"parentNode",{get:function(){return this.parent},set:function(e){this.parent=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"previousSibling",{get:function(){return this.prev},set:function(e){this.prev=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"nextSibling",{get:function(){return this.next},set:function(e){this.next=e},enumerable:!1,configurable:!0}),t.prototype.cloneNode=function(e){return e===void 0&&(e=!1),vc(this,e)},t}();q.Node=gc;var Rs=function(t){Nn(e,t);function e(n,r){var o=t.call(this,n)||this;return o.data=r,o}return Object.defineProperty(e.prototype,"nodeValue",{get:function(){return this.data},set:function(n){this.data=n},enumerable:!1,configurable:!0}),e}(gc);q.DataNode=Rs;var t5=function(t){Nn(e,t);function e(n){return t.call(this,nt.ElementType.Text,n)||this}return e}(Rs);q.Text=t5;var e5=function(t){Nn(e,t);function e(n){return t.call(this,nt.ElementType.Comment,n)||this}return e}(Rs);q.Comment=e5;var n5=function(t){Nn(e,t);function e(n,r){var o=t.call(this,nt.ElementType.Directive,r)||this;return o.name=n,o}return e}(Rs);q.ProcessingInstruction=n5;var Ms=function(t){Nn(e,t);function e(n,r){var o=t.call(this,n)||this;return o.children=r,o}return Object.defineProperty(e.prototype,"firstChild",{get:function(){var n;return(n=this.children[0])!==null&&n!==void 0?n:null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"lastChild",{get:function(){return this.children.length>0?this.children[this.children.length-1]:null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"childNodes",{get:function(){return this.children},set:function(n){this.children=n},enumerable:!1,configurable:!0}),e}(gc);q.NodeWithChildren=Ms;var r5=function(t){Nn(e,t);function e(n){return t.call(this,nt.ElementType.Root,n)||this}return e}(Ms);q.Document=r5;var o5=function(t){Nn(e,t);function e(n,r,o,s){o===void 0&&(o=[]),s===void 0&&(s=n==="script"?nt.ElementType.Script:n==="style"?nt.ElementType.Style:nt.ElementType.Tag);var i=t.call(this,s,o)||this;return i.name=n,i.attribs=r,i}return Object.defineProperty(e.prototype,"tagName",{get:function(){return this.name},set:function(n){this.name=n},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"attributes",{get:function(){var n=this;return Object.keys(this.attribs).map(function(r){var o,s;return{name:r,value:n.attribs[r],namespace:(o=n["x-attribsNamespace"])===null||o===void 0?void 0:o[r],prefix:(s=n["x-attribsPrefix"])===null||s===void 0?void 0:s[r]}})},enumerable:!1,configurable:!0}),e}(Ms);q.Element=o5;function s5(t){return(0,nt.isTag)(t)}q.isTag=s5;function i5(t){return t.type===nt.ElementType.CDATA}q.isCDATA=i5;function a5(t){return t.type===nt.ElementType.Text}q.isText=a5;function l5(t){return t.type===nt.ElementType.Comment}q.isComment=l5;function c5(t){return t.type===nt.ElementType.Directive}q.isDirective=c5;function u5(t){return t.type===nt.ElementType.Root}q.isDocument=u5;function Cm(t){return Object.prototype.hasOwnProperty.call(t,"children")}q.hasChildren=Cm;function vc(t,e){e===void 0&&(e=!1);var n;if(a5(t))n=new t5(t.data);else if(l5(t))n=new e5(t.data);else if(s5(t)){var r=e?_i(t.children):[],o=new o5(t.name,tr({},t.attribs),r);r.forEach(function(l){return l.parent=o}),t.namespace!=null&&(o.namespace=t.namespace),t["x-attribsNamespace"]&&(o["x-attribsNamespace"]=tr({},t["x-attribsNamespace"])),t["x-attribsPrefix"]&&(o["x-attribsPrefix"]=tr({},t["x-attribsPrefix"])),n=o}else if(i5(t)){var r=e?_i(t.children):[],s=new Ms(nt.ElementType.CDATA,r);r.forEach(function(c){return c.parent=s}),n=s}else if(u5(t)){var r=e?_i(t.children):[],i=new r5(r);r.forEach(function(c){return c.parent=i}),t["x-mode"]&&(i["x-mode"]=t["x-mode"]),n=i}else if(c5(t)){var a=new n5(t.name,t.data);t["x-name"]!=null&&(a["x-name"]=t["x-name"],a["x-publicId"]=t["x-publicId"],a["x-systemId"]=t["x-systemId"]),n=a}else throw new Error("Not implemented yet: ".concat(t.type));return n.startIndex=t.startIndex,n.endIndex=t.endIndex,t.sourceCodeLocation!=null&&(n.sourceCodeLocation=t.sourceCodeLocation),n}q.cloneNode=vc;function _i(t){for(var e=t.map(function(r){return vc(r,!0)}),n=1;n<e.length;n++)e[n].prev=e[n-1],e[n-1].next=e[n];return e}(function(t){var e=L&&L.__createBinding||(Object.create?function(l,c,u,f){f===void 0&&(f=u);var d=Object.getOwnPropertyDescriptor(c,u);(!d||("get"in d?!c.__esModule:d.writable||d.configurable))&&(d={enumerable:!0,get:function(){return c[u]}}),Object.defineProperty(l,f,d)}:function(l,c,u,f){f===void 0&&(f=u),l[f]=c[u]}),n=L&&L.__exportStar||function(l,c){for(var u in l)u!=="default"&&!Object.prototype.hasOwnProperty.call(c,u)&&e(c,l,u)};Object.defineProperty(t,"__esModule",{value:!0}),t.DomHandler=void 0;var r=mc,o=q;n(q,t);var s=/\s+/g,i={normalizeWhitespace:!1,withStartIndices:!1,withEndIndices:!1,xmlMode:!1},a=function(){function l(c,u,f){this.dom=[],this.root=new o.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,typeof u=="function"&&(f=u,u=i),typeof c=="object"&&(u=c,c=void 0),this.callback=c!=null?c:null,this.options=u!=null?u:i,this.elementCB=f!=null?f:null}return l.prototype.onparserinit=function(c){this.parser=c},l.prototype.onreset=function(){this.dom=[],this.root=new o.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null},l.prototype.onend=function(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))},l.prototype.onerror=function(c){this.handleCallback(c)},l.prototype.onclosetag=function(){this.lastNode=null;var c=this.tagStack.pop();this.options.withEndIndices&&(c.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(c)},l.prototype.onopentag=function(c,u){var f=this.options.xmlMode?r.ElementType.Tag:void 0,d=new o.Element(c,u,void 0,f);this.addNode(d),this.tagStack.push(d)},l.prototype.ontext=function(c){var u=this.options.normalizeWhitespace,f=this.lastNode;if(f&&f.type===r.ElementType.Text)u?f.data=(f.data+c).replace(s," "):f.data+=c,this.options.withEndIndices&&(f.endIndex=this.parser.endIndex);else{u&&(c=c.replace(s," "));var d=new o.Text(c);this.addNode(d),this.lastNode=d}},l.prototype.oncomment=function(c){if(this.lastNode&&this.lastNode.type===r.ElementType.Comment){this.lastNode.data+=c;return}var u=new o.Comment(c);this.addNode(u),this.lastNode=u},l.prototype.oncommentend=function(){this.lastNode=null},l.prototype.oncdatastart=function(){var c=new o.Text(""),u=new o.NodeWithChildren(r.ElementType.CDATA,[c]);this.addNode(u),c.parent=u,this.lastNode=c},l.prototype.oncdataend=function(){this.lastNode=null},l.prototype.onprocessinginstruction=function(c,u){var f=new o.ProcessingInstruction(c,u);this.addNode(f)},l.prototype.handleCallback=function(c){if(typeof this.callback=="function")this.callback(c,this.dom);else if(c)throw c},l.prototype.addNode=function(c){var u=this.tagStack[this.tagStack.length-1],f=u.children[u.children.length-1];this.options.withStartIndices&&(c.startIndex=this.parser.startIndex),this.options.withEndIndices&&(c.endIndex=this.parser.endIndex),u.children.push(c),f&&(c.prev=f,f.next=c),c.parent=u,this.lastNode=null},l}();t.DomHandler=a,t.default=a})(Re);var f5={};(function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Doctype=t.CDATA=t.Tag=t.Style=t.Script=t.Comment=t.Directive=t.Text=t.Root=t.isTag=t.ElementType=void 0;var e;(function(r){r.Root="root",r.Text="text",r.Directive="directive",r.Comment="comment",r.Script="script",r.Style="style",r.Tag="tag",r.CDATA="cdata",r.Doctype="doctype"})(e=t.ElementType||(t.ElementType={}));function n(r){return r.type===e.Tag||r.type===e.Script||r.type===e.Style}t.isTag=n,t.Root=e.Root,t.Text=e.Text,t.Directive=e.Directive,t.Comment=e.Comment,t.Script=e.Script,t.Style=e.Style,t.Tag=e.Tag,t.CDATA=e.CDATA,t.Doctype=e.Doctype})(f5);var qa={},yc={},Ft={},wc={},xc={};(function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Doctype=t.CDATA=t.Tag=t.Style=t.Script=t.Comment=t.Directive=t.Text=t.Root=t.isTag=t.ElementType=void 0;var e;(function(r){r.Root="root",r.Text="text",r.Directive="directive",r.Comment="comment",r.Script="script",r.Style="style",r.Tag="tag",r.CDATA="cdata",r.Doctype="doctype"})(e=t.ElementType||(t.ElementType={}));function n(r){return r.type===e.Tag||r.type===e.Script||r.type===e.Style}t.isTag=n,t.Root=e.Root,t.Text=e.Text,t.Directive=e.Directive,t.Comment=e.Comment,t.Script=e.Script,t.Style=e.Style,t.Tag=e.Tag,t.CDATA=e.CDATA,t.Doctype=e.Doctype})(xc);var d5={},ke={};const bm="Á",_m="á",km="Ă",Dm="ă",Am="∾",Pm="∿",Lm="∾̳",Nm="Â",Rm="â",Mm="´",Om="А",Vm="а",Im="Æ",qm="æ",jm="⁡",Fm="𝔄",Bm="𝔞",Um="À",zm="à",$m="ℵ",Hm="ℵ",Gm="Α",Wm="α",Qm="Ā",Km="ā",Ym="⨿",Xm="&",Zm="&",Jm="⩕",tg="⩓",eg="∧",ng="⩜",rg="⩘",og="⩚",sg="∠",ig="⦤",ag="∠",lg="⦨",cg="⦩",ug="⦪",fg="⦫",dg="⦬",pg="⦭",hg="⦮",mg="⦯",gg="∡",vg="∟",yg="⊾",wg="⦝",xg="∢",Sg="Å",Tg="⍼",Eg="Ą",Cg="ą",bg="𝔸",_g="𝕒",kg="⩯",Dg="≈",Ag="⩰",Pg="≊",Lg="≋",Ng="'",Rg="⁡",Mg="≈",Og="≊",Vg="Å",Ig="å",qg="𝒜",jg="𝒶",Fg="≔",Bg="*",Ug="≈",zg="≍",$g="Ã",Hg="ã",Gg="Ä",Wg="ä",Qg="∳",Kg="⨑",Yg="≌",Xg="϶",Zg="‵",Jg="∽",tv="⋍",ev="∖",nv="⫧",rv="⊽",ov="⌅",sv="⌆",iv="⌅",av="⎵",lv="⎶",cv="≌",uv="Б",fv="б",dv="„",pv="∵",hv="∵",mv="∵",gv="⦰",vv="϶",yv="ℬ",wv="ℬ",xv="Β",Sv="β",Tv="ℶ",Ev="≬",Cv="𝔅",bv="𝔟",_v="⋂",kv="◯",Dv="⋃",Av="⨀",Pv="⨁",Lv="⨂",Nv="⨆",Rv="★",Mv="▽",Ov="△",Vv="⨄",Iv="⋁",qv="⋀",jv="⤍",Fv="⧫",Bv="▪",Uv="▴",zv="▾",$v="◂",Hv="▸",Gv="␣",Wv="▒",Qv="░",Kv="▓",Yv="█",Xv="=⃥",Zv="≡⃥",Jv="⫭",ty="⌐",ey="𝔹",ny="𝕓",ry="⊥",oy="⊥",sy="⋈",iy="⧉",ay="┐",ly="╕",cy="╖",uy="╗",fy="┌",dy="╒",py="╓",hy="╔",my="─",gy="═",vy="┬",yy="╤",wy="╥",xy="╦",Sy="┴",Ty="╧",Ey="╨",Cy="╩",by="⊟",_y="⊞",ky="⊠",Dy="┘",Ay="╛",Py="╜",Ly="╝",Ny="└",Ry="╘",My="╙",Oy="╚",Vy="│",Iy="║",qy="┼",jy="╪",Fy="╫",By="╬",Uy="┤",zy="╡",$y="╢",Hy="╣",Gy="├",Wy="╞",Qy="╟",Ky="╠",Yy="‵",Xy="˘",Zy="˘",Jy="¦",tw="𝒷",ew="ℬ",nw="⁏",rw="∽",ow="⋍",sw="⧅",iw="\\",aw="⟈",lw="•",cw="•",uw="≎",fw="⪮",dw="≏",pw="≎",hw="≏",mw="Ć",gw="ć",vw="⩄",yw="⩉",ww="⩋",xw="∩",Sw="⋒",Tw="⩇",Ew="⩀",Cw="ⅅ",bw="∩︀",_w="⁁",kw="ˇ",Dw="ℭ",Aw="⩍",Pw="Č",Lw="č",Nw="Ç",Rw="ç",Mw="Ĉ",Ow="ĉ",Vw="∰",Iw="⩌",qw="⩐",jw="Ċ",Fw="ċ",Bw="¸",Uw="¸",zw="⦲",$w="¢",Hw="·",Gw="·",Ww="𝔠",Qw="ℭ",Kw="Ч",Yw="ч",Xw="✓",Zw="✓",Jw="Χ",tx="χ",ex="ˆ",nx="≗",rx="↺",ox="↻",sx="⊛",ix="⊚",ax="⊝",lx="⊙",cx="®",ux="Ⓢ",fx="⊖",dx="⊕",px="⊗",hx="○",mx="⧃",gx="≗",vx="⨐",yx="⫯",wx="⧂",xx="∲",Sx="”",Tx="’",Ex="♣",Cx="♣",bx=":",_x="∷",kx="⩴",Dx="≔",Ax="≔",Px=",",Lx="@",Nx="∁",Rx="∘",Mx="∁",Ox="ℂ",Vx="≅",Ix="⩭",qx="≡",jx="∮",Fx="∯",Bx="∮",Ux="𝕔",zx="ℂ",$x="∐",Hx="∐",Gx="©",Wx="©",Qx="℗",Kx="∳",Yx="↵",Xx="✗",Zx="⨯",Jx="𝒞",tS="𝒸",eS="⫏",nS="⫑",rS="⫐",oS="⫒",sS="⋯",iS="⤸",aS="⤵",lS="⋞",cS="⋟",uS="↶",fS="⤽",dS="⩈",pS="⩆",hS="≍",mS="∪",gS="⋓",vS="⩊",yS="⊍",wS="⩅",xS="∪︀",SS="↷",TS="⤼",ES="⋞",CS="⋟",bS="⋎",_S="⋏",kS="¤",DS="↶",AS="↷",PS="⋎",LS="⋏",NS="∲",RS="∱",MS="⌭",OS="†",VS="‡",IS="ℸ",qS="↓",jS="↡",FS="⇓",BS="‐",US="⫤",zS="⊣",$S="⤏",HS="˝",GS="Ď",WS="ď",QS="Д",KS="д",YS="‡",XS="⇊",ZS="ⅅ",JS="ⅆ",tT="⤑",eT="⩷",nT="°",rT="∇",oT="Δ",sT="δ",iT="⦱",aT="⥿",lT="𝔇",cT="𝔡",uT="⥥",fT="⇃",dT="⇂",pT="´",hT="˙",mT="˝",gT="`",vT="˜",yT="⋄",wT="⋄",xT="⋄",ST="♦",TT="♦",ET="¨",CT="ⅆ",bT="ϝ",_T="⋲",kT="÷",DT="÷",AT="⋇",PT="⋇",LT="Ђ",NT="ђ",RT="⌞",MT="⌍",OT="$",VT="𝔻",IT="𝕕",qT="¨",jT="˙",FT="⃜",BT="≐",UT="≑",zT="≐",$T="∸",HT="∔",GT="⊡",WT="⌆",QT="∯",KT="¨",YT="⇓",XT="⇐",ZT="⇔",JT="⫤",tE="⟸",eE="⟺",nE="⟹",rE="⇒",oE="⊨",sE="⇑",iE="⇕",aE="∥",lE="⤓",cE="↓",uE="↓",fE="⇓",dE="⇵",pE="̑",hE="⇊",mE="⇃",gE="⇂",vE="⥐",yE="⥞",wE="⥖",xE="↽",SE="⥟",TE="⥗",EE="⇁",CE="↧",bE="⊤",_E="⤐",kE="⌟",DE="⌌",AE="𝒟",PE="𝒹",LE="Ѕ",NE="ѕ",RE="⧶",ME="Đ",OE="đ",VE="⋱",IE="▿",qE="▾",jE="⇵",FE="⥯",BE="⦦",UE="Џ",zE="џ",$E="⟿",HE="É",GE="é",WE="⩮",QE="Ě",KE="ě",YE="Ê",XE="ê",ZE="≖",JE="≕",tC="Э",eC="э",nC="⩷",rC="Ė",oC="ė",sC="≑",iC="ⅇ",aC="≒",lC="𝔈",cC="𝔢",uC="⪚",fC="È",dC="è",pC="⪖",hC="⪘",mC="⪙",gC="∈",vC="⏧",yC="ℓ",wC="⪕",xC="⪗",SC="Ē",TC="ē",EC="∅",CC="∅",bC="◻",_C="∅",kC="▫",DC=" ",AC=" ",PC=" ",LC="Ŋ",NC="ŋ",RC=" ",MC="Ę",OC="ę",VC="𝔼",IC="𝕖",qC="⋕",jC="⧣",FC="⩱",BC="ε",UC="Ε",zC="ε",$C="ϵ",HC="≖",GC="≕",WC="≂",QC="⪖",KC="⪕",YC="⩵",XC="=",ZC="≂",JC="≟",tb="⇌",eb="≡",nb="⩸",rb="⧥",ob="⥱",sb="≓",ib="ℯ",ab="ℰ",lb="≐",cb="⩳",ub="≂",fb="Η",db="η",pb="Ð",hb="ð",mb="Ë",gb="ë",vb="€",yb="!",wb="∃",xb="∃",Sb="ℰ",Tb="ⅇ",Eb="ⅇ",Cb="≒",bb="Ф",_b="ф",kb="♀",Db="ﬃ",Ab="ﬀ",Pb="ﬄ",Lb="𝔉",Nb="𝔣",Rb="ﬁ",Mb="◼",Ob="▪",Vb="fj",Ib="♭",qb="ﬂ",jb="▱",Fb="ƒ",Bb="𝔽",Ub="𝕗",zb="∀",$b="∀",Hb="⋔",Gb="⫙",Wb="ℱ",Qb="⨍",Kb="½",Yb="⅓",Xb="¼",Zb="⅕",Jb="⅙",t_="⅛",e_="⅔",n_="⅖",r_="¾",o_="⅗",s_="⅜",i_="⅘",a_="⅚",l_="⅝",c_="⅞",u_="⁄",f_="⌢",d_="𝒻",p_="ℱ",h_="ǵ",m_="Γ",g_="γ",v_="Ϝ",y_="ϝ",w_="⪆",x_="Ğ",S_="ğ",T_="Ģ",E_="Ĝ",C_="ĝ",b_="Г",__="г",k_="Ġ",D_="ġ",A_="≥",P_="≧",L_="⪌",N_="⋛",R_="≥",M_="≧",O_="⩾",V_="⪩",I_="⩾",q_="⪀",j_="⪂",F_="⪄",B_="⋛︀",U_="⪔",z_="𝔊",$_="𝔤",H_="≫",G_="⋙",W_="⋙",Q_="ℷ",K_="Ѓ",Y_="ѓ",X_="⪥",Z_="≷",J_="⪒",tk="⪤",ek="⪊",nk="⪊",rk="⪈",ok="≩",sk="⪈",ik="≩",ak="⋧",lk="𝔾",ck="𝕘",uk="`",fk="≥",dk="⋛",pk="≧",hk="⪢",mk="≷",gk="⩾",vk="≳",yk="𝒢",wk="ℊ",xk="≳",Sk="⪎",Tk="⪐",Ek="⪧",Ck="⩺",bk=">",_k=">",kk="≫",Dk="⋗",Ak="⦕",Pk="⩼",Lk="⪆",Nk="⥸",Rk="⋗",Mk="⋛",Ok="⪌",Vk="≷",Ik="≳",qk="≩︀",jk="≩︀",Fk="ˇ",Bk=" ",Uk="½",zk="ℋ",$k="Ъ",Hk="ъ",Gk="⥈",Wk="↔",Qk="⇔",Kk="↭",Yk="^",Xk="ℏ",Zk="Ĥ",Jk="ĥ",tD="♥",eD="♥",nD="…",rD="⊹",oD="𝔥",sD="ℌ",iD="ℋ",aD="⤥",lD="⤦",cD="⇿",uD="∻",fD="↩",dD="↪",pD="𝕙",hD="ℍ",mD="―",gD="─",vD="𝒽",yD="ℋ",wD="ℏ",xD="Ħ",SD="ħ",TD="≎",ED="≏",CD="⁃",bD="‐",_D="Í",kD="í",DD="⁣",AD="Î",PD="î",LD="И",ND="и",RD="İ",MD="Е",OD="е",VD="¡",ID="⇔",qD="𝔦",jD="ℑ",FD="Ì",BD="ì",UD="ⅈ",zD="⨌",$D="∭",HD="⧜",GD="℩",WD="Ĳ",QD="ĳ",KD="Ī",YD="ī",XD="ℑ",ZD="ⅈ",JD="ℐ",tA="ℑ",eA="ı",nA="ℑ",rA="⊷",oA="Ƶ",sA="⇒",iA="℅",aA="∞",lA="⧝",cA="ı",uA="⊺",fA="∫",dA="∬",pA="ℤ",hA="∫",mA="⊺",gA="⋂",vA="⨗",yA="⨼",wA="⁣",xA="⁢",SA="Ё",TA="ё",EA="Į",CA="į",bA="𝕀",_A="𝕚",kA="Ι",DA="ι",AA="⨼",PA="¿",LA="𝒾",NA="ℐ",RA="∈",MA="⋵",OA="⋹",VA="⋴",IA="⋳",qA="∈",jA="⁢",FA="Ĩ",BA="ĩ",UA="І",zA="і",$A="Ï",HA="ï",GA="Ĵ",WA="ĵ",QA="Й",KA="й",YA="𝔍",XA="𝔧",ZA="ȷ",JA="𝕁",tP="𝕛",eP="𝒥",nP="𝒿",rP="Ј",oP="ј",sP="Є",iP="є",aP="Κ",lP="κ",cP="ϰ",uP="Ķ",fP="ķ",dP="К",pP="к",hP="𝔎",mP="𝔨",gP="ĸ",vP="Х",yP="х",wP="Ќ",xP="ќ",SP="𝕂",TP="𝕜",EP="𝒦",CP="𝓀",bP="⇚",_P="Ĺ",kP="ĺ",DP="⦴",AP="ℒ",PP="Λ",LP="λ",NP="⟨",RP="⟪",MP="⦑",OP="⟨",VP="⪅",IP="ℒ",qP="«",jP="⇤",FP="⤟",BP="←",UP="↞",zP="⇐",$P="⤝",HP="↩",GP="↫",WP="⤹",QP="⥳",KP="↢",YP="⤙",XP="⤛",ZP="⪫",JP="⪭",tL="⪭︀",eL="⤌",nL="⤎",rL="❲",oL="{",sL="[",iL="⦋",aL="⦏",lL="⦍",cL="Ľ",uL="ľ",fL="Ļ",dL="ļ",pL="⌈",hL="{",mL="Л",gL="л",vL="⤶",yL="“",wL="„",xL="⥧",SL="⥋",TL="↲",EL="≤",CL="≦",bL="⟨",_L="⇤",kL="←",DL="←",AL="⇐",PL="⇆",LL="↢",NL="⌈",RL="⟦",ML="⥡",OL="⥙",VL="⇃",IL="⌊",qL="↽",jL="↼",FL="⇇",BL="↔",UL="↔",zL="⇔",$L="⇆",HL="⇋",GL="↭",WL="⥎",QL="↤",KL="⊣",YL="⥚",XL="⋋",ZL="⧏",JL="⊲",tN="⊴",eN="⥑",nN="⥠",rN="⥘",oN="↿",sN="⥒",iN="↼",aN="⪋",lN="⋚",cN="≤",uN="≦",fN="⩽",dN="⪨",pN="⩽",hN="⩿",mN="⪁",gN="⪃",vN="⋚︀",yN="⪓",wN="⪅",xN="⋖",SN="⋚",TN="⪋",EN="⋚",CN="≦",bN="≶",_N="≶",kN="⪡",DN="≲",AN="⩽",PN="≲",LN="⥼",NN="⌊",RN="𝔏",MN="𝔩",ON="≶",VN="⪑",IN="⥢",qN="↽",jN="↼",FN="⥪",BN="▄",UN="Љ",zN="љ",$N="⇇",HN="≪",GN="⋘",WN="⌞",QN="⇚",KN="⥫",YN="◺",XN="Ŀ",ZN="ŀ",JN="⎰",tR="⎰",eR="⪉",nR="⪉",rR="⪇",oR="≨",sR="⪇",iR="≨",aR="⋦",lR="⟬",cR="⇽",uR="⟦",fR="⟵",dR="⟵",pR="⟸",hR="⟷",mR="⟷",gR="⟺",vR="⟼",yR="⟶",wR="⟶",xR="⟹",SR="↫",TR="↬",ER="⦅",CR="𝕃",bR="𝕝",_R="⨭",kR="⨴",DR="∗",AR="_",PR="↙",LR="↘",NR="◊",RR="◊",MR="⧫",OR="(",VR="⦓",IR="⇆",qR="⌟",jR="⇋",FR="⥭",BR="‎",UR="⊿",zR="‹",$R="𝓁",HR="ℒ",GR="↰",WR="↰",QR="≲",KR="⪍",YR="⪏",XR="[",ZR="‘",JR="‚",tM="Ł",eM="ł",nM="⪦",rM="⩹",oM="<",sM="<",iM="≪",aM="⋖",lM="⋋",cM="⋉",uM="⥶",fM="⩻",dM="◃",pM="⊴",hM="◂",mM="⦖",gM="⥊",vM="⥦",yM="≨︀",wM="≨︀",xM="¯",SM="♂",TM="✠",EM="✠",CM="↦",bM="↦",_M="↧",kM="↤",DM="↥",AM="▮",PM="⨩",LM="М",NM="м",RM="—",MM="∺",OM="∡",VM=" ",IM="ℳ",qM="𝔐",jM="𝔪",FM="℧",BM="µ",UM="*",zM="⫰",$M="∣",HM="·",GM="⊟",WM="−",QM="∸",KM="⨪",YM="∓",XM="⫛",ZM="…",JM="∓",tO="⊧",eO="𝕄",nO="𝕞",rO="∓",oO="𝓂",sO="ℳ",iO="∾",aO="Μ",lO="μ",cO="⊸",uO="⊸",fO="∇",dO="Ń",pO="ń",hO="∠⃒",mO="≉",gO="⩰̸",vO="≋̸",yO="ŉ",wO="≉",xO="♮",SO="ℕ",TO="♮",EO=" ",CO="≎̸",bO="≏̸",_O="⩃",kO="Ň",DO="ň",AO="Ņ",PO="ņ",LO="≇",NO="⩭̸",RO="⩂",MO="Н",OO="н",VO="–",IO="⤤",qO="↗",jO="⇗",FO="↗",BO="≠",UO="≐̸",zO="​",$O="​",HO="​",GO="​",WO="≢",QO="⤨",KO="≂̸",YO="≫",XO="≪",ZO=`
`,JO="∄",tV="∄",eV="𝔑",nV="𝔫",rV="≧̸",oV="≱",sV="≱",iV="≧̸",aV="⩾̸",lV="⩾̸",cV="⋙̸",uV="≵",fV="≫⃒",dV="≯",pV="≯",hV="≫̸",mV="↮",gV="⇎",vV="⫲",yV="∋",wV="⋼",xV="⋺",SV="∋",TV="Њ",EV="њ",CV="↚",bV="⇍",_V="‥",kV="≦̸",DV="≰",AV="↚",PV="⇍",LV="↮",NV="⇎",RV="≰",MV="≦̸",OV="⩽̸",VV="⩽̸",IV="≮",qV="⋘̸",jV="≴",FV="≪⃒",BV="≮",UV="⋪",zV="⋬",$V="≪̸",HV="∤",GV="⁠",WV=" ",QV="𝕟",KV="ℕ",YV="⫬",XV="¬",ZV="≢",JV="≭",tI="∦",eI="∉",nI="≠",rI="≂̸",oI="∄",sI="≯",iI="≱",aI="≧̸",lI="≫̸",cI="≹",uI="⩾̸",fI="≵",dI="≎̸",pI="≏̸",hI="∉",mI="⋵̸",gI="⋹̸",vI="∉",yI="⋷",wI="⋶",xI="⧏̸",SI="⋪",TI="⋬",EI="≮",CI="≰",bI="≸",_I="≪̸",kI="⩽̸",DI="≴",AI="⪢̸",PI="⪡̸",LI="∌",NI="∌",RI="⋾",MI="⋽",OI="⊀",VI="⪯̸",II="⋠",qI="∌",jI="⧐̸",FI="⋫",BI="⋭",UI="⊏̸",zI="⋢",$I="⊐̸",HI="⋣",GI="⊂⃒",WI="⊈",QI="⊁",KI="⪰̸",YI="⋡",XI="≿̸",ZI="⊃⃒",JI="⊉",tq="≁",eq="≄",nq="≇",rq="≉",oq="∤",sq="∦",iq="∦",aq="⫽⃥",lq="∂̸",cq="⨔",uq="⊀",fq="⋠",dq="⊀",pq="⪯̸",hq="⪯̸",mq="⤳̸",gq="↛",vq="⇏",yq="↝̸",wq="↛",xq="⇏",Sq="⋫",Tq="⋭",Eq="⊁",Cq="⋡",bq="⪰̸",_q="𝒩",kq="𝓃",Dq="∤",Aq="∦",Pq="≁",Lq="≄",Nq="≄",Rq="∤",Mq="∦",Oq="⋢",Vq="⋣",Iq="⊄",qq="⫅̸",jq="⊈",Fq="⊂⃒",Bq="⊈",Uq="⫅̸",zq="⊁",$q="⪰̸",Hq="⊅",Gq="⫆̸",Wq="⊉",Qq="⊃⃒",Kq="⊉",Yq="⫆̸",Xq="≹",Zq="Ñ",Jq="ñ",tj="≸",ej="⋪",nj="⋬",rj="⋫",oj="⋭",sj="Ν",ij="ν",aj="#",lj="№",cj=" ",uj="≍⃒",fj="⊬",dj="⊭",pj="⊮",hj="⊯",mj="≥⃒",gj=">⃒",vj="⤄",yj="⧞",wj="⤂",xj="≤⃒",Sj="<⃒",Tj="⊴⃒",Ej="⤃",Cj="⊵⃒",bj="∼⃒",_j="⤣",kj="↖",Dj="⇖",Aj="↖",Pj="⤧",Lj="Ó",Nj="ó",Rj="⊛",Mj="Ô",Oj="ô",Vj="⊚",Ij="О",qj="о",jj="⊝",Fj="Ő",Bj="ő",Uj="⨸",zj="⊙",$j="⦼",Hj="Œ",Gj="œ",Wj="⦿",Qj="𝔒",Kj="𝔬",Yj="˛",Xj="Ò",Zj="ò",Jj="⧁",tF="⦵",eF="Ω",nF="∮",rF="↺",oF="⦾",sF="⦻",iF="‾",aF="⧀",lF="Ō",cF="ō",uF="Ω",fF="ω",dF="Ο",pF="ο",hF="⦶",mF="⊖",gF="𝕆",vF="𝕠",yF="⦷",wF="“",xF="‘",SF="⦹",TF="⊕",EF="↻",CF="⩔",bF="∨",_F="⩝",kF="ℴ",DF="ℴ",AF="ª",PF="º",LF="⊶",NF="⩖",RF="⩗",MF="⩛",OF="Ⓢ",VF="𝒪",IF="ℴ",qF="Ø",jF="ø",FF="⊘",BF="Õ",UF="õ",zF="⨶",$F="⨷",HF="⊗",GF="Ö",WF="ö",QF="⌽",KF="‾",YF="⏞",XF="⎴",ZF="⏜",JF="¶",tB="∥",eB="∥",nB="⫳",rB="⫽",oB="∂",sB="∂",iB="П",aB="п",lB="%",cB=".",uB="‰",fB="⊥",dB="‱",pB="𝔓",hB="𝔭",mB="Φ",gB="φ",vB="ϕ",yB="ℳ",wB="☎",xB="Π",SB="π",TB="⋔",EB="ϖ",CB="ℏ",bB="ℎ",_B="ℏ",kB="⨣",DB="⊞",AB="⨢",PB="+",LB="∔",NB="⨥",RB="⩲",MB="±",OB="±",VB="⨦",IB="⨧",qB="±",jB="ℌ",FB="⨕",BB="𝕡",UB="ℙ",zB="£",$B="⪷",HB="⪻",GB="≺",WB="≼",QB="⪷",KB="≺",YB="≼",XB="≺",ZB="⪯",JB="≼",tU="≾",eU="⪯",nU="⪹",rU="⪵",oU="⋨",sU="⪯",iU="⪳",aU="≾",lU="′",cU="″",uU="ℙ",fU="⪹",dU="⪵",pU="⋨",hU="∏",mU="∏",gU="⌮",vU="⌒",yU="⌓",wU="∝",xU="∝",SU="∷",TU="∝",EU="≾",CU="⊰",bU="𝒫",_U="𝓅",kU="Ψ",DU="ψ",AU=" ",PU="𝔔",LU="𝔮",NU="⨌",RU="𝕢",MU="ℚ",OU="⁗",VU="𝒬",IU="𝓆",qU="ℍ",jU="⨖",FU="?",BU="≟",UU='"',zU='"',$U="⇛",HU="∽̱",GU="Ŕ",WU="ŕ",QU="√",KU="⦳",YU="⟩",XU="⟫",ZU="⦒",JU="⦥",tz="⟩",ez="»",nz="⥵",rz="⇥",oz="⤠",sz="⤳",iz="→",az="↠",lz="⇒",cz="⤞",uz="↪",fz="↬",dz="⥅",pz="⥴",hz="⤖",mz="↣",gz="↝",vz="⤚",yz="⤜",wz="∶",xz="ℚ",Sz="⤍",Tz="⤏",Ez="⤐",Cz="❳",bz="}",_z="]",kz="⦌",Dz="⦎",Az="⦐",Pz="Ř",Lz="ř",Nz="Ŗ",Rz="ŗ",Mz="⌉",Oz="}",Vz="Р",Iz="р",qz="⤷",jz="⥩",Fz="”",Bz="”",Uz="↳",zz="ℜ",$z="ℛ",Hz="ℜ",Gz="ℝ",Wz="ℜ",Qz="▭",Kz="®",Yz="®",Xz="∋",Zz="⇋",Jz="⥯",t$="⥽",e$="⌋",n$="𝔯",r$="ℜ",o$="⥤",s$="⇁",i$="⇀",a$="⥬",l$="Ρ",c$="ρ",u$="ϱ",f$="⟩",d$="⇥",p$="→",h$="→",m$="⇒",g$="⇄",v$="↣",y$="⌉",w$="⟧",x$="⥝",S$="⥕",T$="⇂",E$="⌋",C$="⇁",b$="⇀",_$="⇄",k$="⇌",D$="⇉",A$="↝",P$="↦",L$="⊢",N$="⥛",R$="⋌",M$="⧐",O$="⊳",V$="⊵",I$="⥏",q$="⥜",j$="⥔",F$="↾",B$="⥓",U$="⇀",z$="˚",$$="≓",H$="⇄",G$="⇌",W$="‏",Q$="⎱",K$="⎱",Y$="⫮",X$="⟭",Z$="⇾",J$="⟧",tH="⦆",eH="𝕣",nH="ℝ",rH="⨮",oH="⨵",sH="⥰",iH=")",aH="⦔",lH="⨒",cH="⇉",uH="⇛",fH="›",dH="𝓇",pH="ℛ",hH="↱",mH="↱",gH="]",vH="’",yH="’",wH="⋌",xH="⋊",SH="▹",TH="⊵",EH="▸",CH="⧎",bH="⧴",_H="⥨",kH="℞",DH="Ś",AH="ś",PH="‚",LH="⪸",NH="Š",RH="š",MH="⪼",OH="≻",VH="≽",IH="⪰",qH="⪴",jH="Ş",FH="ş",BH="Ŝ",UH="ŝ",zH="⪺",$H="⪶",HH="⋩",GH="⨓",WH="≿",QH="С",KH="с",YH="⊡",XH="⋅",ZH="⩦",JH="⤥",tG="↘",eG="⇘",nG="↘",rG="§",oG=";",sG="⤩",iG="∖",aG="∖",lG="✶",cG="𝔖",uG="𝔰",fG="⌢",dG="♯",pG="Щ",hG="щ",mG="Ш",gG="ш",vG="↓",yG="←",wG="∣",xG="∥",SG="→",TG="↑",EG="­",CG="Σ",bG="σ",_G="ς",kG="ς",DG="∼",AG="⩪",PG="≃",LG="≃",NG="⪞",RG="⪠",MG="⪝",OG="⪟",VG="≆",IG="⨤",qG="⥲",jG="←",FG="∘",BG="∖",UG="⨳",zG="⧤",$G="∣",HG="⌣",GG="⪪",WG="⪬",QG="⪬︀",KG="Ь",YG="ь",XG="⌿",ZG="⧄",JG="/",tW="𝕊",eW="𝕤",nW="♠",rW="♠",oW="∥",sW="⊓",iW="⊓︀",aW="⊔",lW="⊔︀",cW="√",uW="⊏",fW="⊑",dW="⊏",pW="⊑",hW="⊐",mW="⊒",gW="⊐",vW="⊒",yW="□",wW="□",xW="⊓",SW="⊏",TW="⊑",EW="⊐",CW="⊒",bW="⊔",_W="▪",kW="□",DW="▪",AW="→",PW="𝒮",LW="𝓈",NW="∖",RW="⌣",MW="⋆",OW="⋆",VW="☆",IW="★",qW="ϵ",jW="ϕ",FW="¯",BW="⊂",UW="⋐",zW="⪽",$W="⫅",HW="⊆",GW="⫃",WW="⫁",QW="⫋",KW="⊊",YW="⪿",XW="⥹",ZW="⊂",JW="⋐",tQ="⊆",eQ="⫅",nQ="⊆",rQ="⊊",oQ="⫋",sQ="⫇",iQ="⫕",aQ="⫓",lQ="⪸",cQ="≻",uQ="≽",fQ="≻",dQ="⪰",pQ="≽",hQ="≿",mQ="⪰",gQ="⪺",vQ="⪶",yQ="⋩",wQ="≿",xQ="∋",SQ="∑",TQ="∑",EQ="♪",CQ="¹",bQ="²",_Q="³",kQ="⊃",DQ="⋑",AQ="⪾",PQ="⫘",LQ="⫆",NQ="⊇",RQ="⫄",MQ="⊃",OQ="⊇",VQ="⟉",IQ="⫗",qQ="⥻",jQ="⫂",FQ="⫌",BQ="⊋",UQ="⫀",zQ="⊃",$Q="⋑",HQ="⊇",GQ="⫆",WQ="⊋",QQ="⫌",KQ="⫈",YQ="⫔",XQ="⫖",ZQ="⤦",JQ="↙",tK="⇙",eK="↙",nK="⤪",rK="ß",oK="	",sK="⌖",iK="Τ",aK="τ",lK="⎴",cK="Ť",uK="ť",fK="Ţ",dK="ţ",pK="Т",hK="т",mK="⃛",gK="⌕",vK="𝔗",yK="𝔱",wK="∴",xK="∴",SK="∴",TK="Θ",EK="θ",CK="ϑ",bK="ϑ",_K="≈",kK="∼",DK="  ",AK=" ",PK=" ",LK="≈",NK="∼",RK="Þ",MK="þ",OK="˜",VK="∼",IK="≃",qK="≅",jK="≈",FK="⨱",BK="⊠",UK="×",zK="⨰",$K="∭",HK="⤨",GK="⌶",WK="⫱",QK="⊤",KK="𝕋",YK="𝕥",XK="⫚",ZK="⤩",JK="‴",tY="™",eY="™",nY="▵",rY="▿",oY="◃",sY="⊴",iY="≜",aY="▹",lY="⊵",cY="◬",uY="≜",fY="⨺",dY="⃛",pY="⨹",hY="⧍",mY="⨻",gY="⏢",vY="𝒯",yY="𝓉",wY="Ц",xY="ц",SY="Ћ",TY="ћ",EY="Ŧ",CY="ŧ",bY="≬",_Y="↞",kY="↠",DY="Ú",AY="ú",PY="↑",LY="↟",NY="⇑",RY="⥉",MY="Ў",OY="ў",VY="Ŭ",IY="ŭ",qY="Û",jY="û",FY="У",BY="у",UY="⇅",zY="Ű",$Y="ű",HY="⥮",GY="⥾",WY="𝔘",QY="𝔲",KY="Ù",YY="ù",XY="⥣",ZY="↿",JY="↾",tX="▀",eX="⌜",nX="⌜",rX="⌏",oX="◸",sX="Ū",iX="ū",aX="¨",lX="_",cX="⏟",uX="⎵",fX="⏝",dX="⋃",pX="⊎",hX="Ų",mX="ų",gX="𝕌",vX="𝕦",yX="⤒",wX="↑",xX="↑",SX="⇑",TX="⇅",EX="↕",CX="↕",bX="⇕",_X="⥮",kX="↿",DX="↾",AX="⊎",PX="↖",LX="↗",NX="υ",RX="ϒ",MX="ϒ",OX="Υ",VX="υ",IX="↥",qX="⊥",jX="⇈",FX="⌝",BX="⌝",UX="⌎",zX="Ů",$X="ů",HX="◹",GX="𝒰",WX="𝓊",QX="⋰",KX="Ũ",YX="ũ",XX="▵",ZX="▴",JX="⇈",tZ="Ü",eZ="ü",nZ="⦧",rZ="⦜",oZ="ϵ",sZ="ϰ",iZ="∅",aZ="ϕ",lZ="ϖ",cZ="∝",uZ="↕",fZ="⇕",dZ="ϱ",pZ="ς",hZ="⊊︀",mZ="⫋︀",gZ="⊋︀",vZ="⫌︀",yZ="ϑ",wZ="⊲",xZ="⊳",SZ="⫨",TZ="⫫",EZ="⫩",CZ="В",bZ="в",_Z="⊢",kZ="⊨",DZ="⊩",AZ="⊫",PZ="⫦",LZ="⊻",NZ="∨",RZ="⋁",MZ="≚",OZ="⋮",VZ="|",IZ="‖",qZ="|",jZ="‖",FZ="∣",BZ="|",UZ="❘",zZ="≀",$Z=" ",HZ="𝔙",GZ="𝔳",WZ="⊲",QZ="⊂⃒",KZ="⊃⃒",YZ="𝕍",XZ="𝕧",ZZ="∝",JZ="⊳",tJ="𝒱",eJ="𝓋",nJ="⫋︀",rJ="⊊︀",oJ="⫌︀",sJ="⊋︀",iJ="⊪",aJ="⦚",lJ="Ŵ",cJ="ŵ",uJ="⩟",fJ="∧",dJ="⋀",pJ="≙",hJ="℘",mJ="𝔚",gJ="𝔴",vJ="𝕎",yJ="𝕨",wJ="℘",xJ="≀",SJ="≀",TJ="𝒲",EJ="𝓌",CJ="⋂",bJ="◯",_J="⋃",kJ="▽",DJ="𝔛",AJ="𝔵",PJ="⟷",LJ="⟺",NJ="Ξ",RJ="ξ",MJ="⟵",OJ="⟸",VJ="⟼",IJ="⋻",qJ="⨀",jJ="𝕏",FJ="𝕩",BJ="⨁",UJ="⨂",zJ="⟶",$J="⟹",HJ="𝒳",GJ="𝓍",WJ="⨆",QJ="⨄",KJ="△",YJ="⋁",XJ="⋀",ZJ="Ý",JJ="ý",ttt="Я",ett="я",ntt="Ŷ",rtt="ŷ",ott="Ы",stt="ы",itt="¥",att="𝔜",ltt="𝔶",ctt="Ї",utt="ї",ftt="𝕐",dtt="𝕪",ptt="𝒴",htt="𝓎",mtt="Ю",gtt="ю",vtt="ÿ",ytt="Ÿ",wtt="Ź",xtt="ź",Stt="Ž",Ttt="ž",Ett="З",Ctt="з",btt="Ż",_tt="ż",ktt="ℨ",Dtt="​",Att="Ζ",Ptt="ζ",Ltt="𝔷",Ntt="ℨ",Rtt="Ж",Mtt="ж",Ott="⇝",Vtt="𝕫",Itt="ℤ",qtt="𝒵",jtt="𝓏",Ftt="‍",Btt="‌",p5={Aacute:bm,aacute:_m,Abreve:km,abreve:Dm,ac:Am,acd:Pm,acE:Lm,Acirc:Nm,acirc:Rm,acute:Mm,Acy:Om,acy:Vm,AElig:Im,aelig:qm,af:jm,Afr:Fm,afr:Bm,Agrave:Um,agrave:zm,alefsym:$m,aleph:Hm,Alpha:Gm,alpha:Wm,Amacr:Qm,amacr:Km,amalg:Ym,amp:Xm,AMP:Zm,andand:Jm,And:tg,and:eg,andd:ng,andslope:rg,andv:og,ang:sg,ange:ig,angle:ag,angmsdaa:lg,angmsdab:cg,angmsdac:ug,angmsdad:fg,angmsdae:dg,angmsdaf:pg,angmsdag:hg,angmsdah:mg,angmsd:gg,angrt:vg,angrtvb:yg,angrtvbd:wg,angsph:xg,angst:Sg,angzarr:Tg,Aogon:Eg,aogon:Cg,Aopf:bg,aopf:_g,apacir:kg,ap:Dg,apE:Ag,ape:Pg,apid:Lg,apos:Ng,ApplyFunction:Rg,approx:Mg,approxeq:Og,Aring:Vg,aring:Ig,Ascr:qg,ascr:jg,Assign:Fg,ast:Bg,asymp:Ug,asympeq:zg,Atilde:$g,atilde:Hg,Auml:Gg,auml:Wg,awconint:Qg,awint:Kg,backcong:Yg,backepsilon:Xg,backprime:Zg,backsim:Jg,backsimeq:tv,Backslash:ev,Barv:nv,barvee:rv,barwed:ov,Barwed:sv,barwedge:iv,bbrk:av,bbrktbrk:lv,bcong:cv,Bcy:uv,bcy:fv,bdquo:dv,becaus:pv,because:hv,Because:mv,bemptyv:gv,bepsi:vv,bernou:yv,Bernoullis:wv,Beta:xv,beta:Sv,beth:Tv,between:Ev,Bfr:Cv,bfr:bv,bigcap:_v,bigcirc:kv,bigcup:Dv,bigodot:Av,bigoplus:Pv,bigotimes:Lv,bigsqcup:Nv,bigstar:Rv,bigtriangledown:Mv,bigtriangleup:Ov,biguplus:Vv,bigvee:Iv,bigwedge:qv,bkarow:jv,blacklozenge:Fv,blacksquare:Bv,blacktriangle:Uv,blacktriangledown:zv,blacktriangleleft:$v,blacktriangleright:Hv,blank:Gv,blk12:Wv,blk14:Qv,blk34:Kv,block:Yv,bne:Xv,bnequiv:Zv,bNot:Jv,bnot:ty,Bopf:ey,bopf:ny,bot:ry,bottom:oy,bowtie:sy,boxbox:iy,boxdl:ay,boxdL:ly,boxDl:cy,boxDL:uy,boxdr:fy,boxdR:dy,boxDr:py,boxDR:hy,boxh:my,boxH:gy,boxhd:vy,boxHd:yy,boxhD:wy,boxHD:xy,boxhu:Sy,boxHu:Ty,boxhU:Ey,boxHU:Cy,boxminus:by,boxplus:_y,boxtimes:ky,boxul:Dy,boxuL:Ay,boxUl:Py,boxUL:Ly,boxur:Ny,boxuR:Ry,boxUr:My,boxUR:Oy,boxv:Vy,boxV:Iy,boxvh:qy,boxvH:jy,boxVh:Fy,boxVH:By,boxvl:Uy,boxvL:zy,boxVl:$y,boxVL:Hy,boxvr:Gy,boxvR:Wy,boxVr:Qy,boxVR:Ky,bprime:Yy,breve:Xy,Breve:Zy,brvbar:Jy,bscr:tw,Bscr:ew,bsemi:nw,bsim:rw,bsime:ow,bsolb:sw,bsol:iw,bsolhsub:aw,bull:lw,bullet:cw,bump:uw,bumpE:fw,bumpe:dw,Bumpeq:pw,bumpeq:hw,Cacute:mw,cacute:gw,capand:vw,capbrcup:yw,capcap:ww,cap:xw,Cap:Sw,capcup:Tw,capdot:Ew,CapitalDifferentialD:Cw,caps:bw,caret:_w,caron:kw,Cayleys:Dw,ccaps:Aw,Ccaron:Pw,ccaron:Lw,Ccedil:Nw,ccedil:Rw,Ccirc:Mw,ccirc:Ow,Cconint:Vw,ccups:Iw,ccupssm:qw,Cdot:jw,cdot:Fw,cedil:Bw,Cedilla:Uw,cemptyv:zw,cent:$w,centerdot:Hw,CenterDot:Gw,cfr:Ww,Cfr:Qw,CHcy:Kw,chcy:Yw,check:Xw,checkmark:Zw,Chi:Jw,chi:tx,circ:ex,circeq:nx,circlearrowleft:rx,circlearrowright:ox,circledast:sx,circledcirc:ix,circleddash:ax,CircleDot:lx,circledR:cx,circledS:ux,CircleMinus:fx,CirclePlus:dx,CircleTimes:px,cir:hx,cirE:mx,cire:gx,cirfnint:vx,cirmid:yx,cirscir:wx,ClockwiseContourIntegral:xx,CloseCurlyDoubleQuote:Sx,CloseCurlyQuote:Tx,clubs:Ex,clubsuit:Cx,colon:bx,Colon:_x,Colone:kx,colone:Dx,coloneq:Ax,comma:Px,commat:Lx,comp:Nx,compfn:Rx,complement:Mx,complexes:Ox,cong:Vx,congdot:Ix,Congruent:qx,conint:jx,Conint:Fx,ContourIntegral:Bx,copf:Ux,Copf:zx,coprod:$x,Coproduct:Hx,copy:Gx,COPY:Wx,copysr:Qx,CounterClockwiseContourIntegral:Kx,crarr:Yx,cross:Xx,Cross:Zx,Cscr:Jx,cscr:tS,csub:eS,csube:nS,csup:rS,csupe:oS,ctdot:sS,cudarrl:iS,cudarrr:aS,cuepr:lS,cuesc:cS,cularr:uS,cularrp:fS,cupbrcap:dS,cupcap:pS,CupCap:hS,cup:mS,Cup:gS,cupcup:vS,cupdot:yS,cupor:wS,cups:xS,curarr:SS,curarrm:TS,curlyeqprec:ES,curlyeqsucc:CS,curlyvee:bS,curlywedge:_S,curren:kS,curvearrowleft:DS,curvearrowright:AS,cuvee:PS,cuwed:LS,cwconint:NS,cwint:RS,cylcty:MS,dagger:OS,Dagger:VS,daleth:IS,darr:qS,Darr:jS,dArr:FS,dash:BS,Dashv:US,dashv:zS,dbkarow:$S,dblac:HS,Dcaron:GS,dcaron:WS,Dcy:QS,dcy:KS,ddagger:YS,ddarr:XS,DD:ZS,dd:JS,DDotrahd:tT,ddotseq:eT,deg:nT,Del:rT,Delta:oT,delta:sT,demptyv:iT,dfisht:aT,Dfr:lT,dfr:cT,dHar:uT,dharl:fT,dharr:dT,DiacriticalAcute:pT,DiacriticalDot:hT,DiacriticalDoubleAcute:mT,DiacriticalGrave:gT,DiacriticalTilde:vT,diam:yT,diamond:wT,Diamond:xT,diamondsuit:ST,diams:TT,die:ET,DifferentialD:CT,digamma:bT,disin:_T,div:kT,divide:DT,divideontimes:AT,divonx:PT,DJcy:LT,djcy:NT,dlcorn:RT,dlcrop:MT,dollar:OT,Dopf:VT,dopf:IT,Dot:qT,dot:jT,DotDot:FT,doteq:BT,doteqdot:UT,DotEqual:zT,dotminus:$T,dotplus:HT,dotsquare:GT,doublebarwedge:WT,DoubleContourIntegral:QT,DoubleDot:KT,DoubleDownArrow:YT,DoubleLeftArrow:XT,DoubleLeftRightArrow:ZT,DoubleLeftTee:JT,DoubleLongLeftArrow:tE,DoubleLongLeftRightArrow:eE,DoubleLongRightArrow:nE,DoubleRightArrow:rE,DoubleRightTee:oE,DoubleUpArrow:sE,DoubleUpDownArrow:iE,DoubleVerticalBar:aE,DownArrowBar:lE,downarrow:cE,DownArrow:uE,Downarrow:fE,DownArrowUpArrow:dE,DownBreve:pE,downdownarrows:hE,downharpoonleft:mE,downharpoonright:gE,DownLeftRightVector:vE,DownLeftTeeVector:yE,DownLeftVectorBar:wE,DownLeftVector:xE,DownRightTeeVector:SE,DownRightVectorBar:TE,DownRightVector:EE,DownTeeArrow:CE,DownTee:bE,drbkarow:_E,drcorn:kE,drcrop:DE,Dscr:AE,dscr:PE,DScy:LE,dscy:NE,dsol:RE,Dstrok:ME,dstrok:OE,dtdot:VE,dtri:IE,dtrif:qE,duarr:jE,duhar:FE,dwangle:BE,DZcy:UE,dzcy:zE,dzigrarr:$E,Eacute:HE,eacute:GE,easter:WE,Ecaron:QE,ecaron:KE,Ecirc:YE,ecirc:XE,ecir:ZE,ecolon:JE,Ecy:tC,ecy:eC,eDDot:nC,Edot:rC,edot:oC,eDot:sC,ee:iC,efDot:aC,Efr:lC,efr:cC,eg:uC,Egrave:fC,egrave:dC,egs:pC,egsdot:hC,el:mC,Element:gC,elinters:vC,ell:yC,els:wC,elsdot:xC,Emacr:SC,emacr:TC,empty:EC,emptyset:CC,EmptySmallSquare:bC,emptyv:_C,EmptyVerySmallSquare:kC,emsp13:DC,emsp14:AC,emsp:PC,ENG:LC,eng:NC,ensp:RC,Eogon:MC,eogon:OC,Eopf:VC,eopf:IC,epar:qC,eparsl:jC,eplus:FC,epsi:BC,Epsilon:UC,epsilon:zC,epsiv:$C,eqcirc:HC,eqcolon:GC,eqsim:WC,eqslantgtr:QC,eqslantless:KC,Equal:YC,equals:XC,EqualTilde:ZC,equest:JC,Equilibrium:tb,equiv:eb,equivDD:nb,eqvparsl:rb,erarr:ob,erDot:sb,escr:ib,Escr:ab,esdot:lb,Esim:cb,esim:ub,Eta:fb,eta:db,ETH:pb,eth:hb,Euml:mb,euml:gb,euro:vb,excl:yb,exist:wb,Exists:xb,expectation:Sb,exponentiale:Tb,ExponentialE:Eb,fallingdotseq:Cb,Fcy:bb,fcy:_b,female:kb,ffilig:Db,fflig:Ab,ffllig:Pb,Ffr:Lb,ffr:Nb,filig:Rb,FilledSmallSquare:Mb,FilledVerySmallSquare:Ob,fjlig:Vb,flat:Ib,fllig:qb,fltns:jb,fnof:Fb,Fopf:Bb,fopf:Ub,forall:zb,ForAll:$b,fork:Hb,forkv:Gb,Fouriertrf:Wb,fpartint:Qb,frac12:Kb,frac13:Yb,frac14:Xb,frac15:Zb,frac16:Jb,frac18:t_,frac23:e_,frac25:n_,frac34:r_,frac35:o_,frac38:s_,frac45:i_,frac56:a_,frac58:l_,frac78:c_,frasl:u_,frown:f_,fscr:d_,Fscr:p_,gacute:h_,Gamma:m_,gamma:g_,Gammad:v_,gammad:y_,gap:w_,Gbreve:x_,gbreve:S_,Gcedil:T_,Gcirc:E_,gcirc:C_,Gcy:b_,gcy:__,Gdot:k_,gdot:D_,ge:A_,gE:P_,gEl:L_,gel:N_,geq:R_,geqq:M_,geqslant:O_,gescc:V_,ges:I_,gesdot:q_,gesdoto:j_,gesdotol:F_,gesl:B_,gesles:U_,Gfr:z_,gfr:$_,gg:H_,Gg:G_,ggg:W_,gimel:Q_,GJcy:K_,gjcy:Y_,gla:X_,gl:Z_,glE:J_,glj:tk,gnap:ek,gnapprox:nk,gne:rk,gnE:ok,gneq:sk,gneqq:ik,gnsim:ak,Gopf:lk,gopf:ck,grave:uk,GreaterEqual:fk,GreaterEqualLess:dk,GreaterFullEqual:pk,GreaterGreater:hk,GreaterLess:mk,GreaterSlantEqual:gk,GreaterTilde:vk,Gscr:yk,gscr:wk,gsim:xk,gsime:Sk,gsiml:Tk,gtcc:Ek,gtcir:Ck,gt:bk,GT:_k,Gt:kk,gtdot:Dk,gtlPar:Ak,gtquest:Pk,gtrapprox:Lk,gtrarr:Nk,gtrdot:Rk,gtreqless:Mk,gtreqqless:Ok,gtrless:Vk,gtrsim:Ik,gvertneqq:qk,gvnE:jk,Hacek:Fk,hairsp:Bk,half:Uk,hamilt:zk,HARDcy:$k,hardcy:Hk,harrcir:Gk,harr:Wk,hArr:Qk,harrw:Kk,Hat:Yk,hbar:Xk,Hcirc:Zk,hcirc:Jk,hearts:tD,heartsuit:eD,hellip:nD,hercon:rD,hfr:oD,Hfr:sD,HilbertSpace:iD,hksearow:aD,hkswarow:lD,hoarr:cD,homtht:uD,hookleftarrow:fD,hookrightarrow:dD,hopf:pD,Hopf:hD,horbar:mD,HorizontalLine:gD,hscr:vD,Hscr:yD,hslash:wD,Hstrok:xD,hstrok:SD,HumpDownHump:TD,HumpEqual:ED,hybull:CD,hyphen:bD,Iacute:_D,iacute:kD,ic:DD,Icirc:AD,icirc:PD,Icy:LD,icy:ND,Idot:RD,IEcy:MD,iecy:OD,iexcl:VD,iff:ID,ifr:qD,Ifr:jD,Igrave:FD,igrave:BD,ii:UD,iiiint:zD,iiint:$D,iinfin:HD,iiota:GD,IJlig:WD,ijlig:QD,Imacr:KD,imacr:YD,image:XD,ImaginaryI:ZD,imagline:JD,imagpart:tA,imath:eA,Im:nA,imof:rA,imped:oA,Implies:sA,incare:iA,in:"∈",infin:aA,infintie:lA,inodot:cA,intcal:uA,int:fA,Int:dA,integers:pA,Integral:hA,intercal:mA,Intersection:gA,intlarhk:vA,intprod:yA,InvisibleComma:wA,InvisibleTimes:xA,IOcy:SA,iocy:TA,Iogon:EA,iogon:CA,Iopf:bA,iopf:_A,Iota:kA,iota:DA,iprod:AA,iquest:PA,iscr:LA,Iscr:NA,isin:RA,isindot:MA,isinE:OA,isins:VA,isinsv:IA,isinv:qA,it:jA,Itilde:FA,itilde:BA,Iukcy:UA,iukcy:zA,Iuml:$A,iuml:HA,Jcirc:GA,jcirc:WA,Jcy:QA,jcy:KA,Jfr:YA,jfr:XA,jmath:ZA,Jopf:JA,jopf:tP,Jscr:eP,jscr:nP,Jsercy:rP,jsercy:oP,Jukcy:sP,jukcy:iP,Kappa:aP,kappa:lP,kappav:cP,Kcedil:uP,kcedil:fP,Kcy:dP,kcy:pP,Kfr:hP,kfr:mP,kgreen:gP,KHcy:vP,khcy:yP,KJcy:wP,kjcy:xP,Kopf:SP,kopf:TP,Kscr:EP,kscr:CP,lAarr:bP,Lacute:_P,lacute:kP,laemptyv:DP,lagran:AP,Lambda:PP,lambda:LP,lang:NP,Lang:RP,langd:MP,langle:OP,lap:VP,Laplacetrf:IP,laquo:qP,larrb:jP,larrbfs:FP,larr:BP,Larr:UP,lArr:zP,larrfs:$P,larrhk:HP,larrlp:GP,larrpl:WP,larrsim:QP,larrtl:KP,latail:YP,lAtail:XP,lat:ZP,late:JP,lates:tL,lbarr:eL,lBarr:nL,lbbrk:rL,lbrace:oL,lbrack:sL,lbrke:iL,lbrksld:aL,lbrkslu:lL,Lcaron:cL,lcaron:uL,Lcedil:fL,lcedil:dL,lceil:pL,lcub:hL,Lcy:mL,lcy:gL,ldca:vL,ldquo:yL,ldquor:wL,ldrdhar:xL,ldrushar:SL,ldsh:TL,le:EL,lE:CL,LeftAngleBracket:bL,LeftArrowBar:_L,leftarrow:kL,LeftArrow:DL,Leftarrow:AL,LeftArrowRightArrow:PL,leftarrowtail:LL,LeftCeiling:NL,LeftDoubleBracket:RL,LeftDownTeeVector:ML,LeftDownVectorBar:OL,LeftDownVector:VL,LeftFloor:IL,leftharpoondown:qL,leftharpoonup:jL,leftleftarrows:FL,leftrightarrow:BL,LeftRightArrow:UL,Leftrightarrow:zL,leftrightarrows:$L,leftrightharpoons:HL,leftrightsquigarrow:GL,LeftRightVector:WL,LeftTeeArrow:QL,LeftTee:KL,LeftTeeVector:YL,leftthreetimes:XL,LeftTriangleBar:ZL,LeftTriangle:JL,LeftTriangleEqual:tN,LeftUpDownVector:eN,LeftUpTeeVector:nN,LeftUpVectorBar:rN,LeftUpVector:oN,LeftVectorBar:sN,LeftVector:iN,lEg:aN,leg:lN,leq:cN,leqq:uN,leqslant:fN,lescc:dN,les:pN,lesdot:hN,lesdoto:mN,lesdotor:gN,lesg:vN,lesges:yN,lessapprox:wN,lessdot:xN,lesseqgtr:SN,lesseqqgtr:TN,LessEqualGreater:EN,LessFullEqual:CN,LessGreater:bN,lessgtr:_N,LessLess:kN,lesssim:DN,LessSlantEqual:AN,LessTilde:PN,lfisht:LN,lfloor:NN,Lfr:RN,lfr:MN,lg:ON,lgE:VN,lHar:IN,lhard:qN,lharu:jN,lharul:FN,lhblk:BN,LJcy:UN,ljcy:zN,llarr:$N,ll:HN,Ll:GN,llcorner:WN,Lleftarrow:QN,llhard:KN,lltri:YN,Lmidot:XN,lmidot:ZN,lmoustache:JN,lmoust:tR,lnap:eR,lnapprox:nR,lne:rR,lnE:oR,lneq:sR,lneqq:iR,lnsim:aR,loang:lR,loarr:cR,lobrk:uR,longleftarrow:fR,LongLeftArrow:dR,Longleftarrow:pR,longleftrightarrow:hR,LongLeftRightArrow:mR,Longleftrightarrow:gR,longmapsto:vR,longrightarrow:yR,LongRightArrow:wR,Longrightarrow:xR,looparrowleft:SR,looparrowright:TR,lopar:ER,Lopf:CR,lopf:bR,loplus:_R,lotimes:kR,lowast:DR,lowbar:AR,LowerLeftArrow:PR,LowerRightArrow:LR,loz:NR,lozenge:RR,lozf:MR,lpar:OR,lparlt:VR,lrarr:IR,lrcorner:qR,lrhar:jR,lrhard:FR,lrm:BR,lrtri:UR,lsaquo:zR,lscr:$R,Lscr:HR,lsh:GR,Lsh:WR,lsim:QR,lsime:KR,lsimg:YR,lsqb:XR,lsquo:ZR,lsquor:JR,Lstrok:tM,lstrok:eM,ltcc:nM,ltcir:rM,lt:oM,LT:sM,Lt:iM,ltdot:aM,lthree:lM,ltimes:cM,ltlarr:uM,ltquest:fM,ltri:dM,ltrie:pM,ltrif:hM,ltrPar:mM,lurdshar:gM,luruhar:vM,lvertneqq:yM,lvnE:wM,macr:xM,male:SM,malt:TM,maltese:EM,Map:"⤅",map:CM,mapsto:bM,mapstodown:_M,mapstoleft:kM,mapstoup:DM,marker:AM,mcomma:PM,Mcy:LM,mcy:NM,mdash:RM,mDDot:MM,measuredangle:OM,MediumSpace:VM,Mellintrf:IM,Mfr:qM,mfr:jM,mho:FM,micro:BM,midast:UM,midcir:zM,mid:$M,middot:HM,minusb:GM,minus:WM,minusd:QM,minusdu:KM,MinusPlus:YM,mlcp:XM,mldr:ZM,mnplus:JM,models:tO,Mopf:eO,mopf:nO,mp:rO,mscr:oO,Mscr:sO,mstpos:iO,Mu:aO,mu:lO,multimap:cO,mumap:uO,nabla:fO,Nacute:dO,nacute:pO,nang:hO,nap:mO,napE:gO,napid:vO,napos:yO,napprox:wO,natural:xO,naturals:SO,natur:TO,nbsp:EO,nbump:CO,nbumpe:bO,ncap:_O,Ncaron:kO,ncaron:DO,Ncedil:AO,ncedil:PO,ncong:LO,ncongdot:NO,ncup:RO,Ncy:MO,ncy:OO,ndash:VO,nearhk:IO,nearr:qO,neArr:jO,nearrow:FO,ne:BO,nedot:UO,NegativeMediumSpace:zO,NegativeThickSpace:$O,NegativeThinSpace:HO,NegativeVeryThinSpace:GO,nequiv:WO,nesear:QO,nesim:KO,NestedGreaterGreater:YO,NestedLessLess:XO,NewLine:ZO,nexist:JO,nexists:tV,Nfr:eV,nfr:nV,ngE:rV,nge:oV,ngeq:sV,ngeqq:iV,ngeqslant:aV,nges:lV,nGg:cV,ngsim:uV,nGt:fV,ngt:dV,ngtr:pV,nGtv:hV,nharr:mV,nhArr:gV,nhpar:vV,ni:yV,nis:wV,nisd:xV,niv:SV,NJcy:TV,njcy:EV,nlarr:CV,nlArr:bV,nldr:_V,nlE:kV,nle:DV,nleftarrow:AV,nLeftarrow:PV,nleftrightarrow:LV,nLeftrightarrow:NV,nleq:RV,nleqq:MV,nleqslant:OV,nles:VV,nless:IV,nLl:qV,nlsim:jV,nLt:FV,nlt:BV,nltri:UV,nltrie:zV,nLtv:$V,nmid:HV,NoBreak:GV,NonBreakingSpace:WV,nopf:QV,Nopf:KV,Not:YV,not:XV,NotCongruent:ZV,NotCupCap:JV,NotDoubleVerticalBar:tI,NotElement:eI,NotEqual:nI,NotEqualTilde:rI,NotExists:oI,NotGreater:sI,NotGreaterEqual:iI,NotGreaterFullEqual:aI,NotGreaterGreater:lI,NotGreaterLess:cI,NotGreaterSlantEqual:uI,NotGreaterTilde:fI,NotHumpDownHump:dI,NotHumpEqual:pI,notin:hI,notindot:mI,notinE:gI,notinva:vI,notinvb:yI,notinvc:wI,NotLeftTriangleBar:xI,NotLeftTriangle:SI,NotLeftTriangleEqual:TI,NotLess:EI,NotLessEqual:CI,NotLessGreater:bI,NotLessLess:_I,NotLessSlantEqual:kI,NotLessTilde:DI,NotNestedGreaterGreater:AI,NotNestedLessLess:PI,notni:LI,notniva:NI,notnivb:RI,notnivc:MI,NotPrecedes:OI,NotPrecedesEqual:VI,NotPrecedesSlantEqual:II,NotReverseElement:qI,NotRightTriangleBar:jI,NotRightTriangle:FI,NotRightTriangleEqual:BI,NotSquareSubset:UI,NotSquareSubsetEqual:zI,NotSquareSuperset:$I,NotSquareSupersetEqual:HI,NotSubset:GI,NotSubsetEqual:WI,NotSucceeds:QI,NotSucceedsEqual:KI,NotSucceedsSlantEqual:YI,NotSucceedsTilde:XI,NotSuperset:ZI,NotSupersetEqual:JI,NotTilde:tq,NotTildeEqual:eq,NotTildeFullEqual:nq,NotTildeTilde:rq,NotVerticalBar:oq,nparallel:sq,npar:iq,nparsl:aq,npart:lq,npolint:cq,npr:uq,nprcue:fq,nprec:dq,npreceq:pq,npre:hq,nrarrc:mq,nrarr:gq,nrArr:vq,nrarrw:yq,nrightarrow:wq,nRightarrow:xq,nrtri:Sq,nrtrie:Tq,nsc:Eq,nsccue:Cq,nsce:bq,Nscr:_q,nscr:kq,nshortmid:Dq,nshortparallel:Aq,nsim:Pq,nsime:Lq,nsimeq:Nq,nsmid:Rq,nspar:Mq,nsqsube:Oq,nsqsupe:Vq,nsub:Iq,nsubE:qq,nsube:jq,nsubset:Fq,nsubseteq:Bq,nsubseteqq:Uq,nsucc:zq,nsucceq:$q,nsup:Hq,nsupE:Gq,nsupe:Wq,nsupset:Qq,nsupseteq:Kq,nsupseteqq:Yq,ntgl:Xq,Ntilde:Zq,ntilde:Jq,ntlg:tj,ntriangleleft:ej,ntrianglelefteq:nj,ntriangleright:rj,ntrianglerighteq:oj,Nu:sj,nu:ij,num:aj,numero:lj,numsp:cj,nvap:uj,nvdash:fj,nvDash:dj,nVdash:pj,nVDash:hj,nvge:mj,nvgt:gj,nvHarr:vj,nvinfin:yj,nvlArr:wj,nvle:xj,nvlt:Sj,nvltrie:Tj,nvrArr:Ej,nvrtrie:Cj,nvsim:bj,nwarhk:_j,nwarr:kj,nwArr:Dj,nwarrow:Aj,nwnear:Pj,Oacute:Lj,oacute:Nj,oast:Rj,Ocirc:Mj,ocirc:Oj,ocir:Vj,Ocy:Ij,ocy:qj,odash:jj,Odblac:Fj,odblac:Bj,odiv:Uj,odot:zj,odsold:$j,OElig:Hj,oelig:Gj,ofcir:Wj,Ofr:Qj,ofr:Kj,ogon:Yj,Ograve:Xj,ograve:Zj,ogt:Jj,ohbar:tF,ohm:eF,oint:nF,olarr:rF,olcir:oF,olcross:sF,oline:iF,olt:aF,Omacr:lF,omacr:cF,Omega:uF,omega:fF,Omicron:dF,omicron:pF,omid:hF,ominus:mF,Oopf:gF,oopf:vF,opar:yF,OpenCurlyDoubleQuote:wF,OpenCurlyQuote:xF,operp:SF,oplus:TF,orarr:EF,Or:CF,or:bF,ord:_F,order:kF,orderof:DF,ordf:AF,ordm:PF,origof:LF,oror:NF,orslope:RF,orv:MF,oS:OF,Oscr:VF,oscr:IF,Oslash:qF,oslash:jF,osol:FF,Otilde:BF,otilde:UF,otimesas:zF,Otimes:$F,otimes:HF,Ouml:GF,ouml:WF,ovbar:QF,OverBar:KF,OverBrace:YF,OverBracket:XF,OverParenthesis:ZF,para:JF,parallel:tB,par:eB,parsim:nB,parsl:rB,part:oB,PartialD:sB,Pcy:iB,pcy:aB,percnt:lB,period:cB,permil:uB,perp:fB,pertenk:dB,Pfr:pB,pfr:hB,Phi:mB,phi:gB,phiv:vB,phmmat:yB,phone:wB,Pi:xB,pi:SB,pitchfork:TB,piv:EB,planck:CB,planckh:bB,plankv:_B,plusacir:kB,plusb:DB,pluscir:AB,plus:PB,plusdo:LB,plusdu:NB,pluse:RB,PlusMinus:MB,plusmn:OB,plussim:VB,plustwo:IB,pm:qB,Poincareplane:jB,pointint:FB,popf:BB,Popf:UB,pound:zB,prap:$B,Pr:HB,pr:GB,prcue:WB,precapprox:QB,prec:KB,preccurlyeq:YB,Precedes:XB,PrecedesEqual:ZB,PrecedesSlantEqual:JB,PrecedesTilde:tU,preceq:eU,precnapprox:nU,precneqq:rU,precnsim:oU,pre:sU,prE:iU,precsim:aU,prime:lU,Prime:cU,primes:uU,prnap:fU,prnE:dU,prnsim:pU,prod:hU,Product:mU,profalar:gU,profline:vU,profsurf:yU,prop:wU,Proportional:xU,Proportion:SU,propto:TU,prsim:EU,prurel:CU,Pscr:bU,pscr:_U,Psi:kU,psi:DU,puncsp:AU,Qfr:PU,qfr:LU,qint:NU,qopf:RU,Qopf:MU,qprime:OU,Qscr:VU,qscr:IU,quaternions:qU,quatint:jU,quest:FU,questeq:BU,quot:UU,QUOT:zU,rAarr:$U,race:HU,Racute:GU,racute:WU,radic:QU,raemptyv:KU,rang:YU,Rang:XU,rangd:ZU,range:JU,rangle:tz,raquo:ez,rarrap:nz,rarrb:rz,rarrbfs:oz,rarrc:sz,rarr:iz,Rarr:az,rArr:lz,rarrfs:cz,rarrhk:uz,rarrlp:fz,rarrpl:dz,rarrsim:pz,Rarrtl:hz,rarrtl:mz,rarrw:gz,ratail:vz,rAtail:yz,ratio:wz,rationals:xz,rbarr:Sz,rBarr:Tz,RBarr:Ez,rbbrk:Cz,rbrace:bz,rbrack:_z,rbrke:kz,rbrksld:Dz,rbrkslu:Az,Rcaron:Pz,rcaron:Lz,Rcedil:Nz,rcedil:Rz,rceil:Mz,rcub:Oz,Rcy:Vz,rcy:Iz,rdca:qz,rdldhar:jz,rdquo:Fz,rdquor:Bz,rdsh:Uz,real:zz,realine:$z,realpart:Hz,reals:Gz,Re:Wz,rect:Qz,reg:Kz,REG:Yz,ReverseElement:Xz,ReverseEquilibrium:Zz,ReverseUpEquilibrium:Jz,rfisht:t$,rfloor:e$,rfr:n$,Rfr:r$,rHar:o$,rhard:s$,rharu:i$,rharul:a$,Rho:l$,rho:c$,rhov:u$,RightAngleBracket:f$,RightArrowBar:d$,rightarrow:p$,RightArrow:h$,Rightarrow:m$,RightArrowLeftArrow:g$,rightarrowtail:v$,RightCeiling:y$,RightDoubleBracket:w$,RightDownTeeVector:x$,RightDownVectorBar:S$,RightDownVector:T$,RightFloor:E$,rightharpoondown:C$,rightharpoonup:b$,rightleftarrows:_$,rightleftharpoons:k$,rightrightarrows:D$,rightsquigarrow:A$,RightTeeArrow:P$,RightTee:L$,RightTeeVector:N$,rightthreetimes:R$,RightTriangleBar:M$,RightTriangle:O$,RightTriangleEqual:V$,RightUpDownVector:I$,RightUpTeeVector:q$,RightUpVectorBar:j$,RightUpVector:F$,RightVectorBar:B$,RightVector:U$,ring:z$,risingdotseq:$$,rlarr:H$,rlhar:G$,rlm:W$,rmoustache:Q$,rmoust:K$,rnmid:Y$,roang:X$,roarr:Z$,robrk:J$,ropar:tH,ropf:eH,Ropf:nH,roplus:rH,rotimes:oH,RoundImplies:sH,rpar:iH,rpargt:aH,rppolint:lH,rrarr:cH,Rrightarrow:uH,rsaquo:fH,rscr:dH,Rscr:pH,rsh:hH,Rsh:mH,rsqb:gH,rsquo:vH,rsquor:yH,rthree:wH,rtimes:xH,rtri:SH,rtrie:TH,rtrif:EH,rtriltri:CH,RuleDelayed:bH,ruluhar:_H,rx:kH,Sacute:DH,sacute:AH,sbquo:PH,scap:LH,Scaron:NH,scaron:RH,Sc:MH,sc:OH,sccue:VH,sce:IH,scE:qH,Scedil:jH,scedil:FH,Scirc:BH,scirc:UH,scnap:zH,scnE:$H,scnsim:HH,scpolint:GH,scsim:WH,Scy:QH,scy:KH,sdotb:YH,sdot:XH,sdote:ZH,searhk:JH,searr:tG,seArr:eG,searrow:nG,sect:rG,semi:oG,seswar:sG,setminus:iG,setmn:aG,sext:lG,Sfr:cG,sfr:uG,sfrown:fG,sharp:dG,SHCHcy:pG,shchcy:hG,SHcy:mG,shcy:gG,ShortDownArrow:vG,ShortLeftArrow:yG,shortmid:wG,shortparallel:xG,ShortRightArrow:SG,ShortUpArrow:TG,shy:EG,Sigma:CG,sigma:bG,sigmaf:_G,sigmav:kG,sim:DG,simdot:AG,sime:PG,simeq:LG,simg:NG,simgE:RG,siml:MG,simlE:OG,simne:VG,simplus:IG,simrarr:qG,slarr:jG,SmallCircle:FG,smallsetminus:BG,smashp:UG,smeparsl:zG,smid:$G,smile:HG,smt:GG,smte:WG,smtes:QG,SOFTcy:KG,softcy:YG,solbar:XG,solb:ZG,sol:JG,Sopf:tW,sopf:eW,spades:nW,spadesuit:rW,spar:oW,sqcap:sW,sqcaps:iW,sqcup:aW,sqcups:lW,Sqrt:cW,sqsub:uW,sqsube:fW,sqsubset:dW,sqsubseteq:pW,sqsup:hW,sqsupe:mW,sqsupset:gW,sqsupseteq:vW,square:yW,Square:wW,SquareIntersection:xW,SquareSubset:SW,SquareSubsetEqual:TW,SquareSuperset:EW,SquareSupersetEqual:CW,SquareUnion:bW,squarf:_W,squ:kW,squf:DW,srarr:AW,Sscr:PW,sscr:LW,ssetmn:NW,ssmile:RW,sstarf:MW,Star:OW,star:VW,starf:IW,straightepsilon:qW,straightphi:jW,strns:FW,sub:BW,Sub:UW,subdot:zW,subE:$W,sube:HW,subedot:GW,submult:WW,subnE:QW,subne:KW,subplus:YW,subrarr:XW,subset:ZW,Subset:JW,subseteq:tQ,subseteqq:eQ,SubsetEqual:nQ,subsetneq:rQ,subsetneqq:oQ,subsim:sQ,subsub:iQ,subsup:aQ,succapprox:lQ,succ:cQ,succcurlyeq:uQ,Succeeds:fQ,SucceedsEqual:dQ,SucceedsSlantEqual:pQ,SucceedsTilde:hQ,succeq:mQ,succnapprox:gQ,succneqq:vQ,succnsim:yQ,succsim:wQ,SuchThat:xQ,sum:SQ,Sum:TQ,sung:EQ,sup1:CQ,sup2:bQ,sup3:_Q,sup:kQ,Sup:DQ,supdot:AQ,supdsub:PQ,supE:LQ,supe:NQ,supedot:RQ,Superset:MQ,SupersetEqual:OQ,suphsol:VQ,suphsub:IQ,suplarr:qQ,supmult:jQ,supnE:FQ,supne:BQ,supplus:UQ,supset:zQ,Supset:$Q,supseteq:HQ,supseteqq:GQ,supsetneq:WQ,supsetneqq:QQ,supsim:KQ,supsub:YQ,supsup:XQ,swarhk:ZQ,swarr:JQ,swArr:tK,swarrow:eK,swnwar:nK,szlig:rK,Tab:oK,target:sK,Tau:iK,tau:aK,tbrk:lK,Tcaron:cK,tcaron:uK,Tcedil:fK,tcedil:dK,Tcy:pK,tcy:hK,tdot:mK,telrec:gK,Tfr:vK,tfr:yK,there4:wK,therefore:xK,Therefore:SK,Theta:TK,theta:EK,thetasym:CK,thetav:bK,thickapprox:_K,thicksim:kK,ThickSpace:DK,ThinSpace:AK,thinsp:PK,thkap:LK,thksim:NK,THORN:RK,thorn:MK,tilde:OK,Tilde:VK,TildeEqual:IK,TildeFullEqual:qK,TildeTilde:jK,timesbar:FK,timesb:BK,times:UK,timesd:zK,tint:$K,toea:HK,topbot:GK,topcir:WK,top:QK,Topf:KK,topf:YK,topfork:XK,tosa:ZK,tprime:JK,trade:tY,TRADE:eY,triangle:nY,triangledown:rY,triangleleft:oY,trianglelefteq:sY,triangleq:iY,triangleright:aY,trianglerighteq:lY,tridot:cY,trie:uY,triminus:fY,TripleDot:dY,triplus:pY,trisb:hY,tritime:mY,trpezium:gY,Tscr:vY,tscr:yY,TScy:wY,tscy:xY,TSHcy:SY,tshcy:TY,Tstrok:EY,tstrok:CY,twixt:bY,twoheadleftarrow:_Y,twoheadrightarrow:kY,Uacute:DY,uacute:AY,uarr:PY,Uarr:LY,uArr:NY,Uarrocir:RY,Ubrcy:MY,ubrcy:OY,Ubreve:VY,ubreve:IY,Ucirc:qY,ucirc:jY,Ucy:FY,ucy:BY,udarr:UY,Udblac:zY,udblac:$Y,udhar:HY,ufisht:GY,Ufr:WY,ufr:QY,Ugrave:KY,ugrave:YY,uHar:XY,uharl:ZY,uharr:JY,uhblk:tX,ulcorn:eX,ulcorner:nX,ulcrop:rX,ultri:oX,Umacr:sX,umacr:iX,uml:aX,UnderBar:lX,UnderBrace:cX,UnderBracket:uX,UnderParenthesis:fX,Union:dX,UnionPlus:pX,Uogon:hX,uogon:mX,Uopf:gX,uopf:vX,UpArrowBar:yX,uparrow:wX,UpArrow:xX,Uparrow:SX,UpArrowDownArrow:TX,updownarrow:EX,UpDownArrow:CX,Updownarrow:bX,UpEquilibrium:_X,upharpoonleft:kX,upharpoonright:DX,uplus:AX,UpperLeftArrow:PX,UpperRightArrow:LX,upsi:NX,Upsi:RX,upsih:MX,Upsilon:OX,upsilon:VX,UpTeeArrow:IX,UpTee:qX,upuparrows:jX,urcorn:FX,urcorner:BX,urcrop:UX,Uring:zX,uring:$X,urtri:HX,Uscr:GX,uscr:WX,utdot:QX,Utilde:KX,utilde:YX,utri:XX,utrif:ZX,uuarr:JX,Uuml:tZ,uuml:eZ,uwangle:nZ,vangrt:rZ,varepsilon:oZ,varkappa:sZ,varnothing:iZ,varphi:aZ,varpi:lZ,varpropto:cZ,varr:uZ,vArr:fZ,varrho:dZ,varsigma:pZ,varsubsetneq:hZ,varsubsetneqq:mZ,varsupsetneq:gZ,varsupsetneqq:vZ,vartheta:yZ,vartriangleleft:wZ,vartriangleright:xZ,vBar:SZ,Vbar:TZ,vBarv:EZ,Vcy:CZ,vcy:bZ,vdash:_Z,vDash:kZ,Vdash:DZ,VDash:AZ,Vdashl:PZ,veebar:LZ,vee:NZ,Vee:RZ,veeeq:MZ,vellip:OZ,verbar:VZ,Verbar:IZ,vert:qZ,Vert:jZ,VerticalBar:FZ,VerticalLine:BZ,VerticalSeparator:UZ,VerticalTilde:zZ,VeryThinSpace:$Z,Vfr:HZ,vfr:GZ,vltri:WZ,vnsub:QZ,vnsup:KZ,Vopf:YZ,vopf:XZ,vprop:ZZ,vrtri:JZ,Vscr:tJ,vscr:eJ,vsubnE:nJ,vsubne:rJ,vsupnE:oJ,vsupne:sJ,Vvdash:iJ,vzigzag:aJ,Wcirc:lJ,wcirc:cJ,wedbar:uJ,wedge:fJ,Wedge:dJ,wedgeq:pJ,weierp:hJ,Wfr:mJ,wfr:gJ,Wopf:vJ,wopf:yJ,wp:wJ,wr:xJ,wreath:SJ,Wscr:TJ,wscr:EJ,xcap:CJ,xcirc:bJ,xcup:_J,xdtri:kJ,Xfr:DJ,xfr:AJ,xharr:PJ,xhArr:LJ,Xi:NJ,xi:RJ,xlarr:MJ,xlArr:OJ,xmap:VJ,xnis:IJ,xodot:qJ,Xopf:jJ,xopf:FJ,xoplus:BJ,xotime:UJ,xrarr:zJ,xrArr:$J,Xscr:HJ,xscr:GJ,xsqcup:WJ,xuplus:QJ,xutri:KJ,xvee:YJ,xwedge:XJ,Yacute:ZJ,yacute:JJ,YAcy:ttt,yacy:ett,Ycirc:ntt,ycirc:rtt,Ycy:ott,ycy:stt,yen:itt,Yfr:att,yfr:ltt,YIcy:ctt,yicy:utt,Yopf:ftt,yopf:dtt,Yscr:ptt,yscr:htt,YUcy:mtt,yucy:gtt,yuml:vtt,Yuml:ytt,Zacute:wtt,zacute:xtt,Zcaron:Stt,zcaron:Ttt,Zcy:Ett,zcy:Ctt,Zdot:btt,zdot:_tt,zeetrf:ktt,ZeroWidthSpace:Dtt,Zeta:Att,zeta:Ptt,zfr:Ltt,Zfr:Ntt,ZHcy:Rtt,zhcy:Mtt,zigrarr:Ott,zopf:Vtt,Zopf:Itt,Zscr:qtt,zscr:jtt,zwj:Ftt,zwnj:Btt},Utt="Á",ztt="á",$tt="Â",Htt="â",Gtt="´",Wtt="Æ",Qtt="æ",Ktt="À",Ytt="à",Xtt="&",Ztt="&",Jtt="Å",tet="å",eet="Ã",net="ã",ret="Ä",oet="ä",set="¦",iet="Ç",aet="ç",cet="¸",uet="¢",fet="©",det="©",pet="¤",het="°",met="÷",get="É",vet="é",yet="Ê",wet="ê",xet="È",Tet="è",Eet="Ð",Cet="ð",bet="Ë",_et="ë",ket="½",Det="¼",Aet="¾",Pet=">",Let=">",Net="Í",Ret="í",Met="Î",Oet="î",Vet="¡",Iet="Ì",qet="ì",jet="¿",Fet="Ï",Bet="ï",Uet="«",zet="<",$et="<",Het="¯",Get="µ",Wet="·",Qet=" ",Ket="¬",Yet="Ñ",Xet="ñ",Zet="Ó",Jet="ó",tnt="Ô",ent="ô",nnt="Ò",rnt="ò",ont="ª",snt="º",int="Ø",ant="ø",lnt="Õ",cnt="õ",unt="Ö",fnt="ö",dnt="¶",pnt="±",hnt="£",mnt='"',gnt='"',vnt="»",ynt="®",wnt="®",xnt="§",Snt="­",Tnt="¹",Ent="²",Cnt="³",bnt="ß",_nt="Þ",knt="þ",Dnt="×",Ant="Ú",Pnt="ú",Lnt="Û",Nnt="û",Rnt="Ù",Mnt="ù",Ont="¨",Vnt="Ü",Int="ü",qnt="Ý",jnt="ý",Fnt="¥",Bnt="ÿ",Unt={Aacute:Utt,aacute:ztt,Acirc:$tt,acirc:Htt,acute:Gtt,AElig:Wtt,aelig:Qtt,Agrave:Ktt,agrave:Ytt,amp:Xtt,AMP:Ztt,Aring:Jtt,aring:tet,Atilde:eet,atilde:net,Auml:ret,auml:oet,brvbar:set,Ccedil:iet,ccedil:aet,cedil:cet,cent:uet,copy:fet,COPY:det,curren:pet,deg:het,divide:met,Eacute:get,eacute:vet,Ecirc:yet,ecirc:wet,Egrave:xet,egrave:Tet,ETH:Eet,eth:Cet,Euml:bet,euml:_et,frac12:ket,frac14:Det,frac34:Aet,gt:Pet,GT:Let,Iacute:Net,iacute:Ret,Icirc:Met,icirc:Oet,iexcl:Vet,Igrave:Iet,igrave:qet,iquest:jet,Iuml:Fet,iuml:Bet,laquo:Uet,lt:zet,LT:$et,macr:Het,micro:Get,middot:Wet,nbsp:Qet,not:Ket,Ntilde:Yet,ntilde:Xet,Oacute:Zet,oacute:Jet,Ocirc:tnt,ocirc:ent,Ograve:nnt,ograve:rnt,ordf:ont,ordm:snt,Oslash:int,oslash:ant,Otilde:lnt,otilde:cnt,Ouml:unt,ouml:fnt,para:dnt,plusmn:pnt,pound:hnt,quot:mnt,QUOT:gnt,raquo:vnt,reg:ynt,REG:wnt,sect:xnt,shy:Snt,sup1:Tnt,sup2:Ent,sup3:Cnt,szlig:bnt,THORN:_nt,thorn:knt,times:Dnt,Uacute:Ant,uacute:Pnt,Ucirc:Lnt,ucirc:Nnt,Ugrave:Rnt,ugrave:Mnt,uml:Ont,Uuml:Vnt,uuml:Int,Yacute:qnt,yacute:jnt,yen:Fnt,yuml:Bnt},znt="&",$nt="'",Hnt=">",Gnt="<",Wnt='"',h5={amp:znt,apos:$nt,gt:Hnt,lt:Gnt,quot:Wnt};var Sc={};const Qnt={0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376};var Knt=L&&L.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Sc,"__esModule",{value:!0});var Z0=Knt(Qnt),Ynt=String.fromCodePoint||function(t){var e="";return t>65535&&(t-=65536,e+=String.fromCharCode(t>>>10&1023|55296),t=56320|t&1023),e+=String.fromCharCode(t),e};function Xnt(t){return t>=55296&&t<=57343||t>1114111?"�":(t in Z0.default&&(t=Z0.default[t]),Ynt(t))}Sc.default=Xnt;var Os=L&&L.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(ke,"__esModule",{value:!0});ke.decodeHTML=ke.decodeHTMLStrict=ke.decodeXML=void 0;var ja=Os(p5),Znt=Os(Unt),Jnt=Os(h5),J0=Os(Sc),t1t=/&(?:[a-zA-Z0-9]+|#[xX][\da-fA-F]+|#\d+);/g;ke.decodeXML=m5(Jnt.default);ke.decodeHTMLStrict=m5(ja.default);function m5(t){var e=g5(t);return function(n){return String(n).replace(t1t,e)}}var t8=function(t,e){return t<e?1:-1};ke.decodeHTML=function(){for(var t=Object.keys(Znt.default).sort(t8),e=Object.keys(ja.default).sort(t8),n=0,r=0;n<e.length;n++)t[r]===e[n]?(e[n]+=";?",r++):e[n]+=";";var o=new RegExp("&(?:"+e.join("|")+"|#[xX][\\da-fA-F]+;?|#\\d+;?)","g"),s=g5(ja.default);function i(a){return a.substr(-1)!==";"&&(a+=";"),s(a)}return function(a){return String(a).replace(o,i)}}();function g5(t){return function(n){if(n.charAt(1)==="#"){var r=n.charAt(2);return r==="X"||r==="x"?J0.default(parseInt(n.substr(3),16)):J0.default(parseInt(n.substr(2),10))}return t[n.slice(1,-1)]||n}}var Bt={},v5=L&&L.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Bt,"__esModule",{value:!0});Bt.escapeUTF8=Bt.escape=Bt.encodeNonAsciiHTML=Bt.encodeHTML=Bt.encodeXML=void 0;var e1t=v5(h5),y5=x5(e1t.default),w5=S5(y5);Bt.encodeXML=C5(y5);var n1t=v5(p5),Tc=x5(n1t.default),r1t=S5(Tc);Bt.encodeHTML=s1t(Tc,r1t);Bt.encodeNonAsciiHTML=C5(Tc);function x5(t){return Object.keys(t).sort().reduce(function(e,n){return e[t[n]]="&"+n+";",e},{})}function S5(t){for(var e=[],n=[],r=0,o=Object.keys(t);r<o.length;r++){var s=o[r];s.length===1?e.push("\\"+s):n.push(s)}e.sort();for(var i=0;i<e.length-1;i++){for(var a=i;a<e.length-1&&e[a].charCodeAt(1)+1===e[a+1].charCodeAt(1);)a+=1;var l=1+a-i;l<3||e.splice(i,l,e[i]+"-"+e[a])}return n.unshift("["+e.join("")+"]"),new RegExp(n.join("|"),"g")}var T5=/(?:[\x80-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/g,o1t=String.prototype.codePointAt!=null?function(t){return t.codePointAt(0)}:function(t){return(t.charCodeAt(0)-55296)*1024+t.charCodeAt(1)-56320+65536};function Vs(t){return"&#x"+(t.length>1?o1t(t):t.charCodeAt(0)).toString(16).toUpperCase()+";"}function s1t(t,e){return function(n){return n.replace(e,function(r){return t[r]}).replace(T5,Vs)}}var E5=new RegExp(w5.source+"|"+T5.source,"g");function i1t(t){return t.replace(E5,Vs)}Bt.escape=i1t;function a1t(t){return t.replace(w5,Vs)}Bt.escapeUTF8=a1t;function C5(t){return function(e){return e.replace(E5,function(n){return t[n]||Vs(n)})}}(function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.decodeXMLStrict=t.decodeHTML5Strict=t.decodeHTML4Strict=t.decodeHTML5=t.decodeHTML4=t.decodeHTMLStrict=t.decodeHTML=t.decodeXML=t.encodeHTML5=t.encodeHTML4=t.escapeUTF8=t.escape=t.encodeNonAsciiHTML=t.encodeHTML=t.encodeXML=t.encode=t.decodeStrict=t.decode=void 0;var e=ke,n=Bt;function r(l,c){return(!c||c<=0?e.decodeXML:e.decodeHTML)(l)}t.decode=r;function o(l,c){return(!c||c<=0?e.decodeXML:e.decodeHTMLStrict)(l)}t.decodeStrict=o;function s(l,c){return(!c||c<=0?n.encodeXML:n.encodeHTML)(l)}t.encode=s;var i=Bt;Object.defineProperty(t,"encodeXML",{enumerable:!0,get:function(){return i.encodeXML}}),Object.defineProperty(t,"encodeHTML",{enumerable:!0,get:function(){return i.encodeHTML}}),Object.defineProperty(t,"encodeNonAsciiHTML",{enumerable:!0,get:function(){return i.encodeNonAsciiHTML}}),Object.defineProperty(t,"escape",{enumerable:!0,get:function(){return i.escape}}),Object.defineProperty(t,"escapeUTF8",{enumerable:!0,get:function(){return i.escapeUTF8}}),Object.defineProperty(t,"encodeHTML4",{enumerable:!0,get:function(){return i.encodeHTML}}),Object.defineProperty(t,"encodeHTML5",{enumerable:!0,get:function(){return i.encodeHTML}});var a=ke;Object.defineProperty(t,"decodeXML",{enumerable:!0,get:function(){return a.decodeXML}}),Object.defineProperty(t,"decodeHTML",{enumerable:!0,get:function(){return a.decodeHTML}}),Object.defineProperty(t,"decodeHTMLStrict",{enumerable:!0,get:function(){return a.decodeHTMLStrict}}),Object.defineProperty(t,"decodeHTML4",{enumerable:!0,get:function(){return a.decodeHTML}}),Object.defineProperty(t,"decodeHTML5",{enumerable:!0,get:function(){return a.decodeHTML}}),Object.defineProperty(t,"decodeHTML4Strict",{enumerable:!0,get:function(){return a.decodeHTMLStrict}}),Object.defineProperty(t,"decodeHTML5Strict",{enumerable:!0,get:function(){return a.decodeHTMLStrict}}),Object.defineProperty(t,"decodeXMLStrict",{enumerable:!0,get:function(){return a.decodeXML}})})(d5);var g1={};Object.defineProperty(g1,"__esModule",{value:!0});g1.attributeNames=g1.elementNames=void 0;g1.elementNames=new Map([["altglyph","altGlyph"],["altglyphdef","altGlyphDef"],["altglyphitem","altGlyphItem"],["animatecolor","animateColor"],["animatemotion","animateMotion"],["animatetransform","animateTransform"],["clippath","clipPath"],["feblend","feBlend"],["fecolormatrix","feColorMatrix"],["fecomponenttransfer","feComponentTransfer"],["fecomposite","feComposite"],["feconvolvematrix","feConvolveMatrix"],["fediffuselighting","feDiffuseLighting"],["fedisplacementmap","feDisplacementMap"],["fedistantlight","feDistantLight"],["fedropshadow","feDropShadow"],["feflood","feFlood"],["fefunca","feFuncA"],["fefuncb","feFuncB"],["fefuncg","feFuncG"],["fefuncr","feFuncR"],["fegaussianblur","feGaussianBlur"],["feimage","feImage"],["femerge","feMerge"],["femergenode","feMergeNode"],["femorphology","feMorphology"],["feoffset","feOffset"],["fepointlight","fePointLight"],["fespecularlighting","feSpecularLighting"],["fespotlight","feSpotLight"],["fetile","feTile"],["feturbulence","feTurbulence"],["foreignobject","foreignObject"],["glyphref","glyphRef"],["lineargradient","linearGradient"],["radialgradient","radialGradient"],["textpath","textPath"]]);g1.attributeNames=new Map([["definitionurl","definitionURL"],["attributename","attributeName"],["attributetype","attributeType"],["basefrequency","baseFrequency"],["baseprofile","baseProfile"],["calcmode","calcMode"],["clippathunits","clipPathUnits"],["diffuseconstant","diffuseConstant"],["edgemode","edgeMode"],["filterunits","filterUnits"],["glyphref","glyphRef"],["gradienttransform","gradientTransform"],["gradientunits","gradientUnits"],["kernelmatrix","kernelMatrix"],["kernelunitlength","kernelUnitLength"],["keypoints","keyPoints"],["keysplines","keySplines"],["keytimes","keyTimes"],["lengthadjust","lengthAdjust"],["limitingconeangle","limitingConeAngle"],["markerheight","markerHeight"],["markerunits","markerUnits"],["markerwidth","markerWidth"],["maskcontentunits","maskContentUnits"],["maskunits","maskUnits"],["numoctaves","numOctaves"],["pathlength","pathLength"],["patterncontentunits","patternContentUnits"],["patterntransform","patternTransform"],["patternunits","patternUnits"],["pointsatx","pointsAtX"],["pointsaty","pointsAtY"],["pointsatz","pointsAtZ"],["preservealpha","preserveAlpha"],["preserveaspectratio","preserveAspectRatio"],["primitiveunits","primitiveUnits"],["refx","refX"],["refy","refY"],["repeatcount","repeatCount"],["repeatdur","repeatDur"],["requiredextensions","requiredExtensions"],["requiredfeatures","requiredFeatures"],["specularconstant","specularConstant"],["specularexponent","specularExponent"],["spreadmethod","spreadMethod"],["startoffset","startOffset"],["stddeviation","stdDeviation"],["stitchtiles","stitchTiles"],["surfacescale","surfaceScale"],["systemlanguage","systemLanguage"],["tablevalues","tableValues"],["targetx","targetX"],["targety","targetY"],["textlength","textLength"],["viewbox","viewBox"],["viewtarget","viewTarget"],["xchannelselector","xChannelSelector"],["ychannelselector","yChannelSelector"],["zoomandpan","zoomAndPan"]]);var Zn=L&&L.__assign||function(){return Zn=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++){e=arguments[n];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t},Zn.apply(this,arguments)},l1t=L&&L.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),c1t=L&&L.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),u1t=L&&L.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&l1t(e,t,n);return c1t(e,t),e};Object.defineProperty(wc,"__esModule",{value:!0});var we=u1t(xc),b5=d5,_5=g1,f1t=new Set(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]);function d1t(t,e){if(t)return Object.keys(t).map(function(n){var r,o,s=(r=t[n])!==null&&r!==void 0?r:"";return e.xmlMode==="foreign"&&(n=(o=_5.attributeNames.get(n))!==null&&o!==void 0?o:n),!e.emptyAttrs&&!e.xmlMode&&s===""?n:n+'="'+(e.decodeEntities!==!1?b5.encodeXML(s):s.replace(/"/g,"&quot;"))+'"'}).join(" ")}var e8=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);function Ec(t,e){e===void 0&&(e={});for(var n=("length"in t)?t:[t],r="",o=0;o<n.length;o++)r+=p1t(n[o],e);return r}wc.default=Ec;function p1t(t,e){switch(t.type){case we.Root:return Ec(t.children,e);case we.Directive:case we.Doctype:return v1t(t);case we.Comment:return x1t(t);case we.CDATA:return w1t(t);case we.Script:case we.Style:case we.Tag:return g1t(t,e);case we.Text:return y1t(t,e)}}var h1t=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]),m1t=new Set(["svg","math"]);function g1t(t,e){var n;e.xmlMode==="foreign"&&(t.name=(n=_5.elementNames.get(t.name))!==null&&n!==void 0?n:t.name,t.parent&&h1t.has(t.parent.name)&&(e=Zn(Zn({},e),{xmlMode:!1}))),!e.xmlMode&&m1t.has(t.name)&&(e=Zn(Zn({},e),{xmlMode:"foreign"}));var r="<"+t.name,o=d1t(t.attribs,e);return o&&(r+=" "+o),t.children.length===0&&(e.xmlMode?e.selfClosingTags!==!1:e.selfClosingTags&&e8.has(t.name))?(e.xmlMode||(r+=" "),r+="/>"):(r+=">",t.children.length>0&&(r+=Ec(t.children,e)),(e.xmlMode||!e8.has(t.name))&&(r+="</"+t.name+">")),r}function v1t(t){return"<"+t.data+">"}function y1t(t,e){var n=t.data||"";return e.decodeEntities!==!1&&!(!e.xmlMode&&t.parent&&f1t.has(t.parent.name))&&(n=b5.encodeXML(n)),n}function w1t(t){return"<![CDATA["+t.children[0].data+"]]>"}function x1t(t){return"<!--"+t.data+"-->"}var S1t=L&&L.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Ft,"__esModule",{value:!0});Ft.innerText=Ft.textContent=Ft.getText=Ft.getInnerHTML=Ft.getOuterHTML=void 0;var ge=Re,T1t=S1t(wc),E1t=xc;function k5(t,e){return(0,T1t.default)(t,e)}Ft.getOuterHTML=k5;function C1t(t,e){return(0,ge.hasChildren)(t)?t.children.map(function(n){return k5(n,e)}).join(""):""}Ft.getInnerHTML=C1t;function Eo(t){return Array.isArray(t)?t.map(Eo).join(""):(0,ge.isTag)(t)?t.name==="br"?`
`:Eo(t.children):(0,ge.isCDATA)(t)?Eo(t.children):(0,ge.isText)(t)?t.data:""}Ft.getText=Eo;function Fa(t){return Array.isArray(t)?t.map(Fa).join(""):(0,ge.hasChildren)(t)&&!(0,ge.isComment)(t)?Fa(t.children):(0,ge.isText)(t)?t.data:""}Ft.textContent=Fa;function Ba(t){return Array.isArray(t)?t.map(Ba).join(""):(0,ge.hasChildren)(t)&&(t.type===E1t.ElementType.Tag||(0,ge.isCDATA)(t))?Ba(t.children):(0,ge.isText)(t)?t.data:""}Ft.innerText=Ba;var dt={};Object.defineProperty(dt,"__esModule",{value:!0});dt.prevElementSibling=dt.nextElementSibling=dt.getName=dt.hasAttrib=dt.getAttributeValue=dt.getSiblings=dt.getParent=dt.getChildren=void 0;var D5=Re,b1t=[];function A5(t){var e;return(e=t.children)!==null&&e!==void 0?e:b1t}dt.getChildren=A5;function P5(t){return t.parent||null}dt.getParent=P5;function _1t(t){var e,n,r=P5(t);if(r!=null)return A5(r);for(var o=[t],s=t.prev,i=t.next;s!=null;)o.unshift(s),e=s,s=e.prev;for(;i!=null;)o.push(i),n=i,i=n.next;return o}dt.getSiblings=_1t;function k1t(t,e){var n;return(n=t.attribs)===null||n===void 0?void 0:n[e]}dt.getAttributeValue=k1t;function D1t(t,e){return t.attribs!=null&&Object.prototype.hasOwnProperty.call(t.attribs,e)&&t.attribs[e]!=null}dt.hasAttrib=D1t;function A1t(t){return t.name}dt.getName=A1t;function P1t(t){for(var e,n=t.next;n!==null&&!(0,D5.isTag)(n);)e=n,n=e.next;return n}dt.nextElementSibling=P1t;function L1t(t){for(var e,n=t.prev;n!==null&&!(0,D5.isTag)(n);)e=n,n=e.prev;return n}dt.prevElementSibling=L1t;var Nt={};Object.defineProperty(Nt,"__esModule",{value:!0});Nt.prepend=Nt.prependChild=Nt.append=Nt.appendChild=Nt.replaceElement=Nt.removeElement=void 0;function Mr(t){if(t.prev&&(t.prev.next=t.next),t.next&&(t.next.prev=t.prev),t.parent){var e=t.parent.children;e.splice(e.lastIndexOf(t),1)}}Nt.removeElement=Mr;function N1t(t,e){var n=e.prev=t.prev;n&&(n.next=e);var r=e.next=t.next;r&&(r.prev=e);var o=e.parent=t.parent;if(o){var s=o.children;s[s.lastIndexOf(t)]=e}}Nt.replaceElement=N1t;function R1t(t,e){if(Mr(e),e.next=null,e.parent=t,t.children.push(e)>1){var n=t.children[t.children.length-2];n.next=e,e.prev=n}else e.prev=null}Nt.appendChild=R1t;function M1t(t,e){Mr(e);var n=t.parent,r=t.next;if(e.next=r,e.prev=t,t.next=e,e.parent=n,r){if(r.prev=e,n){var o=n.children;o.splice(o.lastIndexOf(r),0,e)}}else n&&n.children.push(e)}Nt.append=M1t;function O1t(t,e){if(Mr(e),e.parent=t,e.prev=null,t.children.unshift(e)!==1){var n=t.children[1];n.prev=e,e.next=n}else e.next=null}Nt.prependChild=O1t;function V1t(t,e){Mr(e);var n=t.parent;if(n){var r=n.children;r.splice(r.indexOf(t),0,e)}t.prev&&(t.prev.next=e),e.parent=n,e.prev=t.prev,e.next=t,t.prev=e}Nt.prepend=V1t;var bt={};Object.defineProperty(bt,"__esModule",{value:!0});bt.findAll=bt.existsOne=bt.findOne=bt.findOneChild=bt.find=bt.filter=void 0;var Er=Re;function I1t(t,e,n,r){return n===void 0&&(n=!0),r===void 0&&(r=1/0),Array.isArray(e)||(e=[e]),Cc(t,e,n,r)}bt.filter=I1t;function Cc(t,e,n,r){for(var o=[],s=0,i=e;s<i.length;s++){var a=i[s];if(t(a)&&(o.push(a),--r<=0))break;if(n&&(0,Er.hasChildren)(a)&&a.children.length>0){var l=Cc(t,a.children,n,r);if(o.push.apply(o,l),r-=l.length,r<=0)break}}return o}bt.find=Cc;function q1t(t,e){return e.find(t)}bt.findOneChild=q1t;function L5(t,e,n){n===void 0&&(n=!0);for(var r=null,o=0;o<e.length&&!r;o++){var s=e[o];if((0,Er.isTag)(s))t(s)?r=s:n&&s.children.length>0&&(r=L5(t,s.children));else continue}return r}bt.findOne=L5;function N5(t,e){return e.some(function(n){return(0,Er.isTag)(n)&&(t(n)||n.children.length>0&&N5(t,n.children))})}bt.existsOne=N5;function j1t(t,e){for(var n,r=[],o=e.filter(Er.isTag),s;s=o.shift();){var i=(n=s.children)===null||n===void 0?void 0:n.filter(Er.isTag);i&&i.length>0&&o.unshift.apply(o,i),t(s)&&r.push(s)}return r}bt.findAll=j1t;var Ut={};Object.defineProperty(Ut,"__esModule",{value:!0});Ut.getElementsByTagType=Ut.getElementsByTagName=Ut.getElementById=Ut.getElements=Ut.testElement=void 0;var hn=Re,Is=bt,ss={tag_name:function(t){return typeof t=="function"?function(e){return(0,hn.isTag)(e)&&t(e.name)}:t==="*"?hn.isTag:function(e){return(0,hn.isTag)(e)&&e.name===t}},tag_type:function(t){return typeof t=="function"?function(e){return t(e.type)}:function(e){return e.type===t}},tag_contains:function(t){return typeof t=="function"?function(e){return(0,hn.isText)(e)&&t(e.data)}:function(e){return(0,hn.isText)(e)&&e.data===t}}};function R5(t,e){return typeof e=="function"?function(n){return(0,hn.isTag)(n)&&e(n.attribs[t])}:function(n){return(0,hn.isTag)(n)&&n.attribs[t]===e}}function F1t(t,e){return function(n){return t(n)||e(n)}}function M5(t){var e=Object.keys(t).map(function(n){var r=t[n];return Object.prototype.hasOwnProperty.call(ss,n)?ss[n](r):R5(n,r)});return e.length===0?null:e.reduce(F1t)}function B1t(t,e){var n=M5(t);return n?n(e):!0}Ut.testElement=B1t;function U1t(t,e,n,r){r===void 0&&(r=1/0);var o=M5(t);return o?(0,Is.filter)(o,e,n,r):[]}Ut.getElements=U1t;function z1t(t,e,n){return n===void 0&&(n=!0),Array.isArray(e)||(e=[e]),(0,Is.findOne)(R5("id",t),e,n)}Ut.getElementById=z1t;function $1t(t,e,n,r){return n===void 0&&(n=!0),r===void 0&&(r=1/0),(0,Is.filter)(ss.tag_name(t),e,n,r)}Ut.getElementsByTagName=$1t;function H1t(t,e,n,r){return n===void 0&&(n=!0),r===void 0&&(r=1/0),(0,Is.filter)(ss.tag_type(t),e,n,r)}Ut.getElementsByTagType=H1t;var Ze={};Object.defineProperty(Ze,"__esModule",{value:!0});Ze.uniqueSort=Ze.compareDocumentPosition=Ze.removeSubsets=void 0;var n8=Re;function G1t(t){for(var e=t.length;--e>=0;){var n=t[e];if(e>0&&t.lastIndexOf(n,e-1)>=0){t.splice(e,1);continue}for(var r=n.parent;r;r=r.parent)if(t.includes(r)){t.splice(e,1);break}}return t}Ze.removeSubsets=G1t;function O5(t,e){var n=[],r=[];if(t===e)return 0;for(var o=(0,n8.hasChildren)(t)?t:t.parent;o;)n.unshift(o),o=o.parent;for(o=(0,n8.hasChildren)(e)?e:e.parent;o;)r.unshift(o),o=o.parent;for(var s=Math.min(n.length,r.length),i=0;i<s&&n[i]===r[i];)i++;if(i===0)return 1;var a=n[i-1],l=a.children,c=n[i],u=r[i];return l.indexOf(c)>l.indexOf(u)?a===e?20:4:a===t?10:2}Ze.compareDocumentPosition=O5;function W1t(t){return t=t.filter(function(e,n,r){return!r.includes(e,n+1)}),t.sort(function(e,n){var r=O5(e,n);return r&2?-1:r&4?1:0}),t}Ze.uniqueSort=W1t;var qs={};Object.defineProperty(qs,"__esModule",{value:!0});qs.getFeed=void 0;var Q1t=Ft,Or=Ut;function K1t(t){var e=is(trt,t);return e?e.name==="feed"?Y1t(e):X1t(e):null}qs.getFeed=K1t;function Y1t(t){var e,n=t.children,r={type:"atom",items:(0,Or.getElementsByTagName)("entry",n).map(function(i){var a,l=i.children,c={media:V5(l)};Pt(c,"id","id",l),Pt(c,"title","title",l);var u=(a=is("link",l))===null||a===void 0?void 0:a.attribs.href;u&&(c.link=u);var f=wn("summary",l)||wn("content",l);f&&(c.description=f);var d=wn("updated",l);return d&&(c.pubDate=new Date(d)),c})};Pt(r,"id","id",n),Pt(r,"title","title",n);var o=(e=is("link",n))===null||e===void 0?void 0:e.attribs.href;o&&(r.link=o),Pt(r,"description","subtitle",n);var s=wn("updated",n);return s&&(r.updated=new Date(s)),Pt(r,"author","email",n,!0),r}function X1t(t){var e,n,r=(n=(e=is("channel",t.children))===null||e===void 0?void 0:e.children)!==null&&n!==void 0?n:[],o={type:t.name.substr(0,3),id:"",items:(0,Or.getElementsByTagName)("item",t.children).map(function(i){var a=i.children,l={media:V5(a)};Pt(l,"id","guid",a),Pt(l,"title","title",a),Pt(l,"link","link",a),Pt(l,"description","description",a);var c=wn("pubDate",a);return c&&(l.pubDate=new Date(c)),l})};Pt(o,"title","title",r),Pt(o,"link","link",r),Pt(o,"description","description",r);var s=wn("lastBuildDate",r);return s&&(o.updated=new Date(s)),Pt(o,"author","managingEditor",r,!0),o}var Z1t=["url","type","lang"],J1t=["fileSize","bitrate","framerate","samplingrate","channels","duration","height","width"];function V5(t){return(0,Or.getElementsByTagName)("media:content",t).map(function(e){for(var n=e.attribs,r={medium:n.medium,isDefault:!!n.isDefault},o=0,s=Z1t;o<s.length;o++){var i=s[o];n[i]&&(r[i]=n[i])}for(var a=0,l=J1t;a<l.length;a++){var i=l[a];n[i]&&(r[i]=parseInt(n[i],10))}return n.expression&&(r.expression=n.expression),r})}function is(t,e){return(0,Or.getElementsByTagName)(t,e,!0,1)[0]}function wn(t,e,n){return n===void 0&&(n=!1),(0,Q1t.textContent)((0,Or.getElementsByTagName)(t,e,n,1)).trim()}function Pt(t,e,n,r,o){o===void 0&&(o=!1);var s=wn(n,r,o);s&&(t[e]=s)}function trt(t){return t==="rss"||t==="feed"||t==="rdf:RDF"}(function(t){var e=L&&L.__createBinding||(Object.create?function(o,s,i,a){a===void 0&&(a=i),Object.defineProperty(o,a,{enumerable:!0,get:function(){return s[i]}})}:function(o,s,i,a){a===void 0&&(a=i),o[a]=s[i]}),n=L&&L.__exportStar||function(o,s){for(var i in o)i!=="default"&&!Object.prototype.hasOwnProperty.call(s,i)&&e(s,o,i)};Object.defineProperty(t,"__esModule",{value:!0}),t.hasChildren=t.isDocument=t.isComment=t.isText=t.isCDATA=t.isTag=void 0,n(Ft,t),n(dt,t),n(Nt,t),n(bt,t),n(Ut,t),n(Ze,t),n(qs,t);var r=Re;Object.defineProperty(t,"isTag",{enumerable:!0,get:function(){return r.isTag}}),Object.defineProperty(t,"isCDATA",{enumerable:!0,get:function(){return r.isCDATA}}),Object.defineProperty(t,"isText",{enumerable:!0,get:function(){return r.isText}}),Object.defineProperty(t,"isComment",{enumerable:!0,get:function(){return r.isComment}}),Object.defineProperty(t,"isDocument",{enumerable:!0,get:function(){return r.isDocument}}),Object.defineProperty(t,"hasChildren",{enumerable:!0,get:function(){return r.hasChildren}})})(yc);(function(t){var e=L&&L.__extends||function(){var l=function(c,u){return l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,d){f.__proto__=d}||function(f,d){for(var m in d)Object.prototype.hasOwnProperty.call(d,m)&&(f[m]=d[m])},l(c,u)};return function(c,u){if(typeof u!="function"&&u!==null)throw new TypeError("Class extends value "+String(u)+" is not a constructor or null");l(c,u);function f(){this.constructor=c}c.prototype=u===null?Object.create(u):(f.prototype=u.prototype,new f)}}(),n=L&&L.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(t,"__esModule",{value:!0}),t.parseFeed=t.FeedHandler=t.getFeed=void 0;var r=n(Re),o=yc;Object.defineProperty(t,"getFeed",{enumerable:!0,get:function(){return o.getFeed}});var s=Rr,i=function(l){e(c,l);function c(u,f){var d=this;return typeof u=="object"&&(u=void 0,f=u),d=l.call(this,u,f)||this,d}return c.prototype.onend=function(){var u=(0,o.getFeed)(this.dom);u?(this.feed=u,this.handleCallback(null)):this.handleCallback(new Error("couldn't find root of feed"))},c}(r.default);t.FeedHandler=i;function a(l,c){c===void 0&&(c={xmlMode:!0});var u=new r.default(null,c);return new s.Parser(u,c).end(l),(0,o.getFeed)(u.dom)}t.parseFeed=a})(qa);(function(t){var e=L&&L.__createBinding||(Object.create?function(y,v,x,p){p===void 0&&(p=x),Object.defineProperty(y,p,{enumerable:!0,get:function(){return v[x]}})}:function(y,v,x,p){p===void 0&&(p=x),y[p]=v[x]}),n=L&&L.__setModuleDefault||(Object.create?function(y,v){Object.defineProperty(y,"default",{enumerable:!0,value:v})}:function(y,v){y.default=v}),r=L&&L.__importStar||function(y){if(y&&y.__esModule)return y;var v={};if(y!=null)for(var x in y)x!=="default"&&Object.prototype.hasOwnProperty.call(y,x)&&e(v,y,x);return n(v,y),v},o=L&&L.__exportStar||function(y,v){for(var x in y)x!=="default"&&!Object.prototype.hasOwnProperty.call(v,x)&&e(v,y,x)},s=L&&L.__importDefault||function(y){return y&&y.__esModule?y:{default:y}};Object.defineProperty(t,"__esModule",{value:!0}),t.RssHandler=t.DefaultHandler=t.DomUtils=t.ElementType=t.Tokenizer=t.createDomStream=t.parseDOM=t.parseDocument=t.DomHandler=t.Parser=void 0;var i=Rr;Object.defineProperty(t,"Parser",{enumerable:!0,get:function(){return i.Parser}});var a=Re;Object.defineProperty(t,"DomHandler",{enumerable:!0,get:function(){return a.DomHandler}}),Object.defineProperty(t,"DefaultHandler",{enumerable:!0,get:function(){return a.DomHandler}});function l(y,v){var x=new a.DomHandler(void 0,v);return new i.Parser(x,v).end(y),x.root}t.parseDocument=l;function c(y,v){return l(y,v).children}t.parseDOM=c;function u(y,v,x){var p=new a.DomHandler(y,v,x);return new i.Parser(p,v)}t.createDomStream=u;var f=Ls;Object.defineProperty(t,"Tokenizer",{enumerable:!0,get:function(){return s(f).default}});var d=r(f5);t.ElementType=d,o(qa,t),t.DomUtils=r(yc);var m=qa;Object.defineProperty(t,"RssHandler",{enumerable:!0,get:function(){return m.FeedHandler}})})(Z2);var ert=(t,e)=>{var[n,r]=$.useState(null);return $.useEffect(()=>{var o=!1;function s(){return i.apply(this,arguments)}function i(){return i=k7(function*(){try{var a=yield fetch(t).then(l=>l.text()).then(l=>Z2.parseFeed(l));o||r(a)}catch(l){console.error("Unable to get data from ".concat(t),l)}}),i.apply(this,arguments)}return s(),()=>{o=!0}},[t]),n},nrt={exports:{}};(function(t){(function(){function e(v,x){document.addEventListener?v.addEventListener("scroll",x,!1):v.attachEvent("scroll",x)}function n(v){document.body?v():document.addEventListener?document.addEventListener("DOMContentLoaded",function x(){document.removeEventListener("DOMContentLoaded",x),v()}):document.attachEvent("onreadystatechange",function x(){(document.readyState=="interactive"||document.readyState=="complete")&&(document.detachEvent("onreadystatechange",x),v())})}function r(v){this.g=document.createElement("div"),this.g.setAttribute("aria-hidden","true"),this.g.appendChild(document.createTextNode(v)),this.h=document.createElement("span"),this.i=document.createElement("span"),this.m=document.createElement("span"),this.j=document.createElement("span"),this.l=-1,this.h.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.i.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.j.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.m.style.cssText="display:inline-block;width:200%;height:200%;font-size:16px;max-width:none;",this.h.appendChild(this.m),this.i.appendChild(this.j),this.g.appendChild(this.h),this.g.appendChild(this.i)}function o(v,x){v.g.style.cssText="max-width:none;min-width:20px;min-height:20px;display:inline-block;overflow:hidden;position:absolute;width:auto;margin:0;padding:0;top:-999px;white-space:nowrap;font-synthesis:none;font:"+x+";"}function s(v){var x=v.g.offsetWidth,p=x+100;return v.j.style.width=p+"px",v.i.scrollLeft=p,v.h.scrollLeft=v.h.scrollWidth+100,v.l!==x?(v.l=x,!0):!1}function i(v,x){function p(){var h=g;s(h)&&h.g.parentNode!==null&&x(h.l)}var g=v;e(v.h,p),e(v.i,p),s(v)}function a(v,x,p){x=x||{},p=p||window,this.family=v,this.style=x.style||"normal",this.weight=x.weight||"normal",this.stretch=x.stretch||"normal",this.context=p}var l=null,c=null,u=null,f=null;function d(v){return c===null&&(m(v)&&/Apple/.test(window.navigator.vendor)?(v=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))(?:\.([0-9]+))/.exec(window.navigator.userAgent),c=!!v&&603>parseInt(v[1],10)):c=!1),c}function m(v){return f===null&&(f=!!v.document.fonts),f}function y(v,x){var p=v.style,g=v.weight;if(u===null){var h=document.createElement("div");try{h.style.font="condensed 100px sans-serif"}catch{}u=h.style.font!==""}return[p,g,u?v.stretch:"","100px",x].join(" ")}a.prototype.load=function(v,x){var p=this,g=v||"BESbswy",h=0,w=x||3e3,S=new Date().getTime();return new Promise(function(E,b){if(m(p.context)&&!d(p.context)){var _=new Promise(function(A,O){function W(){new Date().getTime()-S>=w?O(Error(""+w+"ms timeout exceeded")):p.context.document.fonts.load(y(p,'"'+p.family+'"'),g).then(function(ut){1<=ut.length?A():setTimeout(W,25)},O)}W()}),M=new Promise(function(A,O){h=setTimeout(function(){O(Error(""+w+"ms timeout exceeded"))},w)});Promise.race([M,_]).then(function(){clearTimeout(h),E(p)},b)}else n(function(){function A(){var V;(V=B!=-1&&Q!=-1||B!=-1&&st!=-1||Q!=-1&&st!=-1)&&((V=B!=Q&&B!=st&&Q!=st)||(l===null&&(V=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))/.exec(window.navigator.userAgent),l=!!V&&(536>parseInt(V[1],10)||parseInt(V[1],10)===536&&11>=parseInt(V[2],10))),V=l&&(B==k&&Q==k&&st==k||B==N&&Q==N&&st==N||B==R&&Q==R&&st==R)),V=!V),V&&(j.parentNode!==null&&j.parentNode.removeChild(j),clearTimeout(h),E(p))}function O(){if(new Date().getTime()-S>=w)j.parentNode!==null&&j.parentNode.removeChild(j),b(Error(""+w+"ms timeout exceeded"));else{var V=p.context.document.hidden;(V===!0||V===void 0)&&(B=W.g.offsetWidth,Q=ut.g.offsetWidth,st=Tt.g.offsetWidth,A()),h=setTimeout(O,50)}}var W=new r(g),ut=new r(g),Tt=new r(g),B=-1,Q=-1,st=-1,k=-1,N=-1,R=-1,j=document.createElement("div");j.dir="ltr",o(W,y(p,"sans-serif")),o(ut,y(p,"serif")),o(Tt,y(p,"monospace")),j.appendChild(W.g),j.appendChild(ut.g),j.appendChild(Tt.g),p.context.document.body.appendChild(j),k=W.g.offsetWidth,N=ut.g.offsetWidth,R=Tt.g.offsetWidth,O(),i(W,function(V){B=V,A()}),o(W,y(p,'"'+p.family+'",sans-serif')),i(ut,function(V){Q=V,A()}),o(ut,y(p,'"'+p.family+'",serif')),i(Tt,function(V){st=V,A()}),o(Tt,y(p,'"'+p.family+'",monospace'))})})},t.exports=a})()})(nrt);function rrt(){var n;const{isPlaying:t}=Nf(),e=ert("/templates/tickerV1/54nAGcIl.xml");return Rn.jsx(im,{children:Rn.jsx(Y2.div,{children:Rn.jsx("div",{className:"news-ticker",children:Rn.jsx("div",{className:"content",children:Rn.jsx(am,{play:t,items:(n=e==null?void 0:e.items)==null?void 0:n.slice(0,10),renderItem:({id:r,title:o})=>Rn.jsx("div",{className:"item",children:o},r)})})})})})}C7(rrt);

</script>
    <style>
.news-ticker{position:fixed;bottom:0;width:100%;height:75px;background-color:#fff;z-index:9999;overflow:hidden}.content{display:flex;align-items:center;font-size:40px;font-family:Arial,Helvetica,sans-serif;color:#000;padding:10px}.item{margin-right:50px;white-space:nowrap}

</style>
  </head>
  <body>
  </body>
</html>
