
# nxt-gfx

[Caspar Graphics](https://gfx.nxtedition.com) is a tool for building, testing and packaging HTML graphics for CasparCG using code. It also includes building blocks for some common use cases.

It is built & developed in 2019 by the [nxtedition](https://nxtedition.com/) team.
## Setup

To run tests, run the following command

```bash
  npm install
  npm run dev  
```
Open http://localhost:8080
## Build for CasparCG

```bash
  npm run build
```
This will take the graphic(s) and create an optimized bundle of each, and put them into a /dist folder in the project's root:

```
  my-graphics
  └── templates
      └── example
          └── index.html
          └── manifest.json
  └── dist
      └── example
          └── index.html
  ├── node_modules
  ├── package.json
  ├── .gitignore
```
## 

If you're not me and you have a question, you can contact me using the form [here](https://oblivionmedia.typeform.com/to/EwQYqmPa).

