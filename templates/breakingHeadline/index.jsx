import React from 'react';
import { render, GsapTimeline, useCasparData } from '@nxtedition/graphics-kit';
import './style.css';

const Headline = () => {
  const { heading, subText, type } = useCasparData(); // Retrieve heading, subText, and type from Caspar data

  // Determine the class name or inline style based on the type
  const headingStyle = type === 'breaking' ? 'heading breaking' : 'heading default';

  return (
    <GsapTimeline
      onPlay={(timeline) => {
        timeline.from('.headline-Container', { y: 250, duration: 0.5 });
      }}
      onStop={(timeline) => {
        timeline.to('.headline-Container', { y: 250, duration: 0.3 });
      }}
    >
      <div className="headline-Container">
        <span className={headingStyle}>{heading}</span>
        <span className="subText">{subText}</span>
      </div>
    </GsapTimeline>
  );
};

render(Headline);
