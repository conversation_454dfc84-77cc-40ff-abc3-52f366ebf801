.nameStrapDouble-Container {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 19.5%;
  background-color: white;
  flex-direction: row;
}

.nameStrapDouble-content {
  display: table; /* Use table layout */
  width: 100%;
  padding-top: 1%;
  font-family: sans-serif;
  font-size: 70px;
  font-weight: 600;
  color: #1e2832;
}

.name1, .name2 {
  display: table-cell; /* Each name acts as a table cell */
  width: 50%; /* Split the width equally */
  white-space: nowrap; /* Prevent text from wrapping */
  text-align: center; /* Left-align the first name */
}