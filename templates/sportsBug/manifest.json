{"name": "sportsBug", "width": 1920, "height": 1080, "schema": {"clockMode": {"type": "string", "label": "Clock Mode (Manual, Timer, Countdown)", "enum": ["manual", "timer", "countdown"], "default": "manual"}, "clock": {"type": "string", "label": "Clock (Manual Mode)"}, "timerStartTime": {"type": "string", "label": "Timer Start Time (MM:SS) (Timer Mode)"}, "countdownTime": {"type": "string", "label": "Countdown Time (MM:SS) (Countdown Mode)"}, "timerRunning": {"type": "boolean", "label": "Timer Running?", "default": false}, "homeTeam": {"type": "string", "label": "Home Team"}, "homeScore": {"type": "string", "label": "Home Score"}, "homeColor": {"type": "string", "label": "Home Colour"}, "awayTeam": {"type": "string", "label": "Away Team"}, "awayScore": {"type": "string", "label": "Away Score"}, "awayColor": {"type": "string", "label": "Away Colour"}, "extraTime": {"type": "boolean", "label": "Extra Time?"}, "extraTimeClockMode": {"type": "string", "label": "Extra Time Clock Mode (Manual, Timer, Countdown)", "enum": ["manual", "timer", "countdown"], "default": "manual"}, "extraTimeClock": {"type": "string", "label": "Extra Time Clock (Manual Mode)"}, "extraTimeTimerStartTime": {"type": "string", "label": "Extra Time Timer Start (MM:SS) (Timer Mode)"}, "extraTimeCountdownTime": {"type": "string", "label": "Extra Time Countdown (MM:SS) (Countdown Mode)"}, "extraTimeTimerRunning": {"type": "boolean", "label": "Extra Time Timer Running?", "default": false}, "extraTimeAmount": {"type": "string", "label": "Extra Time Amount"}}, "previewImages": ["./IMG_2894.JPG"]}