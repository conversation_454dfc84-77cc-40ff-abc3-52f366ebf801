# Enhanced SportsBug Features

## Overview

The SportsBug has been enhanced with dual clock functionality and a visual design that matches professional broadcast standards. Both the main clock and extra time clock now support multiple timing modes.

## Visual Design Updates

### Layout Matching Professional Standards
- **Horizontal Layout**: Clock on left, team information on right
- **Seamless Design**: No gaps or rounded corners for professional appearance
- **Color Scheme**: Light gray clock background, blue team section
- **Red Separator**: Visual divider between clock and team sections
- **Typography**: Larger, bold fonts with proper letter spacing

### Design Elements
- **Main Clock**: 90:00 format in light gray container
- **Team Section**: Blue background with team colors, names, and scores
- **Team Names**: Uppercase, abbreviated format (e.g., MUN, CPH)
- **Scores**: Large, prominent display
- **Color Stripes**: Thin team color indicators

## Dual Clock Functionality

### Main Clock Modes

#### Manual Mode
```json
{
  "clockMode": "manual",
  "clock": "90:00"
}
```

#### Timer Mode (Count Up)
```json
{
  "clockMode": "timer",
  "timerStartTime": "00:00",
  "timerRunning": true
}
```

#### Countdown Mode
```json
{
  "clockMode": "countdown",
  "countdownTime": "45:00",
  "timerRunning": true
}
```

### Extra Time Clock Modes

#### Manual Extra Time
```json
{
  "extraTime": true,
  "extraTimeClockMode": "manual",
  "extraTimeClock": "90:00",
  "extraTimeAmount": "3"
}
```

#### Timer Extra Time (Count Up)
```json
{
  "extraTime": true,
  "extraTimeClockMode": "timer",
  "extraTimeTimerStartTime": "90:00",
  "extraTimeTimerRunning": true,
  "extraTimeAmount": "3"
}
```

#### Countdown Extra Time
```json
{
  "extraTime": true,
  "extraTimeClockMode": "countdown",
  "extraTimeCountdownTime": "03:00",
  "extraTimeTimerRunning": true,
  "extraTimeAmount": "3"
}
```

## Use Case Examples

### Soccer Match Workflow

#### First Half (0-45 minutes)
```json
{
  "clockMode": "timer",
  "timerStartTime": "00:00",
  "timerRunning": true,
  "extraTime": false,
  "homeTeam": "MUN",
  "awayTeam": "CPH",
  "homeScore": "1",
  "awayScore": "1",
  "homeColor": "#ff0000",
  "awayColor": "#0066cc"
}
```

#### First Half Injury Time
```json
{
  "clockMode": "manual",
  "clock": "45:00",
  "extraTime": true,
  "extraTimeClockMode": "timer",
  "extraTimeTimerStartTime": "45:00",
  "extraTimeTimerRunning": true,
  "extraTimeAmount": "3"
}
```

#### Half Time
```json
{
  "clockMode": "manual",
  "clock": "45:00",
  "timerRunning": false,
  "extraTime": false
}
```

#### Second Half (45-90 minutes)
```json
{
  "clockMode": "timer",
  "timerStartTime": "45:00",
  "timerRunning": true,
  "extraTime": false
}
```

#### Second Half Injury Time
```json
{
  "clockMode": "manual",
  "clock": "90:00",
  "extraTime": true,
  "extraTimeClockMode": "timer",
  "extraTimeTimerStartTime": "90:00",
  "extraTimeTimerRunning": true,
  "extraTimeAmount": "5"
}
```

### Basketball Game Workflow

#### Quarter Countdown
```json
{
  "clockMode": "countdown",
  "countdownTime": "12:00",
  "timerRunning": true,
  "extraTime": false
}
```

#### Shot Clock (Extra Time as Shot Clock)
```json
{
  "clockMode": "countdown",
  "countdownTime": "05:30",
  "timerRunning": true,
  "extraTime": true,
  "extraTimeClockMode": "countdown",
  "extraTimeCountdownTime": "24:00",
  "extraTimeTimerRunning": true,
  "extraTimeAmount": "24"
}
```

## Technical Implementation

### Independent Timer Management
- Main clock and extra time clock operate independently
- Each has its own timer state and controls
- Separate start/stop/reset functionality
- No interference between timers

### Performance Optimizations
- Hardware-accelerated animations
- Efficient timer management with proper cleanup
- Memoized calculations to prevent unnecessary re-renders
- Optimized CSS for smooth performance

### Animation Features
- Score change highlighting (yellow flash)
- Smooth extra time show/hide animations
- Professional slide-in/out transitions
- Staggered element animations

## Control Parameters

### Main Clock Parameters
- `clockMode`: "manual" | "timer" | "countdown"
- `clock`: Manual time display (MM:SS)
- `timerStartTime`: Timer start time (MM:SS)
- `countdownTime`: Countdown start time (MM:SS)
- `timerRunning`: Boolean control for timer

### Extra Time Parameters
- `extraTime`: Boolean to show/hide extra time
- `extraTimeClockMode`: "manual" | "timer" | "countdown"
- `extraTimeClock`: Manual extra time display (MM:SS)
- `extraTimeTimerStartTime`: Extra time timer start (MM:SS)
- `extraTimeCountdownTime`: Extra time countdown start (MM:SS)
- `extraTimeTimerRunning`: Boolean control for extra time timer
- `extraTimeAmount`: Additional time amount display

### Team Parameters
- `homeTeam`: Home team name (abbreviated)
- `awayTeam`: Away team name (abbreviated)
- `homeScore`: Home team score
- `awayScore`: Away team score
- `homeColor`: Home team color (hex or named)
- `awayColor`: Away team color (hex or named)

## Best Practices

### Operator Guidelines
1. Use abbreviated team names (3-4 characters max)
2. Test timer functionality before going live
3. Have manual mode as backup for critical timing
4. Monitor timer accuracy against official clocks
5. Use appropriate clock modes for different sports

### Technical Considerations
1. Timer accuracy depends on browser performance
2. Long-running timers may drift slightly over time
3. Always sync with official timing when possible
4. Use manual mode for critical timing situations
5. Test all clock modes during rehearsals

### Visual Guidelines
1. Ensure team colors have sufficient contrast
2. Keep team names short for optimal display
3. Verify color combinations work on broadcast monitors
4. Test visibility at different broadcast resolutions

## Integration Notes

### CasparCG Compatibility
- All parameters controllable via CasparCG data interface
- Real-time updates without graphic restart
- Compatible with existing CasparCG workflows
- Supports automation and scripting

### External System Integration
- Manual modes allow integration with official timing systems
- Timer modes provide backup/secondary timing
- Can switch between modes during broadcast
- Supports external trigger systems
