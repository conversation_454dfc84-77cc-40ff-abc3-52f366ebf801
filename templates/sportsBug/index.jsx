import { useEffect, useRef, useMemo, useCallback, useState } from 'react';
import { render, GsapTimeline, useCasparData } from '@nxtedition/graphics-kit';
import gsap from 'gsap';
import './style.css';

const SportsGraphic = () => {
  const casparData = useCasparData();
  const {
    clockMode = 'manual',
    clock,
    timerStartTime,
    countdownTime,
    timerRunning = false,
    homeTeam,
    awayTeam,
    homeScore,
    awayScore,
    homeColor,
    awayColor,
    extraTime,
    extraTimeClock,
    extraTimeAmount
  } = casparData;

  // State for internal timer management
  const [internalTime, setInternalTime] = useState(0); // seconds
  const [lastUpdateTime, setLastUpdateTime] = useState(Date.now());

  // Refs for the extra time and clock elements
  const clockRef = useRef(null);
  const extraTimeRef = useRef(null);
  const timelineRef = useRef(null);
  const homeScoreRef = useRef(null);
  const awayScoreRef = useRef(null);
  const prevScoresRef = useRef({ homeScore: null, awayScore: null });

  // Data validation helpers
  const validateColor = useCallback((color) => {
    if (!color) return '#cccccc';
    // Basic hex color validation
    if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color)) {
      return color;
    }
    // Named colors validation (basic set)
    const namedColors = ['red', 'blue', 'green', 'yellow', 'orange', 'purple', 'black', 'white'];
    if (namedColors.includes(color.toLowerCase())) {
      return color.toLowerCase();
    }
    return '#cccccc';
  }, []);

  const validateScore = useCallback((score) => {
    if (score === null || score === undefined) return '0';
    const scoreStr = String(score).trim();
    // Allow numbers and basic score formats like "2-1", "3(2)" etc.
    if (/^[\d\-\(\)\s]*$/.test(scoreStr) && scoreStr.length <= 10) {
      return scoreStr || '0';
    }
    return '0';
  }, []);

  const validateTeamName = useCallback((teamName, fallback) => {
    if (!teamName || typeof teamName !== 'string') return fallback;
    const trimmed = teamName.trim();
    // Limit team name length and sanitize
    return trimmed.length > 0 && trimmed.length <= 20 ? trimmed : fallback;
  }, []);

  const validateTime = useCallback((time) => {
    if (!time || typeof time !== 'string') return '00:00';
    const trimmed = time.trim();
    // Basic time format validation (MM:SS or HH:MM:SS)
    if (/^\d{1,2}:\d{2}(:\d{2})?$/.test(trimmed)) {
      return trimmed;
    }
    return '00:00';
  }, []);

  // Convert time string (MM:SS) to seconds
  const timeStringToSeconds = useCallback((timeStr) => {
    if (!timeStr) return 0;
    const parts = timeStr.split(':');
    if (parts.length === 2) {
      const minutes = parseInt(parts[0], 10) || 0;
      const seconds = parseInt(parts[1], 10) || 0;
      return minutes * 60 + seconds;
    }
    return 0;
  }, []);

  // Convert seconds to time string (MM:SS)
  const secondsToTimeString = useCallback((totalSeconds) => {
    const minutes = Math.floor(Math.abs(totalSeconds) / 60);
    const seconds = Math.abs(totalSeconds) % 60;
    const sign = totalSeconds < 0 ? '-' : '';
    return `${sign}${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  // Timer management effect
  useEffect(() => {
    let intervalId;

    if (clockMode === 'timer' && timerRunning) {
      // Initialize timer if starting
      if (internalTime === 0 && timerStartTime) {
        setInternalTime(timeStringToSeconds(timerStartTime));
      }

      intervalId = setInterval(() => {
        setInternalTime(prev => prev + 1);
        setLastUpdateTime(Date.now());
      }, 1000);
    } else if (clockMode === 'countdown' && timerRunning) {
      // Initialize countdown if starting
      if (internalTime === 0 && countdownTime) {
        setInternalTime(timeStringToSeconds(countdownTime));
      }

      intervalId = setInterval(() => {
        setInternalTime(prev => {
          const newTime = prev - 1;
          return newTime >= 0 ? newTime : 0; // Don't go below 0
        });
        setLastUpdateTime(Date.now());
      }, 1000);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [clockMode, timerRunning, timerStartTime, countdownTime, internalTime, timeStringToSeconds]);

  // Reset timer when mode changes or start time changes
  useEffect(() => {
    if (clockMode === 'timer' && timerStartTime) {
      setInternalTime(timeStringToSeconds(timerStartTime));
    } else if (clockMode === 'countdown' && countdownTime) {
      setInternalTime(timeStringToSeconds(countdownTime));
    } else if (clockMode === 'manual') {
      setInternalTime(0);
    }
  }, [clockMode, timerStartTime, countdownTime, timeStringToSeconds]);

  // Get the display time based on clock mode
  const getDisplayTime = useCallback(() => {
    switch (clockMode) {
      case 'timer':
        return secondsToTimeString(internalTime);
      case 'countdown':
        return secondsToTimeString(internalTime);
      case 'manual':
      default:
        return validateTime(clock);
    }
  }, [clockMode, internalTime, clock, secondsToTimeString, validateTime]);

  // Memoize data validation to prevent unnecessary re-renders
  const validatedData = useMemo(() => {
    try {
      return {
        clock: getDisplayTime(),
        homeTeam: validateTeamName(homeTeam, 'HOME'),
        awayTeam: validateTeamName(awayTeam, 'AWAY'),
        homeScore: validateScore(homeScore),
        awayScore: validateScore(awayScore),
        homeColor: validateColor(homeColor),
        awayColor: validateColor(awayColor),
        extraTime: Boolean(extraTime),
        extraTimeClock: validateTime(extraTimeClock),
        extraTimeAmount: validateScore(extraTimeAmount)
      };
    } catch (error) {
      console.warn('SportsBug: Error validating data, using fallbacks:', error);
      return {
        clock: '00:00',
        homeTeam: 'HOME',
        awayTeam: 'AWAY',
        homeScore: '0',
        awayScore: '0',
        homeColor: '#cccccc',
        awayColor: '#cccccc',
        extraTime: false,
        extraTimeClock: '00:00',
        extraTimeAmount: '0'
      };
    }
  }, [getDisplayTime, homeTeam, awayTeam, homeScore, awayScore, homeColor, awayColor, extraTime, extraTimeClock, extraTimeAmount, validateColor, validateScore, validateTeamName, validateTime]);

  // Enhanced extra time animation with better state management
  const animateExtraTime = useCallback((show, immediate = false) => {
    if (!extraTimeRef.current) return;

    // Kill any existing animations on this element
    gsap.killTweensOf(extraTimeRef.current);

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    const duration = immediate ? 0 : 0.6;

    timelineRef.current = gsap.timeline({
      defaults: { duration, ease: 'power2.out' }
    });

    if (show) {
      // Set initial state if showing immediately
      if (immediate) {
        gsap.set(extraTimeRef.current, { y: 0, opacity: 1, scale: 1 });
      } else {
        // Animate in with enhanced easing and stagger effect
        timelineRef.current
          .fromTo(extraTimeRef.current,
            { y: 40, opacity: 0, scale: 0.8 },
            { y: 0, opacity: 1, scale: 1, ease: 'back.out(1.4)' }
          )
          .from('.extra-time-clock, .extra-time-plus', {
            y: 20,
            opacity: 0,
            stagger: 0.1,
            duration: 0.4,
            ease: 'power2.out'
          }, '-=0.3');
      }
    } else {
      // Animate out with improved easing
      if (immediate) {
        gsap.set(extraTimeRef.current, { y: 40, opacity: 0, scale: 0.8 });
      } else {
        timelineRef.current
          .to('.extra-time-clock, .extra-time-plus', {
            y: -10,
            opacity: 0,
            stagger: 0.05,
            duration: 0.2,
            ease: 'power2.in'
          })
          .to(extraTimeRef.current, {
            y: 40,
            opacity: 0,
            scale: 0.8,
            ease: 'power2.in',
            duration: 0.3
          }, '-=0.1');
      }
    }
  }, []);

  // Animate score changes with a highlight effect
  const animateScoreChange = useCallback((scoreRef, newScore, oldScore) => {
    if (!scoreRef.current || newScore === oldScore) return;

    gsap.killTweensOf(scoreRef.current);

    const tl = gsap.timeline();

    // Highlight animation for score change
    tl.to(scoreRef.current, {
      scale: 1.2,
      backgroundColor: '#ffff00',
      color: '#000000',
      duration: 0.3,
      ease: 'power2.out'
    })
    .to(scoreRef.current, {
      scale: 1,
      backgroundColor: 'transparent',
      color: 'white',
      duration: 0.4,
      ease: 'power2.out'
    });
  }, []);

  // Use GSAP to animate the extra time section when the `extraTime` boolean changes
  useEffect(() => {
    animateExtraTime(validatedData.extraTime);

    // Cleanup function
    return () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
    };
  }, [validatedData.extraTime, animateExtraTime]);

  // Animate score changes
  useEffect(() => {
    const prevScores = prevScoresRef.current;

    if (prevScores.homeScore !== null && prevScores.homeScore !== validatedData.homeScore) {
      animateScoreChange(homeScoreRef, validatedData.homeScore, prevScores.homeScore);
    }

    if (prevScores.awayScore !== null && prevScores.awayScore !== validatedData.awayScore) {
      animateScoreChange(awayScoreRef, validatedData.awayScore, prevScores.awayScore);
    }

    // Update previous scores
    prevScoresRef.current = {
      homeScore: validatedData.homeScore,
      awayScore: validatedData.awayScore
    };
  }, [validatedData.homeScore, validatedData.awayScore, animateScoreChange]);

  // Enhanced animation callbacks with better performance
  const handlePlay = useCallback((timeline) => {
    // Set initial state for extra time based on current state
    if (extraTimeRef.current) {
      gsap.set(extraTimeRef.current, {
        y: validatedData.extraTime ? 0 : 30,
        opacity: validatedData.extraTime ? 1 : 0,
        scale: validatedData.extraTime ? 1 : 0.95
      });
    }

    // Slide in the main container with improved easing
    timeline.from('.graphic-container', {
      x: '-100%',
      duration: 0.6,
      ease: 'power2.out'
    });
  }, [validatedData.extraTime]);

  const handleStop = useCallback((timeline) => {
    // Slide out the main container with improved easing
    timeline.to('.graphic-container', {
      x: '-100%',
      duration: 0.4,
      ease: 'power2.in'
    });
  }, []);

  // Early return if essential data is missing
  if (!validatedData.homeTeam && !validatedData.awayTeam) {
    return null;
  }

  return (
    <GsapTimeline
      onPlay={handlePlay}
      onStop={handleStop}
    >
      <div className="graphic-container">
        {/* Clock Section */}
        <div className="clock-container" ref={clockRef}>
          <span className="clock-text">{validatedData.clock}</span>
        </div>

        {/* Extra Time Section - Always render but control visibility via animation */}
        <div
          className="extra-time-container"
          ref={extraTimeRef}
          style={{
            transform: 'translateY(40px)',
            opacity: 0,
            scale: 0.8
          }}
        >
          <div className="extra-time-clock">
            {validatedData.extraTimeClock}
          </div>
          <div className="extra-time-plus">
            + {validatedData.extraTimeAmount}
          </div>
        </div>

        {/* Score Section */}
        <div className="score-container">
          {/* Home Team Section */}
          <div
            className="team-color home-color"
            style={{ backgroundColor: validatedData.homeColor }}
            aria-label={`${validatedData.homeTeam} team color`}
          />
          <span className="team-name home-team" title={validatedData.homeTeam}>
            {validatedData.homeTeam}
          </span>
          <span
            ref={homeScoreRef}
            className="team-score home-score"
            aria-label={`${validatedData.homeTeam} score`}
          >
            {validatedData.homeScore}
          </span>

          {/* Divider */}
          <span className="divider" aria-hidden="true">-</span>

          {/* Away Team Section */}
          <span
            ref={awayScoreRef}
            className="team-score away-score"
            aria-label={`${validatedData.awayTeam} score`}
          >
            {validatedData.awayScore}
          </span>
          <span className="team-name away-team" title={validatedData.awayTeam}>
            {validatedData.awayTeam}
          </span>
          <div
            className="team-color away-color"
            style={{ backgroundColor: validatedData.awayColor }}
            aria-label={`${validatedData.awayTeam} team color`}
          />
        </div>
      </div>
    </GsapTimeline>
  );
};

render(SportsGraphic);
