import React, { useEffect, useRef } from 'react';
import { render, GsapTimeline, useCasparData } from '@nxtedition/graphics-kit';
import gsap from 'gsap';
import './style.css';

const SportsGraphic = () => {
  const { clock, homeTeam, awayTeam, homeScore, awayScore, homeColor, awayColor, extraTime, extraTimeClock, extraTimeAmount } = useCasparData();

  // Refs for the extra time and clock elements
  const clockRef = useRef(null);
  const extraTimeRef = useRef(null);

  // Use GSAP to animate the extra time section when the `extraTime` boolean changes
  useEffect(() => {
    const tl = gsap.timeline({ defaults: { duration: 0.5 } });

    if (extraTime) {
      // Slide in the extra time
      tl.to(extraTimeRef.current, { y: 0, opacity: 1, ease: 'power2.out' }, 0);
    } else {
      // Slide out the extra time
      tl.to(extraTimeRef.current, { y: 50, opacity: 0, ease: 'power2.in' }, 0);
    }
  }, [extraTime]);

  return (
    <GsapTimeline
      onPlay={(timeline) => {        
        timeline.from('.graphic-container', { x: '-100%', duration: 0.5 });
      }}
      onStop={(timeline) => {
        timeline.to('.graphic-container', { x: '-100%', duration: 0.3 });
      }}
    >
      <div className="graphic-container">
        {/* Clock Section */}
        <div className="clock-container" ref={clockRef}>
          {clock}
        </div>

        {/* Extra Time Section */}
        <div className="extra-time-container" ref={extraTimeRef}>
          <div className="extra-time-clock">{extraTimeClock}</div>
          <div className="extra-time-plus">+ {extraTimeAmount}</div>
        </div>

        {/* Score Section */}
        <div className="score-container">
          {/* Home Team Section */}
          <div
            className="team-color home-color"
            style={{ backgroundColor: homeColor || '#cccccc' }}
          ></div>
          <span className="team-name">{homeTeam}</span>
          <span className="team-score">{homeScore}</span>

          {/* Divider */}
          <span className="divider">-</span>

          {/* Away Team Section */}
          <span className="team-score">{awayScore}</span>
          <span className="team-name">{awayTeam}</span>
          <div
            className="team-color away-color"
            style={{ backgroundColor: awayColor || '#cccccc' }}
          ></div>
        </div>
      </div>
    </GsapTimeline>
  );
};

render(SportsGraphic);
