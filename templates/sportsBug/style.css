/* Graphic container for clock and score */
.graphic-container {
  position: fixed;
  top: 50px;
  left: 96px;
  display: flex;
  align-items: center;
  gap: 0; /* Remove gap for seamless connection */
  height: 60px; /* Increased height to match image */
  will-change: transform; /* Optimize for animations */
  backface-visibility: hidden; /* Prevent flickering */
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Clock container styles */
.clock-container {
  position: relative;
  background-color: #f0f0f0; /* Light gray background like in image */
  color: black;
  font-family: 'Arial', sans-serif;
  font-size: 32px; /* Larger font to match image */
  font-weight: bold;
  padding: 0 20px;
  border-radius: 0; /* Remove border radius for seamless connection */
  box-shadow: none; /* Remove shadow for flat design */
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-width: 120px; /* Wider to accommodate larger text */
  will-change: transform;
  backface-visibility: hidden;
}

.clock-text {
  display: block;
  line-height: 1;
  letter-spacing: 0.5px;
}

/* Extra time container styles */
.extra-time-container {
  position: absolute;
  top: calc(100% + 8px); /* Positioned below the clock with better spacing */
  left: 0;
  display: flex;
  align-items: center;
  gap: 6px;
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform: translateZ(0);
  z-index: 10;
}

/* Extra time clock styles */
.extra-time-clock {
  background-color: white;
  color: black;
  font-family: 'Arial', sans-serif;
  font-size: 22px;
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 4px;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
  line-height: 1;
  letter-spacing: 0.5px;
  will-change: transform;
  backface-visibility: hidden;
}

/* Extra time plus styles */
.extra-time-plus {
  background-color: #001f3f;
  color: white;
  font-family: 'Arial', sans-serif;
  font-size: 22px;
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 4px;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.2);
  line-height: 1;
  letter-spacing: 0.5px;
  will-change: transform;
  backface-visibility: hidden;
}

/* Score container styles */
.score-container {
  display: flex;
  align-items: center;
  background-color: #1e3a8a; /* Blue background like in image */
  color: white;
  font-family: 'Arial', sans-serif;
  font-size: 32px; /* Larger font to match image */
  font-weight: bold;
  border-radius: 0; /* Remove border radius for seamless connection */
  box-shadow: none; /* Remove shadow for flat design */
  height: 100%;
  overflow: hidden;
  will-change: transform;
  backface-visibility: hidden;
  min-width: 200px; /* Ensure adequate width for team names and scores */
}

/* Team color stripe styles */
.team-color {
  width: 8px; /* Thinner stripes like in image */
  height: 100%;
  flex-shrink: 0;
  will-change: transform;
  backface-visibility: hidden;
}

.home-color {
  border-radius: 0; /* No border radius for flat design */
}

.away-color {
  border-radius: 0; /* No border radius for flat design */
}

/* Team name styles */
.team-name {
  margin: 0 8px; /* Reduced margin for tighter layout */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60px; /* Shorter team names like in image (MUN, CPH) */
  line-height: 1;
  letter-spacing: 1px; /* Increased letter spacing for better readability */
  will-change: transform;
  backface-visibility: hidden;
  font-weight: bold;
  text-transform: uppercase; /* Uppercase like in image */
}

.home-team {
  text-align: right;
}

.away-team {
  text-align: left;
}

/* Score styles */
.team-score {
  margin: 0 6px; /* Tighter spacing */
  font-weight: 900;
  min-width: 32px; /* Wider for larger scores */
  text-align: center;
  line-height: 1;
  letter-spacing: 0.5px;
  will-change: transform;
  backface-visibility: hidden;
  font-size: 36px; /* Larger scores to match image */
}

/* Divider style - hidden in the image layout */
.divider {
  display: none; /* Hide divider to match image layout */
}

/* Red separator between clock and score */
.red-separator {
  width: 4px;
  height: 100%;
  background-color: #dc2626; /* Red separator like in image */
  flex-shrink: 0;
}

/* Performance optimizations */
* {
  box-sizing: border-box;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .graphic-container,
  .extra-time-container,
  .clock-container,
  .score-container {
    transition: none !important;
    animation: none !important;
  }
}
