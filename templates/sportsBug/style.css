/* Graphic container for clock and score */
.graphic-container {
  position: fixed;
  top: 50px;
  left: 96px;
  display: flex;
  align-items: center;
  gap: 10px;
  height: 50px;
}

/* Clock container styles */
.clock-container {
  background-color: white;
  color: black;
  font-family: sans-serif;
  font-size: 28px;
  font-weight: bold;
  padding: 0 15px;
  border-radius: 5px;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  height: 100%;
}

/* Extra time container styles */
.extra-time-container {
  position: absolute;
  top: 120%; /* Positioned below the clock */
  left: 0;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Extra time clock styles */
.extra-time-clock {
  background-color: white;
  color: black;
  font-family: sans-serif;
  font-size: 24px;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 5px;
}

/* Extra time plus styles */
.extra-time-plus {
  background-color: #001f3f;
  color: white;
  font-family: sans-serif;
  font-size: 24px;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 5px;
}

/* Score container styles */
.score-container {
  display: flex;
  align-items: center;
  background-color: #001f3f;
  color: white;
  font-family: sans-serif;
  font-size: 28px;
  font-weight: bold;
  /* padding: 0 15px; */
  border-radius: 5px;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
  height: 100%;
}

/* Team color stripe styles */
.team-color {
  width: 10px;
  height: 100%;
}

/* Team name styles */
.team-name {
  margin: 0 10px;
  white-space: nowrap;
}

/* Score styles */
.team-score {
  margin: 0 5px;
}

/* Divider style */
.divider {
  margin: 0 5px;
  font-size: 24px;
  font-weight: bold;
  color: white;
}
