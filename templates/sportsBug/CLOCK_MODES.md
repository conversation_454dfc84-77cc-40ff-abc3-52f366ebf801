# SportsBug Clock Modes Documentation

## Overview

The SportsBug component now supports three different clock modes to accommodate various broadcast scenarios and workflows. This provides flexibility for different types of sports coverage and operator preferences.

## Clock Modes

### 1. Manual Mode (`clockMode: "manual"`)

**Use Case**: Traditional broadcast workflow where operators manually update the clock
**Data Source**: External controller/operator via CasparCG
**Control**: Full manual control

#### How it works:
- Clock time comes from the `clock` field in the manifest data
- Operator manually updates the time through CasparCG interface
- No automatic incrementing or decrementing
- Immediate updates when data changes

#### Best for:
- Live sports where timing is controlled by official timekeepers
- Situations requiring precise manual control
- Integration with external timing systems
- Traditional broadcast workflows

#### Example Usage:
```json
{
  "clockMode": "manual",
  "clock": "45:30"
}
```

### 2. Timer Mode (`clockMode: "timer"`)

**Use Case**: Automatic counting up from a start time
**Data Source**: JavaScript interval timer
**Control**: Start/stop via `timerRunning` boolean

#### How it works:
- Timer starts from `timerStartTime` (format: MM:SS)
- Automatically increments every second when `timerRunning` is true
- Pauses when `timerRunning` is false
- Resets when `timerStartTime` changes

#### Best for:
- Match timing that counts up (e.g., soccer, basketball)
- Training sessions or practice matches
- Situations where you want automatic timing
- Backup timing when main clock fails

#### Example Usage:
```json
{
  "clockMode": "timer",
  "timerStartTime": "00:00",
  "timerRunning": true
}
```

### 3. Countdown Mode (`clockMode: "countdown"`)

**Use Case**: Automatic counting down from a set time
**Data Source**: JavaScript interval timer
**Control**: Start/stop via `timerRunning` boolean

#### How it works:
- Timer starts from `countdownTime` (format: MM:SS)
- Automatically decrements every second when `timerRunning` is true
- Stops at 00:00 (doesn't go negative)
- Pauses when `timerRunning` is false
- Resets when `countdownTime` changes

#### Best for:
- Shot clocks (basketball)
- Period/quarter timing
- Timeout countdowns
- Pre-game countdowns

#### Example Usage:
```json
{
  "clockMode": "countdown",
  "countdownTime": "24:00",
  "timerRunning": true
}
```

## Control Parameters

### Required for All Modes:
- `clockMode`: String enum ("manual", "timer", "countdown")

### Manual Mode Parameters:
- `clock`: String - The time to display (MM:SS format)

### Timer Mode Parameters:
- `timerStartTime`: String - Starting time in MM:SS format
- `timerRunning`: Boolean - Whether the timer is actively running

### Countdown Mode Parameters:
- `countdownTime`: String - Starting countdown time in MM:SS format
- `timerRunning`: Boolean - Whether the countdown is actively running

## Implementation Details

### Time Format Validation
- All time inputs are validated for MM:SS format
- Invalid formats default to "00:00"
- Supports up to 99:59 (99 minutes, 59 seconds)

### Timer Synchronization
- Timers update every 1000ms (1 second)
- State is maintained even when graphic is hidden
- Timer resets when start time parameters change

### Performance Considerations
- Uses `useCallback` and `useMemo` for optimization
- Automatic cleanup of intervals on unmount
- Minimal re-renders through proper dependency management

## Workflow Examples

### Soccer Match Example:
```json
{
  "clockMode": "timer",
  "timerStartTime": "00:00",
  "timerRunning": true,
  "extraTime": true,
  "extraTimeClock": "90:00",
  "extraTimeAmount": "3"
}
```

### Basketball Shot Clock Example:
```json
{
  "clockMode": "countdown",
  "countdownTime": "24:00",
  "timerRunning": true
}
```

### Manual Control Example:
```json
{
  "clockMode": "manual",
  "clock": "12:45"
}
```

## Integration with External Systems

### CasparCG Integration
- All parameters can be controlled via CasparCG's data interface
- Real-time updates without graphic restart
- Compatible with existing CasparCG workflows

### External Timer Systems
- Manual mode allows integration with official timing systems
- Timer modes provide backup/secondary timing
- Can switch between modes during broadcast

### Automation Scripts
- Timer controls can be automated via CasparCG commands
- Start/stop timing based on external triggers
- Reset timers for new periods/quarters

## Best Practices

### Mode Selection:
1. **Use Manual Mode** for official match timing
2. **Use Timer Mode** for practice or when you need automatic counting up
3. **Use Countdown Mode** for shot clocks, timeouts, or period timing

### Operator Guidelines:
1. Always verify clock mode before going live
2. Test timer functionality during rehearsals
3. Have manual mode as backup for timer modes
4. Monitor timer accuracy against official clocks

### Technical Considerations:
1. Timer accuracy depends on browser performance
2. Long-running timers may drift slightly
3. Always sync with official timing when possible
4. Use manual mode for critical timing situations

## Troubleshooting

### Timer Not Starting:
- Check `timerRunning` is set to `true`
- Verify `timerStartTime` or `countdownTime` is valid
- Ensure `clockMode` is set correctly

### Time Display Issues:
- Validate time format (MM:SS)
- Check for invalid characters in time strings
- Verify clock mode matches intended usage

### Performance Issues:
- Monitor for memory leaks in long broadcasts
- Restart graphic if timer becomes inaccurate
- Use manual mode for critical timing
