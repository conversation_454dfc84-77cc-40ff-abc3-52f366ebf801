.main-container {
  position: absolute;
  bottom: 2%; /* 2% padding from the bottom */
  left: 0;
  width: 95%; /* 100% width minus 5% padding on each side */
  margin: 0 2.5%; /* Centers the graphic with 5% padding on left and right */
  display: flex;
  align-items: stretch; /* Ensures equal height for children */
  height: 17%; /* Set a fixed height (adjust as needed) */
  font-family: Helvetica, sans-serif;
}

.left-container {
  flex: 0.25; /* 30% width */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative; /* Needed for absolute positioning of live-box */
}

.right-container {
  flex: 0.75; /* 70% width */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.live-box {
  position: absolute;
  top: -25%; /* Moves it above the branding-box */
  left: 0;
  width: 100%; /* Same width as branding-box */
  height: 25%; /* Same height as web-box */
  background-color: red;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 25px;
}

.liveFrom-container {
  position: absolute;
  top: 2%;
  right: 2.5%;
  display: flex;
  align-items: stretch;
  height: 5%;
  font-family: Helvetica, sans-serif;
}

.liveFrom-box {
  flex: 1;
  background-color: #3c3d3f;
  color: white;
  font-size: 25px;
  font-weight: bold;
  padding: 5px 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.subject-box {
  z-index: -1;
  position: absolute;
  top: -25%;
  background-color: #3c3d3f;
  color: white;
  font-size: 25px;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: left;
  height: 25%;
}

.subject-bar {
  z-index: 1;
  position: absolute;
  background-color: red;
  height: 100%; /* Matches the height of its parent (headline-box) */
  width: 1%;
  right: -1%; /* Aligns it to the right of branding-box */
}

.branding-box {
  z-index: 2;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #242424;
  color: white;
  font-weight: bold;
  font-size: 45px;
  padding: 10px 15px;
}

.name-box {
  position: absolute;
  width: 73.35%;
  height: 59%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: white;
  color: black;
  font-weight: bold;
  padding: 10px 15px;
  z-index: 1;
}

.name-main {
  font-size: 50px;
  font-weight: bold;
}

.name-sub {
  font-size: 40px;
  font-weight: normal;
}

.headline-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: white;
  color: black;
  font-weight: bold;
  font-size: 45px;
  padding: 10px 15px;
  height: 50%;
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: break-word;
  text-overflow: ellipsis;
}

.web-box {
  z-index: 2;
  background-color: #3c3d3f;
  color: white;
  font-size: 25px;
  padding: 5px 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 25%;
}

.ticker-box {
  z-index: 1;
  background-color: #3c3d3f;
  color: white;
  font-size: 25px;
  padding: 5px 10px;
  display: flex;
  align-items: center;
  justify-content: left;
  height: 25%;
}
