import { useRef, useEffect, useState } from "react";
import {
  render,
  GsapTimeline,
  useCasparData,
  useRssFeed,
} from "@nxtedition/graphics-kit";
import gsap from "gsap";
import "./style.css";

const CBSNewsGraphics = () => {
  const {
    headline,
    subject,
    ticker,
    displayHeadline,
    displayLive,
    displaySubject,
    displayName,
    nameMain,
    nameSub,
    displayLiveFrom,
    liveFrom,
  } = useCasparData();
  const containerRef = useRef(null);
  const headlineRef = useRef(null);
  const tickerRef = useRef(null);
  const brandingBoxRef = useRef(null);
  const webBoxRef = useRef(null);
  const tickerBoxRef = useRef(null);
  const liveBoxRef = useRef(null);
  const subjectBoxRef = useRef(null);
  const subjectLineRef = useRef(null);
  const nameBoxRef = useRef(null);
  const liveFromContainerRef = useRef(null);

  // ********** FETCH RSS FEED FROM TICKER URL **********
  const feed = useRssFeed(ticker);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [currentText, setCurrentText] = useState("");

  // ********** BOX ANIMATIONS **********
  // ********** TICKER CYCLE **********
  useEffect(() => {
    if (!feed?.items?.length) return;

    const cycleText = () => {
      gsap.to(tickerRef.current, {
        opacity: 0,
        duration: 1,
        onComplete: () => {
          setCurrentIndex((prevIndex) => (prevIndex + 1) % feed.items.length);
          setCurrentText(
            feed.items[(currentIndex + 1) % feed.items.length]?.title || ""
          );
          gsap.to(tickerRef.current, { opacity: 1, duration: 1 });
        },
      });
    };

    setCurrentText(feed.items[0]?.title || "");
    const interval = setInterval(cycleText, 12000); // 10s display + 2s fade
    return () => clearInterval(interval);
  }, [feed]);

  // ********** MAIN HEADLINE **********
  useEffect(() => {
    const tl = gsap.timeline({ defaults: { duration: 0.5 } });

    if (displayHeadline) {
      tl.set(headlineRef.current, { visibility: "visible" })
        .to(headlineRef.current, { opacity: 1 });
    } else {
      tl.to(headlineRef.current, { opacity: 0, delay: 0.5 }).set(
        headlineRef.current,
        { visibility: "hidden" }
      );
    }
  }, [displayHeadline]);

  // ********** LIVE **********
  useEffect(() => {
    const tl = gsap.timeline({ defaults: { duration: 0.5 } });

    if (displayLive) {
      tl.set(liveBoxRef.current, { visibility: "visible", y: 50 })
        .to(liveBoxRef.current, { y: 0, opacity: 1 });
    } else {
      tl.to(liveBoxRef.current, { y: 50, opacity: 1 })
        .set(liveBoxRef.current, { visibility: "hidden" });
    }
  }, [displayLive]);

  // ********** SUBJECT **********
  useEffect(() => {
    const tl = gsap.timeline({ defaults: { duration: 0.5 } });

    // Only show subject if both displaySubject AND displayHeadline are true
    if (displaySubject && displayHeadline) {
      tl.set(subjectBoxRef.current, { visibility: "visible", y: 50 })
        .set(subjectLineRef.current, { visibility: "visible", x: -5 })
        .to(subjectBoxRef.current, { y: 0, opacity: 1, delay: 0.5 })
        .to(subjectLineRef.current, { x: 0, opacity: 1 }, "<");
    } else {
      tl.to(subjectBoxRef.current, { y: 50, opacity: 0 })
        .to(subjectLineRef.current, { x: -5, opacity: 0 }, "<")
        .set(subjectBoxRef.current, { visibility: "hidden" })
        .set(subjectLineRef.current, { visibility: "hidden" });
    }
  }, [displaySubject, displayHeadline]); // Add displayHeadline to dependency array

  // ********** NAME BOX **********
  useEffect(() => {
    const tl = gsap.timeline();

    if (displayName) {
      tl.set(nameBoxRef.current, { visibility: "visible", opacity: 0 }).to(
        nameBoxRef.current,
        { opacity: 1, duration: 0.5 }
      );
    } else {
      tl.to(nameBoxRef.current, { opacity: 0, duration: 0.5 }).set(
        nameBoxRef.current,
        { visibility: "hidden" }
      );
    }
  }, [displayName]);

  // ********** LIVE FROM **********
  useEffect(() => {
    const tl = gsap.timeline();

    if (displayLiveFrom) {
      tl.set(liveFromContainerRef.current, {
        visibility: "visible",
        opacity: 0,
      }).to(liveFromContainerRef.current, { opacity: 1, duration: 0.5 });
    } else {
      tl.to(liveFromContainerRef.current, { opacity: 0, duration: 0.5 }).set(
        liveFromContainerRef.current,
        { visibility: "hidden" }
      );
    }
  }, [displayLiveFrom]);

  // ********** MAIN ANIMATION **********
  return (
    <GsapTimeline
      onPlay={(timeline) => {
        // ********** BOX INITIAL VISIBILITY SETUP **********
        if (displayHeadline) {
          gsap.set(headlineRef.current, { opacity: 1, visibility: "visible" });
        } else {
          gsap.set(headlineRef.current, { opacity: 0, visibility: "hidden" });
        }
        if (displayLive) {
          gsap.set(liveBoxRef.current, { opacity: 1, visibility: "visible" });
        } else {
          gsap.set(liveBoxRef.current, { opacity: 0, visibility: "hidden" });
        }
        if (displaySubject && displayHeadline) {
          gsap.set(subjectBoxRef.current, {
            opacity: 1,
            visibility: "visible",
          });
          gsap.set(subjectLineRef.current, {
            opacity: 1,
            visibility: "visible",
          });
        } else {
          gsap.set(subjectBoxRef.current, { opacity: 0, visibility: "hidden" });
          gsap.set(subjectLineRef.current, {
            opacity: 0,
            visibility: "hidden",
          });
        }
        if (displayName) {
          gsap.set(nameBoxRef.current, { opacity: 1, visibility: "visible" });
        } else {
          gsap.set(nameBoxRef.current, { opacity: 0, visibility: "hidden" });
        }
        if (displayLiveFrom) {
          gsap.set(liveFromContainerRef.current, {
            opacity: 1,
            visibility: "visible",
          });
        } else {
          gsap.set(liveFromContainerRef.current, {
            opacity: 0,
            visibility: "hidden",
          });
        }

        // ********** ALL ANIMATE IN **********
        timeline.fromTo(
          [
            brandingBoxRef.current,
            webBoxRef.current,
            tickerBoxRef.current,
            liveFromContainerRef.current,
          ],
          { opacity: 0 },
          { opacity: 1, duration: 0.5 }
        );
        timeline.fromTo(
          [
            subjectBoxRef.current,
            subjectLineRef.current,
          ],
          { opacity: 0 },
          { opacity: 1, duration: 0.3 }
        );
      }}
      // ********** ALL ANIMATE OFF **********
      onStop={(timeline) => {
        timeline.to([subjectBoxRef.current, subjectLineRef.current], {
          opacity: 0,
          duration: 0.3,
        });
        timeline.to(
          [
            liveBoxRef.current,
            brandingBoxRef.current,
            webBoxRef.current,
            tickerBoxRef.current,
            headlineRef.current,
            subjectBoxRef.current,
            subjectLineRef.current,
            nameBoxRef.current,
            liveFromContainerRef.current,
          ],
          { opacity: 0, duration: 0.3 }
        );
      }}
    >
      <div className="main-container" ref={containerRef}>
        <div className="left-container">
          <div className="live-box" ref={liveBoxRef}>
            LIVE
          </div>
          <div className="branding-box" ref={brandingBoxRef}>
            CBS NEWS 24/7
          </div>
          <div className="web-box" ref={webBoxRef}>
            cbsnews.com
          </div>
          <div className="subject-bar" ref={subjectLineRef}></div>
        </div>
        <div className="right-container">
          <div className="name-box" ref={nameBoxRef}>
            <div className="name-main">{nameMain}</div>
            <div className="name-sub">{nameSub}</div>
          </div>
          <div className="subject-box" ref={subjectBoxRef}>
            {subject}
          </div>
          <div className="headline-box" ref={headlineRef}>
            {headline}
          </div>
          <div className="ticker-box" ref={tickerBoxRef}>
            <span ref={tickerRef}>{currentText}</span>
          </div>
        </div>
      </div>
      <div className="liveFrom-container" ref={liveFromContainerRef}>
        <div className="liveFrom-box">{liveFrom}</div>
      </div>
    </GsapTimeline>
  );
};

render(CBSNewsGraphics);
