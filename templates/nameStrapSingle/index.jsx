import React from 'react'
import { render, GsapTimeline, useCasparData } from '@nxtedition/graphics-kit'
import './style.css'

const nameStrapSingle = () => {
  const { name } = useCasparData()

  return (
    <GsapTimeline
      hide={!name}
      onPlay={timeline => {
        // Slide the whole container in from the right
        timeline.from('.nameStrapSingle-Container', { y: 250, duration: 0.5 })
      }}
      onStop={timeline => {
        // Slide the whole container off-screen when stopping
        timeline.to('.nameStrapSingle-Container', { y: 250, duration: 0.3 })
      }}
    >
      <div className="nameStrapSingle-Container">
        <div key={name} className="nameStrapSingle-content">
          {name}
        </div>
      </div>
    </GsapTimeline>
  )
}

render(nameStrapSingle)