import React, { useState, useEffect, useRef } from 'react'
import { render, GsapTimeline, useCaspar, useCasparData } from '@nxtedition/graphics-kit'
import gsap from 'gsap'
import './style.css'

// Function to retrieve the current time in HH:MM format
function getCurrentTime() {
  const now = new Date()
  return now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) // Returns time as HH:MM
}

// Main component that displays the ticker with the current time and "Breaking News"
function tickerClock() {
  // Retrieve 'isPlaying' state from Caspar, indicating whether the graphic is currently playing
  const { isPlaying } = useCaspar()

  // Retrieve 'breaking' (boolean status for breaking news) from Caspar
  const { breaking } = useCasparData()

  // State to manage the current time, updating it every second
  const [currentTime, setCurrentTime] = useState(getCurrentTime())

  // Refs for the clock and breaking news elements to manipulate their opacity using GSAP animations
  const clockRef = useRef(null)
  const breakingRef = useRef(null)

  // Update the current time every second
  useEffect(() => {
    const intervalId = setInterval(() => {
      setCurrentTime(getCurrentTime())
    }, 1000)

    // Cleanup the interval when the component unmounts
    return () => clearInterval(intervalId)
  }, [])

  // Run the GSAP animation when the breaking news status changes
  useEffect(() => {
    const tl = gsap.timeline({ defaults: { duration: 0.5 } }) // Create GSAP timeline with default duration
    if (breaking) {
      // Fade out clock and fade in breaking news
      tl.to(clockRef.current, { opacity: 0 }, 0)
        .to(breakingRef.current, { opacity: 1 }, 0)
    } else {
      // Fade out breaking news and fade in clock
      tl.to(breakingRef.current, { opacity: 0 }, 0)
        .to(clockRef.current, { opacity: 1 }, 0)
    }
  }, [breaking])

  return (
    <GsapTimeline
      hide={!isPlaying} // Hide clock when not playing
      onPlay={timeline => {
        // Check if breaking is true before the animation starts
        if (breaking) {
          // Set initial state: hide the clock, show breaking news
          gsap.set(clockRef.current, { opacity: 0 })
          gsap.set(breakingRef.current, { opacity: 1 })
        } else {
          // Set initial state: show the clock, hide breaking news
          gsap.set(clockRef.current, { opacity: 1 })
          gsap.set(breakingRef.current, { opacity: 0 })
        }

        // Slide in the clock (or breaking news) when the graphic starts playing
        timeline.from('.tickerClock', { y: 100, duration: 0.8 })
      }}
      onStop={timeline => {
        // Fade out clock when stopping the graphic
        timeline.to('.tickerClock', { y: 100, duration: 0.8 })
      }}
    >
      {/* Clock and Breaking News container */}
      <div className="tickerClock">
        {/* Clock element */}
        <div ref={clockRef} className="tickerClock-content">
          {currentTime} {/* Display the current time */}
        </div>
        
        {/* Breaking News element */}
        <div ref={breakingRef} className="tickerClock-breakingNews">
          BREAKING NEWS {/* This appears only when 'breaking' is true */}
        </div>
      </div>
    </GsapTimeline>
  )
}

// Render the Clock component
render(tickerClock)
