import React from 'react'
import { render, GsapTimeline, useCasparData } from '@nxtedition/graphics-kit'
import './style.css'

const nameStrapSingleV2 = () => {
  const { name, subText } = useCasparData()

  return (
    <GsapTimeline
      hide={!name | !subText}
      onPlay={timeline => {
        // Slide the whole container in from the right
        timeline.from('.nameStrapSingle-Container', { y: 250, duration: 0.5 })
      }}
      onStop={timeline => {
        // Slide the whole container off-screen when stopping
        timeline.to('.nameStrapSingle-Container', { y: 250, duration: 0.3 })
      }}
    >
      <div className="nameStrapSingle-Container">
        <div className="nameStrapSingle-content">
          <div className="nameStrapSingle-name">
            {name}
          </div>
          <div className="nameStrapSingle-subText">
            {subText}
          </div>
        </div>
      </div>
    </GsapTimeline>
  )
}

render(nameStrapSingleV2)