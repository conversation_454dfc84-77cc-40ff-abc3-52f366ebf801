import React, { useEffect, useState } from 'react';
import { render, FramerMotion, useCasparData } from '@nxtedition/graphics-kit';
import { motion } from 'framer-motion';
import './style.css';

const EBUColorBars = () => {
  const { overlayText } = useCasparData(); // Get dynamic overlay text from CasparCG data

  // State for current date and time
  const [dateTime, setDateTime] = useState({
    timecode: '',
    date: ''
  });

  useEffect(() => {
    const updateDateTime = () => {
      const now = new Date();

      // Timecode calculation assuming 25 fps
      const h = String(now.getHours()).padStart(2, '0');
      const m = String(now.getMinutes()).padStart(2, '0');
      const s = String(now.getSeconds()).padStart(2, '0');
      const f = String(Math.floor(now.getMilliseconds() / 40)).padStart(2, '0'); // Assuming 25 fps

      const timecodeStr = `${h}:${m}:${s}:${f}`;

      // Always update timecode and update date every hour
      setDateTime(prevState => {
        const lastUpdate = new Date(prevState.date);

        // Update date only if it's a new hour
        if (now.getHours() !== lastUpdate.getHours()) {
          const year = now.getFullYear();
          const month = String(now.getMonth() + 1).padStart(2, '0'); // Months are zero-based
          const day = String(now.getDate()).padStart(2, '0');

          const datecodeStr = `${year}-${month}-${day}`;

          return {
            timecode: timecodeStr,
            date: datecodeStr
          };
        }

        return {
          timecode: timecodeStr,
          date: prevState.date
        };
      });
    };

    // Initial update
    updateDateTime();

    // Update date and time every 40ms for 25 fps
    const intervalId = setInterval(updateDateTime, 40);

    // Clean up on unmount
    return () => clearInterval(intervalId);
  }, [dateTime.date]); // Dependency on dateTime.date to ensure state is updated

  const colors = [
    'white', '#FFFF00', '#00FFFF', '#00FF00', '#FF00FF', '#FF0000', '#0000FF', 'black'
  ];

  return (
    <FramerMotion hide={false}>
      <div className="color-bars-container">
        {colors.map((color, index) => (
          <motion.div
            key={index}
            className="color-bar"
            style={{
              backgroundColor: color,
              left: `${index * 12.5}%`,
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          />
        ))}
        <div className="overlay">{overlayText || "UK London SPG2 HD"}</div>
        <div className="datetimecode">
          <div>{dateTime.timecode}</div>
          <div>{dateTime.date || "Date not available"}</div>
        </div>
      </div>
    </FramerMotion>
  );
}

render(EBUColorBars);
