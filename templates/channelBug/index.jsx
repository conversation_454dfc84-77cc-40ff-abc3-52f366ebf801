import React from 'react'
import { render, GsapTimeline } from '@nxtedition/graphics-kit'
import './style.css'

const channelBug = () => {
  return (
    <GsapTimeline
      onPlay={timeline => {
        timeline.from('.channelBug', { opacity: 0, duration: 0.5 })
      }}
      onStop={timeline => {
        timeline.to('.channelBug', { opacity: 0, duration: 0.3 })
      }}
    >
      <div className="channelBug">
        <img src="https://trleahy.github.io/API-Endpoints/assets/tvplus-logo.png" alt="Channel Bug" />
      </div>
    </GsapTimeline>
  )
}

render(channelBug)
