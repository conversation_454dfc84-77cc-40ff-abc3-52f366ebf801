import React from 'react'
import { render, GsapTimeline, useCasparData } from '@nxtedition/graphics-kit'
import './style.css'

const nameStrapDouble = () => {
  const { channel01, channel02, channel03, channel04 } = useCasparData() // Retrieve name1 and name2 from Caspar data

  // if (!channel01 || !channel02) {
  //   return null // If one of the names is missing, don't render anything
  // }

  return (
    <GsapTimeline
      onPlay={timeline => {
        // Slide the whole container in from the right
        timeline.from('.content1, .content2, .divider', { opacity: 0, duration: 0.3 })
      }}
      onStop={timeline => {
        // Slide the whole container off-screen when stopping
        timeline.to('.content1, .content2, .divider', { opacity: 0, duration: 0.3 })
      }}
    >
      <div className='divider'></div>
      <div className="content1">
        <span className="channel01">{channel01}</span>
        <span className="channel02">{channel02}</span>
      </div>
      <div className="content2">
        <span className="channel03">{channel03}</span>
        <span className="channel04">{channel04}</span>
      </div>
    </GsapTimeline>
  )
}

render(nameStrapDouble)
