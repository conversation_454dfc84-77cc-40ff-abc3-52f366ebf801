.divider {
  position: fixed;
  right: 50%;
  height: 100%;
  background-color: #00000099;
  width: 10px;
}

.content1 {
  position: fixed;
  bottom: 50%;
  width: 100%;
  background-color: #00000099;
  flex-direction: row;
  display: table; /* Use table layout */
  width: 100%;
  font-family: sans-serif;
  font-size: 50px;
  color: white;
  text-transform: uppercase;
}

.content2 {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #00000099;
  flex-direction: row;
  display: table; /* Use table layout */
  width: 100%;
  font-family: sans-serif;
  font-size: 50px;
  color: white;
  text-transform: uppercase;
}

.channel01, .channel02 {
  display: table-cell; /* Each name acts as a table cell */
  width: 50%; /* Split the width equally */
  white-space: nowrap; /* Prevent text from wrapping */
  text-align: center; /* Left-align the first name */
}

.channel03, .channel04 {
  display: table-cell; /* Each name acts as a table cell */
  width: 50%; /* Split the width equally */
  white-space: nowrap; /* Prevent text from wrapping */
  text-align: center; /* Left-align the first name */
}