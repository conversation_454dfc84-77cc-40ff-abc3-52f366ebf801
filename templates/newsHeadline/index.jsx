import React from 'react'
import { render, GsapTimeline, useCasparData } from '@nxtedition/graphics-kit'
import './style.css'

const newsHeadline = () => {
  const { headline } = useCasparData()

  return (
    <GsapTimeline
      hide={!headline}
      onPlay={timeline => {
        // Slide the whole container in from the right
        timeline.from('#text-container', { y: 250, duration: 0.5 })

        // Animate the body text to appear after the headline with a small delay
        timeline.from('#headline', { x: 1920, duration: 0.5 })
      }}
      onStop={timeline => {
        // Slide the whole container off-screen when stopping
        timeline.to('#text-container', { y: 250, duration: 1 })
      }}
    >
      <div id="text-container">
        <div key={headline} id="headline">
          {headline}
        </div>
      </div>
    </GsapTimeline>
  )
}

render(newsHeadline)