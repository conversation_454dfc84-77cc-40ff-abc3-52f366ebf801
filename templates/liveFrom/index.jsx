import React, { useEffect, useState } from 'react'
import { render, GsapTimeline, useCasparData } from '@nxtedition/graphics-kit'
import './style.css'

const liveFrom = () => {
  const { location, displayTime } = useCasparData()
  const [localTime, setLocalTime] = useState('')
  const [timeZones, setTimeZones] = useState({})

  useEffect(() => {
    // Load time zones and pre-compute local time if displayTime is true
    const loadTimeZones = async () => {
      try {
        const response = await fetch('https://trleahy.github.io/casparcgEndpoints/timezones/timezones.json')
        const data = await response.json()
        setTimeZones(data)

        // Pre-calculate the time if displayTime is true and location exists
        if (displayTime && location && data[location]) {
          const timeZone = data[location]
          const initialTime = new Intl.DateTimeFormat('en-GB', {
            hour: '2-digit',
            minute: '2-digit',
            timeZone,
          }).format(new Date())
          setLocalTime(initialTime)
        }
      } catch (error) {
        console.error('Failed to load time zones', error)
      }
    }

    loadTimeZones()
  }, [location, displayTime])

  // Update the time every second if displayTime is true
  useEffect(() => {
    if (displayTime && location && timeZones[location]) {
      const timeZone = timeZones[location]

      const timer = setInterval(() => {
        const currentTime = new Intl.DateTimeFormat('en-GB', {
          hour: '2-digit',
          minute: '2-digit',
          timeZone,
        }).format(new Date())
        setLocalTime(currentTime)
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [location, displayTime, timeZones])

  return (
    <GsapTimeline
      hide={!location}
      onPlay={timeline => {
        timeline.from('.liveFrom-container', { x: '-100%', duration: 0.5 })
      }}
      onStop={timeline => {
        timeline.to('.liveFrom-container', { x: '-100%', duration: 0.3 })
      }}
    >
      <div className="liveFrom-container">
        <div className="liveFrom-location">
          {location}
        </div>
        {displayTime && localTime && (
          <div className="liveFrom-time">
            {localTime}
          </div>
        )}
        <div className="liveFrom-live">
          LIVE
        </div>
      </div>
    </GsapTimeline>
  )
}

render(liveFrom)
