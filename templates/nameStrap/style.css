/* Styling for single name */
.nameStrapSingle-Container {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 19.5%;
  background-color: white;
  display: flex;
  justify-content: center;
}

.nameStrapSingle-content {
  font-family: sans-serif;
  font-size: 70px;
  font-weight: 600;
  white-space: nowrap;
  color: #1e2832;
}

/* Styling for double name */
.nameStrapDouble-Container {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 19.5%;
  background-color: white;
  display: flex;
}

.nameStrapDouble-content {
  display: flex;
  width: 100%;
  font-family: sans-serif;
  font-size: 70px;
  font-weight: 600;
  color: #1e2832;
}

.name1, .name2 {
  flex: 1;
  white-space: nowrap;
  text-align: center;
}

/* Styling for triple name */
.nameStrapTriple-Container {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 19.5%;
  background-color: white;
  display: flex;
}

.nameStrapTriple-content {
  display: flex;
  width: 100%;
  font-family: sans-serif;
  font-size: 65px;
  font-weight: 600;
  color: #1e2832;
}

.name1, .name2, .name3 {
  flex: 1;
  white-space: nowrap;
  text-align: center;
}
