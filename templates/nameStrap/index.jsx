import React, { useEffect } from 'react';
import { render, GsapTimeline, useCasparData } from '@nxtedition/graphics-kit';
import './style.css';

const NameStrap = () => {
  const { name1, name2, name3 } = useCasparData();

  // Helper function to determine the current strap type
  const getStrapType = () => {
    if (name1 && !name2 && !name3) return 'single';
    if (name1 && name2 && !name3) return 'double';
    if (name1 && name2 && name3) return 'triple';
    return null;
  };

  const strapType = getStrapType();

  return (
    <GsapTimeline
      onPlay={(timeline) => {
        const containerClass = strapType === 'single' ? '.nameStrapSingle-Container' :
                              strapType === 'double' ? '.nameStrapDouble-Container' :
                              strapType === 'triple' ? '.nameStrapTriple-Container' : null;

        if (containerClass) {
          // Slide the whole container in from the right
          timeline.from(containerClass, { y: 250, duration: 0.5 });
        }
      }}
      onStop={(timeline) => {
        const containerClass = strapType === 'single' ? '.nameStrapSingle-Container' :
                              strapType === 'double' ? '.nameStrapDouble-Container' :
                              strapType === 'triple' ? '.nameStrapTriple-Container' : null;

        if (containerClass) {
          // Slide the whole container off-screen when stopping
          timeline.to(containerClass, { y: 250, duration: 0.3 });
        }
      }}
    >
      {/* Render based on available names */}
      {strapType === 'single' && (
        <div className="nameStrapSingle-Container">
          <div key={name1} className="nameStrapSingle-content">
            {name1}
          </div>
        </div>
      )}
      {strapType === 'double' && (
        <div className="nameStrapDouble-Container">
          <div className="nameStrapDouble-content">
            <span className="name1">{name1}</span>
            <span className="name2">{name2}</span>
          </div>
        </div>
      )}
      {strapType === 'triple' && (
        <div className="nameStrapTriple-Container">
          <div className="nameStrapTriple-content">
            <span className="name1">{name1}</span>
            <span className="name2">{name2}</span>
            <span className="name3">{name3}</span>
          </div>
        </div>
      )}
    </GsapTimeline>
  );
};

render(NameStrap);
