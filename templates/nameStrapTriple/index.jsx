import React from 'react'
import { render, GsapTimeline, useCasparData } from '@nxtedition/graphics-kit'
import './style.css'

const nameStrapTriple = () => {
  const { name1, name2, name3 } = useCasparData() // Retrieve name1 and name2 from Caspar data

  if (!name1 || !name2 || !name3) {
    return null // If one of the names is missing, don't render anything
  }

  return (
    <GsapTimeline
      onPlay={timeline => {
        // Slide the whole container in from the right
        timeline.from('.nameStrapTriple-Container', { y: 250, duration: 0.5 })
      }}
      onStop={timeline => {
        // Slide the whole container off-screen when stopping
        timeline.to('.nameStrapTriple-Container', { y: 250, duration: 0.3 })
      }}
    >
      <div className="nameStrapTriple-Container">
        {/* Display both names inside a single div as a two-column layout */}
        <div className="nameStrapTriple-content">
          <span className="name1">{name1}</span>
          <span className="name2">{name2}</span>
          <span className="name2">{name3}</span>
        </div>
      </div>
    </GsapTimeline>
  )
}

render(nameStrapTriple)
