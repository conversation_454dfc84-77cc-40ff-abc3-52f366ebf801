.nameStrapTriple-Container {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 19.5%;
  background-color: white;
  flex-direction: row;
}

.nameStrapTriple-content {
  display: table; /* Use table layout */
  width: 100%;
  padding-top: 1%;
  font-family: sans-serif;
  font-size: 65px;
  font-weight: 600;
  color: #1e2832;
}

.name1, .name2, .name3 {
  display: table-cell; /* Each name acts as a table cell */
  width: 33%; /* Split the width equally */
  white-space: nowrap; /* Prevent text from wrapping */
  text-align: center; /* Left-align the first name */
}