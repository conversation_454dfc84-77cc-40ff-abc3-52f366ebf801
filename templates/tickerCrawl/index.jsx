import React, { useState, useEffect, useRef } from 'react'
import { render, GsapTimeline, useCaspar, useCasparData, useRssFeed, Crawl } from '@nxtedition/graphics-kit'
import gsap from 'gsap'
import './style.css'

// Function to retrieve the current time in HH:MM format
function getCurrentTime() {
  const now = new Date()
  return now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) // Returns time as HH:MM
}

function tickerClockFeed() {
  // Retrieve 'isPlaying' state from Caspar
  const { isPlaying } = useCaspar()
  
  // Retrieve 'breaking' and 'feed' data from Caspar
  const { breaking, feed } = useCasparData()
  
  // State to manage the current time, updating it every second
  const [currentTime, setCurrentTime] = useState(getCurrentTime())
  
  // Refs for clock, breaking news, and ticker elements
  const clockRef = useRef(null)
  const breakingRef = useRef(null)
  
  // RSS feed data
  const rssFeed = useRssFeed(feed)
  console.log('Feed URL:', feed)
  console.log('RSS Feed Data:', rssFeed)
  
  // Update the current time every second
  useEffect(() => {
    const intervalId = setInterval(() => {
      setCurrentTime(getCurrentTime())
    }, 1000)
    return () => clearInterval(intervalId)
  }, [])
  
  // GSAP animation for clock and breaking news
  useEffect(() => {
    const tl = gsap.timeline({ defaults: { duration: 0.5 } })
    if (breaking) {
      // Fade out clock and fade in breaking news
      tl.to(clockRef.current, { opacity: 0 }, 0)
        .to(breakingRef.current, { opacity: 1 }, 0)
    } else {
      // Fade out breaking news and fade in clock
      tl.to(breakingRef.current, { opacity: 0 }, 0)
        .to(clockRef.current, { opacity: 1 }, 0)
    }
  }, [breaking])
  
  return (
    <GsapTimeline
      hide={!isPlaying}
      onPlay={timeline => {
        // Initial visibility setup for clock and breaking news
        if (breaking) {
          gsap.set(clockRef.current, { opacity: 0 })
          gsap.set(breakingRef.current, { opacity: 1 })
        } else {
          gsap.set(clockRef.current, { opacity: 1 })
          gsap.set(breakingRef.current, { opacity: 0 })
        }

        // Slide in the clock/breaking news and ticker when the graphic starts
        timeline.from('.tickerClock, .tickerFeed', { y: 100, duration: 0.5 })
      }}
      onStop={timeline => {
        // Slide out both clock/breaking news and ticker
        timeline.to('.tickerClock, .tickerFeed', { y: 100, duration: 0.3 })
      }}
    >
      {/* Clock and Breaking News container */}
      <div className="tickerClock">
        <div ref={clockRef} className="tickerClock-content">
          {currentTime} {/* Display the current time */}
        </div>
        <div ref={breakingRef} className="tickerClock-breakingNews">
          BREAKING NEWS {/* This appears only when 'breaking' is true */}
        </div>
      </div>

      {/* Ticker container */}
      <div className='tickerFeed'>
        <div className='tickerFeed-content'>
          {rssFeed?.items?.length ? (
            <Crawl
              play={isPlaying}
              items={rssFeed.items.slice(0, 10)}
              renderItem={({ id, title }) => (
                <div key={id} className='tickerFeed-item'>
                  {title}
                </div>
              )}
            />
          ) : (
            <div>No data availables</div>
          )}
        </div>
      </div>
    </GsapTimeline>
  )
}

// Render the merged component
render(tickerClockFeed)
