/* Styling for ticker feed */
.tickerFeed {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 7.5%;
  background-color: #1e2832;
  overflow: hidden;
  padding-top: 10px;
}

.tickerFeed-content {
  display: flex;          /* Arrange items horizontally */
  align-items: center;    /* Vertically center the text */
  font-size: 40px;
  font-family: Arial, Helvetica, sans-serif;
  color: #FFFFFF;
}

.tickerFeed-item {
  margin-right: 50px; /* Add space between the news items */
  white-space: nowrap; /* Prevent text wrapping, so it stays on one line */
}

/* styling for tickerClock */
.tickerClock {
  position: fixed;
  bottom: 0;
  width: 25%;
  height: 7.5%;             /* Use height to match the news ticker */
  z-index: 9999;
  display: flex;            /* Enable flexbox on the container */
  justify-content: center;  /* Center horizontally */
  overflow: hidden;         /* Ensure content doesn't spill outside the ticker */
  padding-top: 10px;
  background: linear-gradient(to right, #1e2832, #1e2832 95%, #00000000);
}
.tickerClock-content {
  font-size: 40px;
  font-family: Arial, Helvetica, sans-serif;
  color: #FFFFFF;
  text-align: center;       /* Ensure text is centered */
}
.tickerClock-breakingNews {
  position: absolute;       /* Make breaking news overlap inside tickerClock */
  top: 0;                   /* Align breaking news at the top */
  left: 0;
  right: 0;
  bottom: 0;                /* Stretch the breaking news to fill the space */
  background-color: #D62828; /* Red background for breaking news */
  background: linear-gradient(to right, #D62828, #D62828 90%, #00000000);
  display: flex;            /* Use flexbox to center the breaking news text */
  justify-content: center;  /* Horizontally center the text */
  padding-top: 10px;
  font-weight: bold;
  font-size: 40px;          /* Make it large to stand out */
  font-family: Arial, Helvetica, sans-serif;
  color: white;
}
