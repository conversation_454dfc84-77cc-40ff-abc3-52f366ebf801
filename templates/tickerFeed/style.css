.tickerFeed {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 7.5%;
  background-color: #1e2832;
  overflow: hidden;
  padding-top: 10px;
}

.tickerFeed-content {
  display: flex;          /* Arrange items horizontally */
  align-items: center;    /* Vertically center the text */
  font-size: 40px;
  font-family: Arial, Helvetica, sans-serif;
  color: #FFFFFF;
}

.tickerFeed-item {
  margin-right: 50px; /* Add space between the news items */
  white-space: nowrap; /* Prevent text wrapping, so it stays on one line */
}
