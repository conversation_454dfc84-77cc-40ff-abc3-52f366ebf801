import { render, Crawl, useCaspar, useCasparData, useRssFeed, GsapTimeline } from '@nxtedition/graphics-kit'
import './style.css'

function tickerFeed() {
  const { isPlaying } = useCaspar()
  const { feed } = useCasparData() // Retrieve the feed URL from manifest.json
  console.log('Feed URL:', feed) // Log feed URL to verify it's correct
  const rssFeed = useRssFeed(feed) // Use the retrieved feed URL in useRssFeed
  console.log('RSS Feed Data:', rssFeed) // Log feed data to verify it's correct

  return (
    <GsapTimeline
      hide={!isPlaying}  // Hide the ticker when not playing
      onPlay={timeline => {
        // Slide the news ticker from off-screen (right) to in view
        timeline.from('.tickerFeed', { y: 100, duration: 0.8 })
      }}
      onStop={timeline => {
        // Fade out clock when stopping the graphic
        timeline.to('.tickerFeed', { y: 100, duration: 0.8 })
      }}
    >
      <div className='tickerFeed'>
        <div className='tickerFeed-content'>
          {rssFeed?.items?.length ? (
            <Crawl
              play={isPlaying}
              items={rssFeed.items.slice(0, 10)}
              renderItem={({ id, title }) => (
                <div key={id} className='tickerFeed-item'>
                  {title}
                </div>
              )}
            />
          ) : (
            // if no data is available show the following
            <div>No data available</div>
          )}
        </div>
      </div>
    </GsapTimeline>
  )
}

render(tickerFeed)
